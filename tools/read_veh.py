import pandas as pd
import os

def generate_sql_from_excel(file_path, output_file, sheet_name='drv', column_name='司机id'):
    # 读取Excel文件
    df = pd.read_csv(file_path)

    # 获取车辆ID列表
    vehicle_ids = df[column_name].tolist()

    # 每5000个ID生成一个SQL语句
    sql_statements = []
    batch_size = 5000
    for i in range(0, len(vehicle_ids), batch_size):
        batch_ids = vehicle_ids[i:i + batch_size]
        sql = f"UPDATE drv_driver SET category_synthesize_code = 9, modify_user = '钱程' WHERE drv_id IN ({', '.join(map(str, batch_ids))});"
        sql_statements.append(sql)

    # 将SQL语句写入文件
    with open(output_file, 'w', encoding='utf-8') as file:
        for sql in sql_statements:
            file.write(sql + '\n')

# 使用示例
current_directory = os.getcwd()

# 文件的相对路径
relative_path = 'tools/drv.csv'  # 替换为您的文件名

# 组合成完整的相对路径
file_path = os.path.join(current_directory, relative_path)

output_file = 'output_sql_statements.sql'
generate_sql_from_excel(file_path, output_file)
