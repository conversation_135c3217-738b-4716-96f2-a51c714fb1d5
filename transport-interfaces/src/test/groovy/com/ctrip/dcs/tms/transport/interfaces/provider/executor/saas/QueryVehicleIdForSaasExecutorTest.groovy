package com.ctrip.dcs.tms.transport.interfaces.provider.executor.saas

import base.BaseTest
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType
import com.ctrip.dcs.tms.transport.application.query.IQueryVehicleForSaasService
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils
import org.mockito.InjectMocks
import org.mockito.Mock

class QueryVehicleIdForSaasExecutorTest extends BaseTest{
    @Mock
    ProductLineBridgeManagement productLineBridgeManagement = Mock()
    @InjectMocks
    QueryVehicleIdForSaasExecutor executor = new QueryVehicleIdForSaasExecutor(productLineBridgeManagement:productLineBridgeManagement)

    def "testSuccess"(){
        given:
        productLineBridgeManagement.queryVehicleIdForSaas(_) >> vehIds

        when:
        def result = executor.execute(new QueryVehicleIdForSaasRequestType())
        then:
        result.getResponseResult().isSuccess() == booleanResult
        result.getVehicleIds().size() == size
        where:
        vehIds || booleanResult || size
        getQueryVehicleIdForSaasResponse1()|| true || 0
        getQueryVehicleIdForSaasResponse2() || true || 2
    }

    QueryVehicleIdForSaasResponseType getQueryVehicleIdForSaasResponse2(){
        QueryVehicleIdForSaasResponseType response = new QueryVehicleIdForSaasResponseType()
        response.setVehicleIds(Arrays.asList(1L,2L))
        return ServiceResponseUtils.success(response);
    }

    QueryVehicleIdForSaasResponseType getQueryVehicleIdForSaasResponse1(){
        QueryVehicleIdForSaasResponseType response = new QueryVehicleIdForSaasResponseType()
        response.setVehicleIds(Arrays.asList())
        return ServiceResponseUtils.success(response);
    }

}
