package com.ctrip.dcs.tms.transport.interfaces.interceptor

import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType
import com.ctrip.dcs.tms.transport.api.model.DrvUpdateSOARequestType
import com.ctrip.dcs.tms.transport.api.model.DrvVehRecruitingAddSOARequestType
import com.ctrip.dcs.tms.transport.api.model.QueryAddTransportGroupCityListSOARequestType
import com.ctrip.dcs.tms.transport.api.model.QueryAddTransportGroupCityListSOAResponseType
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.dto.DrvUdlMethodRuleDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.dto.DrvUdlRuleDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.qconfig.DrvUdlQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TrafficDiverter
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.AddDriverExecutor
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.AddDrvVehRecruitingExecutor
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.AddTransportGroupExecutor
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.QueryAddTransportGroupCityListExecutor
import com.ctrip.dcs.tms.transport.interfaces.provider.executor.UpdateDrvExecutor
import com.ctrip.igt.framework.infrastructure.executor.Executor
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils
import spock.lang.Specification
import spock.lang.Unroll

class WhiteListInterceptorSpockTest extends Specification {
    def testObj = new WhiteListInterceptor()
    def drvUdlQconfig = Mock(DrvUdlQconfig)
    def udlHandler = Mock(UDLHandler)
    def diverter = Mock(TrafficDiverter)
    def drvUdlMethodList = Mock(List)

    def setup() {

        testObj.udlHandler = udlHandler
        testObj.drvUdlQconfig = drvUdlQconfig
        testObj.diverter = diverter
    }

    @Unroll
    def "logUdlTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvUdlQconfig.getRule() >> rule
        udlHandler.getDrvUdl(_) >> "getDrvUdlResponse"
        udlHandler.getDrvUdlByCityId(_) >> "getDrvUdlByCityIdResponse"
        diverter.greatThan(_) >> trficResult

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.logUdlErrorInfo(_, _) >> {}
        spy.getDrvUldMethodList() >> ["String"]
        when:
        def result = spy.logUdl(req, operationName)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        operationName   | req | rule |trficResult|| expectedResult
        "operationName" | new DrvUpdateSOARequestType() | new DrvUdlRuleDTO(open: Boolean.TRUE, rule: ["String": new DrvUdlMethodRuleDTO(percentage: 0, udlKey: "udlKey", keyType: "keyType", keyValueType: "keyValueType")]) | false || null
        "operationName" | new DrvUpdateSOARequestType() | new DrvUdlRuleDTO(open: Boolean.TRUE, rule: ["operationName": new DrvUdlMethodRuleDTO(percentage: 0, udlKey: "udlKey", keyType: "keyType", keyValueType: "keyValueType")]) | false || null
        "operationName" | new DrvUpdateSOARequestType(drvId: 1L) | new DrvUdlRuleDTO(open: Boolean.TRUE, rule: ["operationName": new DrvUdlMethodRuleDTO(percentage: 0, udlKey: "drvId", keyType: "Long", keyValueType: "drvId")]) | false || null
        "operationName" | new DriverInfoSOARequestType(driverIds: "1") | new DrvUdlRuleDTO(open: Boolean.TRUE, rule: ["operationName": new DrvUdlMethodRuleDTO(percentage: 0, udlKey: "drvId", keyType: "String", keyValueType: "driverIds")]) | false || null
        "operationName" | new DrvVehRecruitingAddSOARequestType(cityId: 1) | new DrvUdlRuleDTO(open: Boolean.TRUE, rule: ["operationName": new DrvUdlMethodRuleDTO(percentage: 0, udlKey: "cityId", keyType: "Long", keyValueType: "cityId")]) | false || null
    }


    @Unroll
    def "handleTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getFieldValues(_) >> "getFieldValuesResponse"
        spy.logUdlErrorInfo(_, _) >> {}
        when:
        def result = spy.handle(executor, req)

        then: "验证返回结果里属性值是否符合预期"
        ((QueryAddTransportGroupCityListSOAResponseType)result).getResponseResult().getReturnCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        executor | req   || expectedResult
        getExecutor() | new QueryAddTransportGroupCityListSOARequestType() || "200"
    }

    private static Executor<QueryAddTransportGroupCityListSOARequestType, QueryAddTransportGroupCityListSOAResponseType>  getExecutor() {
        return new QueryAddTransportGroupCityListExecutor() {
            @Override
            QueryAddTransportGroupCityListSOAResponseType execute(QueryAddTransportGroupCityListSOARequestType request) {
                return ServiceResponseUtils.success(new QueryAddTransportGroupCityListSOAResponseType())
            }
        }
    }

    @Unroll
    def "getFieldValuesTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getFieldValues(object)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        object   || expectedResult
        "object" || "value|hash|serialPersistentFields|CASE_INSENSITIVE_ORDER"
    }

    @Unroll
    def "logUdlErrorInfoTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvUdlQconfig.getRule() >> new DrvUdlRuleDTO(open: Boolean.TRUE, rule: ["String": new DrvUdlMethodRuleDTO(percentage: 0, udlKey: "udlKey", keyType: "keyType", keyValueType: "keyValueType")])
        udlHandler.getDrvUdl(_) >> "getDrvUdlResponse"
        udlHandler.getDrvUdlByCityId(_) >> "getDrvUdlByCityIdResponse"
        diverter.greatThan(_) >> true

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.logUdlErrorInfo(_, _) >> {}
        spy.getDrvUldMethodList() >> ["String"]
        when:
        def result = spy.logUdlErrorInfo(req, operationName)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        operationName   | req   || expectedResult
        "operationName" | "req" || null
    }


    @Unroll
    def "getDrvUldMethodListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvUdlQconfig.getRule() >> new DrvUdlRuleDTO(rule: ["String": new DrvUdlMethodRuleDTO()])

        when:
        def result = testObj.getDrvUldMethodList()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult || t
        ["String"] || []
    }
}
