package com.ctrip.dcs.tms.transport.interfaces.listener

import com.ctrip.dcs.tms.transport.application.command.DriverCommandManager
import com.ctrip.dcs.tms.transport.application.command.impl.DomesticDriverCommandHandler
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

class IVRCallResultListenerTest extends Specification {
    def testObj = new IVRCallResultListener()
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def ivrCallService = Mock(IVRCallService)
    def tmsTransportQconfig = Mock(TmsTransportQconfig)
    def driverCommandManager = Mock(DriverCommandManager)

    def setup() {

        testObj.tmsTransportQconfig = tmsTransportQconfig
        testObj.ivrCallService = ivrCallService
        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.driverCommandManager = driverCommandManager
    }

    @Unroll
    def "callIvrTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        ivrCallService.callPhoneForVerify(_, _, _) >> 1L
        ivrCallService.isPhoneVerified(_) >> Boolean.TRUE
        tmsTransportQconfig.getIvrCallVerificationDelayMinutes() >> 0

        when:
        def result = testObj.callIvr(message)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        message || expectedResult
        new BaseMessage(attrs: [phoneNumber: "12345678901", igtCode : "86", modifyUse: "system"]) || null
    }

    @Unroll
    def "handleIVRCallResultTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvDrvierRepository.queryOneDrvByPhone(_) >> new DrvDriverPO(internalScope: 0)
        driverCommandManager.getHandler(_) >> new DomesticDriverCommandHandler()

        when:
        def result = testObj.handleIVRCallResult(message)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        message || expectedResult
        new BaseMessage(attrs: [phoneNumber: "12345678901", igtCode : "86", modifyUse: "system"]) || null
    }
}
