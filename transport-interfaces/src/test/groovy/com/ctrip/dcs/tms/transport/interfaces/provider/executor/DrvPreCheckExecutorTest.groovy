package com.ctrip.dcs.tms.transport.interfaces.provider.executor

import com.ctrip.dcs.tms.transport.api.model.DrvPreCheckRequestType
import com.ctrip.dcs.tms.transport.api.model.DrvPreCheckResponseType
import com.ctrip.dcs.tms.transport.application.dto.PreCheckResultDTO
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.igt.framework.common.result.Result
import spock.lang.Specification
import spock.lang.Unroll

class DrvPreCheckExecutorTest extends Specification {
    def testObj = new DrvPreCheckExecutor()
    def driverQueryService = Mock(DriverQueryService)

    def setup() {

        testObj.driverQueryService = driverQueryService
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverQueryService.drvPreCheck(_) >> Result.Builder.newResult().success().withData(new PreCheckResultDTO(driverId:1L,phoneNumber:"138138138")).build()

        when:
        def result = testObj.execute(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.drvId == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                  || expectedResult
        new DrvPreCheckRequestType() || 1L
    }
}
