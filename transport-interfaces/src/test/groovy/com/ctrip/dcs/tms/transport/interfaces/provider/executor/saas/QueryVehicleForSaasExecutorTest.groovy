package com.ctrip.dcs.tms.transport.interfaces.provider.executor.saas

import base.BaseTest
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType
import com.ctrip.dcs.tms.transport.api.saas.SaasVehicleSoaDTO
import com.ctrip.dcs.tms.transport.application.dto.SaasVehicleDTO
import com.ctrip.dcs.tms.transport.application.query.IQueryVehicleForSaasService
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils
import org.mockito.InjectMocks
import org.mockito.Mock
import spock.lang.Unroll

class QueryVehicleForSaasExecutorTest extends BaseTest{
    @Mock
    ProductLineBridgeManagement productLineBridgeManagement = Mock()
    @InjectMocks
    QueryVehicleForSaasExecutor executor = new QueryVehicleForSaasExecutor(productLineBridgeManagement:productLineBridgeManagement)

    @Unroll
    def "testSuccess"(){
        when:
        productLineBridgeManagement.queryVehicleForSaas(_) >> vehList
        QueryVehicleForSaasRequestType type = new QueryVehicleForSaasRequestType();
        type.setVehicleIds(Arrays.asList(1L,2L))
        def result = executor.execute(type)
        def nullResult = result.getVehicleList() == null
        then:
        result.getResponseResult().isSuccess() == booleanResult
        nullResult == vehListResult
        where:
        vehList || vehListResult || booleanResult
        ServiceResponseUtils.success(new QueryVehicleForSaasResponseType()) || true || true
        getVehList() || false || true
    }

    QueryVehicleForSaasResponseType getVehList(){
        SaasVehicleSoaDTO vehCacheDTO = new SaasVehicleSoaDTO();
        QueryVehicleForSaasResponseType responseType = new QueryVehicleForSaasResponseType();
                responseType.setVehicleList(Arrays.asList(vehCacheDTO));
        return ServiceResponseUtils.success(responseType);
    }


}
