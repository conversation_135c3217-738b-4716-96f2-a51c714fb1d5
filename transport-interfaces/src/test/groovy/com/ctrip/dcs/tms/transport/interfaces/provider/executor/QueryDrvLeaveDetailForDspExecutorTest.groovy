package com.ctrip.dcs.tms.transport.interfaces.provider.executor

import com.ctrip.dcs.tms.transport.api.model.DrvLeaveDetailSOAType
import com.ctrip.dcs.tms.transport.api.model.QueryDrvLeaveDetailForDspSOARequestType
import com.ctrip.dcs.tms.transport.api.model.QueryDrvLeaveDetailForDspSOAResponseType
import com.ctrip.dcs.tms.transport.application.query.DriverLeaveQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvLeaveDetailPO
import com.ctrip.igt.framework.common.result.Result
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

class QueryDrvLeaveDetailForDspExecutorTest extends Specification {
    def testObj = new QueryDrvLeaveDetailForDspExecutor()
    def driverLeaveQueryService = Mock(DriverLeaveQueryService)

    def setup() {

        testObj.driverLeaveQueryService = driverLeaveQueryService
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverLeaveQueryService.queryDrvLeaveDetailForDsp(_, _) >> Result.Builder.<List<DrvLeaveDetailPO>>newResult().success().withData(Lists.newArrayList(getDrvDetail())).build()

        when:
        def result = testObj.execute(queryDrvLeaveDetailForDspSOARequestType)

        then: "验证返回结果里属性值是否符合预期"
        result.data.get(0).drvId == expectedResult
        where: "表格方式验证多种分支调用场景"
        queryDrvLeaveDetailForDspSOARequestType                       || expectedResult
        new QueryDrvLeaveDetailForDspSOARequestType(drvIds: "1234") || 1L
    }

    DrvLeaveDetailPO getDrvDetail() {
        String timestampString = "2021-01-01 10:10:10.0";
        DrvLeaveDetailPO po = new DrvLeaveDetailPO();
        po.setId(1L);
        po.setActive(true)
        po.setLeaveBeginTime(Timestamp.valueOf(timestampString));
        po.setLeaveEndTime(Timestamp.valueOf(timestampString))
        po.setDatachangeCreatetime(Timestamp.valueOf(timestampString))
        po.setDatachangeLasttime(Timestamp.valueOf(timestampString))
        po.setDatachangeDeltime(Timestamp.valueOf(timestampString))
        po.setDrvId(1L);
        return po;
    }
}
