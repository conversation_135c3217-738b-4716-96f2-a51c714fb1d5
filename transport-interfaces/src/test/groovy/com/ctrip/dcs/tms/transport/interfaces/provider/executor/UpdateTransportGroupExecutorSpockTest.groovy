package com.ctrip.dcs.tms.transport.interfaces.provider.executor

import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupSOARequestType
import com.ctrip.dcs.tms.transport.application.command.InOrderConfigCommandService
import com.ctrip.dcs.tms.transport.application.command.TransportGroupCommandService
import com.ctrip.dcs.tms.transport.application.command.WorkShiftCommandService
import com.ctrip.dcs.tms.transport.application.helper.ShortTransportGroupHelper
import com.ctrip.dcs.tms.transport.application.query.ICheckSupplierPermissionService
import com.ctrip.dcs.tms.transport.application.query.InOrderConfigQueryService
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.application.query.WorkShiftQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import spock.lang.Specification
import spock.lang.Unroll

class UpdateTransportGroupExecutorSpockTest extends Specification {
    def testObj = new UpdateTransportGroupExecutor()
    def transportGroupCommandService = Mock(TransportGroupCommandService)
    def inOrderConfigCommandService = Mock(InOrderConfigCommandService)
    def inOrderConfigQueryService = Mock(InOrderConfigQueryService)
    def transportGroupQueryService = Mock(TransportGroupQueryService)
    def workShiftCommandService = Mock(WorkShiftCommandService)
    def workShiftQueryService = Mock(WorkShiftQueryService)
    def enumRepository = Mock(EnumRepository)
    def overseasQconfig = Mock(OverseasQconfig)
    def checkSupplierPermissionService = Mock(ICheckSupplierPermissionService)
    def mobileHelper = Mock(MobileHelper)
    def shortTransportGroupHelper = Spy(ShortTransportGroupHelper)

    def setup() {
        testObj.transportGroupCommandService = transportGroupCommandService
        testObj.inOrderConfigCommandService = inOrderConfigCommandService
        testObj.inOrderConfigQueryService = inOrderConfigQueryService
        testObj.transportGroupQueryService = transportGroupQueryService
        testObj.workShiftCommandService = workShiftCommandService
        testObj.workShiftQueryService = workShiftQueryService
        testObj.enumRepository = enumRepository
        testObj.overseasQconfig = overseasQconfig
        testObj.checkSupplierPermissionService = checkSupplierPermissionService
        testObj.mobileHelper = mobileHelper
        testObj.shortTransportGroupHelper = shortTransportGroupHelper
    }

    @Unroll
    def "checkShortTransportGroupDriverTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        transportGroupQueryService.queryRelationDrvIdListByTransportGroupId(_) >> [1L]
        shortTransportGroupHelper.checkShortTransportGroupDriver(_, _, _) >> ResponseResultUtil.success()

        when:
        def result = testObj.checkShortTransportGroupDriver(requestType, dbData)

        then: "验证返回结果里属性值是否符合预期"
        result.code == expectedResult
        where: "表格方式验证多种分支调用场景"
        dbData                                                                                                                         | requestType                                                                                                                || expectedResult
        new TspTransportGroupPO(shortTransportGroup: 0) | new UpdateTransportGroupSOARequestType(transportGroupId: 1L, shortTransportGroup: 0) || "200"
        new TspTransportGroupPO(shortTransportGroup: null) | new UpdateTransportGroupSOARequestType(transportGroupId: 1L, shortTransportGroup: 0) || "200"
        new TspTransportGroupPO(shortTransportGroup: 1) | new UpdateTransportGroupSOARequestType(transportGroupId: 1L, shortTransportGroup: 0) || "200"
    }
}

