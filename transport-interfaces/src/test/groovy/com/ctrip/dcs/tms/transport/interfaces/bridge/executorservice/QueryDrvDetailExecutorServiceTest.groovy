package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice

import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.igt.framework.common.result.Result
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class QueryDrvDetailExecutorServiceTest extends Specification {
    def testObj = new QueryDrvDetailExecutorService()
    def driverQueryService = Mock(DriverQueryService)

    def setup() {

        testObj.driverQueryService = driverQueryService
    }


    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverQueryService.queryDrvDetail(_) >> Result.Builder.<QueryDrvDetailSOAResponseType>newResult().success().withData(new QueryDrvDetailSOAResponseType()).build()


        when:
        def result = testObj.execute(queryDrvDetailRequestType)

        then: "验证返回结果里属性值是否符合预期"

        result.data == expectedResult
        where: "表格方式验证多种分支调用场景"
        queryDrvDetailRequestType                   || expectedResult
        new QueryDrvDetailSOARequestType(drvId: 1L) || null
    }

}

