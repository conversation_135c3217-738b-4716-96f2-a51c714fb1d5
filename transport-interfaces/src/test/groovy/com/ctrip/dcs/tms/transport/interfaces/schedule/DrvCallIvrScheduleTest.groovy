package com.ctrip.dcs.tms.transport.interfaces.schedule

import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

class DrvCallIvrScheduleTest extends Specification {
    def testObj = new DrvCallIvrSchedule()
    def queryAllDrvForSchedule = Mock(QueryAllDrvForSchedule)
    def ivrCallService = Mock(IVRCallService)

    def setup() {

        testObj.ivrCallService = ivrCallService
        testObj.queryAllDrvForSchedule = queryAllDrvForSchedule
    }

    @Unroll
    def "updateDrvInfoTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.updateDrvInfo(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"drvStatus\":\"1\"}") || null
    }
}
