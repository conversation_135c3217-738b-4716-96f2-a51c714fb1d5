package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.RecruitingApproveSOARequestDTO;
import com.ctrip.dcs.tms.transport.api.model.RecruitingApproveSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.RecruitingApproveSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.RecruitingCommandService;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RecruitingApproveActionExecutorTest {

    @InjectMocks
    RecruitingApproveActionExecutor actionExecutor;
    @Mock
    private RecruitingCommandService recruitingCommandService;

    @Test
    public void execute() {
        RecruitingApproveSOARequestType requestType = new RecruitingApproveSOARequestType();
        RecruitingApproveSOARequestDTO soaRequestDTO = new RecruitingApproveSOARequestDTO();
        soaRequestDTO.setApproverAperation(1);
        requestType.setData(soaRequestDTO);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().fail().withCode("500").withMsg("111").build();
        Mockito.when(recruitingCommandService.approveRoute(requestType,1)).thenReturn(result);
        RecruitingApproveSOAResponseType soaResponseType =  actionExecutor.execute(requestType);
        Assert.assertTrue(soaResponseType!=null);
    }
}
