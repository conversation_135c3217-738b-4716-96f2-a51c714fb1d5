package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DrvAddPaiayAccountRequestType;
import com.ctrip.dcs.tms.transport.api.model.DrvAddPaiayAccountResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DrvAddPaianAccountExecutorTest{

    @InjectMocks
    DrvAddPaianAccountExecutor executor;
    @Mock
    private DriverCommandService driverCommandService;

    @Test
    public void testExecute() {
        DrvAddPaiayAccountRequestType requestType = new DrvAddPaiayAccountRequestType();
        requestType.setDrvId(1L);
        requestType.setPaiayAccount("11");
        requestType.setPaiayEmail("111");
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().build();
        Mockito.when(driverCommandService.drvAddPaiayAccount(Mockito.anyLong(),Mockito.anyString(),Mockito.anyString())).thenReturn(result);
        DrvAddPaiayAccountResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(responseType!=null);
    }

    @Test
    public void testExecute1() {
        DrvAddPaiayAccountRequestType requestType = new DrvAddPaiayAccountRequestType();
        requestType.setDrvId(1L);
        requestType.setPaiayAccount("11");
        requestType.setPaiayEmail("111");
        Result<Boolean> result = Result.Builder.<Boolean>newResult().fail().build();
        Mockito.when(driverCommandService.drvAddPaiayAccount(Mockito.anyLong(),Mockito.anyString(),Mockito.anyString())).thenReturn(result);
        DrvAddPaiayAccountResponseType responseType =  executor.execute(requestType);
        Assert.assertTrue(responseType!=null);
    }
}