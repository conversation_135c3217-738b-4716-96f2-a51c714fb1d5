package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryCityNetConfigListExecutorTest {

    @InjectMocks
    QueryCityNetConfigListExecutor executor;
    @Mock
    CommonCommandService commandService;

    @Test
    public void execute() {
        QueryCityNetConfigListSOARequestType soaRequestType = new QueryCityNetConfigListSOARequestType();
        List<QueryCityNetConfigListSOADTO> list = Lists.newArrayList();
        Result<List<QueryCityNetConfigListSOADTO>> result = Result.Builder.<List<QueryCityNetConfigListSOADTO>>newResult().success().withData(list).build();
        Mockito.when(commandService.queryCityNetConfigList(soaRequestType)).thenReturn(result);
        QueryCityNetConfigListSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
