package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryRecruitingDrvForPenaltyRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryRecruitingDrvForPenaltyResponseType;
import com.ctrip.dcs.tms.transport.application.query.RecruitingQueryService;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class QueryRecruitingDrvForPenaltyExecutorTest {

    @InjectMocks
    QueryRecruitingDrvForPenaltyExecutor executor;

    @Mock
    RecruitingQueryService recruitingQueryService;

    @Test
    public void execute() {
        QueryRecruitingDrvForPenaltyRequestType requestType = new QueryRecruitingDrvForPenaltyRequestType();
        requestType.setDriverId(1L);
        QueryRecruitingDrvForPenaltyResponseType responseType = new QueryRecruitingDrvForPenaltyResponseType();
        responseType.setApproveSchedule(1);
        Result<QueryRecruitingDrvForPenaltyResponseType> result = Result.Builder.<QueryRecruitingDrvForPenaltyResponseType>newResult().withData(responseType).success().build();
        Mockito.when(recruitingQueryService.queryRecruitingDrvForPenalty(requestType)).thenReturn(result);
        QueryRecruitingDrvForPenaltyResponseType responseTypes = executor.execute(requestType);
        Assert.assertTrue(responseTypes != null);
    }
}
