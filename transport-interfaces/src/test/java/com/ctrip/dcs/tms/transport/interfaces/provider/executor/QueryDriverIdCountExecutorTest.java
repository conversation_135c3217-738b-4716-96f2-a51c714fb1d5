package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOAResponseType;
import com.ctrip.dcs.tms.transport.application.convert.DrvResourceConverter;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/3/7 16:02
 */
@RunWith(MockitoJUnitRunner.class)
public class QueryDriverIdCountExecutorTest {

    @InjectMocks
    private QueryDriverIdCountExecutor queryDriverIdCountExecutor;

    @Mock
    private DriverQueryService driverQueryService;

    @Mock
    private DrvResourceConverter drvResourceConverter;

    @Test
    public void test() {
        QueryDriverIdCountSOAResponseType responseType = queryDriverIdCountExecutor.execute(new QueryDriverIdCountSOARequestType());
        Assert.assertTrue(responseType != null);
        Assert.assertTrue(responseType.getResponseResult().getReturnCode() == ServiceResponseConstants.ResStatus.SUCCESS_CODE);
    }

    @Test(expected = ServiceValidationException.class)
    public void onExecuting() {
        QueryDriverIdCountSOARequestType req = new QueryDriverIdCountSOARequestType();
        queryDriverIdCountExecutor.onExecuting(req);
    }

    @Test
    public void onExecuting1() {
        QueryDriverIdCountSOARequestType req = new QueryDriverIdCountSOARequestType();
        req.setCountryIdList(Lists.newArrayList(1L));
        queryDriverIdCountExecutor.onExecuting(req);
    }

}