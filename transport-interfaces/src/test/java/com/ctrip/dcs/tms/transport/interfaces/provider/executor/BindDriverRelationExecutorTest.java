package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.command.impl.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class BindDriverRelationExecutorTest extends Mockito {

    @InjectMocks
    BindDriverRelationExecutor executor;
    @Mock
    private TransportGroupCommandService transportGroupCommandService;

    @InjectMocks
    TransportGroupCommandServiceImpl transportGroupCommandServiceImpl;

    @Mock
    private TransportGroupRepository transportGroupRepository;

    @Mock
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;

    @Before
    public void ready() {
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
//        Mockito.when(transportGroupRepository.queryTransportGroupDetail(1L)).thenReturn(transportGroupPO);
        List<Long> list = Lists.newArrayList();
        list.add(1L);
        List<Long> list1 = Lists.newArrayList();
        list1.add(1L);
//        Mockito.when(tspTransportGroupDriverRelationRepository.queryRelationDrvListByTransportGroupIdAndDrvIdList(1L,list,2L)).thenReturn(list1);
    }

    @Test
    public void execute() {
        DriverRelationBindRequestSOAType relationBindRequestSOAType = new DriverRelationBindRequestSOAType();
        relationBindRequestSOAType.setDrvIdList(Arrays.asList(1L));
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().build();
        when(transportGroupCommandService.bindDriverRelation(relationBindRequestSOAType)).thenReturn(result);
        DriverRelationBindResponseSOAType soaType=  executor.execute(relationBindRequestSOAType);
        Assert.assertTrue(!Objects.isNull(soaType));
    }

    @Test
    public void test1() {
        DriverRelationBindRequestSOAType relationBindRequestSOAType = new DriverRelationBindRequestSOAType();
        relationBindRequestSOAType.setDrvIdList(Arrays.asList(1L));
        Result<Boolean> result = null;
        when(transportGroupCommandService.bindDriverRelation(relationBindRequestSOAType)).thenReturn(result);
        DriverRelationBindResponseSOAType soaType = executor.execute(relationBindRequestSOAType);
        Assert.assertTrue(!Objects.isNull(soaType));
    }
}
