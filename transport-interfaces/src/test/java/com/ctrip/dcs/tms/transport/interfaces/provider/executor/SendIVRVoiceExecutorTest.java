package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.model.SendIVRVoiceRequestType;
import com.ctrip.model.SendIVRVoiceResponseType;
import com.dianping.cat.Cat;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest(Cat.class)
public class SendIVRVoiceExecutorTest {

    @InjectMocks
    private SendIVRVoiceExecutor executor;

    @Mock
    private IVRCallService ivrCallService;

    @Mock
    private CommonConfig commonConfig;

    private static final String CHANNEL = "tms_driver_register";

    @Before
    public void setUp() {
        PowerMockito.mockStatic(Cat.class);
//        PowerMockito.when(Cat.logEvent(anyString(), anyString())).thenReturn(null);
//        PowerMockito.when(Cat.logEvent(anyString(), anyString(), anyString(), anyString())).thenReturn(null);

        // Mock the CommonConfig - 不再需要mock getIvrVerificationExpirationMinutes，因为SendIVRVoiceExecutor不直接使用它
    }

    @Test
    public void testExecute_Success() {
        // Setup
        SendIVRVoiceRequestType requestType = new SendIVRVoiceRequestType();
        requestType.setCountryCode("86");
        requestType.setMobilePhone("13800138000");
        requestType.setLanguage("CN");

        when(ivrCallService.callPhoneForVerify("13800138000", "86", CHANNEL, requestType.getLanguage()))
                .thenReturn(123L);

        // Execute
        SendIVRVoiceResponseType result = executor.execute(requestType);

        // Verify
        Assert.assertTrue(result.getResponseResult().isSuccess());
        Assert.assertEquals(Long.valueOf(123L), result.getTaskId());

        // Verify the correct parameters were passed to the service
        verify(ivrCallService).callPhoneForVerify("13800138000", "86", CHANNEL, requestType.getLanguage());
    }

    @Test
    public void testExecute_DifferentCountryCode() {
        // Setup
        SendIVRVoiceRequestType requestType = new SendIVRVoiceRequestType();
        requestType.setCountryCode("1");
        requestType.setMobilePhone("4155552671");
        requestType.setLanguage("CN");

        when(ivrCallService.callPhoneForVerify("4155552671", "1", CHANNEL, requestType.getLanguage()))
                .thenReturn(123L);

        // Execute
        SendIVRVoiceResponseType result = executor.execute(requestType);

        // Verify
        Assert.assertTrue(result.getResponseResult().isSuccess());
        Assert.assertEquals(Long.valueOf(123L), result.getTaskId());

        // Verify the correct parameters were passed to the service
        verify(ivrCallService).callPhoneForVerify("4155552671", "1", CHANNEL, requestType.getLanguage());
    }

    @Test
    public void testExecute_CallInitiationFailed() {
        // Setup
        SendIVRVoiceRequestType requestType = new SendIVRVoiceRequestType();
        requestType.setCountryCode("86");
        requestType.setMobilePhone("13800138000");
        requestType.setLanguage("CN");

        when(ivrCallService.callPhoneForVerify("13800138000", "86", CHANNEL, requestType.getLanguage()))
                .thenReturn(null);

        // Execute
        SendIVRVoiceResponseType result = executor.execute(requestType);

        // Verify
        Assert.assertFalse(result.getResponseResult().isSuccess());
        Assert.assertEquals("FAILED", result.getResponseResult().getReturnCode());
    }
}
