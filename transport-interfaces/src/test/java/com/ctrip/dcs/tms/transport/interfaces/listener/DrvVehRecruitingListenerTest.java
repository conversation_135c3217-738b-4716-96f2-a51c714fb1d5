package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.framework.ucs.client.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.base.*;
import retrofit2.http.HEAD;

import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvVehRecruitingListenerTest {

    @InjectMocks
    DrvVehRecruitingListener listener;
    @Mock
    DrvRecruitingRepository drvRecruitingRepository;
    @Mock
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Mock
    TmsTransportQconfig qconfig;
    @Mock
    CertificateCheckQueryService certificateCheckQueryService;
    @Mock
    TmsCertificateCheckRepository checkRepository;
    @Mock
    private TmsRecruitingApproveStepRepository stepRepository;
    @Mock
    private TmsRecruitingApproveStepChildRepository childRepository;
    @Mock
    private DrvVehRecruitingCommandService drvVehRecruitingCommandService;
    @Mock
    TmsCityNetConfigRepository tmsCityNetConfigRepository;
    @Mock
    TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Test
    public void drvVehRecruitingApproveAgingListener() throws SQLException {
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return null;
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return null;
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 1;
            }

            @Override
            public long getLongProperty(String s) {
                return 1L;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 0;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setApproverStatus(1);
        drvRecruitingPO.setApproveAging(1);
        drvRecruitingPO.setApproveTime(DateUtil.getNow());
        drvRecruitingPO.setDrvFrom(1);
        drvRecruitingPO.setVehicleId(1L);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        Mockito.when(qconfig.getRecruitingApproveAgingThreshold()).thenReturn(0);
        Mockito.when(drvRecruitingRepository.updateApproveAging(Arrays.asList(1L), TmsTransportConstant.ApproveAgingEnum.HASTIMEOUT.getCode())).thenReturn(1);
        Mockito.when(vehicleRecruitingRepository.updateApproveAging(Arrays.asList(1L), TmsTransportConstant.ApproveAgingEnum.HASTIMEOUT.getCode())).thenReturn(1);
        listener.drvVehRecruitingApproveAgingListener(message);
//        Assert.assertTrue(true);
        Assert.assertEquals((Integer) 1, drvRecruitingPO.getDrvFrom());
    }

    @Test
    public void drvVehRecruitingApproveAgingListener2() throws SQLException {
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return null;
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return null;
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 2;
            }

            @Override
            public long getLongProperty(String s) {
                return 1L;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 0;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setVehicleId(1L);
        vehicleRecruitingPO.setApproverStatus(2);
        vehicleRecruitingPO.setApproveAging(1);
        vehicleRecruitingPO.setApproveTime(DateUtil.getNow());
        vehicleRecruitingPO.setVehicleFrom(2);
        vehicleRecruitingPO.setVehicleId(1L);
        Mockito.when(vehicleRecruitingRepository.queryByPK(1L)).thenReturn(vehicleRecruitingPO);
        Mockito.when(qconfig.getRecruitingApproveAgingThreshold()).thenReturn(0);
        Mockito.when(vehicleRecruitingRepository.updateApproveAging(Arrays.asList(1L), TmsTransportConstant.ApproveAgingEnum.HASTIMEOUT.getCode())).thenReturn(1);
        listener.drvVehRecruitingApproveAgingListener(message);
        Assert.assertEquals((Integer) 2, vehicleRecruitingPO.getVehicleFrom());
    }

    @Test
    public void drvVehRecruitingCertificateCheckListener(){
        Message message = new BaseMessage();
        message.setProperty("recruitingId",1L);
        message.setProperty("recruitingType",1);
        message.setProperty("drvIdCard","111");
        message.setProperty("drvName","111");
        message.setProperty("vehicleLicense","11");
        message.setProperty("vin","11");
        message.setProperty("modifyUser","11");
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setDrvIdcard("11");
        drvRecruitingPO.setDrvName("1");
        drvRecruitingPO.setDrvFrom(1);
        drvRecruitingPO.setVehicleId(1L);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setVehicleId(1L);
        vehicleRecruitingPO.setApproverStatus(4);
        vehicleRecruitingPO.setVehicleLicense("11");
        vehicleRecruitingPO.setVin("111");
//        Mockito.when(vehicleRecruitingRepository.queryByPK(1L)).thenReturn(vehicleRecruitingPO);
        Map<String,Object> map = Maps.newHashMap();
        map.put("1",1);
//        Mockito.when(certificateCheckQueryService.drvRecruitingCertificateCheck(DrvCertificateCheckParameterDTO.drvCertificateCheck(1L, 1, "111", "11", "111", "1", "11"))).thenReturn(map);
        listener.drvVehRecruitingCertificateCheckListener(message);
        Assert.assertEquals((Integer) 1, drvRecruitingPO.getDrvFrom());

    }

    @Test
    public void drvVehRecruitingCheckStatusListener(){
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return null;
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return "1";
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 1;
            }

            @Override
            public long getLongProperty(String s) {
                return 1L;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 0;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setDrvIdcard("11");
        drvRecruitingPO.setDrvName("1");
        drvRecruitingPO.setDrvFrom(1);
        drvRecruitingPO.setVehicleId(2L);
        drvRecruitingPO.setVersionFlag(3);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setVehicleId(2L);
        vehicleRecruitingPO.setApproverStatus(4);
        vehicleRecruitingPO.setVehicleLicense("11");
        vehicleRecruitingPO.setVin("111");
        vehicleRecruitingPO.setVersionFlag(3);
        Mockito.when(vehicleRecruitingRepository.queryByPK(2L)).thenReturn(vehicleRecruitingPO);
        Map<String,Object> map = Maps.newHashMap();
        map.put("1",1);
        List<TmsCertificateCheckPO> checkPOS = Lists.newArrayList();
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setCheckId(1L);
        checkPO.setThirdCheckStatus(4);
        checkPO.setCheckType(1);
        checkPO.setCheckStatus(4);
        checkPOS.add(checkPO);
        Mockito.when(checkRepository.queryCerCheckListByCheckIds(Arrays.asList(1L,2L),Arrays.asList(TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),
                TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode()))).thenReturn(checkPOS);
        listener.drvVehRecruitingCheckStatusListener(message);
        Assert.assertEquals((Integer) 1, drvRecruitingPO.getDrvFrom());
    }

    @Test
    public void drvVehRecruitingCheckStatusListener1(){
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return null;
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return "1";
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 2;
            }

            @Override
            public long getLongProperty(String s) {
                return 1L;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 0;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
        VehicleRecruitingPO drvRecruitingPO = new VehicleRecruitingPO();
        drvRecruitingPO.setVehicleId(1L);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setVehicleFrom(1);
        drvRecruitingPO.setVehicleId(1L);
        Mockito.when(vehicleRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        Map<String,Object> map = Maps.newHashMap();
        map.put("1",1);
        listener.drvVehRecruitingCheckStatusListener(message);
        Assert.assertEquals((Integer) 1, drvRecruitingPO.getVehicleFrom());
    }


    @Test
    public void systemAutoRejectedRecruitingListener() throws SQLException {
        List<TmsCertificateCheckPO> checkPOS = Lists.newArrayList();
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setCheckId(1L);
        checkPO.setCheckStatus(4);
        checkPOS.add(checkPO);
//        Mockito.when(checkRepository.queryCertificateByCheckIdOrderBy(1L, 1)).thenReturn(checkPOS);
//        Mockito.when(drvRecruitingRepository.updateCheckStatus(Arrays.asList(1L), 2)).thenReturn(1);
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return null;
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return null;
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 1;
            }

            @Override
            public long getLongProperty(String s) {
                return 1L;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 0;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvFrom(2);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setCityId(1L);
        List<TmsRecruitingApproveStepPO> stepPOList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveFrom(1);
        stepPO.setApproveItem(1);
        stepPOList.add(stepPO);
        Mockito.when(stepRepository.queryApproveStepList(1L,1,TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())).thenReturn(stepPOList);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        Map<Integer, TmsCertificateCheckPO> checkPOMap = Maps.newHashMap();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setCheckId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(1);
        tmsCertificateCheckPO.setThirdCheckStatus(3);
        checkPOMap.put(5,tmsCertificateCheckPO);
        Mockito.when(certificateCheckQueryService.queryCertificateCheckToMap(1L, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode())).thenReturn(checkPOMap);
        Mockito.when(qconfig.getRecruitingSystemAutoRejectedSwitch()).thenReturn(true);
        listener.systemAutoRejectedRecruitingListener(message);
        Assert.assertEquals((Integer) 2, drvRecruitingPO.getDrvFrom());
    }

    @Test
    public void recruitingApproveScheduleListener() throws SQLException {
        List<TmsCertificateCheckPO> checkPOS = Lists.newArrayList();
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setCheckId(1L);
        checkPO.setCheckStatus(4);
        checkPOS.add(checkPO);
//        Mockito.when(checkRepository.queryCertificateByCheckIdOrderBy(1L, 1)).thenReturn(checkPOS);
//        Mockito.when(drvRecruitingRepository.updateCheckStatus(Arrays.asList(1L), 2)).thenReturn(1);
        Message message = new Message() {
            @Override
            public String getMessageId() {
                return null;
            }

            @Override
            public String getSubject() {
                return null;
            }

            @Override
            public Date getCreatedTime() {
                return null;
            }

            @Override
            public Date getScheduleReceiveTime() {
                return null;
            }

            @Override
            public void setProperty(String s, boolean b) {

            }

            @Override
            public void setProperty(String s, Boolean aBoolean) {

            }

            @Override
            public void setProperty(String s, int i) {

            }

            @Override
            public void setProperty(String s, Integer integer) {

            }

            @Override
            public void setProperty(String s, long l) {

            }

            @Override
            public void setProperty(String s, Long aLong) {

            }

            @Override
            public void setProperty(String s, float v) {

            }

            @Override
            public void setProperty(String s, Float aFloat) {

            }

            @Override
            public void setProperty(String s, double v) {

            }

            @Override
            public void setProperty(String s, Double aDouble) {

            }

            @Override
            public void setProperty(String s, Date date) {

            }

            @Override
            public void setProperty(String s, String s1) {

            }

            @Override
            public String getStringProperty(String s) {
                return null;
            }

            @Override
            public boolean getBooleanProperty(String s) {
                return false;
            }

            @Override
            public Date getDateProperty(String s) {
                return null;
            }

            @Override
            public int getIntProperty(String s) {
                return 1;
            }

            @Override
            public long getLongProperty(String s) {
                return 1L;
            }

            @Override
            public float getFloatProperty(String s) {
                return 0;
            }

            @Override
            public double getDoubleProperty(String s) {
                return 0;
            }

            @Override
            public boolean containsKey(String s) {
                return false;
            }

            @Override
            public Message addTag(String s) {
                return null;
            }

            @Override
            public Set<String> getTags() {
                return null;
            }

            @Override
            public Set<String> getBizAttrs() {
                return null;
            }

            @Override
            public Map<String, Object> getAttrs() {
                return null;
            }

            @Override
            public TraceContext getContext() {
                return null;
            }

            @Override
            public void setReliabilityLevel(ReliabilityLevel reliabilityLevel) {

            }

            @Override
            public ReliabilityLevel getReliabilityLevel() {
                return null;
            }

            @Override
            public void autoAck(boolean b) {

            }

            @Override
            public void ack(Throwable throwable) {

            }

            @Override
            public void setDelayTime(Date date) {

            }

            @Override
            public void setDelayTime(long l, TimeUnit timeUnit) {

            }

            @Override
            public int times() {
                return 0;
            }

            @Override
            public void setMaxRetryNum(int i) {

            }

            @Override
            public int getMaxRetryNum() {
                return 0;
            }

            @Override
            public int localRetries() {
                return 0;
            }

            @Override
            public void setDurable(boolean b) {

            }

            @Override
            public boolean isDurable() {
                return false;
            }

            @Override
            public void setStoreAtFailed(boolean b) {

            }

            @Override
            public boolean isStoreAtFailed() {
                return false;
            }

            @Override
            public void setOrderKey(String s) {

            }

            @Override
            public String getOrderKey() {
                return null;
            }

            @Override
            public void setShardingKey(String s) {

            }

            @Override
            public String getShardingKey() {
                return null;
            }

            @Override
            public void setShardingKey(ShardingKeyValue shardingKeyValue) {

            }

            @Override
            public String getPartitionName() {
                return null;
            }

            @Override
            public boolean isCompensation() {
                return false;
            }

            @Override
            public String getSourceRegion() {
                return null;
            }

            @Override
            public String getLane() {
                return "";
            }

            @Override
            public Date getExpiredTime() {
                return null;
            }

            @Override
            public void setNewqmqFlag() {

            }

            @Override
            public boolean isNewqmq() {
                return false;
            }

            @Override
            public <T> T getData(Class<T> aClass) {
                return null;
            }

            @Override
            public void setData(Object o) {

            }

            @Override
            public String getLargeString(String s) {
                return null;
            }

            @Override
            public Object getProperty(String s) {
                return null;
            }

            @Override
            public void setLargeString(String s, String s1) {

            }

            @Override
            public void ack(long l, Throwable throwable) {

            }

            @Override
            public void ack(long l, Throwable throwable, Map<String, String> map) {

            }
        };
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvFrom(2);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setCityId(1L);
        drvRecruitingPO.setVehicleId(1L);
        List<TmsRecruitingApproveStepPO> stepPOList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveFrom(1);
        stepPO.setApproveItem(1);
        stepPOList.add(stepPO);
        Mockito.when(stepRepository.queryApproveStepList(1L,1,TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())).thenReturn(stepPOList);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        Map<Integer, TmsCertificateCheckPO> checkPOMap = Maps.newHashMap();
        TmsCertificateCheckPO tmsCertificateCheckPO = new TmsCertificateCheckPO();
        tmsCertificateCheckPO.setCheckId(1L);
        tmsCertificateCheckPO.setCheckType(1);
        tmsCertificateCheckPO.setCertificateType(1);
        tmsCertificateCheckPO.setThirdCheckStatus(3);
        checkPOMap.put(5,tmsCertificateCheckPO);
//        Mockito.when(certificateCheckQueryService.queryCertificateCheckToMap(1L, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode())).thenReturn(checkPOMap);
        listener.recruitingApproveScheduleListener(message);
        Assert.assertEquals((Integer) 2, drvRecruitingPO.getDrvFrom());
    }

    @Test
    public void getcheckStatusNoPassDesc(){
        String str = listener.getcheckStatusNoPassDesc(1);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void getcheckStatusNoPassDesc1(){
        String str =listener.getcheckStatusNoPassDesc(2);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void getcheckStatusNoPassDesc3(){
        String str = listener.getcheckStatusNoPassDesc(3);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void getcheckStatusNoPassDesc4(){
        String str = listener.getcheckStatusNoPassDesc(4);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void getcheckStatusNoPassDesc5(){
        String str = listener.getcheckStatusNoPassDesc(5);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void checkAppealMaterialsEmpty5(){
        Boolean b = listener.checkAppealMaterialsEmpty(5,"","","","","");
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAppealMaterialsEmpty2(){
        Boolean b = listener.checkAppealMaterialsEmpty(2,"","","","","");
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAppealMaterialsEmpty1(){
        Boolean b = listener.checkAppealMaterialsEmpty(1,"","","","","");
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAppealMaterialsEmpty3(){
        Boolean b = listener.checkAppealMaterialsEmpty(3,"","","","","");
        Assert.assertTrue(!b);
    }

    @Test
    public void checkAppealMaterialsEmpty4(){
        Boolean b = listener.checkAppealMaterialsEmpty(4,"","","","","");
        Assert.assertTrue(!b);
    }



}
