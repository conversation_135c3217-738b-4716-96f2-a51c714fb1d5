package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.resource.driver.*;
import com.ctrip.dcs.tms.transport.application.convert.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.*;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryDriverBaseExecutorTest {

    @InjectMocks
    private QueryDriverBaseExecutor queryDriverBaseExecutor;

    @Mock
    private DriverQueryService driverQueryService;

    @Mock
    private DrvResourceConverter drvResourceConverter;

    @Test
    public void s() {
        QueryDriver4BaseSOARequestType req = new QueryDriver4BaseSOARequestType();
        req.setSupplierIdList(Lists.newArrayList(1L));
        req.setPaginator(new PaginatorDTO(-1,100000));
        req.setDriverPhoneList(Lists.newArrayList("3223232"));
        queryDriverBaseExecutor.onExecuting(req);
    }

    @Test
    public void s1() {
        QueryDriver4BaseSOARequestType req = new QueryDriver4BaseSOARequestType();
        req.setSupplierIdList(Lists.newArrayList(1L));
        req.setPaginator(new PaginatorDTO(-1,-1));
        req.setDriverPhoneList(Lists.newArrayList("3223232"));
        queryDriverBaseExecutor.onExecuting(req);
    }

    @Test
    public void ss() {
        QueryDriver4BaseSOARequestType requestType = new QueryDriver4BaseSOARequestType();
        QueryDriver4BaseSOAResponseType type = queryDriverBaseExecutor.execute(requestType);
        Assert.assertTrue(type != null);
    }

}