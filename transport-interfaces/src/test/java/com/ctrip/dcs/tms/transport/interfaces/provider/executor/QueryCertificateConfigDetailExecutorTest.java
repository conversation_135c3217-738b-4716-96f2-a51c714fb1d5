package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryCertificateConfigDetailExecutorTest extends Mockito {
    @InjectMocks
    QueryCertificateConfigDetailExecutor executor;
    @Mock
    CertificateQueryService queryService;

    @Test
    public void execute() {
        QueryCertificateConfigDetailSOARequestType soaRequestType = new QueryCertificateConfigDetailSOARequestType();
        soaRequestType.setId(1L);
        QueryCertificateConfigListSOADTO soadto = new QueryCertificateConfigListSOADTO();
        soadto.setId(1L);
        Result<QueryCertificateConfigListSOADTO> detailResult = Result.Builder.<QueryCertificateConfigListSOADTO>newResult().success().withData(soadto).build();
        when(queryService.queryCertificateConfigDetail(soaRequestType)).thenReturn(detailResult);
        QueryCertificateConfigDetailSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }

    @Test
    public void executeFail() {
        QueryCertificateConfigDetailSOARequestType soaRequestType = new QueryCertificateConfigDetailSOARequestType();
        soaRequestType.setId(1L);
        QueryCertificateConfigListSOADTO soadto = new QueryCertificateConfigListSOADTO();
        soadto.setId(1L);
        Result<QueryCertificateConfigListSOADTO> detailResult = Result.Builder.<QueryCertificateConfigListSOADTO>newResult().fail().withData(soadto).build();
        when(queryService.queryCertificateConfigDetail(soaRequestType)).thenReturn(detailResult);
        QueryCertificateConfigDetailSOAResponseType soaResponseType = executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
