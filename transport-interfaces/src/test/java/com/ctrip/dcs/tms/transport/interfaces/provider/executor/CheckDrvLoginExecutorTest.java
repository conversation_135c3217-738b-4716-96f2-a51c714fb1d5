package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class CheckDrvLoginExecutorTest {

    @InjectMocks
    private CheckDrvLoginExecutor checkDrvLoginExecutor;

    @Mock
    private DriverAccountQueryService queryService;

    @Before
    public void setUp() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        Result<DrvDriverPO> build = Result.Builder.<DrvDriverPO>newResult().fail().withData(drvDriverPO).build();
        Mockito.when(queryService.login(Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.any(LoginType.class))).thenReturn(build);
    }

    @Test
    public void execute() {
        CheckDrvLoginRequestType checkDrvLoginRequestType = new CheckDrvLoginRequestType();
        checkDrvLoginRequestType.setLoginType(1);
        checkDrvLoginRequestType.setLoginPwd("aa");
        checkDrvLoginRequestType.setHybridLoginAccount("aa");
        checkDrvLoginRequestType.setLoginAreaCode("ss");
        CheckDrvLoginResponseType responseType =  checkDrvLoginExecutor.execute(checkDrvLoginRequestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }

    @Test
    public void test1() {
        CheckDrvLoginRequestType checkDrvLoginRequestType = new CheckDrvLoginRequestType();
        checkDrvLoginRequestType.setLoginType(1);
        checkDrvLoginRequestType.setLoginPwd("aa");
        checkDrvLoginRequestType.setHybridLoginAccount("aa");
        checkDrvLoginRequestType.setLoginAreaCode("ss");
        CheckDrvLoginResponseType responseType = checkDrvLoginExecutor.execute(checkDrvLoginRequestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }
}