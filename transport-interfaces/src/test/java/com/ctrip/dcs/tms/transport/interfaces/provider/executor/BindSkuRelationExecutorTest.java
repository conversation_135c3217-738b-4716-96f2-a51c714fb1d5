package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.ICheckSupplierPermissionService;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class BindSkuRelationExecutorTest extends Mockito {

    @InjectMocks
    BindSkuRelationExecutor executor;
    @Mock
    private TransportGroupCommandService transportGroupCommandService;
    @Mock
    private TmsQmqProducerCommandService qmqCommandService;
    @Mock
    private ICheckSupplierPermissionService checkSupplierPermissionService;

    @Test
    public void execute() {
        SkuRelationBindRequestSOAType soaType = new SkuRelationBindRequestSOAType();
        soaType.setBindStatus(1);
        soaType.setTransportGroupId(1L);
        soaType.setOperator("111");
        soaType.setSkuInfoList(Arrays.asList(1L));
        Result<Boolean> result = Result.Builder.<Boolean>newResult().fail().build();
//        when(transportGroupCommandService.updateTransportGroupSkuRelationStatus(2L,Arrays.asList(1L),"111")).thenReturn(result);
        Mockito.when(checkSupplierPermissionService.check(Mockito.any())).thenReturn(true);
        SkuRelationBindResponseSOAType soaType1 =  executor.execute(soaType);
        Assert.assertTrue(!Objects.isNull(soaType1));

        Mockito.when(checkSupplierPermissionService.check(Mockito.any())).thenReturn(false);
        soaType1 =  executor.execute(soaType);
        Assert.assertTrue(!Objects.isNull(soaType1));
    }
}
