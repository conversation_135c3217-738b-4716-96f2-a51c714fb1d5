package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.CreateDrvChangeEquipmentEventRequestType;
import com.ctrip.dcs.tms.transport.api.model.CreateDrvChangeEquipmentEventResponseType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.igt.framework.common.result.Result;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Incubating;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CreateDrvChangeEquipmentEventExecutorTest {

    @InjectMocks
    CreateDrvChangeEquipmentEventExecutor executor;
    @Mock
    private DriverCommandService driverCommandService;

    @Test
    public void testExecute() {
        CreateDrvChangeEquipmentEventRequestType requestType = new CreateDrvChangeEquipmentEventRequestType();
        requestType.setDrvId(1L);
        requestType.setDriverImei("111");
        requestType.setDriverLocCsys("111");
        requestType.setDriverLocLat(12.33D);
        requestType.setDriverLocLong(12.333D);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverCommandService.createDrvChangeEquipmentEvent(Mockito.any())).thenReturn(result);
        CreateDrvChangeEquipmentEventResponseType responseType = executor.execute(requestType);
        Assert.assertTrue(responseType != null);
    }

    @Test
    public void testExecute1() {
        CreateDrvChangeEquipmentEventRequestType requestType = new CreateDrvChangeEquipmentEventRequestType();
        requestType.setDrvId(1L);
        requestType.setDriverImei("111");
        requestType.setDriverLocCsys("111");
        requestType.setDriverLocLat(12.33D);
        requestType.setDriverLocLong(12.333D);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().fail().withData(true).build();
        Mockito.when(driverCommandService.createDrvChangeEquipmentEvent(Mockito.any())).thenReturn(result);
        CreateDrvChangeEquipmentEventResponseType responseType = executor.execute(requestType);
        Assert.assertTrue(responseType != null);
    }

    @Test
    public void testExecute13() {
        CreateDrvChangeEquipmentEventRequestType requestType = new CreateDrvChangeEquipmentEventRequestType();
        requestType.setDrvId(1L);
        requestType.setDriverImei("111");
        requestType.setDriverLocCsys("111");
        requestType.setDriverLocLat(12.33D);
        requestType.setDriverLocLong(12.333D);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().fail().withData(true).build();
        Mockito.when(driverCommandService.createDrvChangeEquipmentEvent(Mockito.any())).thenReturn(result);
        CreateDrvChangeEquipmentEventResponseType responseType = executor.execute(requestType);
        Assert.assertTrue(responseType != null);
    }
}