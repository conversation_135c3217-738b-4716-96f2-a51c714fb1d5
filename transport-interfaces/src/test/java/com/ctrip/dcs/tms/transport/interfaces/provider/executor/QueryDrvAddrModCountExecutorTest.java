package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryDrvAddrModCountExecutorTest {

    @InjectMocks
    QueryDrvAddrModCountExecutor executor;
    @Mock
     DriverQueryService driverQueryService;

    @Test
    public void execute() {
        QueryDrvAddrModCountSOARequestType soaRequestType = new QueryDrvAddrModCountSOARequestType();
        soaRequestType.setDrvId(1L);
        QueryDrvAddrModCountSOAResponseType soaResponseType = new QueryDrvAddrModCountSOAResponseType();
        soaResponseType.setModCount(1);
        soaResponseType.setModCountThreshold(1);
        Result<QueryDrvAddrModCountSOAResponseType> responseTypeResult = Result.Builder.<QueryDrvAddrModCountSOAResponseType>newResult().success().withData(soaResponseType).build();
        Mockito.when(driverQueryService.queryDrvAddrModCount(soaRequestType)).thenReturn(responseTypeResult);
        QueryDrvAddrModCountSOAResponseType soaResponseType1 =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType1));
    }
}
