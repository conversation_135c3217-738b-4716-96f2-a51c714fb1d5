package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryTransportGroupConfigExecutorTest {

    @InjectMocks
    QueryTransportGroupConfigExecutor executor;
    @Mock
    private TransportGroupQueryService transportGroupQueryService;

    @Test
    public void execute() {
        QueryTransportGroupConfigSOARequestType requestType = new QueryTransportGroupConfigSOARequestType();
        Map<String, String> resultMap = Maps.newHashMap();
        resultMap.put("111","111");
        Result<Map<String, String>> result =  Result.Builder.<Map<String, String>>newResult().success().withData(resultMap).build();
        Mockito.when(transportGroupQueryService.queryTransportGroupConfig(requestType)).thenReturn(result);
        QueryTransportGroupConfigSOAResponseType soaResponseType =  executor.execute(requestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
