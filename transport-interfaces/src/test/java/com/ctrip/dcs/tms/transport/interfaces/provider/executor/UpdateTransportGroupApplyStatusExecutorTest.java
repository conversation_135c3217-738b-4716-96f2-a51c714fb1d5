package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class UpdateTransportGroupApplyStatusExecutorTest {

    @InjectMocks
    UpdateTransportGroupApplyStatusExecutor executor;
    @Mock
    TransportGroupCommandService transportGroupCommandService;

    @Test
    public void execute() {
        UpdateTransportGroupApplyStatusSOARequestType soaRequestType = new UpdateTransportGroupApplyStatusSOARequestType();
        soaRequestType.setModifyUser("system");
        soaRequestType.setTransportGroupId(1L);
        soaRequestType.setUpdateType(1);
        UpdateTransportGroupApplyStatusSOADTO soadto = new UpdateTransportGroupApplyStatusSOADTO();
        soadto.setTrackCount(1);
        soadto.setCode(1);
        Result<UpdateTransportGroupApplyStatusSOADTO> result = Result.Builder.<UpdateTransportGroupApplyStatusSOADTO>newResult().success().withData(soadto).build();
        Mockito.when(transportGroupCommandService.updateTransportGroupApplyStatus(soaRequestType)).thenReturn(result);
        UpdateTransportGroupApplyStatusSOAResponseType soaResponseType =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(soaResponseType));
    }
}
