package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import cn.hutool.core.lang.Assert;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryRandomVehicleExecutorTest {
    @InjectMocks
    QueryRandomVehicleExecutor queryRandomVehicleExecutor;
    @Mock
    IQueryVehicleInfoService queryVehicleInfoService;
    @Test
    public void test(){
        Mockito.when(queryVehicleInfoService.queryRandomVehicle(Mockito.anyString(),Mockito.anyBoolean())).thenReturn(new VehCacheDTO());
        QueryRandomVehicleRequestType requestType = new QueryRandomVehicleRequestType();
        requestType.setDriverId("1");
        requestType.setConformanceFlag(true);
        QueryRandomVehicleResponseType responseType = queryRandomVehicleExecutor.execute(requestType);
        Assert.notNull(responseType);
    }
    @Test
    public void paramNull(){
        QueryRandomVehicleRequestType requestType = new QueryRandomVehicleRequestType();
        QueryRandomVehicleResponseType responseType = queryRandomVehicleExecutor.execute(requestType);
        Assert.notNull(responseType);
    }
}
