package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.TransportGroupCommandService;
import com.ctrip.dcs.tms.transport.application.query.ICheckSupplierPermissionService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class UpdateTransportGroupExecutorTest{
    @InjectMocks
    UpdateTransportGroupExecutor executor;
    @Mock
    private TransportGroupQueryService transportGroupQueryService;
    @Mock
    private EnumRepository enumRepository;
    @Mock
    private OverseasQconfig overseasQconfig;
    @Mock
    private TransportGroupCommandService transportGroupCommandService;
    @Mock
    private ICheckSupplierPermissionService checkSupplierPermissionService;

    @Mock
    MobileHelper mobileHelper;

    @Test
    public void testExecute() {
        UpdateTransportGroupSOARequestType soaRequestType = new UpdateTransportGroupSOARequestType();
        soaRequestType.setTransportGroupId(1L);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        soaRequestType.setStandbyPhone("123");
        soaRequestType.setStandbyIgtCode("1");
        soaRequestType.setDispatcherPhone("456");
        soaRequestType.setIgtCode("4");
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setContractId(1L);
        Result<TspTransportGroupPO> resultDetail = Result.Builder.<TspTransportGroupPO>newResult().success().withData(tspTransportGroupPO).build();
        Mockito.when(transportGroupQueryService.queryTransportGroupDetail(1L)).thenReturn(resultDetail);
        Result<Boolean> resultBool= Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        Mockito.when(transportGroupQueryService.inOrderUpperLimit(Mockito.any(),Mockito.any())).thenReturn(resultBool);
        Mockito.when(mobileHelper.isMobileValid(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Result.Builder.<Boolean>newResult().success().withCode(
          ServiceResponseConstants.ResStatus.SUCCESS_CODE).build());
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","2");
        params.put("accountType","1");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        Mockito.when(checkSupplierPermissionService.checkByContractId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        Result<Boolean> result = Result.Builder.<Boolean>newResult()
                .success()
                .withData(true)
                .build();
        Mockito.when(transportGroupCommandService.updateTransportGroup(Mockito.any())).thenReturn(result);
        UpdateTransportGroupSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertTrue(responseType!=null);

        Mockito.when(checkSupplierPermissionService.checkByContractId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(false);
        responseType =  executor.execute(soaRequestType);
        Assert.assertTrue(responseType!=null);
    }

    @Test
    public void testExecuteMoblieValidate() {
        UpdateTransportGroupSOARequestType soaRequestType = new UpdateTransportGroupSOARequestType();
        soaRequestType.setTransportGroupId(1L);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        soaRequestType.setStandbyPhone("123");
        soaRequestType.setStandbyIgtCode("1");
        soaRequestType.setDispatcherPhone("456");
        soaRequestType.setIgtCode("4");
        Mockito.when(mobileHelper.isMobileValid(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Result.Builder.<Boolean>newResult().fail().withCode(
          ServiceResponseConstants.ResStatus.EXCEPTION_CODE).build());

        UpdateTransportGroupSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertEquals(responseType.getResponseResult().getReturnCode(), ServiceResponseConstants.ResStatus.EXCEPTION_CODE);
    }

    @Test
    public void testExecuteMoblieValidate2() {
        UpdateTransportGroupSOARequestType soaRequestType = new UpdateTransportGroupSOARequestType();
        soaRequestType.setTransportGroupId(1L);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        soaRequestType.setStandbyPhone("456");
        soaRequestType.setStandbyIgtCode("4");
        soaRequestType.setDispatcherPhone("123");
        soaRequestType.setIgtCode("1");
        Mockito.when(mobileHelper.isMobileValid("1", "123", 1L)).thenReturn(Result.Builder.<Boolean>newResult().success().withCode(
          ServiceResponseConstants.ResStatus.SUCCESS_CODE).build());
        Mockito.when(mobileHelper.isMobileValid("4", "456", 1L)).thenReturn(Result.Builder.<Boolean>newResult().fail().withCode(
          ServiceResponseConstants.ResStatus.EXCEPTION_CODE).build());

        UpdateTransportGroupSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertEquals(responseType.getResponseResult().getReturnCode(), ServiceResponseConstants.ResStatus.EXCEPTION_CODE);
    }

    @Test
    public void testExecute1() {
        UpdateTransportGroupSOARequestType soaRequestType = new UpdateTransportGroupSOARequestType();
        soaRequestType.setTransportGroupId(1L);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setContractId(1L);
        Result<TspTransportGroupPO> resultDetail = Result.Builder.<TspTransportGroupPO>newResult().success().withData(tspTransportGroupPO).build();
        Mockito.when(transportGroupQueryService.queryTransportGroupDetail(1L)).thenReturn(resultDetail);
        Result<Boolean> resultBool= Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        Mockito.when(transportGroupQueryService.inOrderUpperLimit(Mockito.any(),Mockito.any())).thenReturn(resultBool);
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","2");
        params.put("accountType","1");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        Result<Boolean> updateTransportGroup= Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        Mockito.when(transportGroupCommandService.updateTransportGroup(Mockito.any())).thenReturn(updateTransportGroup);
        Mockito.when(checkSupplierPermissionService.checkByContractId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        UpdateTransportGroupSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertTrue(responseType!=null);
    }

    @Test
    public void testExecute2() {
        UpdateTransportGroupSOARequestType soaRequestType = new UpdateTransportGroupSOARequestType();
        soaRequestType.setTransportGroupId(1L);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setContractId(1L);
        Result<TspTransportGroupPO> resultDetail = Result.Builder.<TspTransportGroupPO>newResult().success().withData(tspTransportGroupPO).build();
        Mockito.when(transportGroupQueryService.queryTransportGroupDetail(1L)).thenReturn(resultDetail);
        Result<Boolean> resultBool= Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        Mockito.when(transportGroupQueryService.inOrderUpperLimit(Mockito.any(),Mockito.any())).thenReturn(resultBool);
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","2");
        params.put("accountType","2");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        Result<Boolean> updateTransportGroup= Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        Mockito.when(transportGroupCommandService.updateTransportGroup(Mockito.any())).thenReturn(updateTransportGroup);
        Mockito.when(checkSupplierPermissionService.checkByContractId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        UpdateTransportGroupSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertTrue(responseType!=null);
    }
}
