package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class VehRecruitingCertificateCheckResultScheduleTest {
    @InjectMocks
    VehRecruitingCertificateCheckResultSchedule vehRecruitingCertificateCheckResultSchedule;
    @Mock
    private TmsCertificateCheckRepository checkRepository;
    @Mock
    TmsTransportQconfig qconfig;
    @Mock
    DrvDrvierRepository repository;
    @Mock
    CertificateCheckQueryService checkQueryService;
    @Mock
    DrvRecruitingRepository recruitingRepository;
    @Mock
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Mock
    VehicleRepository vehicleRepository;
    @Mock
    RecruitingCommandService recruitingCommandService;
    @Mock
    TmsQmqProducerCommandService qmqProducerCommandService;
    @Test
    public void testnetVehLicenseDataCheck(){
        Mockito.when(checkQueryService.checkFromCityPlatform(Mockito.anyString())).thenReturn(true);
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setCityId(1L);
        Mockito.when(vehicleRecruitingRepository.queryByPK(Mockito.anyLong())).thenReturn(vehicleRecruitingPO);
        Long id = 1L;
        Long checkId = 1L;
        Integer checkType = 1;
        String checkKeyword = "123456";
        Integer checkStatus = 1;
        boolean result = vehRecruitingCertificateCheckResultSchedule.netVehLicenseDataCheck(id,checkId,checkType,checkKeyword,checkStatus);
        Assert.assertTrue(!result);
    }
}
