package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.TransportGroup;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.google.common.collect.Lists;
import org.junit.Assert;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/7/11 15:36
 */
@RunWith(MockitoJUnitRunner.class)
public class DriverCacheExecutorTest {

    @InjectMocks
    private DriverCacheExecutor executor;
    @Mock
    private TmsTransportQconfig tmsTransportQconfig;

    @Mock
    ProductLineBridgeManagement productLineBridgeManagement;

    @Test
    public void test() {
        List<DriverInfo> driverList = Lists.newArrayList();
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.setDispatchSupplierIdList(Lists.newArrayList(3L,1L,2L));
        TransportGroup transportGroup = new TransportGroup();
        transportGroup.setTransportGroupId(2L);
        TransportGroup transportGroup2 = new TransportGroup();
        transportGroup2.setTransportGroupId(1L);
        TransportGroup transportGroup3 = new TransportGroup();
        transportGroup2.setApplyStatus(1);
        driverInfo.setTransportGroups(Lists.newArrayList(transportGroup,transportGroup2));
        driverList.add(driverInfo);
        DriverInfoSOAResponseType responseType = new DriverInfoSOAResponseType(null, null, driverList);
        when(productLineBridgeManagement.queryDriver(any())).thenReturn(responseType);
        DriverInfoSOARequestType req = new DriverInfoSOARequestType();
        req.setCoopModes("null");
        req.setDriverIds("Null");
        req.setTransportGroupIds("NULL");
        req.setDriverPhone("0123456789");
        when(tmsTransportQconfig.getDrvPhoneTrimSwitch()).thenReturn(true);
        executor.onExecuting(req);
        executor.execute(req);
        Assert.assertNull(req.getCoopModes());
        Assert.assertNull(req.getDriverIds());
        Assert.assertNull(req.getTransportGroupIds());
    }

}
