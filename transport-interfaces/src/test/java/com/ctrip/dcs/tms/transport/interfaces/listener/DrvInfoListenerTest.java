package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.assertj.core.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.base.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvInfoListenerTest extends Mo<PERSON>to {

    @InjectMocks
    DrvInfoListener drvInfoListener;
    @Mock
    private DrvDrvierRepository drvierRepository;
    @Mock
    PushDataCommandService pushDataCommandService;

    @Test
    public void driverStartService() {
        Message message = new BaseMessage("123", TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_START_SERVICE);
        message.setProperty("driverId","94");
        message.setProperty("location","{\"longitude\":120.22844622754357,\"latitude\":30.459642068358804,\"type\":\"GCJ02\"}");
        message.setProperty("status","5");
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(94L);
        driverPO.setCityId(17L);
        when(drvierRepository.queryByPk(94L)).thenReturn(driverPO);
        List<DriverEventDTO> eventDTOS = Lists.newArrayList();
        DriverEventDTO eventDTO = new DriverEventDTO();
        eventDTO.setDriverId("94");
        eventDTO.setLatitude(120.22844622754357F);
        eventDTO.setLongitude(30.459642068358804F);
        eventDTO.setReportedAt(DateUtil.dateToString(new Date(),DateUtil.YYYYMMDDHHMMSS));
        eventDTOS.add(eventDTO);
//        when(pushDataCommandService.pushEventsData(eventDTOS)).thenReturn(true);
        drvInfoListener.driverStartService(message);
        Assert.assertTrue(true);
    }

    @Test
    public void driverStartServiceBranch() {
        Message message = new BaseMessage("123", TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_START_SERVICE);
        message.setProperty("driverId","94aa");
        message.setProperty("location","{\"longitude\":120.22844622754357,\"latitude\":30.459642068358804,\"type\":\"GCJ02\"}");
        message.setProperty("status","5");
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(94L);
        driverPO.setCityId(18L);
//        when(drvierRepository.queryByPk(94L)).thenReturn(driverPO);
        List<DriverEventDTO> eventDTOS = Lists.newArrayList();
        DriverEventDTO eventDTO = new DriverEventDTO();
        eventDTO.setDriverId("94");
        eventDTO.setLatitude(120.22844622754357F);
        eventDTO.setLongitude(30.459642068358804F);
        eventDTO.setReportedAt(DateUtil.dateToString(new Date(),DateUtil.YYYYMMDDHHMMSS));
        eventDTOS.add(eventDTO);
//        when(pushDataCommandService.pushEventsData(eventDTOS)).thenReturn(true);
        drvInfoListener.driverStartService(message);
        Assert.assertTrue(true);
    }
}
