package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class TodoListCountExecutorTest extends Mockito {

    @InjectMocks
    TodoListCountExecutor executor;
    @Mock
    private DrvVehRecruitingQueryService queryService;

    @Test
    public void execute() {
        TodoListCountSOARequestType soaRequestType = new TodoListCountSOARequestType();
        soaRequestType.setProviderId(1L);
        soaRequestType.setTodoCode("1");
        soaRequestType.setCategoryCode("1");
        List<TodoListTypeSOA> list = Lists.newArrayList();
        Result<List<TodoListTypeSOA>> result = Result.Builder.<List<TodoListTypeSOA>>newResult().success().withData(list).build();
        when(queryService.todoListCount(soaRequestType)).thenReturn(result);
        TodoListCountSOAResponseType responseType =  executor.execute(soaRequestType);
        Assert.assertTrue(!Objects.isNull(responseType));
    }
}
