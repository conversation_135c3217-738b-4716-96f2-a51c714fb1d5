package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.InOrderConfigSOAType;
import com.ctrip.dcs.tms.transport.api.model.TransportGroupAddSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.TransportGroupAddSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.ICheckSupplierPermissionService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;


@RunWith(MockitoJUnitRunner.class)
public class AddTransportGroupExecutorTest {
    @InjectMocks
    AddTransportGroupExecutor addTransportGroupExecutor;
    @Mock
    EnumRepository enumRepository;
    @Mock
    TransportGroupQueryService transportGroupQueryService;
    @Mock
    private ICheckSupplierPermissionService checkSupplierPermissionService;

    @Mock
    MobileHelper mobileHelper;

    @Mock
    ProductionLineUtil productionLineUtil;

    @Test
    public void execute() {
        TransportGroupAddSOARequestType soaRequestType = new TransportGroupAddSOARequestType();
        soaRequestType.setContractId(1L);
        soaRequestType.setTransportGroupMode(1001);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        soaRequestType.setDispatcherPhone("123");
        soaRequestType.setIgtCode("1");
        soaRequestType.setStandbyPhone("456");
        soaRequestType.setStandbyIgtCode("4");
        List<InOrderConfigSOAType> inOrderConfigs = Lists.newArrayList();
        soaRequestType.setInOrderConfigs(inOrderConfigs);
        Mockito.when(enumRepository.queryServiceProvider(1L)).thenReturn(Maps.newHashMap());
        Mockito.when((enumRepository.getCountryId(1L))).thenReturn(1L);
        Mockito.when(transportGroupQueryService.isDispatcherMode(1001)).thenReturn(false);
        Mockito.when(transportGroupQueryService.isApplyMode(1001)).thenReturn(false);
        Mockito.when(mobileHelper.isMobileValid(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Result.Builder.<Boolean>newResult().success().withCode(
          ServiceResponseConstants.ResStatus.SUCCESS_CODE).build());
        Result<Boolean> result = Result.Builder.<Boolean>newResult().withData(true).success().build();
        Mockito.when(transportGroupQueryService.inOrderUpperLimit(soaRequestType.getInOrderConfigs(),soaRequestType.getPointCityIdList().get(0))).thenReturn(result);
        Mockito.when(checkSupplierPermissionService.checkByContractId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        TransportGroupAddSOAResponseType soaResponseType =  addTransportGroupExecutor.execute(soaRequestType);
        Assert.assertTrue(soaResponseType != null);

        Mockito.when(checkSupplierPermissionService.checkByContractId(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(false);
        soaResponseType =  addTransportGroupExecutor.execute(soaRequestType);
        Assert.assertTrue(soaResponseType != null);
    }

    @Test
    public void executeMobileNotValid() {
        TransportGroupAddSOARequestType soaRequestType = new TransportGroupAddSOARequestType();
        soaRequestType.setContractId(1L);
        soaRequestType.setTransportGroupMode(1001);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        soaRequestType.setDispatcherPhone("123");
        soaRequestType.setIgtCode("1");
        soaRequestType.setStandbyPhone("456");
        soaRequestType.setStandbyIgtCode("4");
        Mockito.when(mobileHelper.isMobileValid(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Result.Builder.<Boolean>newResult().success().withCode(
          ServiceResponseConstants.ResStatus.EXCEPTION_CODE).build());

        TransportGroupAddSOAResponseType soaResponseType =  addTransportGroupExecutor.execute(soaRequestType);
        Assert.assertEquals(ServiceResponseConstants.ResStatus.EXCEPTION_CODE, soaResponseType.getResponseResult().getReturnCode());
    }

    @Test
    public void executeMobileNotValid2() {
        TransportGroupAddSOARequestType soaRequestType = new TransportGroupAddSOARequestType();
        soaRequestType.setContractId(1L);
        soaRequestType.setTransportGroupMode(1001);
        soaRequestType.setPointCityIdList(Arrays.asList(1L));
        soaRequestType.setDispatcherPhone("123");
        soaRequestType.setIgtCode("1");
        soaRequestType.setStandbyPhone("456");
        soaRequestType.setStandbyIgtCode("4");
        Mockito.when(mobileHelper.isMobileValid("1", "123", 1L)).thenReturn(Result.Builder.<Boolean>newResult().success().withCode(
          ServiceResponseConstants.ResStatus.SUCCESS_CODE).build());
        Mockito.when(mobileHelper.isMobileValid("4", "456", 1L)).thenReturn(Result.Builder.<Boolean>newResult().success().withCode(
          ServiceResponseConstants.ResStatus.EXCEPTION_CODE).build());

        TransportGroupAddSOAResponseType soaResponseType =  addTransportGroupExecutor.execute(soaRequestType);
        Assert.assertEquals(ServiceResponseConstants.ResStatus.EXCEPTION_CODE, soaResponseType.getResponseResult().getReturnCode());
    }

    @Test
    public void initDispatcherLanguage(){
        String result = addTransportGroupExecutor.initDispatcherLanguage("cn",1L);
        Assert.assertTrue(!StringUtils.isEmpty(result));
    }

    @Test
    public void initDispatcherLanguage1(){
        Mockito.when(enumRepository.getAreaScope(1L)).thenReturn(1);
        String result = addTransportGroupExecutor.initDispatcherLanguage("",1L);
        Assert.assertTrue(StringUtils.isEmpty(result));
    }
}
