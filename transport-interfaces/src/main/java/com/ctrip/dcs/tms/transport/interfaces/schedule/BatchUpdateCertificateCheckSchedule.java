package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.util.*;
import java.util.stream.*;


/**
 * 批量修改司机车辆标签
 */
@Component
public class BatchUpdateCertificateCheckSchedule {

    private static final Logger logger = LoggerFactory.getLogger(BatchUpdateCertificateCheckSchedule.class);

    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    DrvDrvierRepository repository;

    @QSchedule("btach.update.certificate.checkstatus.job")
    public void updateCertificateCheckSchedule(Parameter parameter) throws Exception {
        String ids = parameter.getString("ids");
        Integer checkStatus = parameter.getProperty("checkStatus", Integer.class);
        this.updateCertificateCheckScheduleMethod(ids, checkStatus);
    }

    public void updateCertificateCheckScheduleMethod(String ids, Integer checkStatus) {
        try {
            if (StringUtils.isEmpty(ids)) {
                return;
            }
            List<Long> idList = Arrays.stream(ids.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCerCheckListByIds(idList);
            if (CollectionUtils.isEmpty(checkPOList)) {
                return;
            }
            //更新标签
            int count = checkRepository.batchUpdateCheckStatus(idList, checkStatus);
            if (count > 0) {
                List<Long> checkids = Lists.newArrayList();
                for (TmsCertificateCheckPO tmsCertificateCheckPO : checkPOList) {
                    //如果变更标签是司机并且是网约车驾驶证,则给TTS发送司机变更MQ
                    if (Objects.equals(tmsCertificateCheckPO.getCheckType(), TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode()) &&
                            Objects.equals(tmsCertificateCheckPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode())) {
                        tmsQmqProducerCommandService.sendDrvChangeQmq(tmsCertificateCheckPO.getCheckId(), 2, 1);
                    }
                    if (Objects.equals(tmsCertificateCheckPO.getCheckType(), TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode()) &&
                            Objects.equals(tmsCertificateCheckPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())) {
                        checkids.add(tmsCertificateCheckPO.getCheckId());
                    }
                }
                if (CollectionUtils.isEmpty(checkids)) {
                    return;
                }

                //如果变更标签是车辆并且是网约车运输证,则给TTS发送车辆信息变更MQ
                List<DrvDriverPO> drvDriverPOList = repository.queryDrvByVehicleIds(checkids);
                if (CollectionUtils.isEmpty(drvDriverPOList)) {
                    return;
                }
                for (DrvDriverPO drvDriverPO : drvDriverPOList) {
                    tmsQmqProducerCommandService.sendDrvVehicleChangeQmq(drvDriverPO.getDrvId(), drvDriverPO.getVehicleId());
                }
            }
            logger.info("job end");
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}