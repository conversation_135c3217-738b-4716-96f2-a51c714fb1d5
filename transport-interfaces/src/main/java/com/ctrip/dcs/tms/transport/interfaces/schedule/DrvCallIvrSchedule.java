package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Objects;

/**
　* @description: 对未接电话的司机，进行常态化呼叫
　* <AUTHOR>
　* @date 2024/11/29
*/
@Component
public class DrvCallIvrSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DrvCallIvrSchedule.class);

    @Autowired
    QueryAllDrvForSchedule queryAllDrvForSchedule;

    @Autowired
    IVRCallService ivrCallService;

    /**
     * 更新司机信息通用job by 司机ID， 任务
     */
    @QSchedule("drv.call.ivr")
    public void updateDrvInfo(Parameter parameter) {
        String drvStatus = parameter == null ? StringUtils.EMPTY : parameter.getString("drvStatus");
        Integer drvStatusInt = StringUtils.isBlank(drvStatus) ?
            TmsTransportConstant.DrvStatusEnum.ONLINE.getCode() : Integer.parseInt(drvStatus);

        queryAllDrvForSchedule.doDrvAction(parameter, drv ->  {
            if (Objects.equals(drv.getDrvStatus(), drvStatusInt)) {
                ivrCallService.asyncSendIvrByLocalTimeForIvrCall(drv.getCityId(), new PhoneDTO(drv.getDrvPhone(), drv.getIgtCode()), drv.getDrvLanguage());
            }
        });
    }
}
