package com.ctrip.dcs.tms.transport.interfaces.provider.executor.saas;

import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.interfaces.bridge.ProductLineBridgeManagement;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryDriverIdForSaasExecutor extends AbstractRpcExecutor<QueryDriverIdForSaasRequestType, QueryDriverIdForSaasResponseType> implements Validator<QueryDriverIdForSaasRequestType> {

    @Autowired
    ProductLineBridgeManagement productLineBridgeManagement;
    @Override
    public QueryDriverIdForSaasResponseType execute(QueryDriverIdForSaasRequestType requestType) {
        return productLineBridgeManagement.queryDriverIdForSaas(requestType);
    }

    @Override
    public void validate(AbstractValidator<QueryDriverIdForSaasRequestType> validator) {
        validator.ruleFor("driverPhone").notNull().notEmpty();
        validator.ruleFor("supplierId").notNull();
    }

}
