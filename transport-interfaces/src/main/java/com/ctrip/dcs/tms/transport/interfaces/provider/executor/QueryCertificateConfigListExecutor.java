package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 证件列表
 */
@Component
public class QueryCertificateConfigListExecutor extends AbstractRpcExecutor<QueryCertificateConfigListSOARequestType, QueryCertificateConfigListSOAResponseType> implements Validator<QueryCertificateConfigListSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryCertificateConfigListExecutor.class);

    @Autowired
    DrvDrvierRepository repository;
    @Autowired
    CertificateQueryService queryService;


    @Override
    public QueryCertificateConfigListSOAResponseType execute(QueryCertificateConfigListSOARequestType requestType) {
        logger.info("QueryCertificateConfigListExecutor  list,params:{}", requestType.toString());
        QueryCertificateConfigListSOAResponseType responseType = new QueryCertificateConfigListSOAResponseType();
        Result<PageHolder<QueryCertificateConfigListSOADTO>> pageHolderResult = queryService.queryCertificateConfigList(requestType);
        PaginationDTO paginationDTO = ServiceResponseUtils.newPagination(pageHolderResult.getData().getPageIndex(),
                pageHolderResult.getData().getPageSize(), pageHolderResult.getData().getTotalSize());
        responseType.setData(pageHolderResult.getData().getData());
        responseType.setPagination(paginationDTO);
        return ServiceResponseUtils.success(responseType);
    }
}
