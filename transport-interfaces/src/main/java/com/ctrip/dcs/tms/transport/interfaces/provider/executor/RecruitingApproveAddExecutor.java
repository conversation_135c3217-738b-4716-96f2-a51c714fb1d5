package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class RecruitingApproveAddExecutor extends AbstractRpcExecutor<RecruitingApproveAddSOARequestType, RecruitingApproveAddSOAResponseType> implements Validator<RecruitingApproveSOARequestType> {

    @Autowired
    private CommonCommandService commonCommandService;
    @Autowired
    private RecruitingCommandService recruitingCommandService;

    @Override
    public RecruitingApproveAddSOAResponseType execute(RecruitingApproveAddSOARequestType requestType) {
        RecruitingApproveAddSOAResponseType soaResponseType = new RecruitingApproveAddSOAResponseType();
        Result<Boolean> result = recruitingCommandService.recruitingApproveAdd(requestType.getRoleCode(), requestType.getAreaScope(),
                requestType.getDrvFrom(), requestType.getMediumId(), requestType.getOperator(), requestType.getRemark(), TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
        if (result != null && result.isSuccess() && result.getData()) {
            return ServiceResponseUtils.success(soaResponseType);
        } else {
            return ServiceResponseUtils.fail(soaResponseType);
        }
    }

    @Override
    public void validate(AbstractValidator<RecruitingApproveSOARequestType> validator) {
        validator.ruleFor("data").notNull();
    }

}