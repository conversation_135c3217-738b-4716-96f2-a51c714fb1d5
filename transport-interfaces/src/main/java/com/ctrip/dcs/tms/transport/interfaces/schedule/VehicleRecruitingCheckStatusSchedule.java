package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Description  车辆招募定时刷新核验状态字段 check_status
 * @Date 20:05 2021/3/18
 * @Param 
 * @return 
 **/
@Component
public class VehicleRecruitingCheckStatusSchedule {

    private static final Logger logger = LoggerFactory.getLogger(VehicleRecruitingCheckStatusSchedule.class);

    @Autowired
    VehicleRecruitingRepository recruitingRepository;
    @Autowired
    TmsCertificateCheckRepository checkRepository;
    @Autowired
    TmsQmqProducerCommandService qmqProducerCommandService;
    @Autowired
    TmsTransportQconfig qconfig;

    @QSchedule("vehicle.recruiting.check.status.job")
    public void vehicleRecruitingCheckStatusSchedule(Parameter parameter) throws Exception {
        String vehicleIds = parameter.getString("vehicleIds");
        this.vehicleRecruitingCheckStatusScheduleMethod(vehicleIds);
    }

    public void vehicleRecruitingCheckStatusScheduleMethod(String vehicleIds) {
        try {
            List<Long> vehicleIdList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(vehicleIds)) {
                vehicleIdList = Arrays.stream(vehicleIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            }
            int count = recruitingRepository.countApproveIngVehicleRecruiting(vehicleIdList, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),2);
            if (count == 0) {
                return;
            }
            List<VehicleRecruitingPO> vehicleRecruitingPOList = recruitingRepository.queryApproveIngVehicleRecruiting(vehicleIdList, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),2);
            for (VehicleRecruitingPO vehicleRecruitingPO : vehicleRecruitingPOList) {
                if (Objects.equals(vehicleRecruitingPO.getVehicleFrom(), TmsTransportConstant.VehicleFromEnum.Veh_AUTO.getCode())) {
                    continue;
                }
                List<TmsCertificateCheckPO> checkPOList = Lists.newArrayList();
                if(qconfig.getQueryCertificateNewVersionSwitch()){
                    checkPOList = checkRepository.queryCertificateByCheckIdOrderBy(vehicleRecruitingPO.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
                }else {
                    checkPOList = checkRepository.queryCertificateByCheckId(vehicleRecruitingPO.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
                }
                if (CollectionUtils.isEmpty(checkPOList)) {
                    continue;
                }
                Boolean flag = Boolean.TRUE;
                for (TmsCertificateCheckPO tmsCertificate : checkPOList) {
                    //兼容三期以后的版本,如果是三期之前的版本则判断原逻辑
                    Integer checkStatus = tmsCertificate.getCheckStatus();
                    if(vehicleRecruitingPO.getVersionFlag() >= 3){
                        checkStatus = tmsCertificate.getThirdCheckStatus();
                    }
                    if (Objects.equals(checkStatus, TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())) {
                        flag = Boolean.FALSE;
                    }
                }
                if(flag){
                    recruitingRepository.updateCheckStatus(Arrays.asList(vehicleRecruitingPO.getVehicleId()),1);
                    recruitingRepository.updateApproveAging(vehicleRecruitingPO.getVehicleId());
                    //发送审批时效qmq
                    qmqProducerCommandService.sendRecruitingApproveAgingQMQ(vehicleRecruitingPO.getVehicleId(), TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(), TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode());
                }
            }
            logger.info("job end");
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}