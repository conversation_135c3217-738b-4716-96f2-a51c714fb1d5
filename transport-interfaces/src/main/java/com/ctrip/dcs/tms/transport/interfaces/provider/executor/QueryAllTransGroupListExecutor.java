package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/4/14 10:49
 */
@Component
public class QueryAllTransGroupListExecutor extends AbstractRpcExecutor<QueryAllTransGroupSOARequestType, QueryAllTransGroupSOAResponseType> implements Validator<QueryAllTransGroupSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryAllTransGroupSOAResponseType execute(QueryAllTransGroupSOARequestType requestType) {
        QueryAllTransGroupSOAResponseType responseType = new QueryAllTransGroupSOAResponseType();
        Result<List<QueryAllTransGroupSOADTO>> listResult = transportGroupQueryService.queryAllTransportGroupsList(buildDO(requestType));
        if (listResult.isSuccess()) {
            responseType.setData(listResult.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, listResult.getCode(), listResult.getMsg());
    }

    private QueryAllTransGroupListDO buildDO(QueryAllTransGroupSOARequestType requestType) {
        QueryAllTransGroupListDO listDO = new QueryAllTransGroupListDO();
        BeanUtils.copyProperties(requestType, listDO);
        return listDO;
    }
}
