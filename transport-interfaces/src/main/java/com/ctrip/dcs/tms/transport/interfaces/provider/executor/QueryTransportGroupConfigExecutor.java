package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/11/23 15:47
 */
@Component
public class QueryTransportGroupConfigExecutor extends AbstractRpcExecutor<QueryTransportGroupConfigSOARequestType, QueryTransportGroupConfigSOAResponseType> implements Validator<QueryTransportGroupConfigSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryTransportGroupConfigSOAResponseType execute(QueryTransportGroupConfigSOARequestType queryTransportGroupConfigSOARequestType) {
        QueryTransportGroupConfigSOAResponseType responseType = new QueryTransportGroupConfigSOAResponseType();
        Result<Map<String, String>> result =  transportGroupQueryService.queryTransportGroupConfig(queryTransportGroupConfigSOARequestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryTransportGroupConfigSOARequestType> validator, QueryTransportGroupConfigSOARequestType req) {

    }
}
