package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 判断是否为调度模式
 * <AUTHOR>
 * @Date 2020/3/18 14:15
 */
@Component
public class IsDispatcherModeExecutor extends AbstractRpcExecutor<IsDispatcherModeSOARequestType, IsDispatcherModeSOAResponseType> implements Validator<IsDispatcherModeSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public IsDispatcherModeSOAResponseType execute(IsDispatcherModeSOARequestType isDispatcherModeSOARequestType) {
        IsDispatcherModeSOAResponseType soaResponseType = new IsDispatcherModeSOAResponseType();
        Integer transportGroupMode = isDispatcherModeSOARequestType.getTransportGroupMode();
        boolean flag = transportGroupQueryService.isDispatcherMode(transportGroupMode);
        soaResponseType.setData(flag);
        return  ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<IsDispatcherModeSOARequestType> validator, IsDispatcherModeSOARequestType req) {
        validator.ruleFor("transportGroupMode").notNull();
    }
}
