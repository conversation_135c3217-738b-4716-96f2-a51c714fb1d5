package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandManager;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.dianping.cat.Cat;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.message.Event;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * IVR电话验证结果监听器
 */
@Component
public class IVRCallResultListener {

    private static final Logger logger = LoggerFactory.getLogger(IVRCallResultListener.class);

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private IVRCallService ivrCallService;

    @Autowired
    TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    DriverCommandManager  driverCommandManager;

    /**
     * 发起IVR
     * @param message QMQ消息
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL,
        consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void callIvr(Message message) {
        // 获取消息中的参数
        String phoneNumber = message.getStringProperty("phoneNumber");
        String igtCode = message.getStringProperty("igtCode");
        String modifyUser = message.getStringProperty("modifyUser");
        String language = message.getStringProperty("language");

        phoneNumber = TmsTransUtil.encrypt(phoneNumber, KeyType.Phone);

        // 查询IRV结果
        boolean isConnected = ivrCallService.isPhoneVerified(
            PhoneDTO.builder().mobilePhone(phoneNumber).countryCode(igtCode).build());

        // 如果手机号没有经过验证，异步发起IVR电话验证
        if (!isConnected) {
            // 使用IVRCallService发起IVR电话验证
            String callTaskId = String.valueOf(ivrCallService.callPhoneForVerify(phoneNumber, igtCode, "tms_driver_register", language));
            Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "callIVR_send_delay_message", Event.SUCCESS, "phoneNumber:" + phoneNumber + ",callTaskId:" + callTaskId);
            // 发送延迟消息(给需要后置逻辑处理的场景进行逻辑处理)
            ivrCallService.sendIvrVerifyDelayMessage(tmsTransportQconfig.getIvrCallVerificationDelayMinutes(), new PhoneDTO(phoneNumber, igtCode, modifyUser));
        }
    }

    /**
     * 处理IVR电话呼叫结果
     * @param message QMQ消息
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL_VERIFICATION,
                 tags = {TmsTransportConstant.QmqTag.TAG_IVR_CALL_VERIFICATION},
                 consumerGroup = TmsTransportConstant.QMQ_CONSUMER_GROUP)
    public void handleIVRCallResult(Message message) {
        
        Cat.logEvent(CatEventType.IVR_CALL_RESULT, "method_entry");

        // 获取消息中的参数
        String phoneNumber = message.getStringProperty("phoneNumber");
        String igtCode = message.getStringProperty("igtCode");
        String modifyUser = message.getStringProperty("modifyUser");

        DrvDriverPO drvDriverPo = drvDrvierRepository.queryOneDrvByPhone(phoneNumber);
        if (drvDriverPo == null) {
            return;
        }
        // 处理ivr回调结果
        driverCommandManager.getHandler(drvDriverPo.getInternalScope()).afterIvrCall(new PhoneDTO(phoneNumber, igtCode, modifyUser));

    }
}
