package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.SaveOverseasTagsSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.SaveOverseasTagsSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.RecruitingCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
　* @description: 境外OCR识别
　* <AUTHOR>
　* @date 2023/6/8 15:59
*/
@Component
public class SaveOverseasTagsExecutor extends AbstractRpcExecutor<SaveOverseasTagsSOARequestType, SaveOverseasTagsSOAResponseType> implements Validator<SaveOverseasTagsSOARequestType> {

    @Autowired
    RecruitingCommandService commandService;

    @Override
    public SaveOverseasTagsSOAResponseType execute(SaveOverseasTagsSOARequestType requestType) {
        SaveOverseasTagsSOAResponseType responseType = new SaveOverseasTagsSOAResponseType();
        Result<Boolean> result = commandService.saveOverseasTags(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, result.getCode(), result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<SaveOverseasTagsSOARequestType> validator) {
//        validator.ruleFor("ocrImgUrl").notNull();
//        validator.ruleFor("cityId").notNull();
//        validator.ruleFor("ocrType").notNull();
    }
}
