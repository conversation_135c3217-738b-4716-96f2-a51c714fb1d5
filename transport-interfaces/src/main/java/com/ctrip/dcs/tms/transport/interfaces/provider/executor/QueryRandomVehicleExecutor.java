package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.exception.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import javax.annotation.*;
import java.util.*;

/**
 * 查询司机所属供应商 城市下 一个随机的车辆信息
 */
@Component
public class QueryRandomVehicleExecutor extends AbstractRpcExecutor<QueryRandomVehicleRequestType, QueryRandomVehicleResponseType> {
    private static final Logger logger = LoggerFactory.getLogger(QueryRandomVehicleExecutor.class);
    @Resource
    private IQueryVehicleInfoService queryVehicleInfoService;
    @Override
    public QueryRandomVehicleResponseType execute(QueryRandomVehicleRequestType requestType) {
        try{
            if(StringUtils.isEmpty(requestType.getDriverId()) || requestType.isConformanceFlag() == null){
                return ServiceResponseUtils.fail(new QueryRandomVehicleResponseType(),"500","param_invalid");
            }
            VehCacheDTO vehCacheDTO = queryVehicleInfoService.queryRandomVehicle(requestType.getDriverId(),requestType.isConformanceFlag());
            return getResponseType(vehCacheDTO);
        }catch (BizException be){
            return ServiceResponseUtils.fail(new QueryRandomVehicleResponseType(),"500",be.getMessage());
        }catch (Exception e){
            Map<String,String> tags = new HashMap<>();
            tags.put("driverId",requestType.getDriverId());
            logger.error("QueryRandomVehicleExecutor_excep",e,tags);
            return ServiceResponseUtils.fail(new QueryRandomVehicleResponseType(),"500",e.getMessage());
        }
    }
    /**
     * 封装车辆数据
     * @param vehCacheDTO
     * @return
     */
    private QueryRandomVehicleResponseType getResponseType(VehCacheDTO vehCacheDTO){
        QueryRandomVehicleResponseType responseType = new QueryRandomVehicleResponseType();
        if(vehCacheDTO == null){
            return ServiceResponseUtils.success(responseType);
        }
        QueryRandomVehicleResult queryRandomVehicleResult = new QueryRandomVehicleResult();
        queryRandomVehicleResult.setCarId(vehCacheDTO.getCarId());
        queryRandomVehicleResult.setCarLicense(vehCacheDTO.getCarLicense());
        queryRandomVehicleResult.setCarBrandId(vehCacheDTO.getCarBrandId());
        queryRandomVehicleResult.setCarBrandName(vehCacheDTO.getCarBrandName());
        queryRandomVehicleResult.setCarColorId(vehCacheDTO.getCarColorId());
        queryRandomVehicleResult.setCarColor(vehCacheDTO.getCarColor());
        queryRandomVehicleResult.setCarTypeId(vehCacheDTO.getCarTypeId());
        queryRandomVehicleResult.setCarTypeName(vehCacheDTO.getCarTypeName());
        queryRandomVehicleResult.setMaxLuggages(vehCacheDTO.getMaxLuggages());
        queryRandomVehicleResult.setMaxPassengers(vehCacheDTO.getMaxPassengers());
        queryRandomVehicleResult.setIsEnergy(vehCacheDTO.getIsEnergy());
        queryRandomVehicleResult.setCarSeriesId(vehCacheDTO.getCarSeriesId());
        queryRandomVehicleResult.setCarSeriesName(vehCacheDTO.getCarSeriesName());
        queryRandomVehicleResult.setVehProductionLineCodeList(vehCacheDTO.getVehProductionLineCodeList());
        queryRandomVehicleResult.setVehicleStatus(vehCacheDTO.getVehicleStatus());
        queryRandomVehicleResult.setRideHailingVehCertValid(vehCacheDTO.getRideHailingVehCertValid());
        queryRandomVehicleResult.setVin(vehCacheDTO.getVin());
        queryRandomVehicleResult.setVehRegstDate(vehCacheDTO.getVehRegstDate());
        queryRandomVehicleResult.setVehCreateTime(vehCacheDTO.getVehCreateTime());
        queryRandomVehicleResult.setVehicleFullImg(vehCacheDTO.getVehicleFullImg());
        responseType.setData(queryRandomVehicleResult);
        return ServiceResponseUtils.success(responseType);
    }
}
