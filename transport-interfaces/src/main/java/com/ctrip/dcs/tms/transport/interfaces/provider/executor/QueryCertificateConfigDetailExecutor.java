package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 证件信息
 */
@Component
public class QueryCertificateConfigDetailExecutor extends AbstractRpcExecutor<QueryCertificateConfigDetailSOARequestType, QueryCertificateConfigDetailSOAResponseType> implements Validator<QueryCertificateConfigDetailSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryCertificateConfigDetailExecutor.class);

    @Autowired
    CertificateQueryService queryService;


    @Override
    public QueryCertificateConfigDetailSOAResponseType execute(QueryCertificateConfigDetailSOARequestType requestType) {
        logger.info("QueryCertificateConfigInfoExecutor  requestType,params:{}", requestType.toString());
        QueryCertificateConfigDetailSOAResponseType responseType = new QueryCertificateConfigDetailSOAResponseType();
        Result<QueryCertificateConfigListSOADTO> result = queryService.queryCertificateConfigDetail(requestType);
        if(result.isSuccess()){
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryCertificateConfigDetailSOARequestType> validator) {
        validator.ruleFor("id").notNull();
    }
}
