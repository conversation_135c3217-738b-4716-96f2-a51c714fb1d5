package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.CheckVehicleNetCertNoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.CheckVehicleNetCertNoSOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.INetCertNoCheckRuleService;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CheckVehicleNetCertNoExecutor extends AbstractRpcExecutor<CheckVehicleNetCertNoSOARequestType, CheckVehicleNetCertNoSOAResponseType> implements Validator<CheckVehicleNetCertNoSOARequestType> {
    @Resource
    private INetCertNoCheckRuleService netCertNoCheckRuleService;
    @Override
    public CheckVehicleNetCertNoSOAResponseType execute(CheckVehicleNetCertNoSOARequestType requestType) {
        boolean result = netCertNoCheckRuleService.checkVehicleNetCertNo(requestType.getCityId().toString(),requestType.getNetCertNo());
        CheckVehicleNetCertNoSOAResponseType responseType = new CheckVehicleNetCertNoSOAResponseType();
        responseType.setData(result);
        return ServiceResponseUtils.success(responseType);
    }
    @Override
    public void validate(AbstractValidator<CheckVehicleNetCertNoSOARequestType> validator, CheckVehicleNetCertNoSOARequestType req) {
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("netCertNo").notNull();
    }
}
