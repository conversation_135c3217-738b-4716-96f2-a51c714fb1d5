package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.scm.merchant.interfaces.dto.*;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
public class QueryContractInfoExecutor extends AbstractRpcExecutor<QueryContractInfoSOARequestType, QueryContractInfoSOAResponseType> implements Validator<QueryContractInfoSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryContractInfoExecutor.class);

    @Autowired
    private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;

    @Override
    public QueryContractInfoSOAResponseType execute(QueryContractInfoSOARequestType requestType) {
        QueryContractInfoSOAResponseType responseType = new QueryContractInfoSOAResponseType();
        if (requestType.getSupplierId() == null) {
            return ServiceResponseUtils.fail(responseType);
        }
        QueryContractListRequestType contractListRequestType = new QueryContractListRequestType();
        contractListRequestType.setRetrievalItems(ImmutableList.of("serviceprovider.base","contract.servedscope"));
        ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
        filterDTO.setSupplierIds(ImmutableList.of(requestType.getSupplierId()));
        contractListRequestType.setInclusionFilter(filterDTO);
        try {
            QueryContractListResponseType listResponseType = dcsScmMerchantServiceClientProxy.queryContractList(contractListRequestType);
            if (listResponseType == null || CollectionUtils.isEmpty(listResponseType.getContracts()) || CollectionUtils.isEmpty(listResponseType.getServiceProviders())) {
                logger.error("QueryContractInfo","supplierId:{} nothing",requestType.getSupplierId());
                return ServiceResponseUtils.success(responseType);
            }
            Map<Long, String> serviceProviderMap = Maps.newHashMapWithExpectedSize(listResponseType.getServiceProviders().size());
            for (ServiceProviderDTO serviceProviderDTO : listResponseType.getServiceProviders()) {
                if (serviceProviderDTO != null && serviceProviderDTO.getBase() != null) {
                    serviceProviderMap.put(serviceProviderDTO.getBase().getId(),serviceProviderDTO.getBase().getBrandLocalName());
                }
            }
            List<ContractInfo> contractInfoList = Lists.newArrayListWithExpectedSize(listResponseType.getContracts().size());
            for (ContractDTO contractDTO : listResponseType.getContracts()) {
                if (contractDTO.getBase() == null || contractDTO.getBase().getServiceProviderId() == null || contractDTO.getBase().getServiceProviderId() == 0) {
                    continue;
                }
                ContractInfo info = new ContractInfo();
                info.setContractId(contractDTO.getBase().id);
                info.setSalesMode(contractDTO.getBase().getSalesMode());
                info.setServiceProviderName(serviceProviderMap.get(contractDTO.getBase().getServiceProviderId()));
                info.setSalesModeName(SharkUtil.getSalesModeName(contractDTO.getBase().getSalesMode()));
                info.setServiceProviderId(contractDTO.getBase().getServiceProviderId());
                contractInfoList.add(info);
            }
            responseType.setData(contractInfoList);
            return ServiceResponseUtils.success(responseType);
            } catch (Exception e) {
            return ServiceResponseUtils.fail(responseType,ServiceResponseConstants.ResStatus.EXCEPTION_CODE,e.getLocalizedMessage());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryContractInfoSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
    }
}
