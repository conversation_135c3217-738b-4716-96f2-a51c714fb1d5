package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 绑定司机关系
 */
@Component
public class BindDriverRelationExecutor extends AbstractRpcExecutor<DriverRelationBindRequestSOAType, DriverRelationBindResponseSOAType> implements Validator<DriverRelationBindRequestSOAType> {

    private static final Logger logger = LoggerFactory.getLogger(BindDriverRelationExecutor.class);

    @Autowired
    private TransportGroupCommandService transportGroupCommandService;

    @Override
    public DriverRelationBindResponseSOAType execute(DriverRelationBindRequestSOAType requestSOAType) {
        DriverRelationBindResponseSOAType responseSOAType = new DriverRelationBindResponseSOAType();
        try {
            MetricsUtils.transportGroupBingDriverConnectTimes(ApiTypeEnum.TRANSPORT_GROUP_BING_DRIVER,requestSOAType.getTransportGroupId(),requestSOAType.getDrvIdList());
            Result<Boolean> result = transportGroupCommandService.bindDriverRelation(requestSOAType);
            if (!result.isSuccess()) {
                return ServiceResponseUtils.fail(responseSOAType, result.getCode(), result.getMsg());
            }
            return ServiceResponseUtils.success(responseSOAType);
        } catch (Exception e) {
            logger.error("BindDriverRelationExecutor error:", e);
            MetricsUtils.transportGroupBingDriverExceptionTimes(ApiTypeEnum.TRANSPORT_GROUP_BING_DRIVER,requestSOAType.getTransportGroupId(),requestSOAType.getDrvIdList());
            return ServiceResponseUtils.fail(responseSOAType);
        }
    }

}