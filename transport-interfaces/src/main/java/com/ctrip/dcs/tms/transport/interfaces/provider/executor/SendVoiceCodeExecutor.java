package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.SendVoiceCodeRequestType;
import com.ctrip.model.SendVoiceCodeResponseType;

@Component
public class SendVoiceCodeExecutor extends AbstractRpcExecutor<SendVoiceCodeRequestType, SendVoiceCodeResponseType> implements Validator<SendVoiceCodeRequestType> {

    @Autowired
    private CommonCommandService commonCommandService;

    @Override
    public SendVoiceCodeResponseType execute(SendVoiceCodeRequestType requestType) {
        String site = requestType.getSite();
        Result<String> result = commonCommandService.sendVoiceCode(requestType.getMobilePhone(), requestType.getCountryCode(), requestType.getSite());
        if (!result.isSuccess()) {
            Cat.logEvent("sendMessageByPhone4China", "-1");
            SendVoiceCodeResponseType sendVoiceCodeResponseType = new SendVoiceCodeResponseType();
            sendVoiceCodeResponseType.setCode(result.getData());
            return ServiceResponseUtils.fail(sendVoiceCodeResponseType, result.getCode(), result.getMsg());
        }
        return ServiceResponseUtils.success(new SendVoiceCodeResponseType());
    }
}
