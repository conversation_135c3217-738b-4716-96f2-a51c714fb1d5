package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

@Component
public class BackGroudCheckCallBackExecutor extends AbstractRpcExecutor<BackGroudCheckCallBackSOARequestType, BackGroudCheckCallBackSOAResponseType> implements Validator<BackGroudCheckCallBackSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(BackGroudCheckCallBackExecutor.class);

    @Autowired
    private CertificateCheckCommandService checkCommandService;

    @Override
    public BackGroudCheckCallBackSOAResponseType execute(BackGroudCheckCallBackSOARequestType requestType) {
        BackGroudCheckCallBackSOAResponseType responseType = new BackGroudCheckCallBackSOAResponseType();
        Result<Boolean> result =  checkCommandService.insertBackGroundCheck(requestType.getChecksDTOList());
        if(result.isSuccess()){
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }
}
