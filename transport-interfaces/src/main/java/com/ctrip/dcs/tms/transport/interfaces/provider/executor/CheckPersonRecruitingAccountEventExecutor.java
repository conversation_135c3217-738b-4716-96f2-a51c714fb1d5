package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.CheckPersonRecruitingAccountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.CheckPersonRecruitingAccountSOAResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.PersonRecruitingRepository;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/***
　* @description: 查询手机号是否加盟过
　* <AUTHOR>
　* @date 2024/6/4 15:14
*/
@Component
public class CheckPersonRecruitingAccountEventExecutor extends AbstractRpcExecutor<CheckPersonRecruitingAccountSOARequestType, CheckPersonRecruitingAccountSOAResponseType> implements Validator<CheckPersonRecruitingAccountSOARequestType> {

    @Autowired
    private PersonRecruitingRepository personRecruitingRepository;

    @Override
    public CheckPersonRecruitingAccountSOAResponseType execute(CheckPersonRecruitingAccountSOARequestType requestType) {
        CheckPersonRecruitingAccountSOAResponseType responseType = new CheckPersonRecruitingAccountSOAResponseType();
        int result = personRecruitingRepository.checkPhoneCount(requestType.getAccount());
        responseType.setData(result == 0?Boolean.TRUE:Boolean.FALSE);
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<CheckPersonRecruitingAccountSOARequestType> validator) {
        validator.ruleFor("account").notNull();
    }

}