package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * 更新司机意愿车型
 * <AUTHOR>
 * @Date 2020/10/12 17:05
 */
@Component
public class UpdateDrvIntendVehicleTypeExecutor extends AbstractRpcExecutor<UpdateDrvIntendVehicleTypeRequestType, UpdateDrvIntendVehicleTypeResponseType> implements Validator<UpdateDrvIntendVehicleTypeRequestType> {

    @Autowired
    private DriverCommandService driverCommandService;

    @Override
    public UpdateDrvIntendVehicleTypeResponseType execute(UpdateDrvIntendVehicleTypeRequestType updateDrvIntendVehicleTypeRequestType) {
        UpdateDrvIntendVehicleTypeResponseType responseType = new UpdateDrvIntendVehicleTypeResponseType();
        Result<Boolean> result = driverCommandService.updateDrvIntendVehicleType(updateDrvIntendVehicleTypeRequestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<UpdateDrvIntendVehicleTypeRequestType> validator, UpdateDrvIntendVehicleTypeRequestType req) {
        validator.ruleFor("drvId").notNull();
    }
}
