package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.*;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RejectReasonDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.CodeNameListVO;
import com.ctrip.dcs.tms.transport.api.model.CodeNameVO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AuditStatusEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.VehicleAgeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.ExitInspectionDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.AuditRejectConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ExitInspectionConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.QueryCommonEnumRequestType;
import com.ctrip.model.QueryCommonEnumResponseType;

@Component
public class QueryCommonEnumExecutor extends AbstractRpcExecutor<QueryCommonEnumRequestType, QueryCommonEnumResponseType> implements Validator<QueryCommonEnumRequestType> {
    public static final String PERFIX = "tms.overage.%s";
    @Autowired
    private ExitInspectionConfig exitInspectionConfig;
    @Autowired
    private AuditRejectConfig auditRejectConfig;

    @Override
    public QueryCommonEnumResponseType execute(QueryCommonEnumRequestType queryCommonEnumRequestType) {
        List<String> keyList = queryCommonEnumRequestType.getKeyList();
        Map<String, CodeNameListVO> map = new HashMap<>();
        QueryCommonEnumResponseType responseType = new QueryCommonEnumResponseType();
        CodeNameListVO codeNameListVO = new CodeNameListVO();
        Map<String, List<ExitInspectionDTO>> exitInspectionMap = exitInspectionConfig.getExitInspectionMap();
        Map<String, List<RejectReasonDTO>> rejectReasonMap = auditRejectConfig.getRejectReasonMap();
        Set<String> exitInspectKeySet = exitInspectionMap.keySet();
        Set<String> rejectReasonKeySet = rejectReasonMap.keySet();
        if (keyList.contains("overage")) {
            List<String> collect = Arrays.stream(VehicleAgeTypeEnum.values()).filter(item -> BooleanUtils.isTrue(item.getShark())).map(VehicleAgeTypeEnum::getCode).collect(Collectors.toList());
            List<CodeNameVO> collect1 = collect.stream().map(item -> {
                String sharkValueDefault = SharkUtils.getSharkValueDefault(String.format(PERFIX, item));
                CodeNameVO codeNameVO = new CodeNameVO();
                codeNameVO.setName(sharkValueDefault);
                codeNameVO.setCode(item);
                return codeNameVO;
            }).collect(Collectors.toList());
            codeNameListVO.setData(collect1);
            map.put("overage", codeNameListVO);
        }
        if (keyList.contains("auditType")) {
            List<CodeNameVO> auditTypeList = Arrays.stream(AuditStatusEnum.values())
                .filter(item -> BooleanUtils.isTrue(item.getShark()))
                .map(item -> {
                    CodeNameVO codeNameVO = new CodeNameVO();
                    codeNameVO.setName(item.getName());
                    codeNameVO.setCode(item.getCode());
                    return codeNameVO;
                }).collect(Collectors.toList());
            CodeNameListVO auditTypeCodeNameListVO = new CodeNameListVO();
            auditTypeCodeNameListVO.setData(auditTypeList);
            map.put("auditType", auditTypeCodeNameListVO);
        }
        List<String> exitKeyList = keyList.stream().filter(exitInspectKeySet::contains).collect(Collectors.toList());
        List<String> rejectReasonKeyList = keyList.stream().filter(rejectReasonKeySet::contains).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exitKeyList)) {
            exitKeyList.forEach(key -> {
                List<ExitInspectionDTO> strings = exitInspectionMap.get(key);
                if (CollectionUtils.isNotEmpty(strings)) {
                    List<CodeNameVO> collect = strings.stream().filter(item -> BooleanUtils.isTrue(item.getEnable())).map(item -> {
                        CodeNameVO codeNameVO = new CodeNameVO();
                        codeNameVO.setName(SharkUtils.getSharkValueDefault(item.getCode()));
                        codeNameVO.setCode(item.getCode());
                        return codeNameVO;
                    }).collect(Collectors.toList());
                    CodeNameListVO codeNameListVO1 = new CodeNameListVO();
                    codeNameListVO1.setData(collect);
                    map.put(key, codeNameListVO1);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(rejectReasonKeyList)) {
            rejectReasonKeyList.forEach(key -> {
                List<RejectReasonDTO> strings = rejectReasonMap.get(key);
                if (CollectionUtils.isNotEmpty(strings)) {
                    List<CodeNameVO> collect = strings.stream().filter(item -> BooleanUtils.isTrue(item.getEnable())).map(item -> {
                        CodeNameVO codeNameVO = new CodeNameVO();
                        codeNameVO.setName(SharkUtils.getSharkValueDefault(item.getCode()));
                        codeNameVO.setCode(item.getCode());
                        return codeNameVO;
                    }).collect(Collectors.toList());
                    CodeNameListVO codeNameListVO1 = new CodeNameListVO();
                    codeNameListVO1.setData(collect);
                    map.put(key, codeNameListVO1);
                }
            });
        }
        responseType.setResult(map);
        return ServiceResponseUtils.success(responseType);
    }
}
