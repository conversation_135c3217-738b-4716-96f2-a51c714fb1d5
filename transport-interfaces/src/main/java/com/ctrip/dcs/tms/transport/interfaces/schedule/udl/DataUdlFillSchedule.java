package com.ctrip.dcs.tms.transport.interfaces.schedule.udl;

import com.ctrip.dcs.tms.transport.api.model.RecruitingSOARequestDTO;
import com.ctrip.dcs.tms.transport.api.model.RecruitingSOAResponseDTO;
import com.ctrip.dcs.tms.transport.application.query.RecruitingQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverLeavePO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvFreezeRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvFreezePO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsModRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsTransportApprovePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDriverLeaveRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvFreezeRecordRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.ModRecordRespository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsTransportApproveRepository;
import com.ctrip.dcs.tms.transport.interfaces.schedule.CheckPhoneNumberSchedule;
import com.ctrip.dcs.tms.transport.interfaces.schedule.QueryAllDrvForSchedule;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
　* @description: 存量数据填充UDL
　* <AUTHOR>
　* @date 2022/12/29 17:17
*/
@Component
public class DataUdlFillSchedule {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataUdlFillSchedule.class);

    @Autowired
    private DrvDrvierRepository driverRepository;

    @Autowired
    RecruitingQueryService recruitingQueryService;

    @Autowired
    DrvRecruitingRepository drvRecruitingRepository;

    @Autowired
    DrvDriverLeaveRepository drvDriverLeaveRepository;

    @Autowired
    DrvFreezeRecordRepository drvFreezeRecordRepository;

    @Autowired
    TmsDrvFreezeRepository drvFreezeRepository;

    @Autowired
    TmsTransportApproveRepository tmsTransportApproveRepository;

    @Autowired
    ModRecordRespository modRecordRespository;

    @Autowired
    UDLHandler udlHandler;
    
    @Autowired
    QueryAllDrvForSchedule queryAllDrvForSchedule;

    /**
     * 报名制运力组剔除报名成功的司机
     * @param parameter
     * @throws Exception
     */
    @QSchedule("driver.data.udl.fill.job")
    public void setUdl(Parameter parameter) throws Exception {
        String tableList = parameter.getString("tableList");
        for (String table : tableList.split(",")) {
            log("===================start {} ========================", table);
            switch (table) {
                case "drv_driver":
                    drvUdl();
                case "drv_recruiting":
                    drvRecruitingUdl();
                case "drv_driver_leave":
                    drvLeaveUdl();
                case "tms_drv_freeze":
                    drvFreezeUdl();
                case "tms_drv_freeze_record":
                    freezeRecordUdl();
                case "tms_mod_record":
                    modRecordUdl();
                case "tms_transport_approve":
                    transportApprovalUdl();
                default:
                    break;
            }
            log("===================end {} ========================", table);
        }
    }

    private void modRecordUdl() {
        queryAllDrvForSchedule.doDrvAction(drvPo -> {
            drvPo.setProviderDataLocation(getDrvUdl(drvPo.getDrvId()));
            TmsModRecordPO modRecordPO = new TmsModRecordPO();
            modRecordPO.setRrdId(drvPo.getDrvId());
            modRecordPO.setRrdType(CommonEnum.RecordTypeEnum.DRIVER.getCode());
            modRecordPO.setProviderDataLocation(getDrvUdl(drvPo.getDrvId()));
            modRecordRespository.getTmsModRecordRepo().update(modRecordPO);
        });
    }


    @SneakyThrows
    private void drvUdl() {
        queryAllDrvForSchedule.doDrvAction(drvPo -> {
            drvPo.setProviderDataLocation(getDrvUdl(drvPo.getDrvId()));
            driverRepository.updateDrv(drvPo);
        });
    }

    private String getDrvUdl(Long drvId) {
        return udlHandler.getDrvUdl(drvId);
    }

    private String getDrvUdlByCityId(Long cityId) {
        return udlHandler.getDrvUdlByCityId(cityId);
    }

    @SneakyThrows
    private void drvRecruitingUdl() {
        int pageNo = 1;
        int pageSize = 10;
        Result<PageHolder<RecruitingSOAResponseDTO>> result = null;
        PaginatorDTO page = new PaginatorDTO(pageNo, pageSize);
        RecruitingSOARequestDTO dto = new RecruitingSOARequestDTO();
        dto.setRecruitingType(TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
        Map<String, String> sessionSource = Maps.newHashMap();
        sessionSource.put("accountType", String.valueOf(TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue()));
        SessionHolder.setSessionSource(sessionSource);
        do{
            log("===================pageNo: {}, pageSize: {} ========================", pageNo, page.getPageSize());
            result = recruitingQueryService.queryRecruitingSOAList(dto, page);
            page.setPageNo(1 + page.getPageNo());
            result.getData().getData().forEach(drvPo -> {
                DrvRecruitingPO po = drvRecruitingRepository.queryByPK(drvPo.getDrvRecruitingId());
                po.setProviderDataLocation(getDrvUdlByCityId(po.getCityId()));
                drvRecruitingRepository.update(po);
            });
        }while (CollectionUtils.isNotEmpty(result.getData().getData()));
    }



    @FunctionalInterface
    public interface UdlCallable {
        /**
         * Computes a result, or throws an exception if unable to do so.
         *
         * @throws Exception if unable to compute a result
         */
        void call(DrvDriverPO drv) throws Exception;
    }

    private void drvLeaveUdl() {
        queryAllDrvForSchedule.doDrvAction(drvPo -> {
            DrvDriverLeavePO po = new DrvDriverLeavePO();
            po.setProviderDataLocation(getDrvUdl(drvPo.getDrvId()));
            po.setDrvId(drvPo.getDrvId());
            drvDriverLeaveRepository.update(po);
        });
    }

    private void freezeRecordUdl() {
        queryAllDrvForSchedule.doDrvAction(drvPo -> {
            DrvFreezeRecordPO po = new DrvFreezeRecordPO();
            po.setProviderDataLocation(getDrvUdl(drvPo.getDrvId()));
            po.setDrvId(drvPo.getDrvId());
            drvFreezeRecordRepository.update(po);
        });
    }

    private void drvFreezeUdl() {
        queryAllDrvForSchedule.doDrvAction(drvPo -> {
            TmsDrvFreezePO po = new TmsDrvFreezePO();
            po.setProviderDataLocation(getDrvUdl(drvPo.getDrvId()));
            po.setDrvId(drvPo.getDrvId());
            drvFreezeRepository.updateDrvFreeze(po);
        });
    }

    private void transportApprovalUdl() {
        queryAllDrvForSchedule.doDrvAction(drvPo -> {
            TmsTransportApprovePO po = new TmsTransportApprovePO();
            po.setProviderDataLocation(getDrvUdl(drvPo.getDrvId()));
            po.setApproveSourceId(drvPo.getDrvId());
            po.setApproveSourceType(TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode());
            tmsTransportApproveRepository.update(po);
        });
    }

    protected void log(String format, Object... arg) {
        try {
            Optional.ofNullable(TaskHolder.getKeeper()).map(TaskMonitor::getLogger).ifPresent(logger -> logger.info(format, arg));
        } catch (Exception e) {
            LOGGER.warn("log fail", e);
        }
        LOGGER.info(format, arg);
    }
}
