package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.application.query.IQueryDriverForSaasService;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryDriverIdForSaasExecutorService {
    @Autowired
    private IQueryDriverForSaasService queryDriverForSaasService;
    public QueryDriverIdForSaasResponseType execute(QueryDriverIdForSaasRequestType requestType) {
        try{
            List<Long> driverIds = queryDriverForSaasService.queryDriverId(requestType.getDriverPhone(),requestType.getSupplierId());
            return ServiceResponseUtils.success(getResponse(driverIds));
        }catch (Exception e){
            return ServiceResponseUtils.fail(new QueryDriverIdForSaasResponseType());
        }
    }

    /**
     * 封装结果
     * @param driverIds
     * @return
     */
    public QueryDriverIdForSaasResponseType getResponse(List<Long> driverIds){
        QueryDriverIdForSaasResponseType responseType = new QueryDriverIdForSaasResponseType();
        responseType.setDriverIds(driverIds);
        return responseType;
    }
}
