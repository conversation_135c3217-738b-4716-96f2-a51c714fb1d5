package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 查询司机可选的意愿车型
 * <AUTHOR>
 * @Date 2020/10/13 14:44
 */
@Component
public class QueryOptionalIntendVehicleTypeExecutor extends AbstractRpcExecutor<QueryOptionalIntendVehicleTypeSOARequestType, QueryOptionalIntendVehicleTypeSOAResponseType> implements Validator<QueryOptionalIntendVehicleTypeSOARequestType> {

    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    public QueryOptionalIntendVehicleTypeSOAResponseType execute(QueryOptionalIntendVehicleTypeSOARequestType queryOptionalIntendVehicleTypeSOARequestType) {
        QueryOptionalIntendVehicleTypeSOAResponseType responseType = new QueryOptionalIntendVehicleTypeSOAResponseType();
        Result<List<QueryIntendVehicleTypeSOAResponseDTO>> result =  driverQueryService.queryOptionalIntendVehicleType(queryOptionalIntendVehicleTypeSOARequestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryOptionalIntendVehicleTypeSOARequestType> validator, QueryOptionalIntendVehicleTypeSOARequestType req) {
    }
}
