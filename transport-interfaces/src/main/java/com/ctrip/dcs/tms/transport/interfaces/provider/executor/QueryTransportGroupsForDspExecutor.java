package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.AbstraceCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil.logQueryResult;

/**
　* @description: TODO
　* <AUTHOR>
　* @date 2022/1/14 14:35
*/
@Component
@ServiceLogTagPair(key = "qTraceId")
public class QueryTransportGroupsForDspExecutor extends AbstractRpcExecutor<QueryTransportGroupsForDspRequestType, QueryTransportGroupsForDspResponseType> implements Validator<QueryTransportGroupsForDspRequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public QueryTransportGroupsForDspResponseType execute(QueryTransportGroupsForDspRequestType requestType) {
        QueryTransportGroupsForDspResponseType soaResponseType = new QueryTransportGroupsForDspResponseType();
        QueryTransportGroupModel queryModel = requestToQueryModel(requestType);
        Result<List<QueryTransportGroupsForDspInfo>> listResult = transportGroupQueryService.queryTransportGroupsForDsp(queryModel);
        if (listResult.isSuccess()) {
            soaResponseType.setData(listResult.getData());
            return ServiceResponseUtils.success(soaResponseType);
        }
        return ServiceResponseUtils.fail(soaResponseType, listResult.getCode(),listResult.getMsg());
    }

    @Override
    public void validate(AbstractValidator<QueryTransportGroupsForDspRequestType> validator, QueryTransportGroupsForDspRequestType req) {
        Cat.logEvent(Constant.EventType.QUERY_TRANSPORT_GROUP_SKU_LIST_SIZE, String.valueOf(Optional.ofNullable(req.getSkuIds()).map(List::size).orElse(0)));
    }

    private QueryTransportGroupModel requestToQueryModel(QueryTransportGroupsForDspRequestType requestType){
        QueryTransportGroupModel queryModel = new QueryTransportGroupModel();
        queryModel.setGroupStatus(requestType.getGroupStatus());
        queryModel.setTransportGroupMode(requestType.getTransportGroupMode());
        queryModel.setSkuIds(requestType.getSkuIds());
        queryModel.setPoiType(requestType.getPoiType());
        queryModel.setLatitude(requestType.getLatitude());
        queryModel.setLongitude(requestType.getLongitude());
        queryModel.setPoiRef(requestType.getPoiRef());
        queryModel.setCarPlaceId(requestType.getCarPlaceId());
        queryModel.setFilterDispatchOnly(requestType.getFilterDispatchOnly());
        queryModel.setPoiList(requestType.getPoiList());
        return queryModel;
    }

    @Override
    public void onExecuted(QueryTransportGroupsForDspRequestType req, QueryTransportGroupsForDspResponseType resp) {
        AbstraceCacheHandler.setExtension(resp);
        logQueryResult(resp.getData());
    }
}
