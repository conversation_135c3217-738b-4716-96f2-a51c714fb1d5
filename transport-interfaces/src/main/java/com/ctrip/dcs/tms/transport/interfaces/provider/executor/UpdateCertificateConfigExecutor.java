package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.jackson.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;


/**
 * 编辑配置
 */
@Component
public class UpdateCertificateConfigExecutor extends AbstractRpcExecutor<CertificateConfigUpdateSOARequestType, CertificateConfigUpdateSOAResponseType> implements Validator<CertificateConfigUpdateSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(UpdateCertificateConfigExecutor.class);

    @Autowired
    private CertificateCommandService certificateCommandService;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public CertificateConfigUpdateSOAResponseType execute(CertificateConfigUpdateSOARequestType requestType){
        CertificateConfigUpdateSOAResponseType responseType = new CertificateConfigUpdateSOAResponseType();
        logger.info("UpdateCertificateConfigExecutor start params: requestType:"+ JacksonUtil.serialize(requestType));
        Result<Boolean> result = certificateCommandService.updateCertificateConfig(requestType);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<CertificateConfigUpdateSOARequestType> validator) {
        validator.ruleFor("id").notNull();
        validator.ruleFor("certificateConfig").notNull();
    }
}
