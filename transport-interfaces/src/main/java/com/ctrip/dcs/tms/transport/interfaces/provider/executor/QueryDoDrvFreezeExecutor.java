package com.ctrip.dcs.tms.transport.interfaces.provider.executor;


import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 查询可冻结的司机
 */
@Component
public class QueryDoDrvFreezeExecutor extends AbstractRpcExecutor<QueryDoDrvFreezeSOARequestType, QueryDoDrvFreezeSOAResponseType> implements Validator<QueryDoDrvFreezeSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryDoDrvFreezeExecutor.class);

    @Autowired
    DrvFreezeQueryService queryService;

    @Override
    public QueryDoDrvFreezeSOAResponseType execute(QueryDoDrvFreezeSOARequestType requestType) {
        QueryDoDrvFreezeSOAResponseType responseType = new QueryDoDrvFreezeSOAResponseType();
        Result<List<QueryDoDrvFreezeSOADTO>> result = queryService.queryDoDrvFreeze(requestType.getDrvIds(),requestType.getFreezeFrom());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType,"400",result.getMsg());
    }
}
