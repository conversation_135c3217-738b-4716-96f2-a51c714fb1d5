package com.ctrip.dcs.tms.transport.interfaces.schedule;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.PhoneNumberServiceGateway;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.NepheleHttpService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.Data;
import lombok.SneakyThrows;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;

/**
 * 运力组手机号校验任务
 * 校验运力组的igtCode+dispatcherPhone 和standbyIgtCode+standbyPhone是否合规
 * 如果不合规，收集起来，然后发送邮件通知对方
 */
@Component
public class TransportGroupPhoneValidationSchedule {

    private static final Logger logger = LoggerFactory.getLogger(TransportGroupPhoneValidationSchedule.class);
    private static final String SCENARIO = "TRANSPORT_GROUP_PHONE_VALIDATION";

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private CommonCommandService commonCommandService;

    @Autowired
    private NepheleHttpService nepheleHttpService;

    @Autowired
    PhoneNumberServiceGateway phoneNumberServiceGateway;

    /**
     * 运力组手机号校验任务
     *
     * @param parameter 参数
     * @throws Exception 异常
     */
    @QSchedule("transport.group.phone.validation.job")
    public void validateTransportGroupPhones(Parameter parameter) throws Exception {
        logger.info("validateTransportGroupPhones", "Start validating transport group phones");

        // 获取参数
        int pageSize = Optional.ofNullable(parameter.getString("pageSize")).map(Integer::parseInt).orElse(200);
        int sleepSeconds = Optional.ofNullable(parameter.getString("sleepSeconds")).map(Integer::parseInt).orElse(1);
        String emailReceivers = parameter.getString("emailReceivers");
        String emailSubject = Optional.ofNullable(parameter.getString("emailSubject")).orElse("运力组手机号校验结果");
        String transportGroupIds = parameter.getString("transportGroupIds");

        if (StringUtils.isBlank(emailReceivers)) {
            logger.warn("validateTransportGroupPhones", "Email receivers not configured. Skipping email notification.");
        }

        // 执行校验
        List<PhoneValidationResult> invalidResults = validatePhones(transportGroupIds, pageSize, sleepSeconds);

        // 发送邮件通知
        if (CollectionUtils.isNotEmpty(invalidResults) && StringUtils.isNotBlank(emailReceivers)) {
            sendEmailNotification(invalidResults, emailReceivers, emailSubject);
        }

        logger.info("validateTransportGroupPhones", "Finished validating transport group phones. Found {} invalid phones",
                invalidResults.size());
    }

    /**
     * 校验手机号
     *
     * @param transportGroupIds 运力组ID列表，如果为空则校验所有运力组
     * @param pageSize 分页大小
     * @param sleepSeconds 每批次处理后的休眠时间（秒）
     * @return 无效手机号结果列表
     * @throws InterruptedException 中断异常
     */
    @SneakyThrows
    private List<PhoneValidationResult> validatePhones(String transportGroupIds, int pageSize, int sleepSeconds)
            throws InterruptedException {
        List<PhoneValidationResult> invalidResults = new ArrayList<>();
        int totalProcessed = 0;

        // 如果指定了运力组ID，则只校验指定的运力组
        if (StringUtils.isNotBlank(transportGroupIds)) {
            List<Long> groupIds = Lists.newArrayList();
            for (String id : transportGroupIds.split(",")) {
                try {
                    groupIds.add(Long.parseLong(id.trim()));
                } catch (NumberFormatException e) {
                    logger.warn("validatePhones", "Invalid transport group ID: {}", id);
                }
            }

            if (CollectionUtils.isNotEmpty(groupIds)) {
                List<TspTransportGroupPO> groups = transportGroupRepository.queryTspTransportByIds(groupIds);
                processTransportGroups(groups, invalidResults);
                totalProcessed = groups.size();
                TaskHolder.getKeeper().setRate(totalProcessed);
            }
        } else {
            // 分页查询所有运力组
            long lastId = 0;
            while (true) {
                List<TspTransportGroupPO> groups = transportGroupRepository.queryTransportGroupByIdFromAndPage(lastId, null, null, 1, pageSize);
                if (CollectionUtils.isEmpty(groups)) {
                    break;
                }

                processTransportGroups(groups, invalidResults);
                totalProcessed += groups.size();

                // 更新进度
                TaskHolder.getKeeper().setRate(totalProcessed);

                // 获取最后一个ID，用于下一次查询
                lastId = groups.get(groups.size() - 1).getTransportGroupId();

                // 休眠一段时间，避免对数据库造成过大压力
                TimeUnit.SECONDS.sleep(sleepSeconds);
            }
        }

        // 设置总容量
        TaskHolder.getKeeper().setRateCapacity(totalProcessed);

        return invalidResults;
    }

    /**
     * 处理运力组列表，校验手机号
     *
     * @param groups 运力组列表
     * @param invalidResults 无效手机号结果列表
     */
    private void processTransportGroups(List<TspTransportGroupPO> groups, List<PhoneValidationResult> invalidResults) {
        if (CollectionUtils.isEmpty(groups)) {
            return;
        }

        for (TspTransportGroupPO group : groups) {
            // 校验调度员手机号
            if (StringUtils.isNotBlank(group.getDispatcherPhone()) && StringUtils.isNotBlank(group.getIgtCode())) {
                PhoneValidationResult result = validatePhone(group, group.getIgtCode(), group.getDispatcherPhone(), "DispatcherPhone");
                if (!result.isValid()) {
                    invalidResults.add(result);
                }
            }

            // 校验备用手机号
            if (StringUtils.isNotBlank(group.getStandbyPhone()) && StringUtils.isNotBlank(group.getStandbyIgtCode())) {
                PhoneValidationResult result = validatePhone(group, group.getStandbyIgtCode(), group.getStandbyPhone(), "StandbyPhone");
                if (!result.isValid()) {
                    invalidResults.add(result);
                }
            }
        }
    }

    /**
     * 校验单个手机号
     *
     * @param group 运力组
     * @param igtCode 国际区号
     * @param phone 手机号
     * @param phoneType 手机号类型
     * @return 校验结果
     */
    private PhoneValidationResult validatePhone(TspTransportGroupPO group, String igtCode, String phone, String phoneType) {
        PhoneValidationResult result = new PhoneValidationResult();
        result.setTransportGroupId(group.getTransportGroupId());
        result.setTransportGroupName(group.getTransportGroupName());
        result.setPhoneType(phoneType);
        result.setIgtCode(igtCode);
        result.setEncryptedPhone(phone);

        // 解密手机号
        String decryptedPhone = TmsTransUtil.decrypt(phone, KeyType.Phone);
        result.setDecryptedPhone(decryptedPhone);

        // 检查手机号是否为空
        if (StringUtils.isBlank(decryptedPhone)) {
            result.setValid(false);
            result.setErrorCode("EMPTY_PHONE");
            result.setErrorMessage("手机号为空");
            Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "TransportGroup_" + phoneType + "_Empty");
            return result;
        }

        Optional<NumberDTO> numberDTO = phoneNumberServiceGateway.splitPhoneNumber(igtCode, phone);
        result.setValid(numberDTO.isPresent() && numberDTO.get().valid);

        Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "TransportGroup_" + phoneType + "_Valid_" + result.isValid());

        // 记录日志
        Map<String, String> logTags = Maps.newHashMapWithExpectedSize(10);
        logTags.put("transportGroupId", String.valueOf(group.getTransportGroupId()));
        logTags.put("transportGroupName", StringUtils.defaultString(group.getTransportGroupName()));
        logTags.put("phoneType", phoneType);
        logTags.put("igtCode", StringUtils.defaultString(igtCode));
        logTags.put("decryptedPhone", StringUtils.defaultString(decryptedPhone));
        logTags.put("valid", String.valueOf(result.isValid()));
        if (!result.isValid()) {
            logTags.put("errorCode", StringUtils.defaultString(result.getErrorCode()));
            logTags.put("errorMessage", StringUtils.defaultString(result.getErrorMessage()));
        }
        Cat.logTags(SCENARIO, logTags, Maps.newHashMap());

        return result;
    }

    /**
     * 发送邮件通知
     *
     * @param invalidResults 无效手机号结果列表
     * @param emailReceivers 邮件接收者
     * @param emailSubject 邮件主题
     */
    private void sendEmailNotification(List<PhoneValidationResult> invalidResults, String emailReceivers, String emailSubject) {
        if (CollectionUtils.isEmpty(invalidResults)) {
            return;
        }

        try {
            // 生成Excel文件并上传到Nephele平台
            String excelUrl = generateAndUploadExcel(invalidResults);
            logger.info("sendEmailNotification", "Excel file uploaded to Nephele, URL: {}", excelUrl);
            Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "TransportGroup_Excel_Uploaded");

            // 构建邮件内容
            StringBuilder emailContent = new StringBuilder();
            emailContent.append("<html><head><style>");
            emailContent.append("table { border-collapse: collapse; width: 100%; }\n");
            emailContent.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
            emailContent.append("th { background-color: #f2f2f2; }\n");
            emailContent.append("tr:nth-child(even) { background-color: #f9f9f9; }\n");
            emailContent.append(".invalid { color: red; font-weight: bold; }\n");
            emailContent.append("</style></head><body>");
            emailContent.append("<h2>运力组手机号校验结果</h2>");
            emailContent.append("<p>以下运力组的手机号格式不符合规范，请及时修正：</p>");

            // 按类型分组统计
            Map<String, Integer> errorTypeCount = new HashMap<>();
            for (PhoneValidationResult result : invalidResults) {
                String errorType = result.getErrorCode();
                errorTypeCount.put(errorType, errorTypeCount.getOrDefault(errorType, 0) + 1);
            }

            // 显示统计信息
            emailContent.append("<h3>错误类型统计</h3>");
            emailContent.append("<table style='width:50%'>");
            emailContent.append("<tr><th>错误类型</th><th>数量</th></tr>");
            for (Map.Entry<String, Integer> entry : errorTypeCount.entrySet()) {
                emailContent.append("<tr>");
                emailContent.append("<td>").append(entry.getKey()).append("</td>");
                emailContent.append("<td>").append(entry.getValue()).append("</td>");
                emailContent.append("</tr>");
            }
            emailContent.append("</table>");
            emailContent.append("<br/>");

            // 添加Excel下载链接
            emailContent.append("<h3>详细错误列表</h3>");
            emailContent.append("<p>请点击以下链接下载详细的错误列表：</p>");
            emailContent.append("<p><a href='").append(excelUrl).append("' target='_blank'>下载Excel文件</a></p>");
            emailContent.append("<p>请尽快修正这些手机号，以确保系统正常运行。</p>");
            emailContent.append("<p><small>本邮件由系统自动发送，请勿直接回复。</small></p>");
            emailContent.append("</body></html>");

            // 发送邮件
            Result<Boolean> sendResult = commonCommandService.sendEmail(emailSubject, emailReceivers, emailContent.toString());
            if (sendResult.isSuccess()) {
                logger.info("sendEmailNotification", "Email notification sent successfully to {}", emailReceivers);
                Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "TransportGroup_Email_Sent");
            } else {
                logger.error("sendEmailNotification", "Failed to send email notification: {}", sendResult.getMsg());
                Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "TransportGroup_Email_Failed");
            }
        } catch (Exception e) {
            logger.error("sendEmailNotification", "Exception when sending email notification", e);
            Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "TransportGroup_Email_Exception");
        }
    }

    /**
     * 生成Excel文件并上传到Nephele平台
     *
     * @param invalidResults 无效手机号结果列表
     * @return Excel文件的URL
     */
    private String generateAndUploadExcel(List<PhoneValidationResult> invalidResults) throws Exception {
        // 准备Excel表头
        List<String> exportFieldList = getExportFieldList();
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet("运力组手机号校验结果").build();

        // 设置表头
        List<List<String>> headList = new ArrayList<>();
        exportFieldList.forEach(field -> {
            List<String> head = new ArrayList<>();
            head.add(getFieldDisplayName(field));
            headList.add(head);
        });
        writeSheet.setHead(headList);

        // 写入Excel数据
        try (ExcelWriter excelWriter = EasyExcelFactory.write(byteArrayOutputStream).build()) {
            List<List<String>> dataList = convertToExcelData(invalidResults, exportFieldList);
            excelWriter.write(dataList, writeSheet);
        }

        // 上传Excel文件到Nephele平台
        return uploadExcel(byteArrayOutputStream);
    }

    /**
     * 获取导出字段列表
     *
     * @return 导出字段列表
     */
    private List<String> getExportFieldList() {
        return Arrays.asList(
                "transportGroupId",
                "transportGroupName",
                "phoneType",
                "igtCode",
                "decryptedPhone",
                "errorCode",
                "errorMessage"
        );
    }

    /**
     * 获取字段显示名称
     *
     * @param fieldName 字段名称
     * @return 字段显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        Map<String, String> fieldDisplayNames = new HashMap<>();
        fieldDisplayNames.put("transportGroupId", "运力组ID");
        fieldDisplayNames.put("transportGroupName", "运力组名称");
        fieldDisplayNames.put("phoneType", "手机号类型");
        fieldDisplayNames.put("igtCode", "国际区号");
        fieldDisplayNames.put("decryptedPhone", "手机号");
        fieldDisplayNames.put("errorCode", "错误类型");
        fieldDisplayNames.put("errorMessage", "错误原因");

        return fieldDisplayNames.getOrDefault(fieldName, fieldName);
    }

    /**
     * 将无效手机号结果转换为Excel数据
     *
     * @param invalidResults 无效手机号结果列表
     * @param exportFieldList 导出字段列表
     * @return Excel数据
     */
    private List<List<String>> convertToExcelData(List<PhoneValidationResult> invalidResults, List<String> exportFieldList) {
        List<List<String>> dataList = new ArrayList<>();

        for (PhoneValidationResult result : invalidResults) {
            List<String> rowData = new ArrayList<>();
            for (String fieldName : exportFieldList) {
                switch (fieldName) {
                    case "transportGroupId":
                        rowData.add(String.valueOf(result.getTransportGroupId()));
                        break;
                    case "transportGroupName":
                        rowData.add(StringUtils.defaultString(result.getTransportGroupName()));
                        break;
                    case "phoneType":
                        rowData.add(result.getPhoneType());
                        break;
                    case "igtCode":
                        rowData.add(StringUtils.defaultString(result.getIgtCode()));
                        break;
                    case "decryptedPhone":
                        rowData.add(StringUtils.defaultString(result.getDecryptedPhone()));
                        break;
                    case "errorCode":
                        rowData.add(StringUtils.defaultString(result.getErrorCode()));
                        break;
                    case "errorMessage":
                        rowData.add(StringUtils.defaultString(result.getErrorMessage()));
                        break;
                    default:
                        rowData.add("");
                }
            }
            dataList.add(rowData);
        }

        return dataList;
    }

    /**
     * 上传Excel文件到Nephele平台
     *
     * @param byteArrayOutputStream Excel文件内容
     * @return Excel文件的URL
     */
    private String uploadExcel(ByteArrayOutputStream byteArrayOutputStream) {
        String fileName = String.format("运力组手机号校验结果_%s.xlsx",
                new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));

        String url = nepheleHttpService.upload(byteArrayOutputStream.toByteArray(), fileName);
        logger.info("uploadExcel", "Excel file uploaded to Nephele, URL: {}", url);

        return url;
    }

    /**
     * 手机号校验结果
     */
    @Data
    static class PhoneValidationResult {
        private Long transportGroupId;
        private String transportGroupName;
        private String phoneType; // "DispatcherPhone" or "StandbyPhone"
        private String igtCode;
        private String encryptedPhone;
        private String decryptedPhone;
        private boolean valid;
        private String errorCode;
        private String errorMessage;
    }
}
