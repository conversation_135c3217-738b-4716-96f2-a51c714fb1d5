package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 * @Description 生成一个随机code码,与司机ID对应，保证一对一
 * @Date 9:49 2020/8/13
 * @Param
 * @return
 **/
@Component
public class QueryDrvIdByCodeExecutor extends AbstractRpcExecutor<QueryDrvIdByCodeSOARequestType, QueryDrvIdByCodeSOAResponseType> implements Validator<QueryDrvIdByCodeSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryDrvIdByCodeExecutor.class);
    @Autowired
    private DriverQueryService driverQueryService;

    @Override
    public QueryDrvIdByCodeSOAResponseType execute(QueryDrvIdByCodeSOARequestType requestType) {
        logger.info("QueryDrvIdByCodeExecutor,params:{}",requestType.toString());
        QueryDrvIdByCodeSOAResponseType responseType = new QueryDrvIdByCodeSOAResponseType();
        Result<String> result =  driverQueryService.queryDrvIdByCode(requestType.getDrvCode());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDrvIdByCodeSOARequestType> validator) {
        validator.ruleFor("drvCode").notNull();
    }
}
