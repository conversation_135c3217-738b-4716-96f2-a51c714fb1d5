package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.RandomUtil;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.SendIVRVoiceRequestType;
import com.ctrip.model.SendIVRVoiceResponseType;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.IVR_PHONE_VERIFIED;

/**
 * 发送IVR语音验证码
 */
@Component
public class SendIVRVoiceExecutor extends AbstractRpcExecutor<SendIVRVoiceRequestType, SendIVRVoiceResponseType> implements Validator<SendIVRVoiceRequestType> {
    private static final Logger logger = LoggerFactory.getLogger(SendIVRVoiceExecutor.class);
    private static final String CHANNEL = "tms_driver_register";

    @Autowired
    private IVRCallService ivrCallService;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public SendIVRVoiceResponseType execute(SendIVRVoiceRequestType sendIVRVoiceRequestType) {
        Cat.logEvent(CatEventType.IVR_VOICE_CALL, "method_entry");

        String mobilePhone = sendIVRVoiceRequestType.getMobilePhone();
        String countryCode = sendIVRVoiceRequestType.getCountryCode();

        // 如果已经验证过
        if (ivrCallService.isPhoneVerified(new PhoneDTO(mobilePhone, countryCode))) {
            SendIVRVoiceResponseType verifiedResponse = new SendIVRVoiceResponseType();
            Long taskId = RandomUtil.randomLong(10000L, 99999L);
            verifiedResponse.setTaskId((long) taskId);
            RedisUtils.set(IVR_PHONE_VERIFIED + "-" + taskId, RedisUtils.TEN_MINIUTES, taskId);
            ServiceResponseUtils.success(verifiedResponse);
            return verifiedResponse;
        }

        // 调用IVRCallService发起IVR电话验证
        Long taskId = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, CHANNEL, sendIVRVoiceRequestType.getLanguage());

        // 检查呼叫是否成功发起
        if (taskId != null) {
            logger.info("execute", "Successfully initiated or retrieved IVR call for phone: {} with country code: {}, taskId: {}",
                    mobilePhone, countryCode, taskId);

            SendIVRVoiceResponseType responseType = new SendIVRVoiceResponseType();
            responseType.setTaskId(taskId);
            return ServiceResponseUtils.success(responseType);
        } else {
            logger.error("execute", "Failed to initiate IVR call for phone: {} with country code: {}",
                    mobilePhone, countryCode);

            SendIVRVoiceResponseType responseType = new SendIVRVoiceResponseType();
            return ServiceResponseUtils.fail(responseType, "FAILED", "Failed to initiate IVR call");
        }
    }

}
