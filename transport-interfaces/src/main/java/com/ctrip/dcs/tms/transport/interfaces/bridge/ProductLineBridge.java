package com.ctrip.dcs.tms.transport.interfaces.bridge;

import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvFreezeInfoForDspSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvFreezeInfoForDspSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleBaseSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailSOAResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverIdForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryVehicleIdForSaasResponseType;
import com.ctrip.igt.framework.soa.server.executor.ServiceExecutors;

public interface ProductLineBridge {

  DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request);

  QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request);

  QueryVehicleBaseSOAResponseType queryVehicleListBySupplierId(QueryVehicleBaseSOARequestType request);

  QueryDrvIdByTransportGroupsResponseType queryDrvIdByTransportGroups(
    QueryDrvIdByTransportGroupsRequestType request);

  VehicleDetailSOAResponseType queryVehicleDetail(VehicleDetailSOARequestType request);

   QueryDriverIdForSaasResponseType queryDriverIdForSaas(
    QueryDriverIdForSaasRequestType queryDriverIdForSaasRequestType);

   QueryVehicleIdForSaasResponseType queryVehicleIdForSaas(
    QueryVehicleIdForSaasRequestType queryVehicleIdForSaasRequestType);

   QueryDriverForSaasResponseType queryDriverForSaas(QueryDriverForSaasRequestType queryDriverForSaasRequestType);

   QueryVehicleForSaasResponseType queryVehicleForSaas(
    QueryVehicleForSaasRequestType queryVehicleForSaasRequestType);
}
