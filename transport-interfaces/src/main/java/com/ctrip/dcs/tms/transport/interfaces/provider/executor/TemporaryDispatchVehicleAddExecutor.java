package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.TemporaryDispatchVehicleAddSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.TemporaryDispatchVehicleAddSOAResponseType;
import com.ctrip.dcs.tms.transport.application.command.VehicleCommandService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
　* @description: 新增临派车辆
　* <AUTHOR>
　* @date 2023/10/9 11:12
*/
@Component
@ServiceLogTag(tagKeys = {"vehicleLicense"})
public class TemporaryDispatchVehicleAddExecutor extends AbstractRpcExecutor<TemporaryDispatchVehicleAddSOARequestType, TemporaryDispatchVehicleAddSOAResponseType> implements Validator<TemporaryDispatchVehicleAddSOARequestType> {

    @Autowired
    private VehicleCommandService vehicleCommandService;

    @Override
    public TemporaryDispatchVehicleAddSOAResponseType execute(TemporaryDispatchVehicleAddSOARequestType requestType) {
        TemporaryDispatchVehicleAddSOAResponseType responseType = new TemporaryDispatchVehicleAddSOAResponseType();
        Result<Long> result = vehicleCommandService.temporaryDispatchVehicleAdd(requestType);
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        } else {
            return ServiceResponseUtils.fail(responseType,result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<TemporaryDispatchVehicleAddSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
        validator.ruleFor("cityId").notNull();
        validator.ruleFor("vehicleLicense").notNull();
    }

}