package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.scm.merchant.interfaces.dto.*;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
public class QueryAddTransportGroupCityListExecutor extends AbstractRpcExecutor<QueryAddTransportGroupCityListSOARequestType, QueryAddTransportGroupCityListSOAResponseType> implements Validator<QueryAddTransportGroupCityListSOARequestType> {

    private static final Logger logger = LoggerFactory.getLogger(QueryAddTransportGroupCityListExecutor.class);

    @Autowired
    private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;
    @Autowired
    private EnumRepository enumRepository;

    @Override
    public QueryAddTransportGroupCityListSOAResponseType execute(QueryAddTransportGroupCityListSOARequestType requestType) {
        QueryAddTransportGroupCityListSOAResponseType responseType = new QueryAddTransportGroupCityListSOAResponseType();
        QueryContractListRequestType contractListRequestType = new QueryContractListRequestType();
        contractListRequestType.setRetrievalItems(ImmutableList.of("serviceprovider.base", "contract.servedscope"));
        ContractListQueryFilterDTO filterDTO = new ContractListQueryFilterDTO();
        filterDTO.setSupplierIds(ImmutableList.of(requestType.getSupplierId()));
        filterDTO.setContractIds(ImmutableList.of(requestType.getContractId()));
        contractListRequestType.setInclusionFilter(filterDTO);
        try {
            QueryContractListResponseType listResponseType = dcsScmMerchantServiceClientProxy.queryContractList(contractListRequestType);
            if (listResponseType == null || CollectionUtils.isEmpty(listResponseType.getContracts())) {
                logger.error("QueryAddTransportGroupCityList", "supplierId:{} nothing", requestType.getSupplierId());
                return ServiceResponseUtils.success(responseType);
            }
            ArrayList<CityInfo> cityInfoList = Lists.newArrayList();
            Set<Long> filter = Sets.newHashSet();
            for (ContractDTO contractDTO : listResponseType.getContracts()) {
                if (contractDTO != null && contractDTO.getServedScope() != null) {
                    List<ServedScopesDTO> scopesDTOList = contractDTO.getServedScope();
                    if (CollectionUtils.isNotEmpty(scopesDTOList)) {
                        for (ServedScopesDTO scopesDTO : scopesDTOList) {
                            if (CollectionUtils.isNotEmpty(scopesDTO.getCityIds())) {
                                for (Long cityId : scopesDTO.getCityIds()) {
                                    if (!filter.add(cityId)) {
                                        continue;
                                    }
                                    CityInfo cityInfo = new CityInfo();
                                    cityInfo.setCityId(cityId);
                                    cityInfo.setCityName(enumRepository.getCityName(cityId));
                                    //是否是境外
                                    Boolean isOverseasCityResult = enumRepository.isNotChinaMainLand(cityId.longValue());
                                    if (isOverseasCityResult != null) {
                                        cityInfo.setIsOverseasCity(isOverseasCityResult ? 1 : 0);
                                    } else {
                                        cityInfo.setIsOverseasCity(0);
                                    }
                                    cityInfoList.add(cityInfo);
                                }
                            }
                        }
                    }
                }
            }
            responseType.setData(cityInfoList);
            return ServiceResponseUtils.success(responseType);
        } catch (Exception e) {
            return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, e.getLocalizedMessage());
        }
    }

    @Override
    public void validate(AbstractValidator<QueryAddTransportGroupCityListSOARequestType> validator) {
        validator.ruleFor("supplierId").notNull();
    }
}
