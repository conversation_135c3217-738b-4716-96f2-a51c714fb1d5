package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.exception.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import javax.annotation.*;
import java.util.*;

/**
 * 根据车辆id或者车牌号查询车辆信息
 */
@Component
public class QueryVehicleInfoExecutor extends AbstractRpcExecutor<QueryVehicleInfoRequestType, QueryVehicleInfoResponseType>{
    private static final Logger logger = LoggerFactory.getLogger(QueryVehicleInfoExecutor.class);
    @Resource
    private IQueryVehicleInfoService queryVehicleInfoService;
    @Override
    public QueryVehicleInfoResponseType execute(QueryVehicleInfoRequestType requestType) {
        try{
            //根据车辆id查询
            if(requestType.getVehicleId() != null && requestType.getVehicleId().longValue() > 0){
                VehCacheDTO vehCacheDTO = queryVehicleInfoService.queryVehicleById(requestType.getVehicleId());
                return getResponseType(vehCacheDTO);
            }
            //根据车牌号查询
            if(!StringUtils.isEmpty(requestType.getVehicleNo())){
                VehCacheDTO vehCacheDTO = queryVehicleInfoService.queryVehicleByVehicleNo(requestType.getVehicleNo());
                return getResponseType(vehCacheDTO);
            }
            return ServiceResponseUtils.fail(new QueryVehicleInfoResponseType(),"500","param_invalid");
        }catch (BizException be){
            return ServiceResponseUtils.fail(new QueryVehicleInfoResponseType(),"500",be.getMessage());
        }catch (Exception e){
            Map<String,String> tags = new HashMap<>();
            tags.put("vehicleNo",requestType.getVehicleNo());
            tags.put("vehicleId",requestType.getVehicleId() == null?"":requestType.getVehicleId().toString());
            logger.error("QueryVehicleInfoExecutor_excep",e,tags);
            return ServiceResponseUtils.fail(new QueryVehicleInfoResponseType(),"500",e.getMessage());
        }
    }

    /**
     * 封装车辆数据
     * @param vehCacheDTO
     * @return
     */
    private QueryVehicleInfoResponseType getResponseType(VehCacheDTO vehCacheDTO){
        QueryVehicleInfoResponseType responseType = new QueryVehicleInfoResponseType();
        if(vehCacheDTO == null){
            return ServiceResponseUtils.success(responseType);
        }
        QueryVehicleInfoResult queryVehicleInfoResult = new QueryVehicleInfoResult();
        queryVehicleInfoResult.setCarId(vehCacheDTO.getCarId());
        queryVehicleInfoResult.setCarLicense(vehCacheDTO.getCarLicense());
        queryVehicleInfoResult.setCarBrandId(vehCacheDTO.getCarBrandId());
        queryVehicleInfoResult.setCarBrandName(vehCacheDTO.getCarBrandName());
        queryVehicleInfoResult.setCarColorId(vehCacheDTO.getCarColorId());
        queryVehicleInfoResult.setCarColor(vehCacheDTO.getCarColor());
        queryVehicleInfoResult.setCarTypeId(vehCacheDTO.getCarTypeId());
        queryVehicleInfoResult.setCarTypeName(vehCacheDTO.getCarTypeName());
        queryVehicleInfoResult.setMaxLuggages(vehCacheDTO.getMaxLuggages());
        queryVehicleInfoResult.setMaxPassengers(vehCacheDTO.getMaxPassengers());
        queryVehicleInfoResult.setIsEnergy(vehCacheDTO.getIsEnergy());
        queryVehicleInfoResult.setCarSeriesId(vehCacheDTO.getCarSeriesId());
        queryVehicleInfoResult.setCarSeriesName(vehCacheDTO.getCarSeriesName());
        queryVehicleInfoResult.setVehProductionLineCodeList(vehCacheDTO.getVehProductionLineCodeList());
        queryVehicleInfoResult.setVehicleStatus(vehCacheDTO.getVehicleStatus());
        queryVehicleInfoResult.setRideHailingVehCertValid(vehCacheDTO.getRideHailingVehCertValid());
        queryVehicleInfoResult.setVin(vehCacheDTO.getVin());
        queryVehicleInfoResult.setVehRegstDate(vehCacheDTO.getVehRegstDate());
        queryVehicleInfoResult.setVehCreateTime(vehCacheDTO.getVehCreateTime());
        queryVehicleInfoResult.setVehicleFullImg(vehCacheDTO.getVehicleFullImg());
        queryVehicleInfoResult.setVehicleEnergyType(vehCacheDTO.getVehicleEnergyType());
        responseType.setData(queryVehicleInfoResult);
        return ServiceResponseUtils.success(responseType);
    }
}
