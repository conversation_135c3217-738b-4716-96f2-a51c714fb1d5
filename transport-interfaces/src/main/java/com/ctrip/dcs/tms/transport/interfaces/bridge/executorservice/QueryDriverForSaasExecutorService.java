package com.ctrip.dcs.tms.transport.interfaces.bridge.executorservice;

import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasRequestType;
import com.ctrip.dcs.tms.transport.api.saas.QueryDriverForSaasResponseType;
import com.ctrip.dcs.tms.transport.api.saas.SaasDriverSoaDTO;
import com.ctrip.dcs.tms.transport.api.saas.SaasTransportGroupSoaDTO;
import com.ctrip.dcs.tms.transport.application.dto.SaasDriverDTO;
import com.ctrip.dcs.tms.transport.application.dto.SaasTransportGroupDTO;
import com.ctrip.dcs.tms.transport.application.query.IQueryDriverForSaasService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalCollectionUtils;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueryDriverForSaasExecutorService {
    @Autowired
    private IQueryDriverForSaasService queryDriverForSaasService;

    public QueryDriverForSaasResponseType execute(QueryDriverForSaasRequestType queryDriverForSaasRequestType) {
        try{
            if(queryDriverForSaasRequestType.getDriverIds().size() > 150){
                return ServiceResponseUtils.fail(new QueryDriverForSaasResponseType());
            }
            List<SaasDriverDTO> driverInfoList = queryDriverForSaasService.query(queryDriverForSaasRequestType.getDriverIds());
            return ServiceResponseUtils.success(getResponse(driverInfoList));
        }catch (Exception e){
            return ServiceResponseUtils.fail(new QueryDriverForSaasResponseType());
        }
    }

    /**
     * 封装返回结果
     * @param driverInfoList
     * @return
     */
    private QueryDriverForSaasResponseType getResponse(List<SaasDriverDTO> driverInfoList){
        if(LocalCollectionUtils.isEmpty(driverInfoList)){
            return new QueryDriverForSaasResponseType();
        }
        List<SaasDriverSoaDTO> driverList = new ArrayList<>();
        for (SaasDriverDTO driverInfo : driverInfoList) {
            driverList.add(getSaasDriverSoaDTO(driverInfo));
        }
        QueryDriverForSaasResponseType responseType = new QueryDriverForSaasResponseType();
        responseType.setDriverList(driverList);
        return responseType;
    }

    /**
     * 封装司机信息
     * @param driverInfo
     * @return
     */
    private SaasDriverSoaDTO getSaasDriverSoaDTO(SaasDriverDTO driverInfo){
        SaasDriverSoaDTO saasDriverSoaDTO = new SaasDriverSoaDTO();
        saasDriverSoaDTO.setDriverId(driverInfo.getDriverId());
        saasDriverSoaDTO.setSupplierId(driverInfo.getSupplierId());
        saasDriverSoaDTO.setDriverStatus(driverInfo.getDriverStatus());
        saasDriverSoaDTO.setDriverName(driverInfo.getDriverName());
        saasDriverSoaDTO.setCityId(driverInfo.getCityId());
        saasDriverSoaDTO.setCityName(driverInfo.getCityName());
        saasDriverSoaDTO.setDriverPhone(driverInfo.getDriverPhone());
        saasDriverSoaDTO.setDriverPhoneAreaCode(driverInfo.getDriverPhoneAreaCode());
        saasDriverSoaDTO.setDriverCategory(driverInfo.getDriverCategory());
        saasDriverSoaDTO.setVehicleId(driverInfo.getVehicleId());
        saasDriverSoaDTO.setTransportGroupList(getTransportGroup(driverInfo.getTransportGroupList()));
        saasDriverSoaDTO.setDriverLanguage(driverInfo.getDriverLanguage());
        return saasDriverSoaDTO;
    }

    /**
     * 封装运力组信息
     * @param transportGroups
     * @return
     */
    private List<SaasTransportGroupSoaDTO> getTransportGroup(List<SaasTransportGroupDTO> transportGroups){
        List<SaasTransportGroupSoaDTO> transportGroupSoaDTOS = new ArrayList<>();
        for (SaasTransportGroupDTO transportGroup : transportGroups) {
            SaasTransportGroupSoaDTO saasTransportGroupSoaDTO = new SaasTransportGroupSoaDTO();
            saasTransportGroupSoaDTO.setTransportGroupId(transportGroup.getTransportGroupId());
            saasTransportGroupSoaDTO.setTransportGroupStatus(transportGroup.getTransportGroupStatus());
            transportGroupSoaDTOS.add(saasTransportGroupSoaDTO);
        }
        return transportGroupSoaDTOS;
    }
}
