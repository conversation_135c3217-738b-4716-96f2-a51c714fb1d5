package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;


/**
 * 司机请假
 * <AUTHOR>
 * @Date 2020/2/24 17:30
 */
@Component
public class AddDrvLeaveExecutor  extends AbstractRpcExecutor<DrvLeaveAddSOARequestType, DrvLeaveAddSOAResponseType> implements Validator<DrvLeaveAddSOARequestType> {

    @Autowired
    private DriverLeaveCommandService driverLeaveCommandService;

    @Override
    public DrvLeaveAddSOAResponseType execute(DrvLeaveAddSOARequestType drvLeaveAddRequestType) {
        DrvLeaveAddSOAResponseType responseType = new DrvLeaveAddSOAResponseType();
        DrvDriverLeavePO driverLeavePO = requestTypeToPO(drvLeaveAddRequestType);
        Result<Boolean> result = driverLeaveCommandService.addDrvLeave(driverLeavePO);
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        }else {
            return ServiceResponseUtils.fail(responseType, result.getCode(),result.getMsg());
        }
    }

    @Override
    public void validate(AbstractValidator<DrvLeaveAddSOARequestType> validator) {
        validator.ruleFor("drvId").notNull();
        validator.ruleFor("leaveBeginTime").notNull().notEmpty().lessThanOrEqualToProperty("leaveEndTime");
        validator.ruleFor("leaveEndTime").notNull().notEmpty().greaterThanOrEqualToProperty("leaveBeginTime");
    }

    private DrvDriverLeavePO requestTypeToPO(DrvLeaveAddSOARequestType drvLeaveAddRequestType){
        DrvDriverLeavePO driverLeavePO = new DrvDriverLeavePO();
        driverLeavePO.setDrvId(drvLeaveAddRequestType.getDrvId());
        driverLeavePO.setLeaveBeginTime(DateUtil.string2Timestamp(drvLeaveAddRequestType.getLeaveBeginTime(),DateUtil.YYYYMMDDHHMMSS));
        driverLeavePO.setLeaveEndTime(DateUtil.string2Timestamp(drvLeaveAddRequestType.getLeaveEndTime(),DateUtil.YYYYMMDDHHMMSS));
        driverLeavePO.setLeaveReason(drvLeaveAddRequestType.getLeaveReason());
        driverLeavePO.setLeaveStatus(TmsTransportConstant.DrvLeaveStatusEnum.VALID.getCode());
        driverLeavePO.setActive(Boolean.TRUE);
        driverLeavePO.setCreateUser(drvLeaveAddRequestType.getCreateUser());
        driverLeavePO.setOperateType(drvLeaveAddRequestType.getOperateType());
        return driverLeavePO;
    }

}
