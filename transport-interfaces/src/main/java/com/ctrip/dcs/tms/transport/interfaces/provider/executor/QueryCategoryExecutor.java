package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.QueryCategorySOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryCategorySOAResponseType;
import com.ctrip.dcs.tms.transport.application.query.QueryCategoryService;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;

/**
 * <AUTHOR>
 */
@Component
public class QueryCategoryExecutor extends AbstractRpcExecutor<QueryCategorySOARequestType, QueryCategorySOAResponseType> implements Validator<QueryCategorySOARequestType> {

    @Autowired
    QueryCategoryService queryCategoryService;

    @Override
    public QueryCategorySOAResponseType execute(QueryCategorySOARequestType queryCategorySOARequestType) {
        QueryCategorySOAResponseType soaResponseType = new QueryCategorySOAResponseType();
        try {
            soaResponseType.setData(queryCategoryService.getContractList(queryCategorySOARequestType.getSupplierId(), queryCategorySOARequestType.getCityIdList()));
            return ServiceResponseUtils.success(soaResponseType);
        } catch (Exception e) {
            return ServiceResponseUtils.fail(soaResponseType);
        }
    }


    @Override
    public void validate(AbstractValidator<QueryCategorySOARequestType> validator) {

    }
}
