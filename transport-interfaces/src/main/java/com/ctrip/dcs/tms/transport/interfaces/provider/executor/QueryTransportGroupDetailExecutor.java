package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.ServiceProviderRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 运力组查询
 * <AUTHOR>
 * @Date 2020/3/3 18:14
 */
@Component
public class QueryTransportGroupDetailExecutor extends AbstractRpcExecutor<QueryTransportGroupDetailSOARequestType, QueryTransportGroupDetailSOAResponseType> implements Validator<QueryTransportGroupDetailSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Autowired
    private InOrderConfigQueryService inOrderConfigQueryService;

    @Autowired
    private WorkShiftQueryService workShiftQueryService;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired /**品牌*/
    private ServiceProviderRepository serviceProviderRepository;

    @Override
    public QueryTransportGroupDetailSOAResponseType execute(QueryTransportGroupDetailSOARequestType queryTransportGroupDetailSOARequestType) {
        QueryTransportGroupDetailSOAResponseType soaResponseType = new QueryTransportGroupDetailSOAResponseType();
        Result<TspTransportGroupPO> groupPOResult = transportGroupQueryService.queryTransportGroupDetail(queryTransportGroupDetailSOARequestType.getTransportGroupId());
        Result<List<TspIntoOrderConfigPO>> inOrderConfigListResult = inOrderConfigQueryService.queryInOrderConfigs(queryTransportGroupDetailSOARequestType.getTransportGroupId(), TmsTransportConstant.IntoOrderConfigActiveEnum.VALID.getCode());
        Result<List<TspTransportGroupWorkShiftPO>> workShiftListResult = workShiftQueryService.queryWorkShifts(queryTransportGroupDetailSOARequestType.getTransportGroupId(), TmsTransportConstant.WorkShiftActiveEnum.VALID.getCode());
        TransportGroupBaseInfoSOAType soaType = poToSOAType(groupPOResult.getData(), inOrderConfigListResult.getData(),workShiftListResult.getData());
        soaResponseType.setData(soaType);
        return  ServiceResponseUtils.success(soaResponseType);
    }

    @Override
    public void validate(AbstractValidator<QueryTransportGroupDetailSOARequestType> validator) {
        validator.ruleFor("transportGroupId").notNull();
    }

    /**
     * 运力组SOAType
     * @param transportGroupPO
     * @param intoOrderConfigPOS
     * @return
     */
    private TransportGroupBaseInfoSOAType poToSOAType(TspTransportGroupPO transportGroupPO, List<TspIntoOrderConfigPO> intoOrderConfigPOS,List<TspTransportGroupWorkShiftPO> workShiftPOS){
        if (transportGroupPO == null) {
            return null;
        }
        TransportGroupBaseInfoSOAType soaType = new TransportGroupBaseInfoSOAType();
        soaType.setTransportGroupId(transportGroupPO.getTransportGroupId());
        soaType.setSupplierId(transportGroupPO.getSupplierId());
        String supplierName = enumRepository.getSupplierName(transportGroupPO.getSupplierId());
        soaType.setSupplierName(supplierName);
        soaType.setTransportGroupName(transportGroupPO.getTransportGroupName());
        soaType.setTransportGroupMode(transportGroupPO.getTransportGroupMode());
        soaType.setDispatcher(transportGroupPO.getDispatcher());
        soaType.setDispatcherPhone(TmsTransUtil.decrypt(transportGroupPO.getDispatcherPhone(), KeyType.Phone));
        soaType.setDispatcherLanguage(transportGroupPO.getDispatcherLanguage());
        soaType.setTakeOrderLimitTime(transportGroupPO.getTakeOrderLimitTime());
        soaType.setVehicleTypeId(transportGroupPO.getVehicleTypeId());
        soaType.setWorkShift(transportGroupPO.getWorkShift());
        soaType.setInOrderConfigs(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(workShiftPOS)) {
            List<WorkShiftDetailSOAType> list = Lists.newArrayList();
            workShiftPOS.forEach(workShiftPO -> {
                WorkShiftDetailSOAType workShiftDetailSOAType = new WorkShiftDetailSOAType();
                workShiftDetailSOAType.setId(workShiftPO.getId());
                workShiftDetailSOAType.setName(workShiftPO.getName());
                workShiftDetailSOAType.setStartTime(workShiftPO.getStarTime());
                workShiftDetailSOAType.setEndTime(workShiftPO.getEndTime());
                workShiftDetailSOAType.setDriverUpperLimit(workShiftPO.getDriverUpperLimit());
                workShiftDetailSOAType.setTransportGroupId(workShiftPO.getTransportGroupId());
                workShiftDetailSOAType.setOrder(workShiftPO.getOrder());
                list.add(workShiftDetailSOAType);
            });
            soaType.setWorkShiftDetails(list);
        }
        if (CollectionUtils.isNotEmpty(intoOrderConfigPOS)) {
            List<InOrderConfigSOAType> list = Lists.newArrayList();
            intoOrderConfigPOS.forEach(intoOrderConfigPO -> {
                InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
                inOrderConfigSOAType.setId(intoOrderConfigPO.getId());
                inOrderConfigSOAType.setCountryId(intoOrderConfigPO.getCountryId());
                inOrderConfigSOAType.setCountryName(intoOrderConfigPO.getCountryName());
                inOrderConfigSOAType.setCityId(intoOrderConfigPO.getCityId());
                inOrderConfigSOAType.setLocationCode(intoOrderConfigPO.getLocationCode());
                inOrderConfigSOAType.setLocationType(intoOrderConfigPO.getLocationType());
                inOrderConfigSOAType.setLocationName("");
                FixedLocation fixedLocation = enumRepository.getFixedLocation(intoOrderConfigPO.getLocationType(),intoOrderConfigPO.getLocationCode());
                if(fixedLocation != null){
                    inOrderConfigSOAType.setLocationName(fixedLocation.getLocation().getLocationName());
                }
                inOrderConfigSOAType.setTransportGroupId(intoOrderConfigPO.getTransportGroupId());
                inOrderConfigSOAType.setActive(intoOrderConfigPO.getActive());
                inOrderConfigSOAType.setCreateUser(intoOrderConfigPO.getCreateUser());
                inOrderConfigSOAType.setModifyUser(intoOrderConfigPO.getModifyUser());
                inOrderConfigSOAType.setDatachangeCreatetime(DateUtil.timestampToString(intoOrderConfigPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
                inOrderConfigSOAType.setDatachangeLasttime(DateUtil.timestampToString(intoOrderConfigPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
                if (!Strings.isNullOrEmpty(intoOrderConfigPO.getConfig())) {
                    inOrderConfigSOAType.setConfigItems(JsonUtil.fromJson(intoOrderConfigPO.getConfig(),new TypeReference<List<ConfigItemSOAType>>(){}));
                }
                list.add(inOrderConfigSOAType);
            });
            soaType.setInOrderConfigs(list);
        }
        soaType.setCreateUser(transportGroupPO.getCreateUser());
        soaType.setGroupStatus(transportGroupPO.getGroupStatus());
        soaType.setGroupStatusName(enumRepository.getTransportGroupStatusMap().get(transportGroupPO.getGroupStatus()));
        soaType.setModifyUser(transportGroupPO.getModifyUser());
        soaType.setDatachangeCreatetime(DateUtil.timestampToString(transportGroupPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
        soaType.setDatachangeLasttime(DateUtil.timestampToString(transportGroupPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
        soaType.setIgtCode(transportGroupPO.getIgtCode());
        soaType.setAreaGroupId(transportGroupPO.getAreaGroupId());
        soaType.setTransportAreaType(transportGroupPO.getAreaGroupType());
        soaType.setPointCityIdList(ImmutableList.of(transportGroupPO.getPointCityId()));
        soaType.setContractId(transportGroupPO.getContractId());
        Contract contract = contractRepository.findOne(transportGroupPO.getContractId());
        if (contract != null) {
            soaType.setSalesModel(contract.getSalesMode());
            if (soaType.getSalesModel() != null) {
                soaType.setSalesModelName(SharkUtil.getSalesModeName(soaType.getSalesModel()));
            }
            ServiceProvider serviceProvider = serviceProviderRepository.findOne(contract.getServiceProviderId());
            if (serviceProvider != null) {
                soaType.setContractName(serviceProvider.getBrandLocalName());
            }
        }
        soaType.setInformPhone(transportGroupPO.getInformPhone());
        soaType.setInformEmail(transportGroupPO.getInformEmail());
        soaType.setInformSwitch(transportGroupPO.getInformSwitch());
        Boolean isOverseasCityResult = enumRepository.isNotChinaMainLand(transportGroupPO.getPointCityId());
        if (isOverseasCityResult != null) {
            soaType.setIsOverseasCity(isOverseasCityResult ? 1 : 0);
        } else {
            soaType.setIsOverseasCity(0);
        }
        List<Integer> lineList = productionLineUtil.getShowProductionLineList(transportGroupPO.getCategorySynthesizeCode());
        if (CollectionUtils.isNotEmpty(lineList)) {
            soaType.setProLineId(lineList.get(0));
            soaType.setProLineName(productionLineUtil.getProductionLineNames(transportGroupPO.getCategorySynthesizeCode()));
        }
        //新增备用区号和备用电话
        soaType.setStandbyIgtCode(transportGroupPO.getStandbyIgtCode());
        soaType.setStandbyPhone(TmsTransUtil.decrypt(transportGroupPO.getStandbyPhone(), KeyType.Phone));
        soaType.setShortTransportGroup(transportGroupPO.getShortTransportGroup());
        return soaType;
    }


}
