package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.frt.common.util.Json;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;

/**
　* @description: 更新司机信息通用job by 司机ID， 任务
　* <AUTHOR>
　* @date 2024/11/29
*/
@Component
public class UpdateDrvInfoByIdSchedule {

    private static final Logger logger = LoggerFactory.getLogger(UpdateDrvInfoByIdSchedule.class);

    @Autowired
    DrvDrvierRepository repository;

    /**
     * 更新司机信息通用job by 司机ID， 任务
     */
    @QSchedule("update.drv.info.by.drvId")
    public void updateDrvInfo(Parameter parameter) {
        Transaction transaction  = Cat.newTransaction("driver", "update.drv.info.by.drvId");
        String drvs = parameter == null ? StringUtils.EMPTY : parameter.getString("drvInfos");
        if (StringUtils.isEmpty(drvs)) {
            return;
        }
        logger.info("update.drv.info.by.drvId", "drvParams:{}", drvs);
        List<DrvDriverPO> driverPoList = JsonUtil.fromJson(drvs, new TypeReference<List<DrvDriverPO>>() { });
        if(CollectionUtils.isEmpty(driverPoList)) {
            return;
        }

        try {
            for (DrvDriverPO drv : driverPoList) {
                repository.updateDrv(drv);
            }
        } catch (Exception e) {
            logger.error("update.drv.info.by.drvId error:", e);
        }finally {
            transaction.complete();
        }
    }
}
