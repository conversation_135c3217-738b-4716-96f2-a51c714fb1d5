package com.ctrip.dcs.tms.transport.interfaces.schedule;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qschedule.config.*;
import qunar.tc.schedule.*;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Description 证件有效期前提醒及到期时处理
 * 驾驶证 :当前日期-注册日期<1month 的提示
 * 过期前1个月提醒；
 * 【运营冻结】如已过期，冻结3个月，冻结期间订单处理，自动下线。（冻结原因：驾驶证/行驶证过期）
 * @Date 10:30 2021/3/12
 **/
@Component
public class DrvLicenseExpiryDateRemindSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DrvLicenseExpiryDateRemindSchedule.class);

    private static final String SMS_DATEEND_REMIND_KEY = "sms_dateend_remind_key_";

    @Autowired
    private DrvDrvierRepository drvierRepository;
    @Autowired
    TmsDrvFreezeCommandService service;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    TmsCertificateCheckRepository checkRepository;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Autowired
    CommonCommandService commandService;
    @Autowired
    EmailTemplateQconfig emailTemplateQconfig;
    @Autowired
    ChangeRecordAttributeNameQconfig attributeNameQconfig;

    @QSchedule("drv.license.expirydateend.remind.job")
    public void drvLicenseExpiryDateRemindSchedule(Parameter parameter) {
        String drvIds = parameter.getString("drvIds");
        this.drvLicenseExpiryDateRemindScheduleMethod(drvIds);
        logger.info("drvLicenseExpiryDateRemindSchedule end ..","drvIds:{}",drvIds);
    }

    public void drvLicenseExpiryDateRemindScheduleMethod(String drvIds) {
        logger.info("drvLicenseExpiryDateRemindSchedule start ..","drvIds:{}",drvIds);
        List<Long> paramsDrvList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(drvIds)) {
            paramsDrvList = Arrays.stream(drvIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
        }
        List<Integer> drvStatusList = Arrays.asList(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode());
        int count = drvierRepository.countDrvByOnlineAndfreeze(drvStatusList, paramsDrvList);
        logger.info("drvLicenseExpiryDateRemindScheduleCount","countparams:{}", count);
        if (count == 0) {
            return;
        }
        int pageSize = 100;
        count = StringUtil.getPageCount(count, pageSize);
        for (int i = 1; i <= count; i++) {
            try {
                int beginSize = i;
                int count1 = drvierRepository.countDrvByOnlineAndfreeze(drvStatusList, paramsDrvList);
                if(count1 < pageSize){
                    beginSize = 1;
                }
                List<DrvDriverPO> drvList = drvierRepository.queryDrvByOnlineAndfreeze(paramsDrvList, drvStatusList, beginSize, pageSize);
                if (CollectionUtils.isEmpty(drvList)) {
                    continue;
                }
                List<Long> drvIdList = drvList.stream().map(DrvDriverPO::getDrvId).collect(Collectors.toList());
                List<TmsCertificateCheckPO> checkPOList = checkRepository.queryIdCardCheckByCheckIng(drvIdList, TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(),
                        Arrays.asList(TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode()), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode(),1,1000000);
                Map<Long,List<TmsCertificateCheckPO>> checkMap = checkPOList.stream().collect(Collectors.groupingBy(TmsCertificateCheckPO::getCheckId));
                for (DrvDriverPO scmDriver : drvList) {
                    Date expiryEndDate = scmDriver.getExpiryEndDate();
                    //先取第三方返回证件有效期,如果第三方无数据，则默认指定OCR识别有效期
                    List<TmsCertificateCheckPO> checkPOList1 = checkMap.get(scmDriver.getDrvId());
                    if(CollectionUtils.isNotEmpty(checkPOList1)){
                        TmsCertificateCheckPO tmsCertificateCheckPO = checkPOList1.get(0);
                        if(StringUtils.isNotEmpty(tmsCertificateCheckPO.getCheckContent())){
                            DrvLicenseCheckContentDTO resultDTO =  JsonUtil.fromJson(tmsCertificateCheckPO.getCheckContent(), new TypeReference<DrvLicenseCheckContentDTO>() {
                            });
                            if (!Objects.isNull(resultDTO) && resultDTO.getData() != null && resultDTO.getData().getData() != null && !StringUtils.isEmpty(resultDTO.getData().getData().getTermoFvalidity())) {
                                expiryEndDate = DateUtil.stringToDate(resultDTO.getData().getData().getTermoFvalidity(),DateUtil.YYYYMMDD);
                            }
                        }
                    }
                    if (expiryEndDate == null) {
                        continue;
                    }
                    String expiryDate =  DateUtil.dateToString(expiryEndDate,DateUtil.YYYYMMDD);
                    //去除1971-01-02表中默认值
                    if(StringUtils.isEmpty(expiryDate) || Objects.equals(expiryDate,"1971-01-02")){
                        continue;
                    }
                    //判断生效结束日期大于一个月,不做任务处理
                    long day = DateUtil.getDatePoor(expiryEndDate, new Date());
                    if (day > qconfig.getDrvLicenseExpiryDateRemind()) {
                        continue;
                    }
                    //如果证件有效结束时间和当前时间相差30天，发短信提醒
                    String key = SMS_DATEEND_REMIND_KEY+scmDriver.getDrvId();
                    Boolean flag = RedisUtils.get(key);
                    String supplierEmail = enumRepository.getSupplierEmail(scmDriver.getSupplierId());
                    String subject = String.format(emailTemplateQconfig.getDrvLicenseExpiryDateRemindSubject(),scmDriver.getDrvName(),scmDriver.getDrvId());
                    if ( (day >1 && day <= qconfig.getDrvLicenseExpiryDateRemind()) && (flag == null || !flag)) {
                         //  发送短信->司机
                        Map<String,String> params = Maps.newHashMap();
                        params.put("DriverName",scmDriver.getDrvName());
                        params.put("ExpiryDate",expiryDate);
                        commandService.sendMessageByPhone(scmDriver.getIgtCode(), TmsTransUtil.decrypt(scmDriver.getDrvPhone(), KeyType.Phone),approvalProcessAuthQconfig.getDrvLicenseExpiryDateCode(),params);
                        //  发邮件->供应商
                        if(StringUtils.isNotEmpty(supplierEmail)){
                            String content = String.format(emailTemplateQconfig.getDrvLicenseExpiryDateRemindContentToVBK(),scmDriver.getDrvName(),expiryDate);
                            sendMessage(subject,supplierEmail,content);
                        }
                        //  发邮件->BD
                        String content = String.format(emailTemplateQconfig.getDrvLicenseExpiryDateRemindContentToBD(),scmDriver.getDrvId(),scmDriver.getDrvName(),scmDriver.getCityId(),scmDriver.getSupplierId(),expiryDate);
                        sendMessage(subject,emailTemplateQconfig.getEmailAddressTOBD(),content);
                        RedisUtils.set(key,RedisUtils.ONE_MONTH,Boolean.TRUE);
                    }
                    //如已过期，冻结3个月，冻结期间订单处理，自动下线。（冻结原因：驾驶证/行驶证过期）
                    if(day <= 0){
                         subject = String.format(emailTemplateQconfig.getDrvLicenseExpiryDateFreezeSubject(),scmDriver.getDrvName(),scmDriver.getDrvId());
                        freezeDrvAndSendEmail(scmDriver.getDrvId());
                        //  发邮件->供应商
                        String content = String.format(emailTemplateQconfig.getDrvLicenseExpiredContentToVBK(),scmDriver.getDrvName(),expiryDate);
                        if(StringUtils.isNotEmpty(supplierEmail)){
                            sendMessage(subject,supplierEmail,content);
                        }
                        //  发邮件->BD
                         content = String.format(emailTemplateQconfig.getDrvLicenseExpiredContentToBD(),scmDriver.getDrvId(),scmDriver.getDrvName(),scmDriver.getCityId(),scmDriver.getSupplierId(),expiryDate);
                        sendMessage(subject,emailTemplateQconfig.getEmailAddressTOBD(),content);
                    }
                }
            } catch (Exception e) {
                logger.info("refreshAllDrvCoopModeScheduleMethod error", "error:{}", i);
            }
        }
    }

    public void freezeDrvAndSendEmail(Long drvId){
        TmsDrvFreezeAddSOARequestType soaRequestType = new TmsDrvFreezeAddSOARequestType();
        soaRequestType.setDrvId(drvId);
        soaRequestType.setTotalHours(2000);
        soaRequestType.setDrvStatus(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode());
        soaRequestType.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        soaRequestType.setCreateUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
        soaRequestType.setFreezeFrom(TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue());
        soaRequestType.setFreezeOrderSet(TmsTransportConstant.FreezeOrderSetEnum.FREEZEORDERCHG.getValue());
        soaRequestType.setFreezeReason(attributeNameQconfig.getFreezeReason());
        soaRequestType.setUnfreezeAction(TmsTransportConstant.UnfreezeActionEnum.UNFREEZEOFFLINE.getValue());
        service.addDrvFreeze(soaRequestType, false);
    }

    //发送邮件
    public void sendMessage(String subject,String emailAddress,String content){
        commandService.sendEmail(subject,emailAddress,content);
    }
}