package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctrip.igt.framework.infrastructure.validator.*;
import com.ctrip.igt.framework.soa.server.executor.*;
import com.ctrip.igt.framework.soa.server.util.*;
import com.ctriposs.baiji.rpc.server.validation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

@Component
public class QueryTransportGroupListByIdListExecutor extends AbstractRpcExecutor<QueryTransportGroupListByIdListSOARequestType, QueryTransportGroupListByIdListSOAResponseType> implements Validator<QueryTransportGroupListByIdListSOARequestType> {

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Override
    public void validate(AbstractValidator<QueryTransportGroupListByIdListSOARequestType> validator, QueryTransportGroupListByIdListSOARequestType req) {
        validator.ruleFor("transportGroupIdList").notNull();
    }

    @Override
    public QueryTransportGroupListByIdListSOAResponseType execute(QueryTransportGroupListByIdListSOARequestType queryTransportGroupListByIdListSOARequestType) {
        QueryTransportGroupListByIdListSOAResponseType responseType = new QueryTransportGroupListByIdListSOAResponseType();
        Result<List<TransportGroupListSOAType>> result = transportGroupQueryService.queryTransportGroupListByIdList(queryTransportGroupListByIdListSOARequestType.getTransportGroupIdList());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        } else {
            return ServiceResponseUtils.fail(responseType, ServiceResponseConstants.ResStatus.EXCEPTION_CODE, result.getMsg());
        }
    }
}
