package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverSafetyInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverSafetyInfoSOAResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;

/**
 * <AUTHOR>
 */
@Component
public class QueryDriverSafetyInfoExecutor extends AbstractRpcExecutor<QueryDriverSafetyInfoSOARequestType, QueryDriverSafetyInfoSOAResponseType> implements Validator<QueryDriverSafetyInfoSOARequestType> {
    @Override
    public QueryDriverSafetyInfoSOAResponseType execute(QueryDriverSafetyInfoSOARequestType queryDriverSafetyInfoSOARequestType) {
        QueryDriverSafetyInfoSOAResponseType responseType = new QueryDriverSafetyInfoSOAResponseType();
        return ServiceResponseUtils.success(responseType);
    }
    @Override
    public void validate(AbstractValidator<QueryDriverSafetyInfoSOARequestType> validator) {
        validator.ruleFor("driverId").notNull();
    }
}