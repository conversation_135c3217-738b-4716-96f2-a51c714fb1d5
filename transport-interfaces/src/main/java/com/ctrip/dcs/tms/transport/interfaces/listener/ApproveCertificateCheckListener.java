package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.collect.Maps;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.consumer.annotation.*;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * @Description  是入审批流需异步核验证件
 * @Date 15:32 2020/11/18
 * @Param
 * @return
 **/
@Component
public class ApproveCertificateCheckListener {
    private static final Logger logger = LoggerFactory.getLogger(ApproveCertificateCheckListener.class);

    @Autowired
    CertificateCheckQueryService checkQueryService;
    @Autowired
    TmsTransportApproveRepository approveRepository;
    @Autowired
    TmsBackgroundChecksRepository tmsBackgroundChecksRepository;
    @Autowired
    TmsCertificateCheckRepository checkRepository;
    @Autowired
    DrvRecruitingRepository drvRecruitingRepository;
    @Autowired
    private RecruitingCommandService recruitingCommandService;
    @Autowired
    TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    DriverQueryService driverQueryService;
    @Autowired
    TmsModRecordCommandService recordCommandService;
    @Autowired
    DrvDrvierRepository drvDrvierRepository;
    @Autowired
    VehicleRepository vehicleRepository;
    @Autowired
    DriverCommandService driverCommandService;
    @Autowired
    VehicleCommandService vehicleCommandService;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    CommonCommandService commandService;
    @Autowired
    OverseasQconfig overseasQconfig;

    /**
     * 司机/车辆审批qmq
     * @param message
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_APPROVECHECK_CHANGE, consumerGroup = TmsTransportConstant.QmqTag.TAG_APPROVECHECK)
    public void approveCertificateCheckListener(Message message){
        try{
            Long approveId = message.getLongProperty("approveId");
            Integer approveType = message.getIntProperty("approveType");
            String orgDrvInfo = message.getStringProperty("orgDrvInfo");
            String newDrvInfo = message.getStringProperty("newDrvInfo");
            String orgVehicleInfo = message.getStringProperty("orgVehicleInfo");
            String newVehicleInfo = message.getStringProperty("newVehicleInfo");
            logger.info("approveCertificateCheckListener start---,approveId:{},approveType:{}",approveId,approveType);
            if(approveId == null || approveId <=0 || approveType == null){
                return;
            }
            //司机审批
            if(Objects.equals(approveType, TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode())){
                checkDrv(approveId,orgDrvInfo,newDrvInfo);
            }
            //车辆审批
            if(Objects.equals(approveType, TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode())){
                checkVehicle(approveId,orgVehicleInfo,newVehicleInfo);
            }
        }catch (Exception e){
            logger.error("approveCertificateCheckListener error:{}",e.getLocalizedMessage());
        }
    }

    /**
     *  审批流中身份证核验结果
     * @param message
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_IDCARDAPPROVE_RESULT, consumerGroup = TmsTransportConstant.QmqTag.TAG_IDCARD_APPROVE)
    public void idcardCheckResultListener(Message message){
        try{
            Long sourceId = message.getLongProperty("sourceId");
            Integer sourceType = message.getIntProperty("sourceType");
            logger.info("idcardCheckResultListener start---,sourceId:{}",sourceId);
            if(sourceId == null || sourceId <= 0 || sourceType == null){
                return;
            }
            if(sourceType == 1){//司机入注
                driverInjection(sourceId);
            }
            if(sourceType == 2){//编辑后审核
                this.updateApproveMethod(sourceId);
            }

        }catch (Exception e){
            logger.error("idcardCheckResultListener error:{}",e.getLocalizedMessage());
        }
    }

    /**
    　* @description: 编辑后审批通过，临时派遣司机转正
    　* <AUTHOR>
    　* @date 2023/11/2 16:43
    */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_APPROVE_TO_OFFICIAL, consumerGroup = TmsTransportConstant.QmqTag.TAG_APPROVE_TO_OFFICIAL)
    public void approveTemporaryToOfficialListener(Message message){
        try{
            Long sourceId = message.getLongProperty("sourceId");
            Integer sourceType = message.getIntProperty("sourceType");
            String modifyUser = message.getStringProperty("modifyUser");
            logger.info("approveTemporaryToOfficialListenerStart","sourceId:{},sourceType:{}",sourceId,sourceType);
            if(sourceId == null || sourceId <= 0 || sourceType == null){
                return;
            }
            if (message.times() > 3) {
                logger.error("approveTemporaryToOfficialListener", "sourceId:{},sourceType:{}", sourceId, sourceType);
                return;
            }
            //localRetries是连续本地重试了多少次的意思(防止死循环)
            if (message.localRetries() > 5) {
                //抛出远程重试异常  NeedRetryException指定下次重试的时间点
                throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "1min retry");
            }

            //审核通过后，当前司机或车辆ID 如果还有对应的待审批事件，则不处理临派转正式
            List<TmsTransportApprovePO> approvePOList = approveRepository.queryApproveBySourceId(sourceId, sourceType, TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode(), TmsTransportConstant.EnentTypeEnum.TEMPORARYDISPATCH.getCode());
            if(CollectionUtils.isNotEmpty(approvePOList)){
                return;
            }
            switch (sourceType){
                case 1:drvApproveTemporaryToOfficial(sourceId,modifyUser);
                break;
                case 2:vehApproveTemporaryToOfficial(sourceId,modifyUser);
            }
        }catch (NeedRetryException e){
            logger.error("approveTemporaryToOfficialListenerError:{}",e.getLocalizedMessage());
            throw e;
        }
    }

    /**
    　* @description: 临时派遣司机补充信息提醒
    　* <AUTHOR>
    　* @date 2023/11/2 16:43
    */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_TEMPORARY_REPLENISH_INFO, consumerGroup = TmsTransportConstant.QmqTag.TAG_TEMPORARY_REPLENISH_INFO)
    public void temporaryReplenishInfoListener(Message message){
        try{
            Long replenishId = message.getLongProperty("replenishId");
            Integer type = message.getIntProperty("type");
            Long vehicleId = message.getLongProperty("vehicleId");
            logger.info("temporaryReplenishInfoListenerStart","replenishId:{},type:{}",replenishId,type);
            if(replenishId == null || replenishId <= 0 || type == null){
                return;
            }
            if (message.times() > 3) {
                logger.error("temporaryReplenishInfoListener", "replenishId:{},type:{}", replenishId, type);
                return;
            }
            //localRetries是连续本地重试了多少次的意思(防止死循环)
            if (message.localRetries() > 5) {
                //抛出远程重试异常  NeedRetryException指定下次重试的时间点
                throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "1min retry");
            }

            switch (type){
                case 1:drvEmailInfo(replenishId);//司机
                    break;
                case 2:vehEmailInfo(replenishId);//车辆
                    break;
                case 3:drvAndVehEmailInfo(replenishId,vehicleId);//司机和车辆
            }
        }catch (NeedRetryException e){
            logger.error("temporaryReplenishInfoListenerError:{}",e.getLocalizedMessage());
            throw e;
        }
    }

    public void driverInjection(Long sourceId){
        try {
            TmsCertificateCheckPO tmsCertificateCheckPO = checkRepository.queryByPK(sourceId);
            if (tmsCertificateCheckPO == null || !Objects.equals(tmsCertificateCheckPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())||
                    !Objects.equals(tmsCertificateCheckPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode())) {
                return;
            }
            DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(tmsCertificateCheckPO.getCheckId());
            if(Objects.isNull(drvRecruitingPO)){
                return;
            }
            String keyWord = tmsCertificateCheckPO.getCheckKeyword();
            List<TmsBackgroundChecksPO> checksPOList = tmsBackgroundChecksRepository.queryBackgroundByPerson(keyWord);
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
            if(CollectionUtils.isNotEmpty(checksPOList)){
                checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            }
            checkRepository.updateIdcardCheckStatus(sourceId,checkStatus,drvRecruitingPO.getVersionFlag() >= 3,drvRecruitingPO.getModifyUser());
            //招募核验状态qmq
            tmsQmqProducerCommandService.sendRecruitingCheckStatusQMQ(drvRecruitingPO.getDrvRecruitingId(),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),drvRecruitingPO.getModifyUser());
            //添加三方标签状态记录
            recordCommandService.insertThirdCertificateRrd(drvRecruitingPO.getDrvRecruitingId(),TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),drvRecruitingPO.getDrvRecruitingId(), TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode(),0,checkStatus,TmsTransportConstant.TMS_DEFAULT_USERNAME);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    /**
     * 编辑后审核身份证结果
     * @param sourceId
     */
    public void updateApproveMethod(Long sourceId){
        try{
            TmsTransportApprovePO approvePO = approveRepository.queryByPk(sourceId);
            if(Objects.isNull(approvePO) || StringUtils.isEmpty(approvePO.getCertificateCheckResult())){
                return;
            }
            List<TmsCertificateCheckPO> checkResultList = JsonUtil.fromJson(approvePO.getCertificateCheckResult(), new TypeReference<List<TmsCertificateCheckPO>>() {
            });
            List<TmsCertificateCheckPO> idcardCheck = checkResultList.stream().filter(soadto -> Objects.equals(soadto.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(idcardCheck)){
                return;
            }
            TmsCertificateCheckPO soadto = idcardCheck.get(0);
            if(Objects.isNull(soadto)){
                return;
            }
            String keyWord = soadto.getCheckKeyword();
            List<TmsBackgroundChecksPO> checksPOList = tmsBackgroundChecksRepository.queryBackgroundByPerson(keyWord);
            Integer checkStatus = TmsTransportConstant.CheckStatusEnum.THROUGH.getCode();
            if(CollectionUtils.isNotEmpty(checksPOList)){
                checkStatus = TmsTransportConstant.CheckStatusEnum.ERROR.getCode();
            }
            for(TmsCertificateCheckPO resultSOADTO : checkResultList){
                if(Objects.equals(resultSOADTO.getCertificateType(),TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode())){
                    resultSOADTO.setCheckStatus(checkStatus);
                }
            }
            approveRepository.updateCheckResult(sourceId,JsonUtil.toJson(checkResultList),approvePO.getModifyUser());
        }catch (Exception e){
            logger.error("idcardCheckResultListener error:{}",e.getLocalizedMessage());
        }
    }

    private void  checkDrv(Long approveId,String orgDrvInfo,String newDrvInfo){
        DrvDriverPO orgDriver = JsonUtil.fromJson(orgDrvInfo, new TypeReference<DrvDriverPO>() {});
        DrvDriverPO newDriver = JsonUtil.fromJson(newDrvInfo, new TypeReference<DrvDriverPO>() {});
        //驾驶证为空或者是境外司机 不核验
        if(orgDriver == null || newDriver == null || Objects.equals(newDriver.getInternalScope(), AreaScopeTypeEnum.OVERSEAS.getCode())){
            return;
        }
        List<TmsCertificateCheckPO>  checkPOList = checkQueryService.approveDrvCertificateCheck(DrvAuditDTO.approveDrvDTO(orgDriver,newDriver,approveId),Boolean.FALSE);
        if(CollectionUtils.isEmpty(checkPOList)){
            return;
        }
        approveRepository.updateCheckResult(approveId,JsonUtil.toJson(checkPOList),newDriver.getModifyUser());
    }

    private void  checkVehicle(Long approveId,String orgVehicleInfo,String newVehicleInfo){
        VehVehiclePO orgVehicle = JsonUtil.fromJson(orgVehicleInfo, new TypeReference<VehVehiclePO>() {});
        VehVehiclePO newVehicle = JsonUtil.fromJson(newVehicleInfo, new TypeReference<VehVehiclePO>() {});
        if(orgVehicle == null || newVehicle == null){
            return;
        }
        List<TmsCertificateCheckPO>  checkPOList = checkQueryService.approveVehicleCertificateCheck(VehicleAuditDTO.approveVehicleDTO(orgVehicle,newVehicle),Boolean.FALSE);
        if(CollectionUtils.isEmpty(checkPOList)){
            return;
        }
        approveRepository.updateCheckResult(approveId,JsonUtil.toJson(checkPOList),newVehicle.getModifyUser());
    }

    //临时派遣司机转正处理
    public Boolean drvApproveTemporaryToOfficial(Long drvId, String modifyUser) {
        try {
            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
            //只处理临派司机
            if (Objects.isNull(drvDriverPO) || Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode(), drvDriverPO.getTemporaryDispatchMark())) {
                return Boolean.FALSE;
            }
            //驾驶证为空
            if (StringUtils.isEmpty(drvDriverPO.getDrvcardImg())) {
                return Boolean.FALSE;
            }
            int count = drvDrvierRepository.updateTemporaryDispatchMark(Arrays.asList(drvId), Boolean.TRUE, modifyUser);
            //数据更新成功，发送临派转正式消息到采购
            if (count > 0) {
                driverCommandService.temToOfficialSendQmq(drvId, drvDriverPO.getDrvPhone(), modifyUser);
            }
            return Boolean.TRUE;
        } catch (NeedRetryException e) {
            throw e;
        }
    }

    //临时派遣车辆转正处理
    public Boolean vehApproveTemporaryToOfficial(Long vehcileId, String modifyUser) {
        try {
            VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehcileId);
            //只处理临派车辆
            if (Objects.isNull(vehVehiclePO) || Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode(), vehVehiclePO.getTemporaryDispatchMark())) {
                return Boolean.FALSE;
            }
            //车身图片为空，不处理
            if (StringUtils.isEmpty(vehVehiclePO.getVehicleFullImg())) {
                return Boolean.FALSE;
            }
            int count = vehicleRepository.updateTemporaryDispatchMark(Arrays.asList(vehcileId), Boolean.TRUE, modifyUser);
            if (count > 0) {
                vehicleCommandService.temToOfficialSendQmq(vehcileId, vehVehiclePO.getVehicleLicense(), modifyUser);
            }
            return Boolean.TRUE;
        } catch (NeedRetryException e) {
            throw e;
        }
    }

    public Boolean drvEmailInfo(Long drvId) {
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
        //只处理临派司机
        if (Objects.isNull(drvDriverPO) || Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode(), drvDriverPO.getTemporaryDispatchMark())) {
            return Boolean.FALSE;
        }
        String subject = SharkUtils.getSharkValueDefault(SharkKeyConstant.drvSupplementInfoSubject);
        Long supplierId = drvDriverPO.getSupplierId();
        String content = String.format(SharkUtils.getSharkValueDefault(SharkKeyConstant.drvSupplementInfoContent), drvDriverPO.getDrvName(), drvDriverPO.getDrvId(), overseasQconfig.getTemporaryReplenishInfohreshold());
        sendEmailToSupplier(supplierId, subject, content);
        return Boolean.TRUE;
    }

    public Boolean vehEmailInfo(Long vehicleId) {
        VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleId);
        //只处理临派车辆
        if (Objects.isNull(vehVehiclePO) || Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode(), vehVehiclePO.getTemporaryDispatchMark())) {
            return Boolean.FALSE;
        }
        String subject = SharkUtils.getSharkValueDefault(SharkKeyConstant.vehSupplementInfoSubject);
        Long supplierId = vehVehiclePO.getSupplierId();
        String content = String.format(SharkUtils.getSharkValueDefault(SharkKeyConstant.vehSupplementInfoContent),
                vehVehiclePO.getVehicleLicense(), vehVehiclePO.getVehicleId(), overseasQconfig.getTemporaryReplenishInfohreshold());
        sendEmailToSupplier(supplierId, subject, content);
        return Boolean.TRUE;
    }

    public Boolean drvAndVehEmailInfo(Long drvId,Long vehicleId) {
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
        VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleId);
        //如果司机和车辆都为正式，则不处理
        if (!Objects.isNull(drvDriverPO) && !Objects.isNull(vehVehiclePO) &&
                Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode(), drvDriverPO.getTemporaryDispatchMark()) &&
                Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode(), vehVehiclePO.getTemporaryDispatchMark())) {
            return Boolean.FALSE;
        }
        //如果司机和车辆都是临时状态，则发送两者邮件
       if(!Objects.isNull(drvDriverPO) && !Objects.isNull(vehVehiclePO) &&
               Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(), drvDriverPO.getTemporaryDispatchMark()) &&
               Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(), vehVehiclePO.getTemporaryDispatchMark())){
           String subject = SharkUtils.getSharkValueDefault(SharkKeyConstant.drvAndVehSupplementInfoSubject);
           Long supplierId = drvDriverPO.getSupplierId();
           String content = String.format(SharkUtils.getSharkValueDefault(SharkKeyConstant.drvAndVehSupplementInfoContent), drvDriverPO.getDrvName(),
                   drvDriverPO.getDrvId(), vehVehiclePO.getVehicleLicense(), vehicleId, overseasQconfig.getTemporaryReplenishInfohreshold());
           sendEmailToSupplier(supplierId, subject, content);
       }
       //如果只有司机为临时，则发司机提醒
        if(!Objects.isNull(drvDriverPO) && Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(), drvDriverPO.getTemporaryDispatchMark())){
            drvEmailInfo(drvId);
        }
        //如果只有车辆为临时，则发车辆提醒
        if(!Objects.isNull(vehVehiclePO) && Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(), vehVehiclePO.getTemporaryDispatchMark())){
            vehEmailInfo(vehicleId);
        }
        return Boolean.TRUE;
    }

    public Boolean sendEmailToSupplier(Long supplierId, String subject, String content) {
        if (supplierId == null || supplierId <= 0 || StringUtils.isEmpty(subject) || StringUtils.isEmpty(content)) {
            return Boolean.FALSE;
        }

        String supplierEmail = enumRepository.getSupplierEmail(supplierId);
        commandService.sendEmail(subject, supplierEmail, content);
        return Boolean.TRUE;
    }
}
