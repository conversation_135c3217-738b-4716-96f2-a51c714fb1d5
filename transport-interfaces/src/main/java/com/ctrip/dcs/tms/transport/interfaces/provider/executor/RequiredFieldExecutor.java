package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.FieldDTO;
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.RequiredFieldRequestType;
import com.ctrip.model.RequiredFieldResponseType;

@Component
public class RequiredFieldExecutor extends AbstractRpcExecutor<RequiredFieldRequestType, RequiredFieldResponseType> implements Validator<RequiredFieldRequestType> {

    @Autowired
    private InternationalEntryService internationalEntryService;

    @Override
    public RequiredFieldResponseType execute(RequiredFieldRequestType requiredFieldRequestType) {
        RequiredFieldResponseType responseType = new RequiredFieldResponseType();
        Result<List<RequiredFieldDTO>> result = internationalEntryService.queryRequiredFiledList(requiredFieldRequestType.getCityId(), requiredFieldRequestType.getSupplierId(), requiredFieldRequestType.getSceneList());
        if (result.isSuccess()) {
            List<RequiredFieldDTO> data = result.getData();
            if (CollectionUtils.isEmpty(data)) {
                return ServiceResponseUtils.success(responseType);
            }
            List<FieldDTO> collect = data.stream().map(item -> new FieldDTO(item.getFieldName(), item.getRequired(), item.getScene(), item.getDisable())).collect(Collectors.toList());
            responseType.setFieldList(collect);
        }
        return ServiceResponseUtils.success(responseType);
    }
}
