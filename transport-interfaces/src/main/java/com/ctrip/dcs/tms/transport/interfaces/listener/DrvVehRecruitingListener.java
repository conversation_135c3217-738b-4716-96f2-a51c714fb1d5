package com.ctrip.dcs.tms.transport.interfaces.listener;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.command.impl.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctriposs.baiji.exception.*;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import qunar.tc.qmq.*;
import qunar.tc.qmq.consumer.annotation.*;

import java.sql.*;
import java.util.Date;
import java.util.Objects;
import java.util.*;
import java.util.stream.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.RECRUITING_APPROVE;

/**
 * <AUTHOR>
 * @Description 是入审批流需异步核验证件
 * @Date 15:32 2020/11/18
 * @Param
 * @return
 **/
@Component
public class DrvVehRecruitingListener {
    private static final Logger logger = LoggerFactory.getLogger(DrvVehRecruitingListener.class);

    @Autowired
    DrvRecruitingRepository drvRecruitingRepository;
    @Autowired
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    CertificateCheckQueryService certificateCheckQueryService;
    @Autowired
    private RecruitingCommandService recruitingCommandService;
    @Autowired
    TmsCertificateCheckRepository checkRepository;
    @Autowired
    TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Autowired
    private TmsRecruitingApproveStepRepository stepRepository;
    @Autowired
    private TmsRecruitingApproveStepChildRepository childRepository;
    @Autowired
    private DrvVehRecruitingCommandService drvVehRecruitingCommandService;
    @Autowired
    TmsCityNetConfigRepository tmsCityNetConfigRepository;
    @Autowired
    private TmsModRecordCommandService tmsModRecordCommandService;

    /***
     　* @description: 招募司机车辆审批时效处理
     超时提醒
     运营工作台审核：最新更新为“待平台审核”后→三方核验状态为“已完成”时开始计时，>X小时，该条审核事件增加超时标识（X可配置，当前X=24小时）
     VBK工作台审核：最新更新为“待供应商审核”或”平台驳回“起开始计时，>Y小时，该条审核事件增加超时标识（Y可配置，当前Y=24小时）
     　* <AUTHOR>
     　* @date 2021/8/18 15:28
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_APPROVE_AGING, consumerGroup = TmsTransportConstant.QmqTag.TAG_RECRUITING_APPROVE_AGING)
    public void drvVehRecruitingApproveAgingListener(Message message) {
        try {
            Long recruitingId = message.getLongProperty("recruitingId");
            //当前审批状态
            Integer approveStatus = message.getIntProperty("approveStatus");
            //审批来源，1.司机,2.车辆
            Integer recruitingType = message.getIntProperty("recruitingType");
            if (recruitingId == null || recruitingId <= 0 || recruitingType == null) {
                return;
            }

            if(message.times() > 3){
                logger.error("drvVehRecruitingApproveAgingListener","drvId:{},approveStatus:{},recruitingType:{}",recruitingId,approveStatus,recruitingType);
                return;
            }
            //localRetries是连续本地重试了多少次的意思(防止死循环)
            if (message.localRetries() > 5){
                //抛出远程重试异常  NeedRetryException指定下次重试的时间点
                throw new NeedRetryException(System. currentTimeMillis() + 60 * 1000 , "1min retry" );
            }

            Timestamp approveTime = null;
            Integer recruitingApproveStatus = null;
            Integer drvFrom = null;
            Long vehicleId = null;
            //H5招募司机或工作台创建司机
            if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.drv.getCode())) {
                DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(recruitingId);
                if (drvRecruitingPO == null || drvRecruitingPO.getApproveAging() == 0) {
                    return;
                }
                approveTime = drvRecruitingPO.getApproveTime();
                recruitingApproveStatus = drvRecruitingPO.getApproverStatus();
                drvFrom = drvRecruitingPO.getDrvFrom();
                vehicleId = drvRecruitingPO.getVehicleId();

            } else if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())) {
                VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(recruitingId);
                if (vehicleRecruitingPO == null ||  vehicleRecruitingPO.getApproveAging() == 0) {
                    return;
                }
                approveTime = vehicleRecruitingPO.getApproveTime();
                recruitingApproveStatus = vehicleRecruitingPO.getApproverStatus();
            }
            if (approveTime == null || !Objects.equals(approveStatus, recruitingApproveStatus)) {
                return;
            }
            long diffMin = DateUtil.getMinOfDate(approveTime, new Date());
            if (diffMin >= qconfig.getRecruitingApproveAgingThreshold()) {
                Boolean vehicleFlag = Boolean.FALSE;
                Long vehicleRecruitingId = recruitingId;
                if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.drv.getCode())) {
                    drvRecruitingRepository.updateApproveAging(Arrays.asList(recruitingId), TmsTransportConstant.ApproveAgingEnum.HASTIMEOUT.getCode());
                    if (Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
                        vehicleFlag = Boolean.TRUE;
                        vehicleRecruitingId = vehicleId;
                    }
                }
                if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode()) || vehicleFlag) {
                    vehicleRecruitingRepository.updateApproveAging(Arrays.asList(vehicleRecruitingId), TmsTransportConstant.ApproveAgingEnum.HASTIMEOUT.getCode());
                }
            }
        } catch (Exception e) {
            logger.error("drvVehRecruitingApproveAgingListener error:{}", e);
        }
    }

    /**
    　* @description: 证件核验
                     身份证、驾驶证、行驶证：VBK新建司机车辆提交或VBK审核提交（无论是否首次），且触发调用三方接口成功或保持上次三方接口结果时
                     若三方的证件核验结果为“不通过”，则触发系统自动驳回并直接置为“平台驳回”，单项审核结果标签为“不通过”
                     若三方的证件核验结果为“通过”或“需要复核”，则流程正常进行
                     网约车双证：VBK新建司机车辆提交或VBK审核提交（无论是否首次），且触发调用三方接口成功或保持上次三方接口结果时
                     若三方的证件核验结果为“通过”时，则流程正常进行
                     若三方的证件核验结果为“需要复核”时
                     有查询网址（按城市）的，则流程正常进行
                     无查询网址（按城市）的：则触发系统自动驳回并直接置为“平台驳回”，单项审核结果标签为“不通过”
                     所有不通过均可线上增加附件资料
    　* <AUTHOR>
    　* @date 2021/10/9 14:11
    */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_CERTIFICATE_CHECK, consumerGroup = TmsTransportConstant.QmqTag.TAG_RECRUITING_CERTIFICATE_CHECK)
    public void drvVehRecruitingCertificateCheckListener(Message message) {
        Long recruitingId = message.getLongProperty("recruitingId");
        //审批来源，1.司机,2.车辆
        Integer recruitingType = message.getIntProperty("recruitingType");
        String drvIdCard = "";
        String drvName = "";
        String vehicleLicense = "";
        String vinCode = "";
        String modifyUser = message.getStringProperty("modifyUser");
        String drvRequestInfo = message.getStringProperty("drvRequestInfo");
        String vehRequestInfo = message.getStringProperty("vehRequestInfo");
        if (recruitingId == null || recruitingId <= 0 || recruitingType == null) {
            return;
        }

        if(StringUtils.isNotEmpty(drvRequestInfo)){
            Map<String,String> drvRequestInfoMap = JsonUtil.fromJson(drvRequestInfo, new TypeReference<Map<String, String>>() {
            });
            if(!MapUtils.isEmpty(drvRequestInfoMap)){
                drvIdCard = drvRequestInfoMap.get("drvIdCard");
                drvName = drvRequestInfoMap.get("drvName");
            }
        }
        if(StringUtils.isNotEmpty(vehRequestInfo)){
            Map<String,String> vehRequestInfoMap = JsonUtil.fromJson(vehRequestInfo, new TypeReference<Map<String, String>>() {
            });
            if(!MapUtils.isEmpty(vehRequestInfoMap)){
                vehicleLicense = vehRequestInfoMap.get("vehicleLicense");
                vinCode = vehRequestInfoMap.get("vin");
            }
        }
        Integer approveStatus = null, drvFrom = null;
        String newDrvIdCard = "", newDrvName = "", newVehicleLicense = "", newVinCode = "",netVehiclePeoImg = "",netTansCtfctImg = "",drvNetAppealMaterials = "",vehNetAppealMaterials = "";
        Long vehicleRecruitingId = recruitingId;
        Boolean drvCheckFlag = Boolean.FALSE;
        Boolean vehCheckFlag = Boolean.FALSE;
        Integer versionFlag = null;
        Long cityId = null;
        java.sql.Date nucleicAcidTestingTime = null;
        String ocrNucleicAcidData = null;
        String vaccinationTimeList =null;
        String ocrVaccineData = null;
        Long vehicleCheckId = null;
        String drvPhone = "";
        //H5招募司机或工作台创建司机
        if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.drv.getCode())) {
            DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(recruitingId);
            if (drvRecruitingPO == null) {
                return;
            }
            drvCheckFlag = Boolean.TRUE;
            approveStatus = drvRecruitingPO.getApproverStatus();
            newDrvIdCard = drvRecruitingPO.getDrvIdcard();
            newDrvName = drvRecruitingPO.getDrvName();
            drvFrom = drvRecruitingPO.getDrvFrom();
            vehicleRecruitingId = drvRecruitingPO.getVehicleId();
            netVehiclePeoImg = drvRecruitingPO.getNetVehiclePeoImg();
            drvNetAppealMaterials = drvRecruitingPO.getNetAppealMaterials();
            versionFlag = drvRecruitingPO.getVersionFlag();
            cityId = drvRecruitingPO.getCityId();
            nucleicAcidTestingTime = drvRecruitingPO.getNucleicAcidTestingTime();
            ocrNucleicAcidData = drvRecruitingPO.getOcrNucleicAcidData();
            ocrVaccineData = drvRecruitingPO.getOcrVaccineData();
            if(Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())){
                vehicleCheckId =  drvRecruitingPO.getVehicleId();
            }
            drvPhone = drvRecruitingPO.getDrvPhone();
        }
        if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode()) ||
                Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
            VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(vehicleRecruitingId);
            if (vehicleRecruitingPO == null) {
                return;
            }
            vehCheckFlag = Boolean.TRUE;
            newVehicleLicense = vehicleRecruitingPO.getVehicleLicense();
            newVinCode = vehicleRecruitingPO.getVin();
            netTansCtfctImg = vehicleRecruitingPO.getNetTansCtfctImg();
            vehNetAppealMaterials = vehicleRecruitingPO.getNetAppealMaterials();
            versionFlag = vehicleRecruitingPO.getVersionFlag();
            if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())) {
                approveStatus = vehicleRecruitingPO.getApproverStatus();
            }
        }
        //只平台审批进核验
        if (!Objects.equals(approveStatus, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())) {
            return;
        }
        //当前qmq是为三期以后开放
        if(versionFlag == null || versionFlag < 3 ){
            return;
        }
        //进入司机核验流程
        if (drvCheckFlag) {
            Map<String, Object> checkMap = certificateCheckQueryService.drvRecruitingCertificateCheck(DrvCertificateCheckParameterDTO.drvCertificateCheck(recruitingId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(), drvIdCard, newDrvIdCard, drvName, newDrvName, modifyUser,netVehiclePeoImg,drvNetAppealMaterials,drvPhone));
            //核酸和疫苗
            drvVehRecruitingCommandService.toDoNucleicAcidLabel(recruitingId, cityId, newDrvName, newDrvIdCard, BaseUtil.dealWithDateToStr(nucleicAcidTestingTime), ocrNucleicAcidData);
            List<String> timeList = Strings.isNullOrEmpty(vaccinationTimeList) ? Lists.newArrayList() : Lists.newArrayList(vaccinationTimeList.split(","));
            drvVehRecruitingCommandService.toDoVaccineLabel(recruitingId, newDrvName, newDrvIdCard, timeList, ocrVaccineData);
        }
        if (vehCheckFlag) {
            Map<String, Object> checkMap = certificateCheckQueryService.vehRecruitingCertificateCheck((VehCertificateCheckParameterDTO.vehCertificateCheck(vehicleRecruitingId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode(), vehicleLicense, newVehicleLicense, newVinCode, modifyUser,netTansCtfctImg,vehNetAppealMaterials)));
        }
        //司机核验状态
        tmsQmqProducerCommandService.sendRecruitingCheckStatusQMQ(recruitingId,recruitingType,TmsTransportConstant.TMS_DEFAULT_USERNAME);
    }

    /**
    　* @description: 司机、车辆核验状态实时监测
    　* <AUTHOR>
    　* @date 2021/10/12 9:32
    */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_CHECK_STATUS, consumerGroup = TmsTransportConstant.QmqTag.TAG_RECRUITING_CHECK_STATUS)
    public void drvVehRecruitingCheckStatusListener(Message message) {
        Long recruitingId = message.getLongProperty("recruitingId");
        //审批来源，1.司机,2.车辆
        Integer recruitingType = message.getIntProperty("recruitingType");
        String modifyUser = message.getStringProperty("modifyUser");
        if (recruitingId == null || recruitingId <= 0 || recruitingType == null) {
            return;
        }

        try {
            if (message.times() > 3) {
                logger.error("drvVehRecruitingCheckStatusListener", "recruitingId:{},recruitingType:{}", recruitingId, recruitingType);
                return;
            }
            //localRetries是连续本地重试了多少次的意思(防止死循环)
            if (message.localRetries() > 5) {
                //抛出远程重试异常  NeedRetryException指定下次重试的时间点
                throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "1min retry");
            }

            Integer drvFrom = null;
            Long vehicleRecruitingId = recruitingId;
            List<Long> drvRecruitingIds = Lists.newArrayList();
            Boolean drvCheckFlag = Boolean.FALSE;
            Boolean vehCheckFlag = Boolean.FALSE;
            Integer approveStatus = null;
            Map<Long,Integer> dataCheckMap = Maps.newHashMap();
            Integer versionFlag = null;
            Integer bdApproveStatus = null;
            Integer orgCheckStatus  = null;
            //H5招募司机或工作台创建司机
            if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.drv.getCode())) {
                DrvRecruitingPO drvRecruitingPO = drvRecruitingRepository.queryByPK(recruitingId);
                if (drvRecruitingPO == null) {
                    return;
                }
                drvFrom = drvRecruitingPO.getDrvFrom();
                approveStatus = drvRecruitingPO.getApproverStatus();
                vehicleRecruitingId = drvRecruitingPO.getVehicleId();
                drvRecruitingIds.add(recruitingId);
                drvCheckFlag = Boolean.TRUE;
                dataCheckMap.put(recruitingId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode());
                versionFlag = drvRecruitingPO.getVersionFlag();
                bdApproveStatus = drvRecruitingPO.getBdApproveStatus();
                orgCheckStatus = drvRecruitingPO.getCheckStatus();
            }
            if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode()) ||
                    Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
                VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(vehicleRecruitingId);
                if (vehicleRecruitingPO == null) {
                    return;
                }
                drvRecruitingIds.add(vehicleRecruitingId);
                vehCheckFlag = Boolean.TRUE;
                dataCheckMap.put(vehicleRecruitingId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
                approveStatus = vehicleRecruitingPO.getApproverStatus();
                versionFlag = vehicleRecruitingPO.getVersionFlag();
                if(Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
                    bdApproveStatus = vehicleRecruitingPO.getBdApproveStatus();
                    orgCheckStatus = vehicleRecruitingPO.getCheckStatus();
                }
            }

            //对于从未提交给平台的审核事件来说，应该都归属到“未完成”里；对于已经提交过平台的，才存在三方结果是否已完成
            if(!Objects.equals(approveStatus, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode()) && (bdApproveStatus == null || bdApproveStatus == 0)){
                return;
            }

            List<TmsCertificateCheckPO> checkPOList = checkRepository.queryCerCheckListByCheckIds(drvRecruitingIds, Arrays.asList(TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode(),
                    TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode()));
            //如果没有三方记录表数据，则按完成处理
//            if (CollectionUtils.isEmpty(checkPOList)) {
//                return;
//            }
            Boolean flag = Boolean.TRUE;
            Integer checkStatus = TmsTransportConstant.RecruitingCheckStatusEnum.HASCOMPLETED.getCode();
            if(CollectionUtils.isNotEmpty(checkPOList)){
                for (TmsCertificateCheckPO tmsCertificate : checkPOList) {
                    if(!Objects.equals(dataCheckMap.get(tmsCertificate.getCheckId()),tmsCertificate.getCheckType())){
                        continue;
                    }
                    Integer tmsCheckStatus = tmsCertificate.getCheckStatus();
                    if(versionFlag >= 3){
                        tmsCheckStatus = tmsCertificate.getThirdCheckStatus();
                    }
                    if (Objects.equals(tmsCheckStatus, TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())) {
                        flag = Boolean.FALSE;
                        checkStatus = TmsTransportConstant.RecruitingCheckStatusEnum.NOTCOMPLETED.getCode();
                    }
                }
            }

            if (drvCheckFlag) {
                drvRecruitingRepository.updateDrvCheckStatus(recruitingId, checkStatus,modifyUser);
                if(flag){
                    if(Objects.equals(orgCheckStatus,TmsTransportConstant.RecruitingCheckStatusEnum.NOTCOMPLETED.getCode())){
                        drvRecruitingRepository.updateApproveAging(recruitingId);
                    }
                    //计算时效
                    tmsQmqProducerCommandService.sendRecruitingApproveAgingQMQ(recruitingId,TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),approveStatus);
                }
            }
            if (vehCheckFlag) {
                vehicleRecruitingRepository.updateVehCheckStatus(vehicleRecruitingId, checkStatus,modifyUser);
                if(flag){
                    //工作台创建车辆发送审批时效qmq
                    if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())){
                        if(Objects.equals(orgCheckStatus,TmsTransportConstant.RecruitingCheckStatusEnum.NOTCOMPLETED.getCode())){
                            vehicleRecruitingRepository.updateApproveAging(vehicleRecruitingId);
                        }
                        tmsQmqProducerCommandService.sendRecruitingApproveAgingQMQ(vehicleRecruitingId,TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode(),approveStatus);
                    }
                }
            }
            //如果核验状态已完成，触发证件不通过系统驳回
            autoRejectedQMQ(checkStatus,recruitingId,recruitingType);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
    /**
     　* @description: 身份证、驾驶证、行驶证：VBK新建司机车辆提交或VBK审核提交（无论是否首次），且触发调用三方接口成功或保持上次三方接口结果时
     若三方的证件核验结果为“不通过”，则触发系统自动驳回并直接置为“平台驳回”，单项审核结果标签为“不通过”
     若三方的证件核验结果为“通过”或“需要复核”，则流程正常进行
     网约车双证：VBK新建司机车辆提交或VBK审核提交（无论是否首次），且触发调用三方接口成功或保持上次三方接口结果时
     若三方的证件核验结果为“通过”时，则流程正常进行
     若三方的证件核验结果为“需要复核”时
     有查询网址（按城市）的，则流程正常进行
     无查询网址（按城市）的：则触发系统自动驳回并直接置为“平台驳回”，单项审核结果标签为“不通过”
     所有不通过均可线上增加附件资料
     自动驳回后状态为“平台驳回”，在VBK审核页面看到的状态
     单项不通过原因：{证件所在单项的合规子项名称}核验不通过，eg.身份与背景审核核验不通过（有几个不通过就展示几个）
     　* <AUTHOR>
     　* @date 2021/10/12 9:32
     */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_SYSTEM_AUTO_REJECTED_RECRUITING, consumerGroup = TmsTransportConstant.QmqTag.TAG_RECRUITING_REJECTED)
    public void systemAutoRejectedRecruitingListener(Message message) {
        Long recruitingId = message.getLongProperty("recruitingId");
        //审批来源，1.司机,2.车辆
        Integer recruitingType = message.getIntProperty("recruitingType");
        if (recruitingId == null || recruitingId <= 0 || recruitingType == null) {
            return;
        }

        try {
            //新增自动驳回开关(true:开,false:关)
            if(!qconfig.getRecruitingSystemAutoRejectedSwitch()){
                return;
            }
            Long vehicleId = null;
            Integer drvFrom = null;
            Integer approverStatus = null;
            Long cityId = null;
            String drvNetAppealMaterials = "";
            String vehNetAppealMaterials = "";
            String idcardAppealMaterials = "";
            String drvLicenseAppealMaterials = "";
            String vehicleLicenseAppealMaterials = "";
            switch (recruitingType){
                case 1:
                    DrvRecruitingPO drvRecruitingPO =  drvRecruitingRepository.queryByPK(recruitingId);
                    if(!Objects.isNull(drvRecruitingPO)){
                        vehicleId = drvRecruitingPO.getVehicleId();
                        drvFrom = drvRecruitingPO.getDrvFrom();
                        approverStatus = drvRecruitingPO.getApproverStatus();
                        cityId = drvRecruitingPO.getCityId();
                        drvNetAppealMaterials = drvRecruitingPO.getNetAppealMaterials();
                        idcardAppealMaterials = drvRecruitingPO.getIdcardAppealMaterials();
                        drvLicenseAppealMaterials = drvRecruitingPO.getDrvLicenseAppealMaterials();
                    }
                    break;
                case 2:
                    VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(recruitingId);
                    if(!Objects.isNull(vehicleRecruitingPO)){
                        approverStatus = vehicleRecruitingPO.getApproverStatus();
                        vehicleId = vehicleRecruitingPO.getVehicleId();
                        cityId = vehicleRecruitingPO.getCityId();
                        vehNetAppealMaterials = vehicleRecruitingPO.getNetAppealMaterials();
                        vehicleLicenseAppealMaterials = vehicleRecruitingPO.getVehicleLicenseAppealMaterials();
                    }
            }

            //如果是H5入注司机，则需要查询车辆的申诉材料
            if(recruitingType == 1 && Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())){
                VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(vehicleId);
                if(!Objects.isNull(vehicleRecruitingPO)){
                    vehNetAppealMaterials = vehicleRecruitingPO.getNetAppealMaterials();
                    vehicleLicenseAppealMaterials = vehicleRecruitingPO.getVehicleLicenseAppealMaterials();
                }
            }

            //只平台审批进核验
            if (!Objects.equals(approverStatus, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())) {
                return;
            }
            List<TmsRecruitingApproveStepPO> stepPOList = stepRepository.queryApproveStepList(recruitingId,recruitingType,TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue());
            //只代表H5请求，工作台请求RequestFromH5 = null
            if(recruitingType == 1 && Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())){
                List<TmsRecruitingApproveStepPO> vehStepList = stepRepository.queryApproveStepList(vehicleId,2,TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue());
                stepPOList = (List<TmsRecruitingApproveStepPO>) CollectionUtils.union(stepPOList,vehStepList);
            }
            if(CollectionUtils.isEmpty(stepPOList)){
                return;
            }
            stepPOList = stepPOList.stream().sorted(Comparator.comparing(TmsRecruitingApproveStepPO::getApproveItem).reversed()).collect(Collectors.toList());
            Map<Integer, TmsCertificateCheckPO> drvCheckPOMap = Maps.newHashMap();
            Map<Integer, TmsCertificateCheckPO> vehCheckPOMap = Maps.newHashMap();
            if(recruitingType.intValue() == 1){
                //司机对应证件核验记录
                drvCheckPOMap = certificateCheckQueryService.queryCertificateCheckToMap(recruitingId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_DRV.getCode());
            }
            if(recruitingType.intValue() == 2 || vehicleId != null){
                //司机对应证件核验记录
                vehCheckPOMap = certificateCheckQueryService.queryCertificateCheckToMap(vehicleId, TmsTransportConstant.CertificateCheckTypeEnum.RECRUITING_VEHICLE.getCode());
            }

            Map<Integer, TmsCertificateCheckPO> totalCheckMap = Maps.newHashMap();
            for(Map.Entry<Integer, TmsCertificateCheckPO> entry: drvCheckPOMap.entrySet()){
                totalCheckMap.put(entry.getKey(),entry.getValue());
            }
            for(Map.Entry<Integer, TmsCertificateCheckPO> entry: vehCheckPOMap.entrySet()){
                totalCheckMap.put(entry.getKey(),entry.getValue());
            }
            Boolean noPassFlag = Boolean.FALSE;
            for(Map.Entry<Integer, TmsCertificateCheckPO> entry: totalCheckMap.entrySet()){
                TmsCertificateCheckPO tmsCertificateCheckPO = entry.getValue();
                if(Objects.isNull(tmsCertificateCheckPO)){
                    continue;
                }
                if(Objects.equals(tmsCertificateCheckPO.getThirdCheckStatus(), TmsTransportConstant.CheckStatusEnum.ERROR.getCode())||
                        Objects.equals(tmsCertificateCheckPO.getThirdCheckStatus(), TmsTransportConstant.CheckStatusEnum.REVIEW.getCode())){
                    noPassFlag = Boolean.TRUE;
                    break;
                }
            }
            if(!noPassFlag){
                return;
            }
            //如果身份证三方是不通过,则头像单项也置为不通过，特殊处理
            TmsRecruitingApproveStepPO headStep = new TmsRecruitingApproveStepPO();
            Boolean headUpdateFlag = Boolean.FALSE;
            Boolean autoRejectedFlag = Boolean.FALSE;
            List<Long> autoRejectedStepIds = Lists.newArrayList();
            List<Long> netAutoRejectedIds = Lists.newArrayList();
            for(TmsRecruitingApproveStepPO stepPO : stepPOList){
                //将头像单独挑出单独处理
                if(stepPO.getApproveItem().intValue() == 3){
                    BeanUtils.copyProperties(stepPO,headStep);
                }
                TmsCertificateCheckPO tmsCertificateCheckPO = totalCheckMap.get(getCheckStatus(stepPO.getApproveItem()));
                if(Objects.isNull(tmsCertificateCheckPO)){
                    continue;
                }
                String desc = getcheckStatusNoPassDesc(tmsCertificateCheckPO.getCertificateType());
                //网约车双证：VBK新建司机车辆提交或VBK审核提交（无论是否首次），且触发调用三方接口成功或保持上次三方接口结果时
                //若三方的证件核验结果为“通过”时，则流程正常进行
                //若三方的证件核验结果为“需要复核”时
                //有查询网址（按城市）的，则流程正常进行
                //无查询网址（按城市）的：则触发系统自动驳回并直接置为“平台驳回”，单项审核结果标签为“不通过”
                if((Objects.equals(tmsCertificateCheckPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())||
                        Objects.equals(tmsCertificateCheckPO.getCertificateType(), TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode()))
                && Objects.equals(tmsCertificateCheckPO.getThirdCheckStatus(), TmsTransportConstant.CheckStatusEnum.REVIEW.getCode()) &&
                        !judgeIsNetConfig(cityId,stepPO.getApproveType()) &&
                        !checkAppealMaterialsEmpty(tmsCertificateCheckPO.getCertificateType(),drvNetAppealMaterials,vehNetAppealMaterials,idcardAppealMaterials,drvLicenseAppealMaterials,vehicleLicenseAppealMaterials)){
                    autoRejectedFlag = this.updateSingleAutoRejected(stepPO,desc);
                    if(autoRejectedFlag){
                        autoRejectedStepIds.add(stepPO.getId());
                        netAutoRejectedIds.add(tmsCertificateCheckPO.getId());
                    }
                    //修改网约车子标签状态为不通过
                    checkRepository.updateCheckStatus(tmsCertificateCheckPO.getId(),TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                }
                //只处理第三方核验状态不通过的数据
                //身份证、驾驶证、行驶证：VBK新建司机车辆提交或VBK审核提交（无论是否首次），且触发调用三方接口成功或保持上次三方接口结果时
                //若三方的证件核验结果为“不通过”，则触发系统自动驳回并直接置为“平台驳回”，单项审核结果标签为“不通过”
                //若三方的证件核验结果为“通过”或“需要复核”，则流程正常进行
                if(Objects.equals(tmsCertificateCheckPO.getThirdCheckStatus(), TmsTransportConstant.CheckStatusEnum.ERROR.getCode()) &&
                        !checkAppealMaterialsEmpty(tmsCertificateCheckPO.getCertificateType(),drvNetAppealMaterials,vehNetAppealMaterials,idcardAppealMaterials,drvLicenseAppealMaterials,vehicleLicenseAppealMaterials)){
                    if(stepPO.getApproveItem().intValue() == 1){
                        headUpdateFlag = Boolean.TRUE;
                    }
                    autoRejectedFlag = this.updateSingleAutoRejected(stepPO,desc);
                    if(autoRejectedFlag){
                        autoRejectedStepIds.add(stepPO.getId());
                    }
                }
            }
            //处理头像,如果头像单项是待审核,将单项状态置为不通过
            if(headUpdateFlag && !Objects.isNull(headStep)){
                autoRejectedFlag = this.updateSingleAutoRejected(headStep,"头像信息核验不通过");
                if(autoRejectedFlag){
                    autoRejectedStepIds.add(headStep.getId());
                }
            }
            //触发自动驳回
            if(autoRejectedFlag){
                autoRejectedRecord(autoRejectedStepIds,recruitingId,recruitingType);
                recruitingCommandService.approveRoute(RecruitingCommandServiceImpl.buildRequest(recruitingId), recruitingType);
                //更新招募进度
                tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(recruitingId,recruitingType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue());
                tmsQmqProducerCommandService.sendRecruitingApproveScheduleQMQ(recruitingId,recruitingType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue());
            }

        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    public Integer getCheckStatus(Integer approveItem){
        switch (approveItem){
            case 1: return TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode();
            case 2:return TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode();
            case 4:return TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode();
            case 101: return TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode();
            case 102: return TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode();
        }
        return 0;
    }

    public String getcheckStatusNoPassDesc(Integer certificateType){
        switch (certificateType){
            case 5: return "身份证信息核验不通过";
            case 1:return "驾驶证信息核验不通过";
            case 3:return "网约车驾驶证信息核验不通过";
            case 2: return "行驶证信息核验不通过";
            case 4: return "网约车运输证信息核验不通过";
        }
        return "";
    }

    /**
    　* @description: 审核进度：Y/X
                     X=审核单项和（目前：H5注册入口，X=9；工作台新建入口，X=6或3，即司机或车辆）
                     Y=在当前审核页面审核完成的单项（即审核单项button已变为单项审核通过与否的标识）
                     VBK工作台审核页面，以供应商审核进度为准，首次进入待供应商审核进度为0/X
                     运营工作台审核页面，以平台审核进度为准，首次进入待平台审核进度为0/X
     　* <AUTHOR>
    　* @date 2022/1/10 11:35
    */
    @QmqConsumer(prefix = TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_APPROVE_SCHEDULE, consumerGroup = TmsTransportConstant.QmqTag.TAG_RECRUITING_APPROVE_SCHEDULE)
    public void recruitingApproveScheduleListener(Message message) {
        Long recruitingId = message.getLongProperty("recruitingId");
        //审批来源，1.司机,2.车辆
        Integer recruitingType = message.getIntProperty("recruitingType");
        //操作角色(1.vbk，2.bd)
        Integer accountType = message.containsKey(TmsTransportConstant.ACCOUNT_TYPE_KEY) ?
                message.getIntProperty(TmsTransportConstant.ACCOUNT_TYPE_KEY) : message.getIntProperty("accountType");
        if (recruitingId == null || recruitingId <= 0 || recruitingType == null || accountType == null) {
            Cat.logEvent(RECRUITING_APPROVE, String.format("%s_%s", recruitingType, accountType));
            return;
        }
        try {
            Integer drvFrom = null;
            Long vehicleId = null;
            switch (recruitingType){
                case 1:
                    DrvRecruitingPO drvRecruitingPO =  drvRecruitingRepository.queryByPK(recruitingId);
                    if(!Objects.isNull(drvRecruitingPO)){
                        drvFrom = drvRecruitingPO.getDrvFrom();
                        vehicleId = drvRecruitingPO.getVehicleId();
                    }
            }
            List<TmsRecruitingApproveStepPO> stepPOList = stepRepository.queryApproveStepList(recruitingId,recruitingType,accountType);
            if(CollectionUtils.isEmpty(stepPOList)){
                return;
            }
            if(Objects.equals(drvFrom, TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())){
                List<TmsRecruitingApproveStepPO> vehStepList = stepRepository.queryApproveStepList(vehicleId,2,accountType);
                stepPOList = (List<TmsRecruitingApproveStepPO>) CollectionUtils.union(stepPOList,vehStepList);
            }
            //计算审批通过数(通过+不审批+不通过)
            int approveThroughCount = 0;
            for(TmsRecruitingApproveStepPO stepPO:stepPOList){
                if(!Objects.equals(TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode(),stepPO.getApproveStatus())){
                    approveThroughCount += 1;
                }
            }
            //审批进度标识 0=未完成,1=已完成
            Integer approveSchedule = 0;
            if(approveThroughCount == stepPOList.size()){
                approveSchedule = 1;
            }
            if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.drv.getCode())) {
                drvRecruitingRepository.updateDrvApproveSchedule(recruitingId,accountType,approveSchedule);
            }
            if (Objects.equals(recruitingType, TmsTransportConstant.RecruitingTypeEnum.vehicle.getCode())) {
                vehicleRecruitingRepository.updateDrvApproveSchedule(recruitingId,accountType,approveSchedule);
            }
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    public Boolean judgeIsNetConfig(Long cityId,Integer configType){
        int count = tmsCityNetConfigRepository.countConfigByCity(cityId,2,configType);
        if(count > 0){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean updateSingleAutoRejected(TmsRecruitingApproveStepPO stepPO ,String desc){
        if(Objects.equals(stepPO.getApproveStatus(), TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode())){
            stepPO.setApproveStatus(TmsTransportConstant.SingleApproveStatusEnum.APPROVE_NO_THROUGH.getCode());
            stepPO.setModifyUser(TmsTransportConstant.TMS_DEFAULT_USERNAME);
            stepPO.setApproveReason(desc);
            stepPO.setApproveTime(DateUtil.getNow());
            stepRepository.update(stepPO);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean autoRejectedQMQ(Integer checkStatus,Long recruitingId,Integer recruitingType){
        //如果核验状态已完成，触发证件不通过系统驳回
        if(Objects.equals(checkStatus,TmsTransportConstant.RecruitingCheckStatusEnum.HASCOMPLETED.getCode())){
            tmsQmqProducerCommandService.sendSystemAutoRejectedRecruitingQMQ(recruitingId,recruitingType);
        }
        return Boolean.TRUE;
    }

    /**
    　* @description: “申诉材料”字段出现逻辑
     平台驳回次数=第n次时，VBK-五个证件单项都分别出现“申诉材料”字段（即VBK审核页面初始化时，五个证件都不出现“申诉材料“字段）
     计算进n的平台驳回次数包含“手动驳回及自动驳回”
     n可配置，本次配置n=2
     “申诉材料”出现后
     “网约车人证”三方返回结果聚合为“需要复核”且“城市核验地址为空”
     若“申诉材料”字段值为空，则子项自动打“不通过”标签，在“三方核验结果=已完成”时，触发整体审核事件的自动驳回
     若“申诉材料”字段值不为空，则子项打“请打标签”标签，在“三方核验结果=已完成”时，不会因为人证触发自动驳回
     “网约车车证”三方返回结果聚合为“需要复核”且“城市核验地址为空”
     若“申诉材料”字段值为空，则子项自动打“不通过”标签，在“三方核验结果=已完成”时，触发整体审核事件的自动驳回
     若“申诉材料”字段值不为空，则子项打“请打标签”标签，在“三方核验结果=已完成”时，不会因为车证触发自动驳回
     “身份证/驾驶证/行驶证”（三者逻辑一致），当证件”三方返回结果聚合为“不通过”
     若“申诉材料”字段值为空，则子项自动打“不通过”标签，在“三方核验结果=已完成”时，触发整体审核事件的自动驳回
     若“申诉材料”字段值不为空，则子项打“请打标签”标签，在“三方核验结果=已完成”时，不会因为车证触发自动驳回
    　* <AUTHOR>
    　* @date 2022/3/1 9:48
    */
    public Boolean checkAppealMaterialsEmpty(Integer certificateType,String drvNetAppealMaterials,String vehNetAppealMaterials,String idcardAppealMaterials,String drvLicenseAppealMaterials,String  vehicleLicenseAppealMaterials){
        switch (certificateType){
            case 5: return StringUtils.isNotEmpty(idcardAppealMaterials);
            case 1:return StringUtils.isNotEmpty(drvLicenseAppealMaterials);
            case 2:return StringUtils.isNotEmpty(vehicleLicenseAppealMaterials);
            case 3:return StringUtils.isNotEmpty(drvNetAppealMaterials);
            case 4:return StringUtils.isNotEmpty(vehNetAppealMaterials);
        }
        return Boolean.FALSE;
    }

    //系统自动驳回，记录单项操作日志
    public Boolean autoRejectedRecord(List<Long> autoRejectedStepIds,Long recruitingId, Integer recruitingType){
        for(Long autoRejected :autoRejectedStepIds){
            tmsModRecordCommandService.insertAutoTurnDownStepRrd(recruitingId,recruitingType, TmsTransportConstant.SingleApproveStatusEnum.APPROVE_NO_THROUGH.getCode(),autoRejected);
        }
        return Boolean.TRUE;
    }

}
