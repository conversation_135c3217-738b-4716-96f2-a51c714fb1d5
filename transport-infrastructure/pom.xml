<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tms-transport-service</artifactId>
        <groupId>com.ctrip.dcs.tms</groupId>
        <version>1.4.2</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>transport-infrastructure</artifactId>

    <name>transport-infrastructure</name>

    <properties>
        <framework-bom.version>4.5.0</framework-bom.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.ctrip.soa.dcs.jnt.price</groupId>
            <artifactId>compute-price-soa-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs</groupId>
            <artifactId>dcs-phonebridge-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.im</groupId>
            <artifactId>im-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>distlock-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.locationqueryservice</groupId>
            <artifactId>location-query-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.infrastructure.service</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>dal</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc</groupId>
            <artifactId>qmq-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc</groupId>
            <artifactId>qmq-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.tms</groupId>
            <artifactId>transport-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.tms</groupId>
            <artifactId>transport-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.bizcomponent</groupId>
            <artifactId>basicdata</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>exchangerateservice</artifactId>
                    <groupId>com.ctrip.soa.platform.accounting.exchangerateservice.v1</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>igt-basicservice-client</artifactId>
                    <groupId>com.ctrip.igt.basicservice</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.pms</groupId>
            <artifactId>product-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.dcs.jnt.price</groupId>
            <artifactId>sku-price-soa-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.dcs.jnt.dcsscmmerchantservice.v1</groupId>
            <artifactId>dcsscmmerchantservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.open</groupId>
            <artifactId>open-pub-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.tms</groupId>
            <artifactId>tmsconnectservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.promeg</groupId>
            <artifactId>tinypinyin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs</groupId>
            <artifactId>recognitionservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.21828</groupId>
            <artifactId>dcsdriverlevelservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.23343</groupId>
            <artifactId>phonenumbersplitservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.dispatchorder</groupId>
            <artifactId>self-dispatchorder-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs</groupId>
            <artifactId>basicdatadomain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.ibu.platform</groupId>
            <artifactId>ibu-shark-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-prometheus-exporter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.geo</groupId>
            <artifactId>geo-platform-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.scm</groupId>
            <artifactId>scm-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.infosec.kms</groupId>
            <artifactId>kms-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>junit-dep</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.domain</groupId>
            <artifactId>driver-domain-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.domain</groupId>
            <artifactId>driver-domain-service-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.tour.driver</groupId>
            <artifactId>driver-platform-api-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.frt</groupId>
            <artifactId>product-basic-client</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.common</groupId>
            <artifactId>common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>qunar.tc.qconfig</groupId>
            <artifactId>qconfig-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.credis</groupId>
            <artifactId>credis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.common</groupId>
            <artifactId>dcs-common-component-international-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs</groupId>
            <artifactId>vehicle-platform-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.dcs.go</groupId>
                    <artifactId>domain</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.corp.pub.user</groupId>
            <artifactId>soa-extend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>account-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.order.hybrid.search</groupId>
            <artifactId>hybrid-search-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.car.och.orderqueryservice.v1</groupId>
            <artifactId>igtorderqueryservice-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.self</groupId>
            <artifactId>self-orderquery-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.order</groupId>
            <artifactId>vbk-supplier-order-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.tms</groupId>
            <artifactId>transport-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.poi</groupId>
            <artifactId>dcs-poi-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>service-accountsmessage-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz.ai.aiplatform</groupId>
            <artifactId>ai-platform-contract</artifactId>
        </dependency>
    </dependencies>
</project>
