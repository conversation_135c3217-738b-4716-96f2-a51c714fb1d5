package com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvLeaveDetailPO
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ModRecordConstant
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDriverLeaveRepository
import com.google.common.collect.Lists
import org.apache.commons.lang3.reflect.FieldUtils
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

class DriverLeaveCacheHandlerTest extends Specification {
    def testObj = new DriverLeaveCacheHandler()
    def drvDriverLeaveRepository = Mock(DrvDriverLeaveRepository)
    def tmsTransportQconfig = Mock(TmsTransportQconfig)
    def tableCacheIDMap = Mock(Map)

    def setup() {

        testObj.drvDriverLeaveRepository = drvDriverLeaveRepository
        testObj.tmsTransportQconfig = tmsTransportQconfig
        FieldUtils.writeField(testObj, "tableCacheIDMap", tableCacheIDMap, true)
    }

    def "getCacheNameTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getCacheName()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult || t
        "drv_leave" | ""
    }

    @Unroll
    def "supportTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.support(tableName)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        tableName   || expectedResult
        ModRecordConstant.TableName.drvDriverLeave || true
    }

    @Unroll
    def "queryDrvLeaveDetailTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsTransportQconfig.getDrvLeaveQueryBatchSize() >> 1

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.mgetHit(_, _) >> [Lists.newArrayList(new DrvLeaveDetailPO(leaveEndTime: new Timestamp(2057744415000)))]
        when:
        def result = spy.queryDrvLeaveDetail(drvIds)

        then: "验证返回结果里属性值是否符合预期"
        result.get(0).getLeaveEndTime() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvIds || expectedResult
        [1L]   || new Timestamp(2057744415000)
    }

    @Unroll
    def "refreshCacheTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.doRefresh(_) >> {}
        spy.getBusinessId(_) >> "getBusinessIdResponse"
        when:
        def result = spy.refreshCache(dataChange)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        dataChange       || expectedResult
        new DataChange() || null
    }

    def "getFillDataActionTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvDriverLeaveRepository.queryDrvLeaveDetailForDsp(_) >> [new DrvLeaveDetailPO(drvId: 1L)]

        when:
        def result = testObj.getFillDataAction()

        then: "验证返回结果里属性值是否符合预期"
        assert result != null
    }
}
