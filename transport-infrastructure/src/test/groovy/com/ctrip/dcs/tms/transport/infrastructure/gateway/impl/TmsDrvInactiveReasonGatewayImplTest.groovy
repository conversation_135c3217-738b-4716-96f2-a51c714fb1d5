package com.ctrip.dcs.tms.transport.infrastructure.gateway.impl

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository
import spock.lang.Specification
import spock.lang.Unroll

class TmsDrvInactiveReasonGatewayImplTest extends Specification {
    def testObj = new TmsDrvInactiveReasonGatewayImpl()
    def tmsDrvInactiveReasonRepository = Mock(TmsDrvInactiveReasonRepository)

    def setup() {

        testObj.tmsDrvInactiveReasonRepository = tmsDrvInactiveReasonRepository
    }

    @Unroll
    def "enableActiveAfterDeleteReasonByCodeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsDrvInactiveReasonRepository.query(_) >> drv
        tmsDrvInactiveReasonRepository.deleteReasonByCode(_, _, _) >> 0

        when:
        def result = testObj.enableActiveAfterDeleteReasonByCode(drvId, reasonCode, modifyUser)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        modifyUser   | drvId | drv| reasonCode || expectedResult
        "modifyUser" | 1L    |[new TmsDrvInactiveReasonPO()]| [0]        || false
        "modifyUser" | 1L    |[]| [0]        || true
    }

    @Unroll
    def "insertWhenNotExistTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsDrvInactiveReasonRepository.insert(_) >> 0
        tmsDrvInactiveReasonRepository.query(_) >> [new TmsDrvInactiveReasonPO(drvId: 1L, reasonCode: 0)]

        when:
        def result = testObj.insertWhenNotExist(po)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        po                                                   || expectedResult
        new TmsDrvInactiveReasonPO(drvId: 1L, reasonCode: 0) || null
    }
}
