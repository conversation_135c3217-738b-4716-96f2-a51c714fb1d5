package com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl

import com.ctrip.arch.canal.DataChange
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.CacheBusinessJsonConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CacheBusinessQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import com.google.common.collect.Maps
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class TransportGroupCacheHandlerTest extends Specification {
    def testObj = new TransportGroupCacheHandler()
    def transportGroupRepository = Mock(TransportGroupRepository)
    def cacheBusinessQconfig = Mock(CacheBusinessQconfig)

    def setup() {

        testObj.transportGroupRepository = transportGroupRepository
        testObj.cacheBusinessQconfig = cacheBusinessQconfig
    }


    @Unroll
    def "refreshCacheTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        spy.doRefresh(_) >> {}
        spy.getBusinessId(_) >> "getBusinessIdResponse"

        when:
        def result = spy.refreshCache(dataChange)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        dataChange       || expectedResult
        new DataChange() || null
    }


    @Unroll
    def "supportTest"() {

        when:
        def result = testObj.support(tableName)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        tableName   || expectedResult
        "tableName" || false
    }


    @Unroll
    def "getCacheNameTest"() {

        when:
        def result = testObj.getCacheName()

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult | t
        "tsp" | ""
    }

    @Unroll
    def "getFillDataAction"() {
        given:
        cacheBusinessQconfig.getConfig() >> new CacheBusinessJsonConfig()

        when:
        def result = testObj.getFillDataAction()
        result.refreshAction([]) >> Maps.newHashMap()
        result.getVersion() >> null
        result.enableDiff()

        then: "验证返回结果里属性值是否符合预期"

        (result == null) == expectedResult
        where: "表格方式验证多种分支调用场景"
        tableName || expectedResult
        "tsp" || false
    }

}
