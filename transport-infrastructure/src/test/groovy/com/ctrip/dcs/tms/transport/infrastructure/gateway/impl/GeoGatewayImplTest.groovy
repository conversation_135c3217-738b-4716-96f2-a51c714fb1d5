package com.ctrip.dcs.tms.transport.infrastructure.gateway.impl

import com.ctrip.dcs.geo.domain.repository.TimeZoneRepository
import com.ctrip.dcs.geo.domain.value.TransformTimeZoneResult
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime

class GeoGatewayImplTest extends Specification {
    def testObj = new GeoGatewayImpl()
    def timeZoneRepository = Mock(TimeZoneRepository)

    def setup() {

        testObj.timeZoneRepository = timeZoneRepository
    }

    @Unroll
    def "getLocalCurrentTimeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.convertToLocalTime(_, _) >> localDateTime
        when:
        def result = spy.getLocalCurrentTime(targetCityId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        targetCityId | localDateTime || expectedResult
        1L          | LocalDateTime.now() || localDateTime
    }

    @Unroll
    def "convertToLocalTimeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.transform(_, _, _) >> timeZone
        when:
        def result = spy.convertToLocalTime(sourceDateTime, targetCityId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        sourceDateTime      | targetCityId | timeZone || expectedResult
        LocalDateTime.now() | 1L           | new TransformTimeZoneResult() || timeZone.getTargetDateTime()
    }

    @Unroll
    def "transformTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        timeZoneRepository.transform(_) >> timeZone

        when:
        def result = testObj.transform(sourceDateTime, sourceCityId, targetCityId)

        then: "验证返回结果里属性值是否符合预期"
        result.getTargetDateTime() == expectedResult
        where: "表格方式验证多种分支调用场景"
        sourceDateTime      | targetCityId | sourceCityId | timeZone || expectedResult
        LocalDateTime.now() | 1L           | 1L           | new TransformTimeZoneResult() || timeZone.getTargetDateTime()
    }
}
