package com.ctrip.dcs.tms.transport.infrastructure.common.util

import spock.lang.Specification
import spock.lang.Unroll

class TrafficDiverterTest extends Specification {
    def testObj = new TrafficDiverter()
    def random = Mock(Random)

    def setup() {

        testObj.random = random
    }

    @Unroll
    def "greatThanTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.greatThan(rate)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        rate || expectedResult
        0    || true
        1001    || false
    }
}
