package com.ctrip.dcs.tms.transport.infrastructure.common.util


import com.ctrip.dcs.geo.domain.repository.CityRepository
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.MobileCountryRuleDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService
import com.ctrip.igt.framework.common.result.Result
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants
import spock.lang.Specification
import spock.lang.Unroll

class MobileHelperTest extends Specification {
    def testObj = new MobileHelper()
    def tmsTransportQconfig = Mock(TmsTransportQconfig)
    def cityRepository = Mock(CityRepository)
    def businessQConfig = Mock(BusinessQConfig)
    def driverDomainServiceProxy = Mock(DriverDomainServiceProxy)
    def ivrCallService = Mock(IVRCallService)

    def setup() {

        testObj.tmsTransportQconfig = tmsTransportQconfig
        testObj.cityRepository = cityRepository
        testObj.businessQConfig = businessQConfig
        testObj.driverDomainServiceProxy = driverDomainServiceProxy
        testObj.ivrCallService = ivrCallService
    }

    @Unroll
    def "isMobileValidTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsTransportQconfig.getCheckMobileSwitch() >> Boolean.TRUE
        businessQConfig.getMobileCountryRuleDTO(_) >> mobileCountryRuleDTO
        businessQConfig.isOpen() >> true

        cityRepository.findRoot(_ as Long) >> City.builder().countryId(2).build()

        when:
        def result = testObj.isMobileValid(igtCode, phone, 1)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        phone    | igtCode   || mobileCountryRuleDTO                  || expectedResult
        "phone"  | "igtCode" || new MobileCountryRuleDTO()            || ServiceResponseConstants.ResStatus.SUCCESS_CODE
        "1phone" | "igtCode" || new MobileCountryRuleDTO(rule: ["1"]) || ServiceResponseConstants.ResStatus.SUCCESS_CODE
    }

    @Unroll
    def "isMobileValidTestCity"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsTransportQconfig.getCheckMobileSwitch() >> Boolean.TRUE
        cityRepository.findRoot(_ as Long) >> city

        when:
        def result = testObj.isMobileValid(igtCode, phone, 1)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        phone   | igtCode   || city || expectedResult
        "phone" | "igtCode" || null || ServiceResponseConstants.ResStatus.SUCCESS_CODE

    }

    @Unroll
    def "isMobileValidTest2"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"
        tmsTransportQconfig.getCheckMobileSwitch() >> Boolean.TRUE
        spy.isValid(_, _, _) >> isvalid

        when:
        def result = spy.isMobileValid(igtCode, phone, cityId)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        phone   | igtCode   | cityId | isvalid || expectedResult
        "phone" | "igtCode" | 1L     | true    || "200"
        "phone" | "igtCode" | 1L     | false   || "200"
    }

    @Unroll
    def "isMobileValidSwithOFFTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsTransportQconfig.getCheckMobileSwitch() >> Boolean.FALSE

        when:
        def result = testObj.isMobileValid(igtCode, phone, cityId)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        phone   | igtCode   | cityId || expectedResult
        "phone" | "igtCode" | 1L     || "200"
    }

    @Unroll
    def "isValidTest"() {
        given: "设定相关方法入参"
        driverDomainServiceProxy.parsePhoneNumber(_) >> res
        and: "Mock相关接口返回"
        businessQConfig.isCheckMobileByGooglePhoneNumberUtil() >> true

        and: "Spy相关接口"
        def spy = Spy(testObj)
        when:
        def result = spy.isValid(igtCode, decryptPhone, 1L)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        decryptPhone  | igtCode | res                                                   || expectedResult
        "***********" | "86"    | Result.Builder.<String> newResult().success().build() || true
        "***********" | "86"    | Result.Builder.<String> newResult().fail().build()    || false
    }

    @Unroll
    def "isMobileVerifiedTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        businessQConfig.isVerifyPhone(_) >> isVerifyPhone
        ivrCallService.isPhoneVerified(_) >> isPhoneVerified
        businessQConfig.isOpen() >> false

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.isMobileValid(_, _, _) >> isMobileValid
        when:
        def result = spy.isMobileVerified(igtCode, phone, internalScope)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        phone   | igtCode   | internalScope | isVerifyPhone |isPhoneVerified |isMobileValid|| expectedResult
        "phone" | "igtCode" | 0             | true|true|ResponseResultUtil.success()|| "200"
    }

    @Unroll
    def "isMobileVerifiedTest2"() {
        given: "设定相关方法入参"
        SessionHolder.setSessionSource(["accountType" : "1"])
        and: "Mock相关接口返回"
        businessQConfig.isVerifyPhone(_) >> isVerifyPhone
        ivrCallService.isPhoneVerified(_) >> isPhoneVerified
        businessQConfig.isOpen() >> false

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.isMobileValid(_, _, _) >> isMobileValid
        when:
        def result = spy.isMobileVerified(igtCode, phone, internalScope, null)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        phone   | igtCode   | internalScope | isVerifyPhone |isPhoneVerified |isMobileValid|| expectedResult
        "phone" | "igtCode" | 0             | true|true|ResponseResultUtil.success()|| "200"
        "phone" | "igtCode" | 0             | false|true|ResponseResultUtil.failed("500", "")|| "500"
        "phone" | "igtCode" | 0             | false|true|ResponseResultUtil.failed("500", "")|| "500"
        "phone" | "igtCode" | 0             | false|false|ResponseResultUtil.failed("500", "")|| "500"
    }
}
