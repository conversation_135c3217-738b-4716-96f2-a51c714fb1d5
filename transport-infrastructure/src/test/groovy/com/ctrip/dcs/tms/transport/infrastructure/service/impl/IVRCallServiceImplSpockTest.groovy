package com.ctrip.dcs.tms.transport.infrastructure.service.impl

import com.ctrip.dcs.driver.domain.account.CallResultDTO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig
import com.ctrip.dcs.tms.transport.infrastructure.gateway.GeoGateway
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper
import com.ctrip.igt.ResponseResult
import com.ctrip.model.CallPhoneForVerifyResponseType
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType
import spock.lang.*

import java.time.LocalDateTime
import java.time.Month

class IVRCallServiceImplSpockTest extends Specification {
    def testObj = new IVRCallServiceImpl()
    def driverDomainService = Mock(DriverDomainService)
    def enumRepositoryHelper = Mock(EnumRepositoryHelper)
    def commonConfig = Mock(CommonConfig)
    def geoGateway = Mock(GeoGateway)
    def tmsQmqProducer = Mock(TmsQmqProducer)
    def businessQConfig = Mock(BusinessQConfig)
    def enumRepository = Mock(EnumRepository)

    def setup() {

        testObj.driverDomainService = driverDomainService
        testObj.geoGateway = geoGateway
        testObj.commonConfig = commonConfig
        testObj.tmsQmqProducer = tmsQmqProducer
        testObj.enumRepositoryHelper = enumRepositoryHelper
        testObj.enumRepository = enumRepository
        testObj.businessQConfig = businessQConfig
    }

    @Unroll
    def "sendIvrVerifiedMessageTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.sendIvrVerifyDelayMessage(_, _) >> {}
        when:
        def result = spy.sendIvrVerifiedMessage(phoneDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        phoneDTO       || expectedResult
        new PhoneDTO() || null
    }

    @Unroll
    def "sendIvrVerifyDelayMessageTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.sendIvrVerifyDelayMessage(delayMin, phoneDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        phoneDTO                                                                                       | delayMin || expectedResult
        new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode", modifyUser: "modifyUser") | 0        || null
    }

    @Unroll
    def "isPhoneVerifiedTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainService.queryCallPhoneForVerifyResult(_) >> verifyList
        commonConfig.getCallResultStatus() >> ["String"]

        when:
        def result = testObj.isPhoneVerified(phoneDTOList)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        phoneDTOList                                                          | verifyList || expectedResult
        [new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode")] | new QueryCallPhoneForVerifyResultResponseType(responseResult: new ResponseResult(), callResultList: [new CallResultDTO(callHangupCodeList: ["String"], countryCode: "countryCode", phoneNumber: "phoneNumber")]) || ["countryCode-mobilePhone": Boolean.TRUE]
        [new PhoneDTO(mobilePhone: "", countryCode: "countryCode")] | new QueryCallPhoneForVerifyResultResponseType(responseResult: new ResponseResult(), callResultList: [new CallResultDTO(callHangupCodeList: ["String"], countryCode: "countryCode", phoneNumber: "phoneNumber")]) || [:]
        [new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode")] | new QueryCallPhoneForVerifyResultResponseType(responseResult: new ResponseResult(), callResultList: [new CallResultDTO(callHangupCodeList: ["String"], countryCode: "countryCode", phoneNumber: "phoneNumber")]) || ["countryCode-mobilePhone": Boolean.TRUE]
        [new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode")] | new QueryCallPhoneForVerifyResultResponseType(responseResult: new ResponseResult(success: true), callResultList: [new CallResultDTO(callHangupCodeList: ["String"], countryCode: "countryCode", phoneNumber: "phoneNumber")]) || ["countryCode-mobilePhone": Boolean.FALSE]
    }

    @Unroll
    def "isPhoneVerifiedTest2"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.isPhoneVerified(_) >> ["String": Boolean.TRUE]
        when:
        def result = spy.isPhoneVerified(phoneDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        phoneDTO                                                             || expectedResult
        new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode") || Boolean.TRUE
    }

    @Unroll
    def "getLocationTimeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        geoGateway.getLocalCurrentTime(_) >> locaDateTime

        when:
        def result = testObj.getLocationTime(cityId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cityId | locaDateTime || expectedResult
        1L     |LocalDateTime.now() || locaDateTime
    }

    @Unroll
    def "getLocalTimeForIvrCallTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        commonConfig.getIrvCallAbleStartTime() >> "09:00:00"
        commonConfig.getIrvCallAbleEndTime() >> "18:00:00"
        commonConfig.getIrvCallIntervalMin() >> [0]

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.generateTimePoints(_, _, _, _, _) >> [1L]
        spy.getLocationTime(_) >> LocalDateTime.now()
        when:
        def result = spy.getLocalTimeForIvrCall(cityId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cityId || expectedResult
        1L     || [1L]
    }

    @Unroll
    def "asyncSendIvrByLocalTimeForIvrCallTest"() {
        given: "设定相关方法入参"
        businessQConfig.isVerifyPhone(_) >> Boolean.TRUE
        enumRepository.isNotChinaMainLand(_) >> Boolean.TRUE
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getLocalTimeForIvrCall(_) >> [1L]
        when:
        def result = spy.asyncSendIvrByLocalTimeForIvrCall(cityId, phoneDTO, "zh-CN")

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        phoneDTO                                                                                       | cityId || expectedResult
        new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode", modifyUser: "modifyUser") | 1L || null
    }

    @Unroll
    def "callPhoneForVerifyTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"
        driverDomainService.callPhoneForVerify(_) >> new CallPhoneForVerifyResponseType(callTaskId: 1L)
        driverDomainService.queryCallPhoneForVerifyResult(_) >> new QueryCallPhoneForVerifyResultResponseType(callResultStatus: "callResultStatus")
        commonConfig.getIvrTaskIdValidityMinutes() >> 0
        commonConfig.getIvrRedisTtlHours() >> 0
        commonConfig.getCallResultStatus() >> ["String"]

        and: "Spy相关接口"
        spy.getLanguage(_) >> "getLanguageResponse"

        when:
        def result = spy.callPhoneForVerify(mobilePhone, countryCode, channel, language)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        mobilePhone   | countryCode   | channel   | language   || expectedResult
        "mobilePhone" | "countryCode" | "channel" | "language" || 1L
    }


    @Unroll
    def "getLanguageTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        enumRepositoryHelper.getLocaleCode() >> "getLocaleCodeResponse"


        when:
        def result = testObj.getLanguage(language)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        language   || expectedResult
        "language" || "getLocaleCodeResponse"
    }

    @Unroll
    def "getLocationTimeTest2"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        geoGateway.getLocalCurrentTime(_) >> LocalDateTime.of(2025, Month.JUNE, 15, 14, 47, 16)

        when:
        def result = testObj.getLocationTime(cityId)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cityId || expectedResult
        1L     || LocalDateTime.of(2025, Month.JUNE, 15, 14, 47, 16)
    }


    @Unroll
    def "generateTimePointsTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"

        when:
        def result = spy.generateTimePoints(startTime, endTime, lcoalDateTime, intervals, cityId)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        lcoalDateTime|intervals | startTime                                          | endTime                                            | cityId || expectedResult
        LocalDateTime.of(2025, Month.JUNE, 15, 9, 0, 0) | [0,10,100,10]  | LocalDateTime.of(2025, Month.JUNE, 15, 9, 0, 0) | LocalDateTime.of(2025, Month.JUNE, 15, 18, 0, 0) | 1L     || [0, 10, 110, 120]
        LocalDateTime.of(2025, Month.JUNE, 15, 9, 0, 0) | [0,10,1000,10]  | LocalDateTime.of(2025, Month.JUNE, 15, 9, 0, 0) | LocalDateTime.of(2025, Month.JUNE, 15, 18, 0, 0) | 1L     || [0, 10, 1440, 1450]
        LocalDateTime.of(2025, Month.JUNE, 15, 3, 0, 0) | [0,10,1000,10]  | LocalDateTime.of(2025, Month.JUNE, 15, 9, 0, 0) | LocalDateTime.of(2025, Month.JUNE, 15, 18, 0, 0) | 1L     || [360, 370, 1800, 1810]
        LocalDateTime.of(2025, Month.JUNE, 15, 21, 0, 0) | [0,10,1000,10]  | LocalDateTime.of(2025, Month.JUNE, 15, 9, 0, 0) | LocalDateTime.of(2025, Month.JUNE, 15, 18, 0, 0) | 1L     || [720, 730, 2160, 2170]
    }


}

