package com.ctrip.dcs.tms.transport.infrastructure.common.util

import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import spock.lang.*
import static org.mockito.ArgumentMatchers.*
import static org.powermock.api.mockito.PowerMockito.*


class NetCardCheckUtilTest extends Specification {
    def testObj = new NetCardCheckUtil()
    def tmsTransportQconfig = Mock(TmsTransportQconfig)

    def setup() {

        testObj.tmsTransportQconfig = tmsTransportQconfig
    }


    @Unroll
    def "getCheckStatusTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        spy.isNetCardNoNeedCheck(2L) >> true
        spy.isNetCardNoNeedCheck(1L) >> false

        when:
        def result = spy.getCheckStatus(cityId)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cityId || expectedResult
        2L     || 1
        1L     || null
    }


    @Unroll
    def "isNetCardNeedCheckTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tmsTransportQconfig.getNetCardNeedCheckCityList() >> list


        when:
        def result = testObj.isNetCardNeedCheck(cityId)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cityId |list|| expectedResult
        1L     | [1L] || true
        1L     | [2L] || false
    }


    @Unroll
    def "isNetCardNoNeedCheckTest"() {
        given: "设定相关方法入参"
        def spy = Spy(testObj)
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        spy.isNetCardNeedCheck(1L) >> true
        spy.isNetCardNeedCheck(2L) >> false

        when:
        def result = spy.isNetCardNoNeedCheck(cityId)

        then: "验证返回结果里属性值是否符合预期"

        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cityId || expectedResult
        1L     || false
        2L     || true
    }

}

