package com.ctrip.dcs.tms.transport.infrastructure.gateway.impl

import com.ctrip.dcs.tms.transport.infrastructure.base.MockExcutorService

import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.IThreadPoolService
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import spock.lang.Specification
import spock.lang.Unroll

class DriverGatewayImplTest extends Specification {
    def testObj = new DriverGatewayImpl()
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def threadPoolService = Mock(IThreadPoolService)
    def tmsTransportQconfig = Mock(TmsTransportQconfig)
    def random = Mock(Random)

    def setup() {

        testObj.random = random
        testObj.tmsTransportQconfig = tmsTransportQconfig
        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.threadPoolService = threadPoolService
    }

    @Unroll
    def "queryDrvIdByMarkPageTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.cacheDrivers(_) >> {}
        spy.logDriverRemarkResult(_, _) >> {}
        spy.getResultData(_, _) >> [1L]
        spy.getCachedOfficialDrivers(_) >> [1L]
        when:
        def result = spy.queryDrvIdByMarkPage(drvIds, temporaryDispatchMark)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvIds | temporaryDispatchMark || expectedResult
        [1L]   | 0                     || [1L]
    }

    @Unroll
    def "getCachedOfficialDriversTest"() {
        given: "设定相关方法入参"
        tmsTransportQconfig.getRedisBatchSize() >> 1000
        when:
        def result = testObj.getCachedOfficialDrivers(drvIds)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvIds || expectedResult
        [1L]   || []
    }

    @Unroll
    def "cacheDriversTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        threadPoolService.getInQueryDriverPool() >> new MockExcutorService()
        tmsTransportQconfig.getInQueryBatchSize() >> 1
        tmsTransportQconfig.getDriverTempRemarkExpSeconds() >> 100
        tmsTransportQconfig.getDriverTempRemarkExpRandomSeconds() >> 100

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getTemporaryMarkDriverKey(_) >> "getTemporaryMarkDriverKeyResponse"
        when:
        def result = spy.cacheDrivers(drvIds)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvIds || expectedResult
        [1L]   || null
    }

    @Unroll
    def "getTemporaryMarkDriverKeyTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getTemporaryMarkDriverKey(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || "dtms_d_1"
    }

    @Unroll
    def "logResultTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.logDriverRemarkResult(drvIds, result1)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        result1 | drvIds || expectedResult
        [1L]   | [1L]   || null
    }

    @Unroll
    def "getResultDataTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvDrvierRepository.queryDrvIdByMarkPage(_, _) >> [1L]
        threadPoolService.getInQueryDriverPool() >> new MockExcutorService()
        tmsTransportQconfig.getInQueryBatchSize() >> 1

        when:
        def result = testObj.getResultData(resultList, temporaryDispatchMark)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        resultList | temporaryDispatchMark || expectedResult
        [1L]       | 0                     || [1L]
    }
}
