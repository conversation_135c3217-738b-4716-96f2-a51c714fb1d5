package com.ctrip.dcs.tms.transport.infrastructure.common.util

import spock.lang.Specification
import spock.lang.Unroll

class PlatformUtilSpockTest extends Specification {

    @Unroll
    def "getOperationTest"() {
        given: "设定相关方法入参"
        when:
        def result = PlatformUtil.getOperation()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult || t
        "" || ""
    }
}
