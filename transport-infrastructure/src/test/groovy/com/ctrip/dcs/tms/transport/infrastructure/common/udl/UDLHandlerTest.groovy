package com.ctrip.dcs.tms.transport.infrastructure.common.udl

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.dto.DriverUdlInfo
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import spock.lang.Specification
import spock.lang.Unroll

class UDLHandlerTest extends Specification {
    def testObj = new UDLHandler()
    def driverDomainServiceProxy = Mock(DriverDomainServiceProxy)
    def enumRepository = Mock(EnumRepository)

    def setup() {

        testObj.driverDomainServiceProxy = driverDomainServiceProxy
        testObj.enumRepository = enumRepository
    }

    @Unroll
    def "getDrvUdlTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainServiceProxy.queryUDLByDriverId(_) >> new DriverUdlInfo(udl: "udl")

        when:
        def result = testObj.getDrvUdl(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || "udl"
    }

    @Unroll
    def "getDrvUdlByCityIdTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        enumRepository.isNotChinaMainLand(_) >> true

        when:
        def result = testObj.getDrvUdlByCityId(cityId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cityId || expectedResult
        1L     || "US_SPD"
    }
}
