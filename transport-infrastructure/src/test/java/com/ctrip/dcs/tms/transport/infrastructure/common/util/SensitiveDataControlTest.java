package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
//import org.powermock.api.mockito.*;
//import org.powermock.core.classloader.annotations.*;
//import org.powermock.modules.junit4.*;

import static org.mockito.Mockito.when;

//@RunWith(PowerMockRunner.class)
//@PrepareForTest({PlatformUtil.class, TmsTransUtil.class})
//@PowerMockIgnore({"javax.management.*", "javax.script.*", "com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*",
//        "org.w3c.dom.*", "javax.crypto.*"})
public class SensitiveDataControlTest {
/*

    @InjectMocks
    private SensitiveDataControl control;

    @Mock
    private TmsTransportQconfig config;

    @Test
    public void getSensitiveDataTest() {
        PowerMockito.mockStatic(PlatformUtil.class);
        PowerMockito.mockStatic(TmsTransUtil.class);
        when(PlatformUtil.isQReq()).thenReturn(true);
        control.getSensitiveData("2222222", KeyType.Identity_Card);
    }

    @Test
    public void getSensitiveDataTest1() {
        PowerMockito.mockStatic(PlatformUtil.class);
        when(PlatformUtil.isQReq()).thenReturn(false);
        when(config.getPrivacyDataSwitch()).thenReturn(false);
        control.getSensitiveData("2222222", KeyType.Identity_Card);
    }
*/

}
