package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.InfrastructureService;
import com.ctrip.igt.infrastructureservice.executor.contract.NameTranslateRequestType;
import com.ctrip.igt.infrastructureservice.executor.contract.NameTranslateResponseType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class InfrastructureServiceClientProxyImplTest {

    @InjectMocks
    private InfrastructureServiceClientProxyImpl infrastructureServiceClientProxy;

    @Mock
    private InfrastructureService infrastructureService;

    @Test
    public void nameTranslateTest() {
        String name = "秦琼";
        Mockito.when(infrastructureService.nameTranslate(getRequest(name))).thenReturn(getResponse(
          Collections.singletonMap(name, "秦师傅")));
        Assert.assertEquals(infrastructureServiceClientProxy.nameTranslate(name), "秦师傅");
    }

    @Test
    public void nameTranslateTest1() {
        String name = "张三";
        Mockito.when(infrastructureService.nameTranslate(getRequest(name))).thenReturn(null);
        String str = infrastructureServiceClientProxy.nameTranslate(name);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void nameTranslateTest2() {
        String name = "李四";
        Mockito.when(infrastructureService.nameTranslate(getRequest(name))).thenReturn(getResponse(null));
        String str = infrastructureServiceClientProxy.nameTranslate(name);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void nameTranslateTest3() {
        String name = "王五";
        Mockito.when(infrastructureService.nameTranslate(getRequest(name))).thenReturn(getResponse(Maps.newHashMap()));
        String str = infrastructureServiceClientProxy.nameTranslate(name);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void nameTranslateTest4() {
        String name = "赵六";
        Mockito.when(infrastructureService.nameTranslate(getRequest(name))).thenThrow(new RuntimeException());
        String str = infrastructureServiceClientProxy.nameTranslate(name);
        Assert.assertTrue(str!=null);
    }

    private NameTranslateRequestType getRequest(String name) {
        NameTranslateRequestType nameTranslateResponseType = new NameTranslateRequestType();
        nameTranslateResponseType.setType(0);
        nameTranslateResponseType.setNames(Lists.newArrayList(name));
        return nameTranslateResponseType;
    }

    private NameTranslateResponseType getResponse(Map<String, String> res) {
        NameTranslateResponseType nameTranslateResponseType = new NameTranslateResponseType();
        nameTranslateResponseType.setNameMap(res);
        return nameTranslateResponseType;
    }
}
