package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class AddDrvCertificateCheckDTOTest {

    @InjectMocks
    AddDrvCertificateCheckDTO checkDTO;

    @Test
    public void test() {

        HashSet<Long> set1 = Sets.newHashSet();
        set1.add(1L);
        set1.add(2L);
        set1.add(3L);
        set1.add(4L);
        set1.add(5L);

        HashSet<Long> set2 = Sets.newHashSet();
        set2.add(1L);
        set2.add(2L);
        set2.add(3L);
        set2.add(4L);
        set2.add(5L);

        HashSet<Long> set3 = Sets.newHashSet();
        set3.add(4L);
        set3.add(5L);

        Sets.SetView<Long> res1 = Sets.difference(set1,set2);

        System.out.println(Sets.difference(set1,set2));

        System.out.println(Sets.difference(set1,set3));

        System.out.println(Sets.difference(res1,set3));


    }

    @Test
    public void buildDrvDTO() {
        AddDrvCertificateCheckDTO checkDTO = new AddDrvCertificateCheckDTO();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvName("11");
        DrvDriverPO drvDriverPO1 = new DrvDriverPO();
        drvDriverPO1.setDrvId(1L);
        drvDriverPO1.setDrvName("11");
        checkDTO.buildDrvDTO(drvDriverPO,drvDriverPO1);
    }

    @Test
    public void getCheckId() {
        Long long1 = checkDTO.getCheckId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setCheckId() {
        checkDTO.setCheckId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getCheckType() {
        Integer integer = checkDTO.getCheckType();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setCheckType() {
        checkDTO.setCheckType(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getCertificateType() {
        Integer integer = checkDTO.getCertificateType();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setCertificateType() {
        checkDTO.setCertificateType(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getDrvIdcard() {
        String str = checkDTO.getDrvIdcard();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setDrvIdcard() {
        checkDTO.setDrvIdcard("1213213");
        Assert.assertTrue(true);
    }

    @Test
    public void getDrvName() {
        String str = checkDTO.getDrvName();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setDrvName() {
        checkDTO.setDrvName("11");
        Assert.assertTrue(true);
    }

    @Test
    public void getFirstDrvLicenseDate() {
        String str = checkDTO.getFirstDrvLicenseDate();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setFirstDrvLicenseDate() {
        checkDTO.setFirstDrvLicenseDate("2020-02");
        Assert.assertTrue(true);
    }

    @Test
    public void getExpiryEndDate() {
        String str = checkDTO.getExpiryEndDate();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setExpiryEndDate() {
        checkDTO.setExpiryEndDate("2020");
        Assert.assertTrue(true);
    }

    @Test
    public void getNewDrvcardImg() {
        String str = checkDTO.getNewDrvcardImg();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setNewDrvcardImg() {
        checkDTO.setNewDrvcardImg("111");
        Assert.assertTrue(true);
    }

    @Test
    public void getUserName() {
        String str = checkDTO.getUserName();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setUserName() {
        checkDTO.setUserName("11");
        Assert.assertTrue(true);
    }

    @Test
    public void getNetVehiclePeoImg() {
        String str = checkDTO.getNetVehiclePeoImg();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setNetVehiclePeoImg() {
        checkDTO.setNetVehiclePeoImg("111");
        Assert.assertTrue(true);
    }

}
