package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DriverCacheInfoPOTest {

    @InjectMocks
    private DriverCacheInfoPO driverCacheInfoPO;

    @Test
    public void setVehicleSeriesTest() {
        driverCacheInfoPO.setVehicleSeries(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void setDrvHeadImgTest() {
        driverCacheInfoPO.setDrvHeadImg("");
        Assert.assertTrue(true);
    }

    @Test
    public void setDrvCategorySynthesizeCodeTest() {
        driverCacheInfoPO.setDrvCategorySynthesizeCode(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getVehCategorySynthesizeCodeTest() {
        Integer integer = driverCacheInfoPO.getVehCategorySynthesizeCode();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setVehCategorySynthesizeCodeTest() {
        driverCacheInfoPO.setVehCategorySynthesizeCode(1);
        Assert.assertTrue(true);
    }

}