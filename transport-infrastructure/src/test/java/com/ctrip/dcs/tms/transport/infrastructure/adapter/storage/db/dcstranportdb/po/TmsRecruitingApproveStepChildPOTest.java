package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;

@RunWith(MockitoJUnitRunner.class)
public class TmsRecruitingApproveStepChildPOTest {


    @InjectMocks
    TmsRecruitingApproveStepChildPO childPO;

    @Test
    public void getId() {
        Long long1 = childPO.getId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setId() {
        childPO.setId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getRecruitingApproveStepId() {
        Long long1 = childPO.getRecruitingApproveStepId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setRecruitingApproveStepId() {
        childPO.setRecruitingApproveStepId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getChildItem() {
        Integer integer = childPO.getChildItem();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setChildItem() {
        childPO.setChildItem(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getCheckStatus() {
        Integer integer =childPO.getCheckStatus();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setCheckStatus() {
        childPO.setCheckStatus(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getChildDesc() {
        String str = childPO.getChildDesc();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setChildDesc() {
        childPO.setChildDesc("11");
        Assert.assertTrue(true);
    }

    @Test
    public void getDatachangeCreatetime() {
        Timestamp timestamp =  childPO.getDatachangeCreatetime();
        Assert.assertTrue(timestamp == null);
    }

    @Test
    public void setDatachangeCreatetime() {
        childPO.setDatachangeCreatetime(DateUtil.getNow());
        Assert.assertTrue(true);
    }

    @Test
    public void getDatachangeLasttime() {
        Timestamp timestamp =   childPO.getDatachangeLasttime();
        Assert.assertTrue(timestamp == null);
    }

    @Test
    public void setDatachangeLasttime() {
        childPO.setDatachangeLasttime(DateUtil.getNow());
        Assert.assertTrue(true);
    }

    @Test
    public void getCreateUser() {
        String str = childPO.getCreateUser();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setCreateUser() {
        childPO.setCreateUser("11");
        Assert.assertTrue(true);
    }

    @Test
    public void getModifyUser() {
        String str = childPO.getModifyUser();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setModifyUser() {
        childPO.setModifyUser("11");
        Assert.assertTrue(true);
    }
}
