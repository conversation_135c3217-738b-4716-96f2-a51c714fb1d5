package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig

import qunar.tc.qconfig.client.JsonConfig
import spock.lang.Specification
import spock.lang.Unroll

class BusinessQConfigTest extends Specification {
    def testObj = new BusinessQConfig()
    def config = Mock(JsonConfig)

    def setup() {

    }

    @Unroll
    def "getMobileCountryRuleDTOTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getMobileCountryRuleDTO(countryId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        countryId || expectedResult
        1L        || null
    }

    @Unroll
    def "isCheckMobileByGooglePhoneNumberUtilTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.isCheckMobileByGooglePhoneNumberUtil()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << true
    }
}
