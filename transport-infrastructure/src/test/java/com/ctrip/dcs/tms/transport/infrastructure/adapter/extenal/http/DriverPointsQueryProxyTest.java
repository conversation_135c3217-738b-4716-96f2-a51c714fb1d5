package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http;

import com.ctrip.dcs.dsp.driver.level.api.BatchDriverPointsQry;
import com.ctrip.dcs.dsp.driver.level.api.BatchDriverPointsResp;
import com.ctrip.dcs.dsp.driver.level.api.DriverServicePointsDto;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverPoint;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsDriverLevelServiceClientProxy;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.result.Result;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class DriverPointsQueryProxyTest {

    @InjectMocks
    DriverPointsQueryProxy queryProxy;
    @Mock
    DcsDriverLevelServiceClientProxy driverLevelServiceClientProxy;

    @Test
    public void queryDrvCityRankingFromC() {
        BatchDriverPointsQry pointsResp = new BatchDriverPointsQry();
        pointsResp.setCityId(1L);
        pointsResp.setDriverIdList("1");
        BatchDriverPointsResp resp = new BatchDriverPointsResp();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        resp.setResponseResult(responseResult);
        List<DriverServicePointsDto> dtos = Lists.newArrayList();
        DriverServicePointsDto driverServicePointsDto = new DriverServicePointsDto();
        driverServicePointsDto.setCityRanking(1L);
        driverServicePointsDto.setDriverId(1);
        driverServicePointsDto.setPoints(1.1D);
        dtos.add(driverServicePointsDto);
        resp.setDtos(dtos);
        Mockito.when(driverLevelServiceClientProxy.queryDriverBatchPoints(pointsResp)).thenReturn(resp);
        Result<Map<Long,List<DriverPoint>>> result =  queryProxy.queryDrvCityRankingFromC("1",1L);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryDrvCityRankingFromC1() {
        BatchDriverPointsQry pointsResp = new BatchDriverPointsQry();
        pointsResp.setCityId(1L);
        pointsResp.setDriverIdList("1");
        BatchDriverPointsResp resp = new BatchDriverPointsResp();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        resp.setResponseResult(responseResult);
        List<DriverServicePointsDto> dtos = Lists.newArrayList();
        DriverServicePointsDto driverServicePointsDto = new DriverServicePointsDto();
        driverServicePointsDto.setCityRanking(1L);
        driverServicePointsDto.setDriverId(1);
        driverServicePointsDto.setPoints(1.1D);
        dtos.add(driverServicePointsDto);
        resp.setDtos(Lists.newArrayList());
        Mockito.when(driverLevelServiceClientProxy.queryDriverBatchPoints(pointsResp)).thenReturn(resp);
        Result<Map<Long,List<DriverPoint>>> result =  queryProxy.queryDrvCityRankingFromC("1",1L);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryDriverPointListFromC() {
        BatchDriverPointsQry pointsResp = new BatchDriverPointsQry();
        pointsResp.setCityId(1L);
        pointsResp.setDriverIdList("1");
        BatchDriverPointsResp resp = new BatchDriverPointsResp();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        resp.setResponseResult(responseResult);
        List<DriverServicePointsDto> dtos = Lists.newArrayList();
        DriverServicePointsDto driverServicePointsDto = new DriverServicePointsDto();
        driverServicePointsDto.setCityRanking(1L);
        driverServicePointsDto.setDriverId(1);
        driverServicePointsDto.setPoints(1.1D);
        dtos.add(driverServicePointsDto);
        resp.setDtos(dtos);
        Mockito.when(driverLevelServiceClientProxy.queryDriverBatchPoints(pointsResp)).thenReturn(resp);
        List<DriverPoint> result =  queryProxy.queryDriverPointListFromC("1",1L);
        Assert.assertTrue(result.size() > 0);
    }

    @Test
    public void queryDriverPointListFromC1() {
        BatchDriverPointsQry pointsResp = new BatchDriverPointsQry();
        pointsResp.setCityId(1L);
        pointsResp.setDriverIdList("1");
        BatchDriverPointsResp resp = new BatchDriverPointsResp();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        resp.setResponseResult(responseResult);
        List<DriverServicePointsDto> dtos = Lists.newArrayList();
        DriverServicePointsDto driverServicePointsDto = new DriverServicePointsDto();
        driverServicePointsDto.setCityRanking(1L);
        driverServicePointsDto.setDriverId(1);
        driverServicePointsDto.setPoints(1.1D);
        dtos.add(driverServicePointsDto);
        resp.setDtos(Lists.newArrayList());
        Mockito.when(driverLevelServiceClientProxy.queryDriverBatchPoints(pointsResp)).thenReturn(resp);
        List<DriverPoint> result =  queryProxy.queryDriverPointListFromC("1",1L);
        Assert.assertTrue(result.size() == 0);
    }


    @Test
    public void queryDriverPointListFromC2() {
        BatchDriverPointsQry pointsResp = new BatchDriverPointsQry();
        pointsResp.setCityId(1L);
        pointsResp.setDriverIdList("1");
        BatchDriverPointsResp resp = new BatchDriverPointsResp();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        resp.setResponseResult(responseResult);
        List<DriverServicePointsDto> dtos = Lists.newArrayList();
        DriverServicePointsDto driverServicePointsDto = new DriverServicePointsDto();
        driverServicePointsDto.setCityRanking(null);
        driverServicePointsDto.setDriverId(1);
        driverServicePointsDto.setPoints(1.1D);
        dtos.add(driverServicePointsDto);
        resp.setDtos(dtos);
        Mockito.when(driverLevelServiceClientProxy.queryDriverBatchPoints(pointsResp)).thenReturn(resp);
        List<DriverPoint> result =  queryProxy.queryDriverPointListFromC("1",1L);
        Assert.assertTrue(result.size() == 0);
    }

    @Test
    public void requestDriverPoints() {
        BatchDriverPointsQry pointsResp = new BatchDriverPointsQry();
        pointsResp.setCityId(1L);
        pointsResp.setDriverIdList("1");
        BatchDriverPointsResp resp = new BatchDriverPointsResp();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        resp.setResponseResult(responseResult);
        List<DriverServicePointsDto> dtos = Lists.newArrayList();
        DriverServicePointsDto driverServicePointsDto = new DriverServicePointsDto();
        driverServicePointsDto.setCityRanking(1L);
        driverServicePointsDto.setDriverId(1);
        driverServicePointsDto.setPoints(1.1D);
        dtos.add(driverServicePointsDto);
        resp.setDtos(dtos);
        Mockito.when(driverLevelServiceClientProxy.queryDriverBatchPoints(pointsResp)).thenReturn(resp);
        List<DriverServicePointsDto> result =  queryProxy.requestDriverPoints("1",1L);
        Assert.assertTrue(result.size() > 0);
    }
}
