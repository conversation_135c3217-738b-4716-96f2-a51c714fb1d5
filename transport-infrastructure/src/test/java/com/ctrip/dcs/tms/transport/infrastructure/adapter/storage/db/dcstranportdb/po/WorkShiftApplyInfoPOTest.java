package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WorkShiftApplyInfoPOTest {

    @InjectMocks
    WorkShiftApplyInfoPO infoPO;


    @Test
    public void getStartTime() {
        String string = infoPO.getStartTime();
        Assert.assertTrue(string == null);
    }

    @Test
    public void setStartTime() {
        infoPO.setStartTime("1");
        Assert.assertTrue(true);
    }

    @Test
    public void getEndTime() {
        String string = infoPO.getEndTime();
        Assert.assertTrue(string == null);
    }

    @Test
    public void setEndTime() {
        infoPO.setEndTime("1");
        Assert.assertTrue(true);
    }

    @Test
    public void getDriverUpperLimit() {
        Long long1 = infoPO.getDriverUpperLimit();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setDriverUpperLimit() {
        infoPO.setDriverUpperLimit(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getOrder() {
        Integer integer = infoPO.getOrder();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setOrder() {
        infoPO.setOrder(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getWorkShiftId() {
        Long long1 = infoPO.getWorkShiftId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setWorkShiftId() {
        infoPO.setWorkShiftId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getTransportGroupId() {
        Long long1 = infoPO.getTransportGroupId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setTransportGroupId() {
        infoPO.setTransportGroupId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getDriverBindedNum() {
        Long long1 = infoPO.getDriverBindedNum();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setDriverBindedNum() {
        infoPO.setDriverBindedNum(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getDriverAppliedSuccessNum() {
        Long long1 =  infoPO.getDriverAppliedSuccessNum();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setDriverAppliedSuccessNum() {
        infoPO.setDriverAppliedSuccessNum(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getDriverAppliedSuccessValidNum() {
        Long long1 =   infoPO.getDriverAppliedSuccessValidNum();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setDriverAppliedSuccessValidNum() {
        infoPO.setDriverAppliedSuccessValidNum(1L);
        Assert.assertTrue(true);
    }
}
