package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.common.sdk.repository.DictionaryRepository;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.pms.product.api.GetSystemDictionaryRequestType;
import com.ctrip.dcs.pms.product.api.GetSystemDictionaryResponseType;
import com.ctrip.dcs.scm.merchant.interfaces.message.QueryBasicContractResponseType;
import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.PrdDictionaryRepository;
import com.ctrip.dcs.scm.sdk.domain.ServiceProviderRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.scm.sdk.domain.prddictionary.PrdDictionary;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DcsScmMerchantServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.IGTOpenPubServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PmsProductServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.VehicleCoreService;
import com.ctrip.dcs.vehicle.core.contract.QueryVehicleCategoryRelationsResponse;
import com.ctrip.dcs.vehicle.core.contract.VehicleCategoryRelationContract;
import com.ctrip.dcs.vehicle.domain.repository.SceneVehicleModelRepository;
import com.ctrip.dcs.vehicle.domain.repository.StandardVehicleModelRepository;
import com.ctrip.dcs.vehicle.domain.repository.VehicleModelGroupRepository;
import com.ctrip.dcs.vehicle.domain.value.SceneVehicleModel;
import com.ctrip.dcs.vehicle.domain.value.StandardVehicleModel;
import com.ctrip.dcs.vehicle.domain.value.VehicleModelGroup;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.language.Language;
import com.ctrip.igt.framework.common.language.LanguageContext;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.open.message.QueryBrandAndVehicleModelResponseType;
import com.ctrip.igt.open.message.VehicleModelDTO;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RunWith(PowerMockRunner.class)
@PrepareForTest({LanguageContext.class,Language.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class EnumRepositoryImplTest {
    @InjectMocks
    private EnumRepositoryImpl enumRepository;
    @Mock
    private IGTOpenPubServiceClientProxy igtOpenPubServiceClientProxy;
    @Mock
    PmsProductServiceClientProxy pmsProductServiceClientProxy;
    @Mock
    private SceneVehicleModelRepository vehicleModelRepository;
    @Mock
    ContractRepository contractRepository;
    @Mock
    ServiceProviderRepository serviceProviderRepository;
    @Mock
    PrdDictionaryRepository prdDictionaryRepository;
    @Mock
    private DictionaryRepository dictionaryRepository;
    @Mock
    private CityRepository cityRepository;
    @Mock
    private EnumRepositoryHelper enumRepositoryHelper;

    @Mock
    private DcsScmMerchantServiceClientProxy dcsScmMerchantServiceClientProxy;

    @Mock
    VehicleCoreService vehicleCoreService;

    @Mock
    StandardVehicleModelRepository standardVehicleModelRepository;

    @Mock
    VehicleModelGroupRepository vehicleModelGroupRepository;

    @Before
    public void setUp() throws Exception {

        QueryBrandAndVehicleModelResponseType queryBrandAndVehicleModelResponseType = new QueryBrandAndVehicleModelResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE);
        queryBrandAndVehicleModelResponseType.setResponseResult(responseResult);
        List<VehicleModelDTO> modelList = Lists.newArrayList();
        VehicleModelDTO vehicleModelDTO = new VehicleModelDTO();
        vehicleModelDTO.setId(1L);
        vehicleModelDTO.setGroupName("经济型");
        vehicleModelDTO.setModelName("经济5");
        modelList.add(vehicleModelDTO);
        queryBrandAndVehicleModelResponseType.setModelList(modelList);
        Mockito.when(igtOpenPubServiceClientProxy.queryBrandAndVehicleModel(Mockito.any())).thenReturn(queryBrandAndVehicleModelResponseType);
        Mockito.when(vehicleModelRepository.findOne(1L, 1L, "")).thenReturn(SceneVehicleModel.builder().build());
        Mockito.when(vehicleModelRepository.findOne(1L, null, null)).thenReturn(SceneVehicleModel.builder().build());
    }

    @Test
    public void getOptionalIntendVehicleType() {
        VehicleCategoryRelationContract contract = new VehicleCategoryRelationContract();
        contract.setRelationCode("1");
        contract.setSourceVehicleCategoryCode("117");
        QueryVehicleCategoryRelationsResponse relation =
          new QueryVehicleCategoryRelationsResponse();
        relation.setData(Lists.newArrayList(contract));
        Mockito.when(standardVehicleModelRepository.findMany(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(
          StandardVehicleModel.builder().id(117L).vehicleModelGroupId(111L).translationName("经济型5座").build()));
        Mockito.when(vehicleModelGroupRepository.findMany(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(
          VehicleModelGroup.builder().groupId(111L).groupName("经济型group").build()));
        Mockito.when(vehicleCoreService.queryVehicleCategoryRelations(Mockito.any())).thenReturn(relation);
        Map<Long, String> optionalIntendVehicleType = enumRepository.getOptionalIntendVehicleType(2L, 119L, true);
        Assert.assertTrue("经济型group-经济型5座".equals(optionalIntendVehicleType.get(117L)));
    }

    @Test
    public void queryCheckStatusMap() throws Exception {
        GetSystemDictionaryRequestType requestType = new GetSystemDictionaryRequestType();
        GetSystemDictionaryResponseType responseType = new GetSystemDictionaryResponseType();
//        Mockito.when(pmsProductServiceClientProxy.getSystemDictionary(requestType)).thenReturn(responseType);
        Map<Integer, String> optionalIntendVehicleType = enumRepository.queryCheckStatusMap();
        Assert.assertTrue(optionalIntendVehicleType!=null);

    }

    @Test
    public void getApplyStatusName() throws Exception {
        GetSystemDictionaryRequestType requestType = new GetSystemDictionaryRequestType();
        GetSystemDictionaryResponseType responseType = new GetSystemDictionaryResponseType();
//        Mockito.when(pmsProductServiceClientProxy.getSystemDictionary(requestType)).thenReturn(responseType);
        String string = enumRepository.getApplyStatusName(2);
        Assert.assertTrue(string==null);
    }

    @Test
    public void getVehicleTypeTest() {
        enumRepository.getVehicleType(0L);
        SceneVehicleModel vehicleModel = enumRepository.getVehicleType(1L);
        Assert.assertTrue(vehicleModel!=null);
    }

    @Test
    public void getContractRepository() {
        List<Contract> contractList = Lists.newArrayList();
        Contract contract = Contract.newBuilder().withSupplierId(1L).withServiceProviderId(1L).build();
        contractList.add(contract);
        QueryBasicContractResponseType responseType = new QueryBasicContractResponseType();
        responseType.setContracts(Lists.newArrayList());
        Mockito.when(dcsScmMerchantServiceClientProxy.queryBasicContract(Mockito.any())).thenReturn(responseType);
        Map<Long, List<Contract>> map = enumRepository.getContractRepository();
        Assert.assertTrue(map!=null);
    }

    @Test
    public void getServiceProviderByContractId(){
        Contract contract = Contract.newBuilder().withId(1L).withServiceProviderId(1L).build();
        Mockito.when(contractRepository.findOne(1L)).thenReturn(contract);
        ServiceProvider serviceProvider = ServiceProvider.newBuilder().withBrandName("1").build();
        Mockito.when(serviceProviderRepository.findOne(1L)).thenReturn(serviceProvider);
        ServiceProvider serviceProvider1 = enumRepository.getServiceProviderByContractId(1L);
        Assert.assertTrue(serviceProvider!=null);
    }

    @Test
    public void getDicItemV(){
        List<PrdDictionary> itemDTOList = Lists.newArrayList();
        PrdDictionary prdDictionary =  PrdDictionary.newBuilder().withCode("111").withItemKey("1").withItemValue("11").build();
        itemDTOList.add(prdDictionary);
        Mockito.when(prdDictionaryRepository.findByCode("111", "")).thenReturn(itemDTOList);
        String str = enumRepository.getDicItemV(1,"111");
        Assert.assertTrue(str!=null);
    }

    @Test
    public void getColorKey(){
        com.ctrip.dcs.common.sdk.value.Dictionary dictionary = com.ctrip.dcs.common.sdk.value.Dictionary.newBuilder().dictKey("11")
          .build();
        Mockito.when(dictionaryRepository.findOne(1L)).thenReturn(dictionary);
        String str = enumRepository.getColorKey(1L);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void getQueryByCityIds()throws Exception{
        Mockito.when(enumRepositoryHelper.getLocaleCode()).thenReturn("zh");
        Map<Long,City> cityMap = new HashMap<>();
        Mockito.when(cityRepository.findMany(Mockito.any(),Mockito.any())).thenReturn(cityMap);
        List<City> cityList = enumRepository.queryByCityIds(Arrays.asList(1L));
        Assert.assertTrue(cityList.size() == 0);
        cityMap.put(1L,new City());
        cityList = enumRepository.queryByCityIds(Arrays.asList(1L));
        Assert.assertTrue(cityList.size() == 1);
    }

    @Test
    public void isNotChinaMainLand(){
        boolean result = enumRepository.isNotChinaMainLand(1L);
        Assert.assertTrue(!result);
    }

    @Test
    public void getQCityIdByCCityId(){
        String result = enumRepository.getQCityIdByCCityId(1L);
        Assert.assertTrue(result == null);
    }
    @Test
    public void getCityNameSplt(){
        String name = enumRepository.getCityNameSplt(Arrays.asList(1L,2L));
        Assert.assertTrue(name == "");
        City city = City.builder().id(1L).translationName("zhangSan").build();
        City city2 = City.builder().id(2L).translationName("lisi").build();
        Map<Long,City> cityMap = new HashMap<>();
        cityMap.put(1L,city);
        cityMap.put(2L,city2);
        Mockito.when(cityRepository.findMany(Mockito.any(),Mockito.any())).thenReturn(cityMap);
        name = enumRepository.getCityNameSplt(Arrays.asList(1L,2L));
        Assert.assertTrue(name != null);
    }
    @Test
    public void getAreaScope(){
        int result = enumRepository.getAreaScope(1L);
        Assert.assertTrue(result == 0);
    }
    @Test
    public void getCityName(){
        String result = enumRepository.getCityName(1L);
        Assert.assertTrue(result == "");
        City city = City.builder().id(1L).translationName("beiJing").build();
        Mockito.when(cityRepository.findOne(Mockito.any(),Mockito.any())).thenReturn(city);
        result = enumRepository.getCityName(1L);
        Assert.assertTrue(result != null);
    }
    @Test
    public void getCityById(){
        City city = enumRepository.getCityById(1L);
        Assert.assertTrue(city == null);
    }
}
