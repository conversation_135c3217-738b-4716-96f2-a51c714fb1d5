package com.ctrip.dcs.tms.transport.infrastructure.common.parser;

import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ChangeRecordAttributeNameQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.TmsModContent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DriverDispatchTmsModRecordParserTest {
    @InjectMocks
    DriverDispatchTmsModRecordParser parser;
    @Mock
    private ChangeRecordAttributeNameQconfig qconfig;
    @Mock
    private EnumRepository enumRepository;

    @Test
    public void initModContent() {
        List<TmsModContent> modContentList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("supplier_id");
        tmsModContent.setOriginalValue("1");
        tmsModContent.setChangeValue("0");
        modContentList.add(tmsModContent);
        tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("active");
        tmsModContent.setOriginalValue("1");
        tmsModContent.setChangeValue("0");
        modContentList.add(tmsModContent);
        Mockito.when(qconfig.getSharkKeyRecordMap()).thenReturn(Maps.newHashMap());
        List<TmsModContentVO>  contentVOS = parser.initModContent(modContentList);
        Assert.assertTrue(!contentVOS.isEmpty());
    }
}
