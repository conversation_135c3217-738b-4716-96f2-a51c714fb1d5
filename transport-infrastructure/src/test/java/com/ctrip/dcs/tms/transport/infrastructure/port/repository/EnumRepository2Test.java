package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.ctrip.dcs.common.sdk.repository.DictionaryRepository;
import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.PrdDictionaryRepository;
import com.ctrip.dcs.scm.sdk.domain.SupplierRepository;
import com.ctrip.dcs.scm.sdk.domain.supplier.Supplier;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.IGTOpenPubServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PmsProductServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryImpl;
import com.ctrip.dcs.vehicle.domain.repository.VehicleBrandRepository;
import com.ctrip.dcs.vehicle.domain.repository.VehicleSeriesRepository;
import com.ctrip.dcs.vehicle.domain.value.VehicleBrand;
import com.ctrip.dcs.vehicle.domain.value.VehicleSeries;
import com.ctrip.igt.bizcomponent.basicdata.station.StationRepository;

/**
 * <AUTHOR>
 * @Date 2020/6/19 16:02
 */
@RunWith(MockitoJUnitRunner.class)
public class EnumRepository2Test {

    @InjectMocks
    private EnumRepositoryImpl enumRepository;

    @Mock
    SupplierRepository supplierRepository;

    @Mock
    private VehicleBrandRepository brandRepository;

    @Mock
    private VehicleSeriesRepository carRepository;

    @Mock
    PmsProductServiceClientProxy pmsProductServiceClientProxy;
    @Mock
    PrdDictionaryRepository prdDictionaryRepository;

    @Mock
    EnumRepositoryHelper helper;

    @Mock
    ProductionLineUtil productionLineUtil;

    @Mock
    ContractRepository contractRepository;

    @Mock
    StationRepository stationRepository;

    @Mock
    IGTOpenPubServiceClientProxy igtOpenPubServiceClientProxy;

    @Mock
    DictionaryRepository dictionaryRepository;

    @Test
    public void getSupplierName() {
        Supplier supplier = Supplier.newBuilder().withId(10L).withName("test").build();
//        Mockito.when(supplierRepository.findOne(Mockito.anyLong(), Mockito.anyString())).thenReturn(supplier);
        String supplierName = enumRepository.getSupplierName(10L);
        String supplierName1 = enumRepository.getSupplierName(0L);
        Assert.assertTrue(supplierName != null);
        Assert.assertTrue(supplierName1 != null);
    }

    @Test
    public void getBandName() {
        VehicleBrand brand = VehicleBrand.builder().brandId(10L).brandName("test").build();
//        Mockito.when(brandRepository.findOne(Mockito.anyLong(), Mockito.anyString())).thenReturn(brand);
        String bandName = enumRepository.getBandName(10L);
        String bandName1 = enumRepository.getBandName(0L);
        Assert.assertTrue(bandName != null);
        Assert.assertTrue(bandName1 != null);
    }

    @Test
    public void getVehicleSeriesName() {
        VehicleSeries car = VehicleSeries.builder().seriesId(10L).seriesName("test").build();
//        Mockito.when(carRepository.findOne(Mockito.anyLong(), Mockito.anyString())).thenReturn(car);
        String vehicleSeriesName = enumRepository.getVehicleSeriesName(10L);
        String vehicleSeriesName1 = enumRepository.getVehicleSeriesName(0L);
        Assert.assertTrue(vehicleSeriesName1!=null);
    }

    @Test
    public void testHasDrvStr() throws Exception {
//        Mockito.when(pmsProductServiceClientProxy.getSystemDictionary(Mockito.any(GetSystemDictionaryRequestType.class))).thenReturn(new GetSystemDictionaryResponseType());
        String str = enumRepository.getHasDrvStr(true);
        Assert.assertTrue(str==null);
    }

    @Test
    public void getUsingNatureValue() throws Exception {
        String str = enumRepository.getUsingNatureValue(1);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void getDrvStatusName() throws Exception {
        Map<Integer, String> str = enumRepository.getDrvStatusName();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getDrvFrom() throws Exception {
        Map<Integer, String> str = enumRepository.getDrvFrom();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getTakeOrderLimitTimeMap() throws Exception {
        Map<Integer, String> str = enumRepository.getTakeOrderLimitTimeMap();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getTransportGroupMode() throws Exception {
        Map<Integer, String> str = enumRepository.getTransportGroupMode();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getRecruitingApproverStatus() throws Exception {
        Map<Integer, String> str = enumRepository.getRecruitingApproverStatus();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getDrvCoopMode() throws Exception {
        Map<Integer, String> str = enumRepository.getDrvCoopMode();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getTransportGroupStatusMap() throws Exception {
        Map<Integer, String> str = enumRepository.getTransportGroupStatusMap();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getVehicleStatus() throws Exception {
        Map<Integer, String> str = enumRepository.getVehicleStatus();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void queryCheckStatusMap() throws Exception {
        Map<Integer, String> str = enumRepository.queryCheckStatusMap();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void queryTagsStatusMap() throws Exception {
        Map<Integer, String> str = enumRepository.queryTagsStatusMap();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getApproveNodeName() throws Exception {
        Map<Integer, String> str = enumRepository.getApproveNodeName();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getApproveEnentType() throws Exception {
        Map<Integer, String> str = enumRepository.getApproveEnentType();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }

    @Test
    public void getTransportApproveStatus() throws Exception {
        Map<Integer, String> str = enumRepository.getTransportApproveStatus();
        Assert.assertTrue(MapUtils.isEmpty(str));
    }
}
