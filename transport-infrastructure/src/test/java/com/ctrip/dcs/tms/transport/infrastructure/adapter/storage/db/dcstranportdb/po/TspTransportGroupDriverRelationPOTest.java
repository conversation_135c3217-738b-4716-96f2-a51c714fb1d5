package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TspTransportGroupDriverRelationPOTest {

    @InjectMocks
    TspTransportGroupDriverRelationPO relationPO;

    @Test
    public void getWorkShiftId() {
        Long long1 = relationPO.getWorkShiftId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setWorkShiftId() {
        relationPO.setWorkShiftId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getApplyStatus() {
        Integer integer = relationPO.getApplyStatus();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setApplyStatus() {
        relationPO.setApplyStatus(1);
        Assert.assertTrue(true);
    }
}
