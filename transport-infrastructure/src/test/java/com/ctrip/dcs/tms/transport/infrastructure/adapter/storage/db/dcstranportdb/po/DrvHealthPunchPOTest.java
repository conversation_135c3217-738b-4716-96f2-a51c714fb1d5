package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvHealthPunchPOTest {

    @InjectMocks
    DrvHealthPunchPO punchPO;

    @Test
    public void getId() {
        Long long1 = punchPO.getId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setId() {
        punchPO.setId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getDrvId() {
        Long long1 = punchPO.getDrvId();
        Assert.assertTrue(long1 == null);
    }

    @Test
    public void setDrvId() {
        punchPO.setDrvId(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void getHealthCode() {
        Integer integer = punchPO.getHealthCode();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setHealthCode() {
        punchPO.setHealthCode(1);

    }

    @Test
    public void getHealthCodeImg() {
       String str =  punchPO.getHealthCodeImg();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setHealthCodeImg() {
        punchPO.setHealthCodeImg("11");
        Assert.assertTrue(true);
    }

    @Test
    public void getTemperature() {
        Integer integer = punchPO.getTemperature();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setTemperature() {
        punchPO.setTemperature(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getVehicleDisinfection() {
        Integer integer = punchPO.getVehicleDisinfection();
        Assert.assertTrue(integer == null);
    }

    @Test
    public void setVehicleDisinfection() {
        punchPO.setVehicleDisinfection(1);
        Assert.assertTrue(true);
    }

    @Test
    public void getDatachangeCreatetime() {
        Timestamp timestamp =  punchPO.getDatachangeCreatetime();
        Assert.assertTrue(timestamp == null);
    }

    @Test
    public void setDatachangeCreatetime() {
        punchPO.setDatachangeCreatetime(DateUtil.getNow());
        Assert.assertTrue(true);
    }

    @Test
    public void getDatachangeLasttime() {
        Timestamp timestamp =  punchPO.getDatachangeLasttime();
        Assert.assertTrue(timestamp == null);
    }

    @Test
    public void setDatachangeLasttime() {
        punchPO.setDatachangeLasttime(DateUtil.getNow());
        Assert.assertTrue(true);
    }

    @Test
    public void getCreateUser() {
        String str = punchPO.getCreateUser();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setCreateUser() {
        punchPO.setCreateUser("11");
        Assert.assertTrue(true);
    }

    @Test
    public void getModifyUser() {
        String str = punchPO.getModifyUser();
        Assert.assertTrue(str == null);
    }

    @Test
    public void setModifyUser() {
        punchPO.setModifyUser("11");
        Assert.assertTrue(true);
    }

    @Test
    public void tests() {
        punchPO.getPlaceQRCode();
        Boolean b = punchPO.getShowPlaceQRCode();
        Assert.assertTrue(b == null);
    }

    @Test
    public void tests1() {
        punchPO.setAntigenReport(1);
        punchPO.setNucleicReport(1);
        punchPO.setAntigenReportImg("11");
        punchPO.setNucleicReportImg("111");
        punchPO.getAntigenReport();
        punchPO.getNucleicReport();
        punchPO.getNucleicReportImg();
        String str = punchPO.getNucleicReportImg();
        Assert.assertTrue(str != null);
    }
}
