package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;

import java.util.*;

//dao mock example

@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, DrvDrvierRepositoryImpl.class, DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class DrvDrvierRepositoryTest {

    @Test
    public void queryDrvByOnlineAndfreeze() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<DrvDriverPO> pos = relationRepository.queryDrvByOnlineAndfreeze(Arrays.asList(353935L),Arrays.asList(1),1,10);
        Assert.assertTrue(pos.size() == 0);
    }

    @Test
    public void queryDrvBySupplierIdAndIdTest() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        List<DrvDriverPO> pos = relationRepository.queryDrvBySupplierIdAndId(Arrays.asList(353935L),10L);
        Assert.assertTrue(pos.size() == 0);
    }


    @Test
    public void queryDrvResourceByConditionTest() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        QueryDrvResourceConditionDTO drvResourceConditionDTO = QueryDrvResourceConditionDTO.newCondition().withDrvCoopModeList(Lists.newArrayList()).
                withDrvIdList(Lists.newArrayList(1L)).withDrvCoopModeList(Lists.newArrayList(1)).withCityIdList(Lists.newArrayList(1L)).withDrvNameList(Lists.newArrayList("w")).withDrvStatusList(Lists.newArrayList(1)).
                withDriverPhoneList(Lists.newArrayList("w")).withSupplierIdList(Lists.newArrayList(1L)).withPaginator(new PaginatorDTO(1,10)).withProLineIdList(Lists.newArrayList(1)).build();
        List<DrvDriverPO> pos = relationRepository.queryDrvResourceByCondition(drvResourceConditionDTO);
        Assert.assertTrue(pos.size() == 0);
    }

    @Test
    public void queryDrvResourceCountByConditionTest() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DriverGroupRelationPO()));
        QueryDrvResourceConditionDTO drvResourceConditionDTO = QueryDrvResourceConditionDTO.newCondition().
                withDrvIdList(Lists.newArrayList(1L)).
                withDrvCoopModeList(Lists.newArrayList(1)).
                withCityIdList(Lists.newArrayList(1L)).
                withDrvNameList(Lists.newArrayList("w")).
                withDrvStatusList(Lists.newArrayList(1)).
                withDriverPhoneList(Lists.newArrayList("w")).
                withSupplierIdList(Lists.newArrayList(1L)).
                withPaginator(new PaginatorDTO(1,10)).
                withBoundaryDrvId(1L).withCountryIdList(Lists.newArrayList(1L)).withVehicleTypeIdList(Lists.newArrayList(1L)).
                withProLineIdList(Lists.newArrayList(1)).build();
        int count = relationRepository.queryDrvResourceCountByCondition(drvResourceConditionDTO);
        Assert.assertTrue(count == 0);
    }

    @Test
    public void queryDriverIdTest() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        List<DrvDriverPO> resList = relationRepository.queryDriverId(Lists.newArrayList("111"));
        Assert.assertTrue(resList.size() == 0);
    }

    @Test
    public void queryDriverIdTest1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        List<DrvDriverPO> resList = relationRepository.queryDriverId(Lists.newArrayList());
        Assert.assertTrue(resList.size() == 0);
    }

    @Test
    public void queryRecentDateDrvListByDriverIdFromAndPage() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        List<DrvDriverPO> resList = relationRepository.queryRecentDateDrvListByDriverIdFromAndPage(1, 1, new Date());
        Assert.assertTrue(resList.size() == 0);
    }

    @Test
    public void getDrvDriverPoList() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        FreeSelectSqlBuilder sqlBuilder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(sqlBuilder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        List<DrvDriverPO> resList = relationRepository.getDrvDriverPoList(Lists.newArrayList(1L));
        Assert.assertTrue(resList == null);
    }

    @Test
    public void testCountDrvDirtyPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        int count = relationRepository.countDrvDirtyPhone();
        Assert.assertTrue(count == 0);
    }

    @Test
    public void testQueryDrvDirtyPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        List<DrvDriverPO> pos = relationRepository.queryDrvDirtyPhone(1,10);
        Assert.assertTrue(pos.size() == 0);
    }

    @Test
    public void testQueryOneDrvByPhone() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        DrvDriverPO pos = relationRepository.queryOneDrvByPhone("111");
        Assert.assertTrue(pos == null);
    }

    @Test
    public void testQueryOfficialDriverIdBySupplierIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        DrvDrvierRepositoryImpl relationRepository = new DrvDrvierRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new DrvDriverPO()));
        List<Long> resList = relationRepository.queryOfficialDriverIdBySupplierIds(Arrays.asList(1L));
        Assert.assertTrue(resList.size() == 0);
    }


}
