package com.ctrip.dcs.tms.transport.infrastructure.common.util

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.QunarUserDto
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.clogging.LoggerFactory
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SharkUtils.class, LoggerFactory.class, TmsTransUtil.class,TmsServiceProvider.class])
@PowerMockIgnore(["javax.management.*", "javax.script.*", "javax.xml.*", "org.xml.*", "org.w3c.dom.*", "jdk.xml.*", "com.sun.org.apache.*"])
class RedisUtilsTest extends Specification {

    RedisUtils redisUtils = new RedisUtils()

    //初始化静态类mock
    void setup(){
        def loggerMock = Mock(Logger)
        PowerMockito.mockStatic(LoggerFactory.class)
        PowerMockito.mockStatic(TmsTransUtil.class)
        PowerMockito.mockStatic(SharkUtils.class)
        PowerMockito.mockStatic(TmsServiceProvider.class)
        PowerMockito.when(LoggerFactory.getLogger(Mockito.anyString())).thenReturn(loggerMock)
        PowerMockito.when(TmsTransUtil.decrypt(Mockito.any(),Mockito.any())).thenReturn("")
        PowerMockito.when(SharkUtils.getSharkValue(Mockito.anyString(),Mockito.anyString())).thenReturn("")
    }

    def "test remove"(){
        given:
        Set<String> set = new HashSet<>()
        set.add("11")
        when:
        def res = redisUtils.remove(set)
        then:
        res != null
    }

    def "test mGet"(){
        given:
        Set<String> set = new HashSet<>()
        set.add("11")
        when:
        def res = redisUtils.mGet(set)
        then:
        res != null
    }
}
