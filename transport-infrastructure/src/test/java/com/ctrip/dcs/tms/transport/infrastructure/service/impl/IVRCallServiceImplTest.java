package com.ctrip.dcs.tms.transport.infrastructure.service.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.model.CallPhoneForVerifyRequestType;
import com.ctrip.model.CallPhoneForVerifyResponseType;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisUtils.class, TmsTransUtil.class})
@PowerMockIgnore({"javax.management.*", "javax.crypto.*"})
public class IVRCallServiceImplTest {

    @InjectMocks
    private IVRCallServiceImpl ivrCallService;

    @Mock
    private DriverDomainService driverDomainService;

    @Mock
    private EnumRepositoryHelper enumRepositoryHelper;

    @Mock
    private CommonConfig commonConfig;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(RedisUtils.class);
        PowerMockito.mockStatic(TmsTransUtil.class);
    }

    /**
     * 测试场景：Redis中不存在taskId，新发起IVR呼叫
     */
    @Test
    public void testCallPhoneForVerify_NewCall() {
        // 准备测试数据
        String mobilePhone = "13800138000";
        String countryCode = "86";
        String channel = "tms_driver_register";
        Long expectedTaskId = 1234567890L;

        // 模拟Redis中不存在taskId
        PowerMockito.when(RedisUtils.get(Mockito.anyString())).thenReturn(null);

        // 模拟DriverDomainService返回
        CallPhoneForVerifyResponseType responseType = new CallPhoneForVerifyResponseType();
        responseType.setCallTaskId(expectedTaskId);
        Mockito.when(driverDomainService.callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class)))
                .thenReturn(responseType);

        // 模拟其他依赖
        Mockito.when(enumRepositoryHelper.getLocaleCode()).thenReturn("zh-CN");
        Mockito.when(commonConfig.getIvrTaskIdValidityMinutes()).thenReturn(10);
        Mockito.when(commonConfig.getIvrRedisTtlHours()).thenReturn(24);
        PowerMockito.when(TmsTransUtil.encrypt(Mockito.anyString(), Mockito.any())).thenReturn("encrypted");

        // 调用被测方法
        Long result = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, channel, "zh-CN");

        // 验证结果
        Assert.assertEquals(expectedTaskId, result);
        // 验证是否调用了Redis.set保存新的IVRTaskInfo
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.times(1));
        RedisUtils.set(Mockito.anyString(), Mockito.eq(24 * 60 * 60), Mockito.any(IVRCallServiceImpl.IVRTaskInfo.class));
    }

    /**
     * 测试场景：Redis中存在taskId，且在有效期内，IVR结果为success
     */
    @Test
    public void testCallPhoneForVerify_ExistingTaskSuccess() {
        // 准备测试数据
        String mobilePhone = "13800138000";
        String countryCode = "86";
        String channel = "tms_driver_register";
        Long existingTaskId = 1234567890L;

        // 创建有效的IVRTaskInfo（当前时间-5分钟）
        IVRCallServiceImpl.IVRTaskInfo taskInfo = new IVRCallServiceImpl.IVRTaskInfo(
                existingTaskId, System.currentTimeMillis() - 5 * 60 * 1000L);

        // 模拟Redis中存在taskId
        PowerMockito.when(RedisUtils.get(Mockito.anyString())).thenReturn(taskInfo);

        // 模拟IVR结果查询返回success
        QueryCallPhoneForVerifyResultResponseType resultResponseType = new QueryCallPhoneForVerifyResultResponseType();
        resultResponseType.setCallResultStatus("success");
        Mockito.when(driverDomainService.queryCallPhoneForVerifyResult(Mockito.any(QueryCallPhoneForVerifyResultRequestType.class)))
                .thenReturn(resultResponseType);
        Mockito.when(commonConfig.getCallResultStatus()).thenReturn(Arrays.asList("success"));
        Mockito.when(commonConfig.getIvrTaskIdValidityMinutes()).thenReturn(10);

        // 调用被测方法
        Long result = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, channel, "zh-CN");

        // 验证结果
        Assert.assertEquals(existingTaskId, result);
        // 验证没有调用Redis.del
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.never());
        RedisUtils.del(Mockito.anyString());
        // 验证没有发起新的IVR呼叫
        Mockito.verify(driverDomainService, Mockito.never()).callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class));
    }

    /**
     * 测试场景：Redis中存在taskId，但已过期
     */
    @Test
    public void testCallPhoneForVerify_ExistingTaskExpired() {
        // 准备测试数据
        String mobilePhone = "13800138000";
        String countryCode = "86";
        String channel = "tms_driver_register";
        Long existingTaskId = 1234567890L;
        Long newTaskId = 9876543210L;

        // 创建过期的IVRTaskInfo（当前时间-15分钟，超过10分钟有效期）
        IVRCallServiceImpl.IVRTaskInfo expiredTaskInfo = new IVRCallServiceImpl.IVRTaskInfo(
                existingTaskId, System.currentTimeMillis() - 15 * 60 * 1000L);

        // 模拟Redis中存在过期的taskId
        PowerMockito.when(RedisUtils.get(Mockito.anyString())).thenReturn(expiredTaskInfo);

        // 模拟新的IVR呼叫
        CallPhoneForVerifyResponseType responseType = new CallPhoneForVerifyResponseType();
        responseType.setCallTaskId(newTaskId);
        Mockito.when(driverDomainService.callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class)))
                .thenReturn(responseType);

        // 模拟其他依赖
        Mockito.when(enumRepositoryHelper.getLocaleCode()).thenReturn("zh-CN");
        Mockito.when(commonConfig.getIvrTaskIdValidityMinutes()).thenReturn(10);
        Mockito.when(commonConfig.getIvrRedisTtlHours()).thenReturn(24);
        PowerMockito.when(TmsTransUtil.encrypt(Mockito.anyString(), Mockito.any())).thenReturn("encrypted");

        // 调用被测方法
        Long result = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, channel, "zh-CN");

        // 验证结果
        Assert.assertEquals(newTaskId, result);
        // 验证调用了Redis.del删除过期taskId
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.times(1));
        RedisUtils.del(Mockito.anyString());
        // 验证发起了新的IVR呼叫
        Mockito.verify(driverDomainService, Mockito.times(1)).callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class));
        // 验证保存了新的taskId
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.times(1));
        RedisUtils.set(Mockito.anyString(), Mockito.eq(24 * 60 * 60), Mockito.any(IVRCallServiceImpl.IVRTaskInfo.class));
    }

    /**
     * 测试场景：Redis中存在taskId，在有效期内但IVR结果为fail
     */
    @Test
    public void testCallPhoneForVerify_ExistingTaskFail() {
        // 准备测试数据
        String mobilePhone = "13800138000";
        String countryCode = "86";
        String channel = "tms_driver_register";
        Long existingTaskId = 1234567890L;
        Long newTaskId = 9876543210L;

        // 创建有效的IVRTaskInfo
        IVRCallServiceImpl.IVRTaskInfo taskInfo = new IVRCallServiceImpl.IVRTaskInfo(
                existingTaskId, System.currentTimeMillis() - 5 * 60 * 1000L);

        // 模拟Redis中存在taskId
        PowerMockito.when(RedisUtils.get(Mockito.anyString())).thenReturn(taskInfo);

        // 模拟IVR结果查询返回fail
        QueryCallPhoneForVerifyResultResponseType resultResponseType = new QueryCallPhoneForVerifyResultResponseType();
        resultResponseType.setCallResultStatus("fail");
        Mockito.when(driverDomainService.queryCallPhoneForVerifyResult(Mockito.any(QueryCallPhoneForVerifyResultRequestType.class)))
                .thenReturn(resultResponseType);
        Mockito.when(commonConfig.getCallResultStatus()).thenReturn(Arrays.asList("success"));
        Mockito.when(commonConfig.getIvrTaskIdValidityMinutes()).thenReturn(10);

        // 模拟新的IVR呼叫
        CallPhoneForVerifyResponseType responseType = new CallPhoneForVerifyResponseType();
        responseType.setCallTaskId(newTaskId);
        Mockito.when(driverDomainService.callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class)))
                .thenReturn(responseType);

        // 模拟其他依赖
        Mockito.when(enumRepositoryHelper.getLocaleCode()).thenReturn("zh-CN");
        Mockito.when(commonConfig.getIvrRedisTtlHours()).thenReturn(24);
        PowerMockito.when(TmsTransUtil.encrypt(Mockito.anyString(), Mockito.any())).thenReturn("encrypted");

        // 调用被测方法
        Long result = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, channel, "zh-CN");

        // 验证结果
        Assert.assertEquals(newTaskId, result);
        // 验证调用了Redis.del删除旧taskId
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.times(1));
        RedisUtils.del(Mockito.anyString());
        // 验证发起了新的IVR呼叫
        Mockito.verify(driverDomainService, Mockito.times(1)).callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class));
        // 验证保存了新的taskId
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.times(1));
        RedisUtils.set(Mockito.anyString(), Mockito.eq(24 * 60 * 60), Mockito.any(IVRCallServiceImpl.IVRTaskInfo.class));
    }

    /**
     * 测试场景：Redis中存在taskId，在有效期内，IVR结果为pending
     */
    @Test
    public void testCallPhoneForVerify_ExistingTaskPending() {
        // 准备测试数据
        String mobilePhone = "13800138000";
        String countryCode = "86";
        String channel = "tms_driver_register";
        Long existingTaskId = 1234567890L;

        // 创建有效的IVRTaskInfo
        IVRCallServiceImpl.IVRTaskInfo taskInfo = new IVRCallServiceImpl.IVRTaskInfo(
                existingTaskId, System.currentTimeMillis() - 5 * 60 * 1000L);

        // 模拟Redis中存在taskId
        PowerMockito.when(RedisUtils.get(Mockito.anyString())).thenReturn(taskInfo);

        // 模拟IVR结果查询返回pending（空状态）
        QueryCallPhoneForVerifyResultResponseType resultResponseType = new QueryCallPhoneForVerifyResultResponseType();
        resultResponseType.setCallResultStatus("");
        Mockito.when(driverDomainService.queryCallPhoneForVerifyResult(Mockito.any(QueryCallPhoneForVerifyResultRequestType.class)))
                .thenReturn(resultResponseType);
        Mockito.when(commonConfig.getIvrTaskIdValidityMinutes()).thenReturn(10);

        // 调用被测方法
        Long result = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, channel, "zh-CN");

        // 验证结果
        Assert.assertEquals(existingTaskId, result);
        // 验证没有调用Redis.del
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.never());
        RedisUtils.del(Mockito.anyString());
        // 验证没有发起新的IVR呼叫
        Mockito.verify(driverDomainService, Mockito.never()).callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class));
    }

    /**
     * 测试场景：参数无效，返回null
     */
    @Test
    public void testCallPhoneForVerify_InvalidParams() {
        // 准备测试数据 - 空手机号
        String mobilePhone = "";
        String countryCode = "86";
        String channel = "tms_driver_register";

        // 调用被测方法
        Long result = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, channel, "zh-CN");

        // 验证结果
        Assert.assertNull(result);
        // 验证没有进行任何Redis或RPC调用
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.never());
        RedisUtils.get(Mockito.anyString());
        Mockito.verify(driverDomainService, Mockito.never()).queryCallPhoneForVerifyResult(Mockito.any());
        Mockito.verify(driverDomainService, Mockito.never()).callPhoneForVerify(Mockito.any());
    }

    /**
     * 测试场景：IVR呼叫失败，返回null
     */
    @Test
    public void testCallPhoneForVerify_CallFailed() {
        // 准备测试数据
        String mobilePhone = "13800138000";
        String countryCode = "86";
        String channel = "tms_driver_register";

        // 模拟Redis中不存在taskId
        PowerMockito.when(RedisUtils.get(Mockito.anyString())).thenReturn(null);

        // 模拟DriverDomainService返回null或没有taskId
        CallPhoneForVerifyResponseType responseType = new CallPhoneForVerifyResponseType();
        responseType.setCallTaskId(null);
        Mockito.when(driverDomainService.callPhoneForVerify(Mockito.any(CallPhoneForVerifyRequestType.class)))
                .thenReturn(responseType);

        // 模拟其他依赖
        Mockito.when(enumRepositoryHelper.getLocaleCode()).thenReturn("zh-CN");
        PowerMockito.when(TmsTransUtil.encrypt(Mockito.anyString(), Mockito.any())).thenReturn("encrypted");

        // 调用被测方法
        Long result = ivrCallService.callPhoneForVerify(mobilePhone, countryCode, channel, "zh-CN");

        // 验证结果
        Assert.assertNull(result);
        // 验证没有调用Redis.set
        PowerMockito.verifyStatic(RedisUtils.class, Mockito.never());
        RedisUtils.set(Mockito.anyString(), Mockito.anyInt(), Mockito.any());
    }
} 
