package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.api.model.QueryDiscardVehicleListSOARequestType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.*;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;

import java.util.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DalQueryDao.class, VehicleRepositoryImpl.class, DalTableDao.class})
@PowerMockIgnore({"javax.management.*","javax.crypto.*"})
public class VehicleRepositoryImplTest {

    @Test
    public void queryVehicleRecruitingList() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(1);
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setVehicleId(1L);
        vehiclePO.setCityId(1L);
        try {
            int i = vehicleRepository.batchUpdateVehicle(ImmutableList.of(vehiclePO));
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void countIllegalCertiDateVehicleTest() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList());
        List<VehVehiclePO> i = vehicleRepository.queryIllegalCertiDateVehicle(ImmutableList.of(),1,1);
        Assert.assertTrue(i.size() == 0);
    }

    @Test
    public void countAllVehVehicleByIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList());
        try {
            int i = vehicleRepository.countAllVehVehicleByIds(Arrays.asList(1L),1);
            Assert.assertTrue(i == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryAllVehVehicleByIds() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList());
        try {
            List<VehVehiclePO> vehiclePOList = vehicleRepository.queryAllVehVehicleByIds(Arrays.asList(1L),1,1,50);
            Assert.assertTrue(vehiclePOList.size() == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void updateDrvImgQToC() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,parameters,hints)).thenReturn(0);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList());
        try {
            VehVehiclePO vehVehiclePO  = new VehVehiclePO();
            vehVehiclePO.setVehicleId(1L);
            vehVehiclePO.setNetTansCtfctImg("qunarzz.com111");
            vehVehiclePO.setVehicleCertiImg("qunarzz.com111");
            vehVehiclePO.setVehicleFullImg("qunarzz.com111");
            vehVehiclePO.setVehicleFrontImg("111qunarzz.com");
            vehVehiclePO.setVehicleBackImg("111qunarzz.com");
            vehVehiclePO.setVehicleTrunkImg("111qunarzz.com");
            int count  = vehicleRepository.updateDrvImgQToC(vehVehiclePO);
            Assert.assertTrue(count == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void updateDrvImgQToC1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,parameters,hints)).thenReturn(0);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList());
        try {
            VehVehiclePO vehVehiclePO  = new VehVehiclePO();
            vehVehiclePO.setVehicleId(1L);
            vehVehiclePO.setNetTansCtfctImg("qunarzz.com111");
            int count  = vehicleRepository.updateDrvImgQToC(vehVehiclePO);
            Assert.assertTrue(count == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryCacheDrvListTest() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        sqlStr.append("select 1 from dual;");
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            List<VehVehiclePO> list = vehicleRepository.queryVehInfo4Cache(Sets.newHashSet(1L));
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void queryVehByVehicleLicense() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        sqlStr.append("select 1 from dual;");
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            List<VehVehiclePO> list = vehicleRepository.queryVehByVehicleLicense("1");
            Assert.assertTrue(list.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void queryCountQunarImgList() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(builder,parameters,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            int count = vehicleRepository.queryCountQunarImgList(Arrays.asList(1L));
            Assert.assertTrue( count == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryQunarImgList() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(builder,parameters,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            List<VehVehiclePO> list = vehicleRepository.queryQunarImgList(Arrays.asList(1L),1,1);
            Assert.assertTrue( list.size() == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void countDiscardVeh() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            QueryDiscardVehicleListSOARequestType requestType = new QueryDiscardVehicleListSOARequestType();
            requestType.setSupplierId(1L);
            requestType.setVehicleLicense("111");
            int  list = vehicleRepository.countDiscardVeh(requestType);
            Assert.assertTrue( list == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void countDiscardVeh1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().count(builder,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            QueryDiscardVehicleListSOARequestType requestType = new QueryDiscardVehicleListSOARequestType();
            requestType.setSupplierId(1L);
            int  list = vehicleRepository.countDiscardVeh(requestType);
            Assert.assertTrue( list == 0);
        }catch (Exception e){

        }
    }


    @Test
    public void queryDiscardVeh() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            QueryDiscardVehicleListSOARequestType requestType = new QueryDiscardVehicleListSOARequestType();
            requestType.setSupplierId(1L);
            requestType.setVehicleLicense("111");
            PaginatorDTO paginatorDTO = new PaginatorDTO();
            paginatorDTO.setPageNo(1);
            paginatorDTO.setPageSize(1);
            requestType.setPaginator(paginatorDTO);
            List<VehVehiclePO>  list = vehicleRepository.queryDiscardVeh(requestType);
            Assert.assertTrue( list.size() == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryDiscardVeh1() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        SelectSqlBuilder builder = PowerMockito.mock(SelectSqlBuilder.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getDao().query(builder,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            QueryDiscardVehicleListSOARequestType requestType = new QueryDiscardVehicleListSOARequestType();
            requestType.setSupplierId(1L);
            PaginatorDTO paginatorDTO = new PaginatorDTO();
            paginatorDTO.setPageNo(1);
            paginatorDTO.setPageSize(1);
            requestType.setPaginator(paginatorDTO);
            List<VehVehiclePO>  list = vehicleRepository.queryDiscardVeh(requestType);
            Assert.assertTrue( list.size() == 0);
        }catch (Exception e){

        }
    }

    @Test
    public void queryDiscardTemVeh() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeSelectSqlBuilder builder = PowerMockito.mock(FreeSelectSqlBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        sqlStr.append("select 1 from dual;");
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().query(builder,parameters,hints)).thenReturn(Lists.newArrayList());
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            List<Long> list = vehicleRepository.queryDiscardTemVeh("1");
            List<Long> list1 = vehicleRepository.queryDiscardTemVeh("");
            Assert.assertTrue(!list.isEmpty());
            Assert.assertTrue(list1.isEmpty());
        }catch (Exception e){

        }
    }

    @Test
    public void updateTemporaryDispatchMark() throws Exception {
        DalRepositoryImpl dalRepository = PowerMockito.mock(DalRepositoryImpl.class);
        PowerMockito.whenNew(DalDefaultJpaMapper.class).withAnyArguments().thenReturn(Mockito.mock(DalDefaultJpaMapper.class));
        PowerMockito.whenNew(DalRepositoryImpl.class).withAnyArguments().thenReturn(dalRepository);
        VehicleRepositoryImpl vehicleRepository = new VehicleRepositoryImpl();
        DalQueryDao dao = PowerMockito.mock(DalQueryDao.class);
        DalTableDao dalTableDao = PowerMockito.mock(DalTableDao.class);
        DalHints hints = PowerMockito.mock(DalHints.class);
        FreeUpdateSqlBuilder builder = PowerMockito.mock(FreeUpdateSqlBuilder.class);
        StatementParameters parameters = PowerMockito.mock(StatementParameters.class);
        StringBuilder sqlStr = PowerMockito.mock(StringBuilder.class);
        sqlStr.append("select 1 from dual;");
        PowerMockito.when(dalRepository.getDao()).thenReturn(dalTableDao);
        PowerMockito.when(dalRepository.getQueryDao()).thenReturn(dao);
        PowerMockito.when(dalRepository.getQueryDao().update(builder,parameters,hints)).thenReturn(1);
        PowerMockito.when(dao.query(Mockito.any(), Mockito.any(StatementParameters.class), Mockito.any(DalHints.class))).thenReturn(Lists.newArrayList(new VehVehiclePO()));
        try {
            int count = vehicleRepository.updateTemporaryDispatchMark(Arrays.asList(1L),true,"111");
            int count1 = vehicleRepository.updateTemporaryDispatchMark(Lists.newArrayList(),true,"111");
            Assert.assertTrue(count >= 0);
            Assert.assertTrue(count1 >= 0);
        }catch (Exception e){

        }
    }

}
