package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import org.apache.dubbo.common.utils.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.internal.util.collections.Sets;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Set;

@RunWith(MockitoJUnitRunner.class)
public class BaseUtilTest {

    @InjectMocks
    private BaseUtil baseUtil;

    @Test
    public void toCacheKeyListTest() {
        String prefix = "prefixFirst";
        Set<Long> idSet = Sets.newSet(1L, 2L, 3L);
        List<String> cacheKeyList = baseUtil.toCacheKeyList(prefix, idSet);
        Assert.assertTrue(CollectionUtils.isNotEmpty(cacheKeyList));
        Assert.assertTrue(cacheKeyList.size() == idSet.size());
        Assert.assertTrue(cacheKeyList.contains("prefixFirst1"));
        Assert.assertTrue(cacheKeyList.contains("prefixFirst2"));
        Assert.assertTrue(cacheKeyList.contains("prefixFirst3"));
    }


}