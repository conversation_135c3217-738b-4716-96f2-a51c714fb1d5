package com.ctrip.dcs.tms.transport.infrastructure.common.cache;

import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SessionHolderTest {

    @InjectMocks
    private SessionHolder sessionHolder;

    @Before
    public void ready() {
        sessionHolder.setSessionSource(ImmutableMap.of(SessionHolder.REQ_HEAD_ACCOUNT_ID, "1", SessionHolder.REQ_HEAD_ACCOUNT_TYPE, "2", SessionHolder.REQ_HEAD_ACCOUNT_NAME, "zs"));
    }

    @Test
    public void getRestSessionAccountTypeTest() {
        String accountType = sessionHolder.getRestSessionAccountType();
        Assert.assertTrue("2".equals(accountType));
    }

    @Test
    public void getRestSessionAccountIdTest() {
        String accountId = sessionHolder.getRestSessionAccountId();
        Assert.assertTrue("1".equals(accountId));
    }

    @Test
    public void getRestSessionAccountNameTest() {
        String accountName = sessionHolder.getRestSessionAccountName();
        Assert.assertTrue("zs".equals(accountName));
    }

    @Test
    public void badCase() {
        sessionHolder.setSessionSource(null);
        String accountName = sessionHolder.getRestSessionAccountName();
        Assert.assertTrue("".equals(accountName));
    }

}