package com.ctrip.dcs.tms.transport.infrastructure.common.parser;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ChangeRecordAttributeNameQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.TmsModContent;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class VehicleTmsModRecordParserTest {

    @InjectMocks
    VehicleTmsModRecordParser parser;

    @Mock
    private ChangeRecordAttributeNameQconfig qconfig;

    @Mock
    private EnumRepository enumRepository;

    @Mock
    private ProductionLineUtil productionLineUtil;

    @Test
    public void initModContent() {
        List<TmsModContent> modContentList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("category_synthesize_code");
        tmsModContent.setOriginalValue("1");
        tmsModContent.setChangeValue("4");
        modContentList.add(tmsModContent);
        List<TmsModContentVO>  contentVOS = parser.initModContent(modContentList);
        Assert.assertTrue(!contentVOS.isEmpty());
    }

    @Test
    public void initModContent1() {
        List<TmsModContent> modContentList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("category_synthesize_code");
        tmsModContent.setOriginalValue("");
        tmsModContent.setChangeValue("");
        modContentList.add(tmsModContent);
        List<TmsModContentVO>  contentVOS = parser.initModContent(modContentList);
        Assert.assertTrue(!contentVOS.isEmpty());
    }

    @Test
    public void initModContent4AuditStatus() {
        List<TmsModContent> modContentList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("audit_status");
        tmsModContent.setOriginalValue("1");
        tmsModContent.setChangeValue("2");
        modContentList.add(tmsModContent);
        List<TmsModContentVO>  contentVOS = parser.initModContent(modContentList);
        Assert.assertTrue(!contentVOS.isEmpty());
    }

    @Test
    public void temporary_dispatch_mark() {
        List<TmsModContent> modContentList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("temporary_dispatch_mark");
        tmsModContent.setOriginalValue("1");
        tmsModContent.setChangeValue("2");
        modContentList.add(tmsModContent);
        List<TmsModContentVO>  contentVOS = parser.initModContent(modContentList);
        Assert.assertTrue(!contentVOS.isEmpty());
    }

    @Test
    public void temporary_dispatch_mark2() {
        List<TmsModContent> modContentList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("temporary_dispatch_mark");
        tmsModContent.setOriginalValue("");
        tmsModContent.setChangeValue("");
        modContentList.add(tmsModContent);
        List<TmsModContentVO>  contentVOS = parser.initModContent(modContentList);
        Assert.assertTrue(!contentVOS.isEmpty());
    }

}
