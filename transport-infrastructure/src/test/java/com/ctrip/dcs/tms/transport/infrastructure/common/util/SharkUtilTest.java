package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PlatformUtil.class,  SharkUtils.class})
@PowerMockIgnore({"javax.management.*", "javax.script.*", "com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*",
        "org.w3c.dom.*", "javax.crypto.*"})
public class SharkUtilTest {
    @Test
    public void test(){
        PowerMockito.mockStatic(SharkUtils.class);
        Mockito.when(SharkUtils.getSharkValueDefault(Mockito.anyString())).thenReturn("1");
        String result = SharkUtil.getSalesModeName(0);
        Assert.assertTrue("1".equals(result));
        result = SharkUtil.getSalesModeName(1);
        Assert.assertTrue("1".equals(result));
        result = SharkUtil.getSalesModeName(2);
        Assert.assertTrue("1".equals(result));
        result = SharkUtil.getSalesModeName(5);
        Assert.assertTrue("1".equals(result));
        result = SharkUtil.getSalesModeName(6);
        Assert.assertTrue("".equals(result));
    }
}
