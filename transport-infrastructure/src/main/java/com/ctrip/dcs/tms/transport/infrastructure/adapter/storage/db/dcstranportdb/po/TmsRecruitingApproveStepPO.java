package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2021-08-17
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_recruiting_approve_step")
public class TmsRecruitingApproveStepPO implements DalPojo {

    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 审批来源ID(司机ID,车辆ID)
     */
    @Column(name = "approve_source_id")
    @Type(value = Types.BIGINT)
    private Long approveSourceId;

    /**
     * 审批来源类型(1.司机,2.车辆)
     */
    @Column(name = "approve_type")
    @Type(value = Types.TINYINT)
    private Integer approveType;

    /**
     * 审批单项(1.身份与背景信息,2.驾驶证信息,3.网约车人证信息,4.疫苗信息,5.核酸信息,6.司机基础信息,7.行驶证信息,8.网约车车证信息,9.车辆基础信息)
     */
    @Column(name = "approve_item")
    @Type(value = Types.INTEGER)
    private Integer approveItem;

    /**
     * 审批状态(0.待审核,1.审批通过,2.审批不通过)
     */
    @Column(name = "approve_status")
    @Type(value = Types.INTEGER)
    private Integer approveStatus;

    /**
     * 操作角色(1.供应商,2.BD)
     */
    @Column(name = "approve_from")
    @Type(value = Types.TINYINT)
    private Integer approveFrom;

    /**
     * 审批原因
     */
    @Column(name = "approve_reason")
    @Type(value = Types.VARCHAR)
    private String approveReason;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 修改人
     */
    @Column(name = "modify_user")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    /**
     * 最终通过状态(0-不通过,1-通过)
     */
    @Column(name = "final_pass_status")
    @Type(value = Types.TINYINT)
    private Integer finalPassStatus;

    /**
     * 审批时间
     */
    @Column(name = "approve_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp approveTime;

    public Integer getFinalPassStatus() {
        return finalPassStatus;
    }

    public void setFinalPassStatus(Integer finalPassStatus) {
        this.finalPassStatus = finalPassStatus;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getApproveSourceId() {
        return approveSourceId;
    }

    public void setApproveSourceId(Long approveSourceId) {
        this.approveSourceId = approveSourceId;
    }

    public Integer getApproveType() {
        return approveType;
    }

    public void setApproveType(Integer approveType) {
        this.approveType = approveType;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveFrom() {
        return approveFrom;
    }

    public void setApproveFrom(Integer approveFrom) {
        this.approveFrom = approveFrom;
    }

    public String getApproveReason() {
        return approveReason;
    }

    public void setApproveReason(String approveReason) {
        this.approveReason = approveReason;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Timestamp getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Timestamp approveTime) {
        this.approveTime = approveTime;
    }
}
