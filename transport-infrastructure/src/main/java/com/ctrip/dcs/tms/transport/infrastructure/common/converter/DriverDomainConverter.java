package com.ctrip.dcs.tms.transport.infrastructure.common.converter;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.driver.domain.account.AccountDetailDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;

public class DriverDomainConverter {
  public static DrvDriverPO buildAccount2DrvDriverPO(Long drvId, AccountDetailDTO accountDetailDTO) {
    DrvDriverPO drvDriverPO = new DrvDriverPO();
    drvDriverPO.setDrvId(drvId);
    drvDriverPO.setIgtCode(accountDetailDTO.getCountryCode());
    drvDriverPO.setDrvPhone(TmsTransUtil.encrypt(accountDetailDTO.getPhoneNumber(), KeyType.Phone));
    drvDriverPO.setEmail(TmsTransUtil.encrypt(accountDetailDTO.getEmail(), KeyType.Mail));
    drvDriverPO.setDrvIdcard(accountDetailDTO.getIdCardNo());
    drvDriverPO.setPaiayAccount(accountDetailDTO.getPayoneerAccountId());
    drvDriverPO.setPpmAccount(accountDetailDTO.getPpmAccountId());
    drvDriverPO.setDrvName(accountDetailDTO.getName());
    drvDriverPO.setModifyUser(accountDetailDTO.getModifyUser());
    return drvDriverPO;
  }
}
