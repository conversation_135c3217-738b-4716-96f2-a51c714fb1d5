package com.ctrip.dcs.tms.transport.infrastructure.common.aspect;

import com.ctrip.framework.ucs.client.ShardingKey;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.unidal.cat.traceContext.TraceContext;

import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/03 14:43
 * @Description: 供应链调用消息平台短信和邮件发送接口 补全Requestfrom、UDL参数统一处理
 * @see <a href="http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********">消息平台UDL治理</a>
 */
@Aspect
@Component
public class IgtMsgEmailUdlRequestAspect {

    private static final Logger logger = LoggerFactory.getLogger(IgtMsgEmailUdlRequestAspect.class);

    private static final String UDL_DEFAULT_VALUE = "CN_90001";
    private static final String REQUEST_FROM_DEFAULT_VALUE = "CTRIP";

    /**
     * 发送短信和邮件方法
     */
    @Pointcut("execution(* com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.InfrastructureServiceClientProxy.sendEmail(..)) " +
            "|| execution(* com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.InfrastructureServiceClientProxy.sendMessage(..)) " +
            "|| execution(* com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.InfrastructureServiceClientProxy.sendMessageByPhone(..)) ")
    private void methodEnter() {
    }

    @Around("methodEnter()")
    public Object igtMsgEmailUdl(ProceedingJoinPoint joinPoint) throws Throwable {
        TraceContext traceContext = Cat.getTraceContext(true);
        if (Objects.nonNull(traceContext) && Objects.isNull(traceContext.get(ShardingKey.UDL.getTraceContextKey()))) {
            traceContext.add(ShardingKey.UDL.getTraceContextKey(), UDL_DEFAULT_VALUE);
            traceContext.add(ShardingKey.RequestFrom.getTraceContextKey(), REQUEST_FROM_DEFAULT_VALUE);
        }

        return joinPoint.proceed();
    }
}
