package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsUdlMapPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.UdlMapRepository;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2025/6/5
 */
@Repository
public class UdlMapRepositoryImpl implements UdlMapRepository {

  private DalRepository<TmsUdlMapPO> repository;

  public UdlMapRepositoryImpl() {
    repository = new DalRepositoryImpl<>(TmsUdlMapPO.class);
  }

  @Override
  public int insert(TmsUdlMapPO po) throws SQLException {
    return repository.insert(new DalHints(), po);
  }

}
