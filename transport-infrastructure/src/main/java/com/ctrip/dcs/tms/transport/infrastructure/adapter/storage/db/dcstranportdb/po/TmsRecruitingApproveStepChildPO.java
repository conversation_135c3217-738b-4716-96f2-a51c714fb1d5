package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2021-08-17
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_recruiting_approve_step_child")
public class TmsRecruitingApproveStepChildPO implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 单项ID
     */
    @Column(name = "recruiting_approve_step_id")
    @Type(value = Types.BIGINT)
    private Long recruitingApproveStepId;

    /**
     * 各子项的标签项
     */
    @Column(name = "child_item")
    @Type(value = Types.TINYINT)
    private Integer childItem;

    /**
     * BD审核状态(0.请打标签,1.通过,3.不通过)
     */
    @Column(name = "check_status")
    @Type(value = Types.TINYINT)
    private Integer checkStatus;


    /**
     * 供应商审核状态(0.请打标签,1.通过,3.不通过)
     */
    @Column(name = "supplier_check_status")
    @Type(value = Types.TINYINT)
    private Integer supplierCheckStatus;

    /**
     * 子项描述
     */
    @Column(name = "child_desc")
    @Type(value = Types.VARCHAR)
    private String childDesc;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 变更时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @Type(value = Types.VARCHAR)
    private String createUser;

    /**
     * 操作人
     */
    @Column(name = "modify_user")
    @Type(value = Types.VARCHAR)
    private String modifyUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecruitingApproveStepId() {
        return recruitingApproveStepId;
    }

    public void setRecruitingApproveStepId(Long recruitingApproveStepId) {
        this.recruitingApproveStepId = recruitingApproveStepId;
    }

    public Integer getChildItem() {
        return childItem;
    }

    public void setChildItem(Integer childItem) {
        this.childItem = childItem;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getChildDesc() {
        return childDesc;
    }

    public void setChildDesc(String childDesc) {
        this.childDesc = childDesc;
    }

    public Timestamp getDatachangeCreatetime() {
        return datachangeCreatetime;
    }

    public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
        this.datachangeCreatetime = datachangeCreatetime;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public Integer getSupplierCheckStatus() {
        return supplierCheckStatus;
    }

    public void setSupplierCheckStatus(Integer supplierCheckStatus) {
        this.supplierCheckStatus = supplierCheckStatus;
    }
}
