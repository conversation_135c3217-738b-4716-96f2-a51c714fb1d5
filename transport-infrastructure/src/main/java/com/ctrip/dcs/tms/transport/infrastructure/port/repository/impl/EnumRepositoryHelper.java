package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.igt.framework.common.language.Language;
import com.ctrip.igt.framework.common.language.LanguageContext;
import org.springframework.stereotype.Component;

@Component
public class EnumRepositoryHelper {
    /**
     * 查询上下文的语言
     * @return
     */
    public String getLocaleCode(){
        Language language = LanguageContext.getLanguage();
        if(language == null){
            return "zh-CN";
        }
        return language.getLocaleCode();
    }
}
