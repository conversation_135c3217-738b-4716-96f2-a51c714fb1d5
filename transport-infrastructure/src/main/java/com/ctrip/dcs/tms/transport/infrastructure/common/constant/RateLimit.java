package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import com.google.common.util.concurrent.*;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * 默认配置会在服务启动前由配置文件重新加载替换
 */
public class RateLimit {

    /**
     * 访问DB限流 依据配置：DriverCacheQconfig 变化
     * */
    public static RateLimiter dbLimiter = RateLimiter.create(15, 3, TimeUnit.SECONDS);

    /**
     * 访问Cache限流 依据配置：DriverCacheQconfig 变化
     * */
    public static RateLimiter cacheLimiter = RateLimiter.create(30, 1, TimeUnit.SECONDS);

    /**
     * 访问Cache 依据配置：DriverCacheQconfig 变化
     * */
    public static RateLimiter fusingLimiter = RateLimiter.create(200, 1, TimeUnit.SECONDS);

}