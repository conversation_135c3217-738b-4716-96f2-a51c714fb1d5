package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.dal.*;

import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/17 14:11
 */
public interface InOrderConfigRepository {

    DalRepository<TspIntoOrderConfigPO> getTspIntoOrderConfigRepo();

    /**
     * 新增进单配置
     * @param intoOrderConfigPOList
     * @return
     */
    int addInOrderConfig(List<TspIntoOrderConfigPO> intoOrderConfigPOList);

    /**
     * 更新进单配置
     * @param intoOrderConfigPOList
     * @return
     */
    void updateInOrderConfig(List<TspIntoOrderConfigPO> intoOrderConfigPOList);

    /**
     * 查询进单配置
     * @param transportGroupId
     * @param active
     * @return
     */
    List<TspIntoOrderConfigPO> queryInOrderConfigs(Long transportGroupId,Integer active);

    /**
     * 查询进单配置
     * @param transportGroupIds
     * @param active
     * @return
     */
    List<TspIntoOrderConfigPO> queryInOrderConfigs(List<Long> transportGroupIds, Integer active);

    int queryCountTspIntoOrderConfigAll(Long id);

    List<TspIntoOrderConfigPO> queryTspIntoOrderConfigAll(Long id ,Integer pageNo,Integer pageSize);

    int updateIntoOrderCounryId(Long id,Long countryId) throws SQLException;

    /**
     * 查询进单配置信息
     * @param transportGroupIds
     * @param active
     * @param cityId
     * @param locationCode
     * @return
     * @throws Exception
     */
    List<TspIntoOrderConfigPO> queryInOrderConfigs(List<Long> transportGroupIds,Integer active,Long cityId,String locationCode)throws SQLException;

}
