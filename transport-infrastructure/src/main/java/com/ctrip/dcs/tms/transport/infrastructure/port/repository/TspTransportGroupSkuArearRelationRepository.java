package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;

import java.util.*;

public interface TspTransportGroupSkuArearRelationRepository {

    /**
     * 通过运力组查询商品区域
     * @param transportGroupId
     * @return
     */
    List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuInfoList(Long transportGroupId);

    /**
     * 查询运力组商品区域
     * @param queryModel
     * @return
     */
    List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuInfos(QueryTransportGroupSkuInfoModel queryModel);

    List<Long> querySkuBindInfo(List<Long> skuIds, Integer groupStatus);

    List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuInfoListBySkuIdList(List<Long> skuIdList);

    List<TspTransportGroupSkuAreaRelationPO> queryTransportGroupSkuIds(QueryTransportGroupSkuInfoModel queryModel);
}