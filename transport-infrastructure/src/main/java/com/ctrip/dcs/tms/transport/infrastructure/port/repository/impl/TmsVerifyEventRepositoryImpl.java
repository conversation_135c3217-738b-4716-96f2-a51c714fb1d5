package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.Date;
import java.util.*;

/**
 *
 */
@Repository(value = "tmsVerifyEventRepository")
public class TmsVerifyEventRepositoryImpl implements TmsVerifyEventRepository {

    private DalRepository<TmsVerifyEventPO> tmsVerifyEventRepo;

    private DalRowMapper<TmsVerifyEventPO> tmsVerifyEventPODalRowMapper;

    public TmsVerifyEventRepositoryImpl() throws SQLException {
        tmsVerifyEventRepo = new DalRepositoryImpl<>(TmsVerifyEventPO.class);
        this.tmsVerifyEventPODalRowMapper = new DalDefaultJpaMapper<>(TmsVerifyEventPO.class);

    }
    @Override
    public TmsVerifyEventPO queryByPk(Long id) {
        return tmsVerifyEventRepo.queryByPk(id);
    }

    @Override
    public long insert(TmsVerifyEventPO verifyEventPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        tmsVerifyEventRepo.insert(new DalHints(), keyHolder, verifyEventPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public List<TmsVerifyEventPO> queryWaitVerifyEvent(List<Long> verifySourceId, Integer verifyType, Integer verifyStatus, Integer verifyReasonStatus, Boolean verifyTimeFlag) {
        try {
            if(CollectionUtils.isEmpty(verifySourceId)){
                return Collections.emptyList();
            }
            DalHints hints = DalHints.createIfAbsent(null);

            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.in("verify_source_id", verifySourceId, Types.BIGINT);
            sqlBuilder.and();
            sqlBuilder.equal("verify_status", verifyStatus, Types.INTEGER);
            if(verifyReasonStatus!=null){
                sqlBuilder.and();
                sqlBuilder.equal("verify_reason_status", verifyReasonStatus, Types.INTEGER);
            }
            if (verifyType != null) {
                sqlBuilder.and();
                sqlBuilder.equal("verify_type", verifyType, Types.INTEGER);
            }
            if(verifyTimeFlag){
                sqlBuilder.and().lessThanEquals("verify_start_time",DateUtil.getNow(),Types.TIMESTAMP);
            }
            return tmsVerifyEventRepo.getDao().query(sqlBuilder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int batchUpdate(List<TmsVerifyEventPO> eventPOList) {
        int[] count =  tmsVerifyEventRepo.batchUpdate(eventPOList);
        return count.length;
    }

    @Override
    public List<TmsVerifyEventPO> queryVerifyedEvent(Long verifySourceId, Integer verifyType, Integer verifyReasonStatus) {

        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<TmsVerifyEventPO>> builder = new FreeSelectSqlBuilder<>();
        StringBuilder sqlStr = new StringBuilder("select * from tms_verify_event where verify_source_id= ? and verify_reason_status=? and verify_type= ?  and date_format(verify_start_time,'%Y-%m-%d') = ?");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.set(i++, "verify_source_id", Types.BIGINT, verifySourceId);
        parameters.set(i++, "verify_reason_status", Types.INTEGER, verifyReasonStatus);
        parameters.set(i++, "verify_type", Types.INTEGER, verifyType);
        parameters.set(i++, "verify_start_time", Types.VARCHAR, DateUtil.dateToString(new Date(), DateUtil.YYYYMMDD));
        builder.setTemplate(sqlStr.toString());
        try {
            builder.mapWith(tmsVerifyEventPODalRowMapper);
            return tmsVerifyEventRepo.getQueryDao().query(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateVerifyEventCheckTime(Long id, String verifyCheckTime,String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update tms_verify_event set verify_start_time = ?,modify_user=? where id=? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "verify_start_time", Types.TIMESTAMP, DateUtil.string2Timestamp(verifyCheckTime, DateUtil.YYYYMMDDHHMMSS));
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser) ? TmsTransportConstant.TMS_DEFAULT_USERNAME : modifyUser);
        parameters.setSensitive(i++, "id", Types.BIGINT, id);
        return tmsVerifyEventRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int updatemsVerifyEventResult(Long verifySourceId, Integer verifyType, String verifyResult, String failReason, String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update tms_verify_event set verify_time = ?,verify_result_code = ?,fail_reason=?,modify_user=? where verify_source_id=? and verify_type=? and verify_status=1 and verify_flag=2 ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "verify_time", Types.TIMESTAMP, DateUtil.getNow());
        parameters.setSensitive(i++, "verify_result_code", Types.VARCHAR, StringUtils.isEmpty(verifyResult)?"":verifyResult);
        parameters.setSensitive(i++, "fail_reason", Types.VARCHAR, StringUtils.isEmpty(failReason)?"":failReason);
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser) ? TmsTransportConstant.TMS_DEFAULT_USERNAME : modifyUser);
        parameters.setSensitive(i++, "verify_source_id", Types.BIGINT, verifySourceId);
        parameters.setSensitive(i++, "verify_type", Types.INTEGER, verifyType);

        return tmsVerifyEventRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<TmsVerifyEventPO> queryVerifyEventByIds(List<Long> ids) {
        try {
            if(CollectionUtils.isEmpty(ids)){
                return Collections.emptyList();
            }
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.in("id", ids, Types.BIGINT);
            return tmsVerifyEventRepo.getDao().query(sqlBuilder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateVerifyEventByIds(List<Long> ids,String verifyResult, String failReason, String modifyUser) throws SQLException {
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update tms_verify_event set verify_flag = 1,verify_time = ?,verify_result_code = ?,fail_reason=?,verify_status=?,modify_user=? where id in (?) ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "verify_time", Types.TIMESTAMP, DateUtil.getNow());
        parameters.setSensitive(i++, "verify_result_code", Types.VARCHAR, verifyResult);
        parameters.setSensitive(i++, "fail_reason", Types.VARCHAR, StringUtils.isEmpty(failReason)?"":failReason);
        parameters.setSensitive(i++, "verify_status", Types.INTEGER, TmsTransportConstant.VerifyStatusEnum.VERIFYED.getCode());
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser) ? TmsTransportConstant.TMS_DEFAULT_USERNAME : modifyUser);
        parameters.setInParameter(i++, "id", Types.BIGINT, ids);

        return tmsVerifyEventRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<TmsVerifyEventPO> queryVerifyedEventBySourceId(Long verifySourceId, Integer verifyType, Integer verifyReasonStatus) {
        try {
            if(verifySourceId == null){
                return Collections.emptyList();
            }
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.equal("verify_source_id", verifySourceId, Types.BIGINT);
            sqlBuilder.and().equal("verify_type",verifyType,Types.INTEGER);
            sqlBuilder.and().equal("verify_reason_status",verifyReasonStatus,Types.INTEGER);
            sqlBuilder.orderBy("id",false);
            return tmsVerifyEventRepo.getDao().query(sqlBuilder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsVerifyEventPO> queryVerifyedEventBySourceIdOneMonth(Long verifySourceId, Integer verifyType, Integer verifyReasonStatus) {
        try {
            if(verifySourceId == null){
                return Collections.emptyList();
            }
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.equal("verify_source_id", verifySourceId, Types.BIGINT);
            sqlBuilder.and().equal("verify_type",verifyType,Types.INTEGER);
            sqlBuilder.and().equal("verify_reason_status",verifyReasonStatus,Types.INTEGER);
            sqlBuilder.and().greaterThanEquals("verify_start_time", DateUtil.dayDisplacement(new Date(),-30), Types.TIMESTAMP);
            sqlBuilder.orderBy("id",false);
            return tmsVerifyEventRepo.getDao().query(sqlBuilder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}
