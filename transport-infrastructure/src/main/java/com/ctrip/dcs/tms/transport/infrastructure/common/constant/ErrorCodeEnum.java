package com.ctrip.dcs.tms.transport.infrastructure.common.constant;


import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DRIVER_MOBILE_CAN_NOT_EMPTY;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DRIVER_MOBILE_CAN_NOT_START_WITH_0;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DRIVER_MOBILE_NOT_VALID;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DRIVER_MOBILE_NOT_VERIFIED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DRIVER_NAME_NOT_MATCH_LICENSE_NAME;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DRIVER_STATUS_NOT_SUPPORT_FREEZE;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.DRIVER_UPDATE_ACCOUNT_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.GENERATE_VEHICLE_GLOBAL_ID_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.SUPPLIER_CITY_CATEGORY_NOT_MATCH;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.TRANSPORT_DRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.TRANSPORT_DRIVER__CREATE_DRIVER_ACCOUNT_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.UN_SUPPORT_DRIVER_DISCARD;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.UN_SUPPORT_VEHICLE_DISCARD;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.UN_SUPPORT_VEHICLE_LICENSE_EDIT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.drvUnfreezeAndStatusError;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.transportDrvisEmpty;

public enum ErrorCodeEnum {

  TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_FAILED("1515000", TRANSPORT_DRIVER__CREATE_DRIVER_ACCOUNT_FAILED),
  TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED("1515001", TRANSPORT_DRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED),
  TRANSPORT_GENERATE_VEHICLE_GLOBAL_ID_FAILED("1515002", GENERATE_VEHICLE_GLOBAL_ID_FAILED),
  TRANSPORT_UN_SUPPORT_DRIVER_DISCARD("1515003", UN_SUPPORT_DRIVER_DISCARD),
  TRANSPORT_UN_SUPPORT_VEHICLE_DISCARD("1515004", UN_SUPPORT_VEHICLE_DISCARD),
  TRANSPORT_UN_SUPPORT_VEHICLE_LICENSE_EDIT("1515005", UN_SUPPORT_VEHICLE_LICENSE_EDIT),
  TRANSPORT_DRIVER_MOBILE_CAN_NOT_EMPTY("1515006", DRIVER_MOBILE_CAN_NOT_EMPTY),
  TRANSPORT_DRIVER_MOBILE_NOT_VALID("1515007", DRIVER_MOBILE_NOT_VALID),
  TRANSPORT_DRIVER_MOBILE_CAN_NOT_START_WITH_0("1515008", DRIVER_MOBILE_CAN_NOT_START_WITH_0),
  TRANSPORT_DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID("1515009", DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID),
  TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH("1515010", SUPPLIER_CITY_CATEGORY_NOT_MATCH),
  TRANSPORT_DRIVER_NAME_NOT_MATCH_LICENSE_NAME("1515011", DRIVER_NAME_NOT_MATCH_LICENSE_NAME),

  //更新司机账号失败
  TRANSPORT_DRIVER_UPDATE_ACCOUNT_FAILED("1515012", DRIVER_UPDATE_ACCOUNT_FAILED),

  TRANSPORT_DRIVER_STATUS_NOT_SUPPORT_FREEZE("1515013", DRIVER_STATUS_NOT_SUPPORT_FREEZE),
  TRANSPORT_DRIVER_IN_UNFREEZE_STATUS("1515014", drvUnfreezeAndStatusError),
  TRANSPORT_DRIVER_NOT_FOUND("1515015", transportDrvisEmpty),
  TRANSPORT_VEHICLE_NOT_EXISTS("1515016", SharkKeyConstant.transportVehicleIsEmpty),
  TRANSPORT_DRIVER_NOT_EXISTS("1515017", SharkKeyConstant.driverNotExist),
  TRANSPORT_VEHICLE_COLOR_ILLEGAL("1515018", SharkKeyConstant.VEHICLE_OCR_COLOR_ILLEGAL),
  TRANSPORT_VEHICLE_DISPATCH_PHOTO_MUST_NOT_EMPTY("1515019", SharkKeyConstant.VEHICLE_DISPATCH_PHOTO_MUST_NOT_EMPTY),

  SUPPLIER_AUTHENTICATION_FAILED("1515020", SharkKeyConstant.SUPPLIER_AUTHENTICATION_FAILD),
  EMAIL_HAS_ALREADY_BEEN_USED_IN_THE_ACCOUNT_CENTER("1515021", SharkKeyConstant.EMAIL_HAS_ALREADY_BEEN_USED_IN_THE_ACCOUNT_CENTER),
  SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_OTHER_NOT_SHORT_TRANSPORT_GROUP("1515022", SharkKeyConstant.SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_OTHER_NOT_SHORT_TRANSPORT_GROUP),
  SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_SHORT_TRANSPORT_GROUP("1515023", SharkKeyConstant.SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_SHORT_TRANSPORT_GROUP),
  BD_NO_PERMISSION("1515024", SharkKeyConstant.ONLY_SUPER_ADMIN_CAN_OPERATE),
  SUPPLIER_NO_PERMISSION("1515025", SharkKeyConstant.EXIST_BD_OR_PENALTY_OPERATE),
  SEND_SMS_OVER_LIMIT("1515026", SharkKeyConstant.TRANSPORT_SEND_MSG_OVER_LIMIT),
  TRANSPORT_DRIVER_MOBILE_NOT_VERIFIED("1515027", DRIVER_MOBILE_NOT_VERIFIED),
  TRANSPORT_GROUP_DISPATCHER_MOBILE_NOT_VERIFIED("1515028", SharkKeyConstant.TRANSPORT_GROUP_DISPATCHER_MOBILE_NOT_VERIFIED),


  ;
  private String code;
  private String message;

  ErrorCodeEnum(String code, String message) {
    this.code = code;
    this.message = message;
  }

  public String getCode() {
    return code;
  }
  public String getMessage() {
    return message;
  }
}
