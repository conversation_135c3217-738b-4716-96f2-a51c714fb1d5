package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.api.model.RecruitingSOARequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TodoListCountPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehicleRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.CtripCommonUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRecruitingRepository;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalRowMapper;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaMapper;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeSelectSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeUpdateSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.utils.StringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Repository(value = "vehicleRecruitingRepository")
public class VehicleRecruitingRepositoryImpl implements VehicleRecruitingRepository {

    private static final Logger logger = LoggerFactory.getLogger(VehicleRecruitingRepositoryImpl.class);

    private DalRepository<VehicleRecruitingPO> vehicleRecruitingRepo;

    @Autowired
    DriverGroupRelationRepository driverGroupRelationRepository;

    private DalRowMapper<VehicleRecruitingPO> vehicleRecruitingPORowMapper;

    private DalRepository<TodoListCountPO> toduListCountRepo;

    private DalRowMapper<TodoListCountPO> toduListCountPORowMapper;

    public VehicleRecruitingRepositoryImpl() throws SQLException {
        vehicleRecruitingRepo = new DalRepositoryImpl<>(VehicleRecruitingPO.class);
        this.vehicleRecruitingPORowMapper = new DalDefaultJpaMapper<>(VehicleRecruitingPO.class);
        toduListCountRepo = new DalRepositoryImpl<>(TodoListCountPO.class);
        this.toduListCountPORowMapper = new DalDefaultJpaMapper<>(TodoListCountPO.class);
    }

    public DalRepository<VehicleRecruitingPO> getVehicleRecruitingRepo() {
        return vehicleRecruitingRepo;
    }

    @Override
    public VehicleRecruitingPO queryByPK(Long vehicleRecruitingId) {
        return vehicleRecruitingRepo.queryByPk(vehicleRecruitingId);
    }

    @Override
    public Long addVehicleRecruiting(VehicleRecruitingPO po) throws SQLException{
        KeyHolder keyHolder = new KeyHolder();
        vehicleRecruitingRepo.insert(new DalHints(), keyHolder, po);
        return keyHolder.getKey().longValue();
    }

    @Override
    public int update(VehicleRecruitingPO po) throws SQLException {
        return vehicleRecruitingRepo.update(po);
    }

    @Override
    public Integer updateVehicleRecApproverStatus(List<Long> vehicleRecruitingIds, Integer approverStatus, String remark, String modifyUser,Integer checkStatus,Boolean approveTimeFlag,Integer bdTurnDownCount) throws SQLException {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(vehicleRecruitingIds)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        StringBuilder sqlBuilder = new StringBuilder("update vehicle_recruiting set approver_status = ? ,datachange_lasttime = ?,modify_user = ?,remark = ? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "approver_status", Types.INTEGER, approverStatus);
        parameters.setSensitive(i++, "datachange_lasttime", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
        parameters.setSensitive(i++, "remark", Types.VARCHAR, StringUtils.isEmpty(remark)?"":remark);
        if(checkStatus!=null){
            sqlBuilder.append(",check_status = ? ");
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
        }
        if(approveTimeFlag){
            sqlBuilder.append(",approve_time = ? ");
            parameters.setSensitive(i++, "approve_time", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
            sqlBuilder.append(",approve_aging = ? ");
            parameters.setSensitive(i++, "approve_aging", Types.INTEGER, TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
        }
        if(Objects.equals(approverStatus, TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())){
            sqlBuilder.append(",bd_approve_status = ? ");
            parameters.setSensitive(i++, "bd_approve_status", Types.INTEGER, 1);
            sqlBuilder.append(",bd_turn_down_count = ? ");
            parameters.setSensitive(i++, "bd_turn_down_count", Types.INTEGER, bdTurnDownCount + 1);
        }
        if(Objects.equals(approverStatus, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
            sqlBuilder.append(",approve_aging = ? ");
            parameters.setSensitive(i++, "approve_aging", Types.INTEGER, TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
        }
        sqlBuilder.append(" where vehicle_id in (?) ");
        parameters.setInParameter(i++,"vehicle_id",Types.BIGINT, vehicleRecruitingIds);
        builder.setTemplate(sqlBuilder.toString());
        return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<VehicleRecruitingPO> getVehicleRecruitingList(Set<Long> idList) throws SQLException {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.in("vehicle_id", Lists.newArrayList(idList), Types.BIGINT);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        return vehicleRecruitingRepo.getDao().query(builder,hints);
    }


    @Override
    public int countVehicleRecruitingList(RecruitingSOARequestDTO soaRequestDTO,List<Integer> jurisdictionList, List<Integer> productionLineList,Integer accountType,Boolean newOldSwitch) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        if(!newOldSwitch || soaRequestDTO.getQueryVersion() == null){
        builder.equal("vehicle_from", TmsTransportConstant.VehicleFromEnum.Veh_MANUAL.getCode(),Types.INTEGER);
        }
        if (soaRequestDTO.getRecruitingId() != null && soaRequestDTO.getRecruitingId().intValue() != 0) {
            builder.and().equal("vehicle_id",soaRequestDTO.getRecruitingId(),Types.BIGINT);
        }
        if (soaRequestDTO.getVehicleTypeId() != null && soaRequestDTO.getVehicleTypeId().intValue() != 0) {
            builder.and().equal("vehicle_type_id",soaRequestDTO.getVehicleTypeId(),Types.BIGINT);
        }
        if (soaRequestDTO.getCityId() != null) {
            builder.and().equal("city_id",soaRequestDTO.getCityId(),Types.BIGINT);
        }
        if (soaRequestDTO.getSupplierId() != null) {
            builder.and().equal("supplier_id",soaRequestDTO.getSupplierId(),Types.BIGINT);
        }
        if (!Strings.isNullOrEmpty(soaRequestDTO.getVehicleLicense())) {
            builder.and().like("vehicle_license",soaRequestDTO.getVehicleLicense()+"%",Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(soaRequestDTO.getDatachangeCreatetimeStart()) && !Strings.isNullOrEmpty(soaRequestDTO.getDatachangeCreatetimeEnd())) {
            Timestamp datachangeCreatetimeStart = DateUtil.string2Timestamp(soaRequestDTO.getDatachangeCreatetimeStart(),DateUtil.YYYYMMDDHHMMSS);
            Timestamp datachangeCreatetimeEnd = DateUtil.string2Timestamp(soaRequestDTO.getDatachangeCreatetimeEnd(),DateUtil.YYYYMMDDHHMMSS);
            builder.and().between("datachange_createtime",datachangeCreatetimeStart,datachangeCreatetimeEnd,Types.TIMESTAMP);
        }
        if (soaRequestDTO.getCheckStatus() != null && soaRequestDTO.getCheckStatus() > 0) {
            builder.and().equal("check_status",soaRequestDTO.getCheckStatus(),Types.INTEGER);
        }
        if (!CollectionUtils.isEmpty(productionLineList)) {
            builder.and().in("category_synthesize_code",productionLineList,Types.INTEGER);
        }
        if (soaRequestDTO.getApproveSchedule()!=null) {
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
                builder.and().equal("supplier_approve_schedule",soaRequestDTO.getApproveSchedule(),Types.INTEGER);
            }
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                builder.and().equal("approve_schedule",soaRequestDTO.getApproveSchedule(),Types.INTEGER);
            }
        }
        if(soaRequestDTO.getApproveAging()!=null){
            builder.and().equal("approve_aging",soaRequestDTO.getApproveAging(),Types.INTEGER);
        }
        if (soaRequestDTO.getApproveStatus()!=null) {
            builder.and().equal("approver_status",soaRequestDTO.getApproveStatus(),Types.INTEGER);
        }
        if (soaRequestDTO.getSupplierId() == null) {
            builder.and();
            builder.greaterThan("supplier_id", 0L,Types.BIGINT,false);
        }
        builder.and().equal("active", CtripCommonUtils.queryActiveChoose(soaRequestDTO.isActive()),Types.BIT);
        return vehicleRecruitingRepo.getDao().count(builder,hints).intValue();
    }

    @Override
    public List<VehicleRecruitingPO> queryVehicleRecruitingList(RecruitingSOARequestDTO soaRequestDTO, List<Integer> jurisdictionList, PaginatorDTO paginator, List<Integer> productionLineList,Integer accountType,Boolean newOldSwitch) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        if(!newOldSwitch || soaRequestDTO.getQueryVersion() == null){
            builder.equal("vehicle_from", TmsTransportConstant.VehicleFromEnum.Veh_MANUAL.getCode(),Types.INTEGER);
        }
        if (soaRequestDTO.getRecruitingId() != null && soaRequestDTO.getRecruitingId().intValue() != 0) {
            builder.and().equal("vehicle_id",soaRequestDTO.getRecruitingId(),Types.BIGINT);
        }
        if (soaRequestDTO.getVehicleTypeId() != null && soaRequestDTO.getVehicleTypeId().intValue() != 0) {
            builder.and().equal("vehicle_type_id",soaRequestDTO.getVehicleTypeId(),Types.BIGINT);
        }
        if (soaRequestDTO.getCityId() != null) {
            builder.and().equal("city_id",soaRequestDTO.getCityId(),Types.BIGINT);
        }
        if (soaRequestDTO.getSupplierId() != null) {
            builder.and().equal("supplier_id",soaRequestDTO.getSupplierId(),Types.BIGINT);
        }
        if (!Strings.isNullOrEmpty(soaRequestDTO.getVehicleLicense())) {
            builder.and().like("vehicle_license",soaRequestDTO.getVehicleLicense()+"%",Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(soaRequestDTO.getDatachangeCreatetimeStart()) && !Strings.isNullOrEmpty(soaRequestDTO.getDatachangeCreatetimeEnd())) {
            Timestamp datachangeCreatetimeStart = DateUtil.string2Timestamp(soaRequestDTO.getDatachangeCreatetimeStart(),DateUtil.YYYYMMDDHHMMSS);
            Timestamp datachangeCreatetimeEnd = DateUtil.string2Timestamp(soaRequestDTO.getDatachangeCreatetimeEnd(),DateUtil.YYYYMMDDHHMMSS);
            builder.and().between("datachange_createtime",datachangeCreatetimeStart,datachangeCreatetimeEnd,Types.TIMESTAMP);
        }
        if (soaRequestDTO.getCheckStatus() != null && soaRequestDTO.getCheckStatus() > 0) {
            builder.and().equal("check_status",soaRequestDTO.getCheckStatus(),Types.INTEGER);
        }
        if (!CollectionUtils.isEmpty(productionLineList)) {
            builder.and().in("category_synthesize_code",productionLineList,Types.INTEGER);
        }
        if (soaRequestDTO.getApproveSchedule()!=null) {
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
                builder.and().equal("supplier_approve_schedule",soaRequestDTO.getApproveSchedule(),Types.INTEGER);
            }
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                builder.and().equal("approve_schedule",soaRequestDTO.getApproveSchedule(),Types.INTEGER);
            }
        }
        if(soaRequestDTO.getApproveAging()!=null){
            builder.and().equal("approve_aging",soaRequestDTO.getApproveAging(),Types.INTEGER);
        }
        if (soaRequestDTO.getApproveStatus()!=null) {
            builder.and().equal("approver_status",soaRequestDTO.getApproveStatus(),Types.INTEGER);
        }
        if (soaRequestDTO.getSupplierId() == null) {
            builder.and();
            builder.greaterThan("supplier_id", 0L,Types.BIGINT,false);
        }
        builder.and().equal("active", CtripCommonUtils.queryActiveChoose(soaRequestDTO.isActive()),Types.BIT);
        int page = 1;
        int size = 15;
        if (paginator != null) {
            page = paginator.getPageNo();
            size = paginator.getPageSize();
        }
        builder.orderBy("datachange_lasttime",false);
        builder.atPage(page, size);

        return queryPage(builder);
    }

    /**
     * 先查出车辆ID列表再根据车辆ID查出车辆信息
     * @param builder builder
     * @return List<DrvDriverPO>
     * @throws SQLException SQLException
     */
    protected List<VehicleRecruitingPO> queryPage(SelectSqlBuilder builder) throws SQLException {
        builder.select("vehicle_id");
        List<VehicleRecruitingPO> resultList = vehicleRecruitingRepo.getDao().query(builder, DalHints.createIfAbsent(null));
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        SelectSqlBuilder newBuilder = new SelectSqlBuilder();
        newBuilder.selectAll();
        newBuilder.in("vehicle_id", resultList.stream().map(VehicleRecruitingPO::getVehicleId).collect(Collectors.toList()), Types.BIGINT);
        newBuilder.orderBy("datachange_lasttime",false);
        return vehicleRecruitingRepo.getDao().query(newBuilder, DalHints.createIfAbsent(null));
    }

    @Override
    public List<VehicleRecruitingPO> queryVehicleRecruitingBySupplierIdAndId(List<Long> idList, Long supplierId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("vehicle_id", idList, Types.BIGINT);
            builder.and().equal("supplier_id", supplierId, Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehicleRecruitingRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public int updateCheckStatus(List<Long> vehicleRecruitingIds, int checkStatus) throws SQLException {
        if(CollectionUtils.isEmpty(vehicleRecruitingIds)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update vehicle_recruiting set check_status = ?,approve_time = now()  where vehicle_id in (?)");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
        parameters.setInParameter(i++,"vehicle_id",Types.BIGINT, vehicleRecruitingIds);
        return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<TodoListCountPO> todoListCount(Long supplierId) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<TodoListCountPO>> builder = new FreeSelectSqlBuilder<>();
        StringBuilder sqlStr = new StringBuilder("select supplier_id,count(1) count from vehicle_recruiting where approver_status = 0 and acitve = 1 ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        if (supplierId !=null && supplierId >0) {
            sqlStr.append(" AND supplier_id = ? ");
            parameters.set(i++, "supplier_id", Types.BIGINT, supplierId);
        }
        sqlStr.append(" group by supplier_id");
        builder.setTemplate(sqlStr.toString());
        try {
            builder.mapWith(toduListCountPORowMapper);
            return toduListCountRepo.getQueryDao().query(builder, parameters, hints);
        } catch (SQLException e) {
            return Lists.newArrayList();
        }
    }

    @Override
    public int countApproveIngVehicleRecruiting(List<Long> vehicleIds, Integer approveStatus,Integer checkStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        try {
            builder.equal("approver_status",approveStatus,Types.INTEGER);
            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            if(!CollectionUtils.isEmpty(vehicleIds)){
                builder.and().in("vehicle_id",vehicleIds,Types.BIGINT);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehicleRecruitingRepo.getDao().count(builder,hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<VehicleRecruitingPO> queryApproveIngVehicleRecruiting(List<Long> vehicleIds, Integer approveStatus,Integer checkStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        try {
            builder.equal("approver_status",approveStatus,Types.INTEGER);
            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            if(!CollectionUtils.isEmpty(vehicleIds)){
                builder.and().in("vehicle_id",vehicleIds,Types.BIGINT);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            return vehicleRecruitingRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateApproveAging(List<Long> vehicleRecruitingIds, int approveAging) throws SQLException {
        if (CollectionUtils.isEmpty(vehicleRecruitingIds)) {
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update vehicle_recruiting set approve_aging = ?  where vehicle_id in (?)");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "approve_aging", Types.INTEGER, approveAging);
        parameters.setInParameter(i++, "vehicle_id", Types.BIGINT, vehicleRecruitingIds);
        return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int updateVehCheckStatus(Long vehRecruitingId, int checkStatus, String modifyUser) throws SQLException {
        if(vehRecruitingId == null || vehRecruitingId<= 0){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update vehicle_recruiting set check_status = ?,approve_time = now(),approve_time = now(),approve_aging = 1,modify_user = ?  where vehicle_id = ? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
        parameters.setSensitive(i++,"vehicle_id",Types.BIGINT, vehRecruitingId);
        return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public Boolean checkRecruitingVehOnly(String str, Integer type){
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        try {
            builder.selectCount();
            if (Objects.equals(type, TmsTransportConstant.VehOnlyTypeEnum.vehicle_license.getCode())) {
                builder.and();
                builder.equal("vehicle_license", str, Types.VARCHAR, false);
            }
            if (Objects.equals(type, TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())) {
                // vin 码不再校验唯一性
                return Boolean.FALSE;
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            if (vehicleRecruitingRepo.getDao().count(builder, hints).intValue() > 0) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }catch (Exception e){
            throw new  BaijiRuntimeException(e);
        }
    }

    @Override
    public List<VehicleRecruitingPO> queryvWaitApproveRecruitingByPage(int pageNo, int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        try {
            builder.equal("approver_status", TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("vehicle_id", false);
            builder.atPage(pageNo,pageSize);
            return vehicleRecruitingRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int queryvCountWaitApproveRecruiting() {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        try {
            builder.equal("approver_status", TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehicleRecruitingRepo.getDao().count(builder, hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateDrvApproveSchedule(Long recruitingId, Integer accountType, Integer approveSchedule) throws SQLException {
        if(recruitingId == null || recruitingId <= 0){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("update vehicle_recruiting set ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
            sqlStr.append(" supplier_approve_schedule = ? ");
            parameters.setSensitive(i++, "supplier_approve_schedule", Types.INTEGER, approveSchedule);
        }
        if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
            sqlStr.append(" approve_schedule = ? ");
            parameters.setSensitive(i++, "approve_schedule", Types.INTEGER, approveSchedule);
        }
        sqlStr.append(" where vehicle_id = ? ");
        parameters.setSensitive(i++,"vehicle_id",Types.BIGINT, recruitingId);
        builder.setTemplate(sqlStr.toString());
        return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int updateApproveAging(Long recruitingId) throws SQLException {
        if(recruitingId == null){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        StringBuilder sqlBuilder = new StringBuilder("update vehicle_recruiting set approve_time = ? ,approve_aging = ? where vehicle_id = ? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "approve_time", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        parameters.setSensitive(i++, "approve_aging", Types.INTEGER, TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
        parameters.setSensitive(i++,"vehicle_id",Types.BIGINT, recruitingId);
        builder.setTemplate(sqlBuilder.toString());
        return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int discardRecruitingVeh(Long recruitingId, Boolean active, String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if(recruitingId == null || recruitingId <= 0){
                return 0;
            }
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlBuilder = new StringBuilder("update vehicle_recruiting set active = ? ,modify_user = ? where vehicle_id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "active", Types.BIT, active);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++,"vehicle_id",Types.BIGINT, recruitingId);
            builder.setTemplate(sqlBuilder.toString());
            return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateDrvImgQToC(VehicleRecruitingPO vehVehiclePO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if(StringUtils.isEmpty(vehVehiclePO.getNetTansCtfctImg()) && StringUtils.isEmpty(vehVehiclePO.getVehicleCertiImg()) && StringUtils.isEmpty(vehVehiclePO.getVehicleFullImg()) &&
                    StringUtils.isEmpty(vehVehiclePO.getVehicleFrontImg()) && StringUtils.isEmpty(vehVehiclePO.getVehicleBackImg())&& StringUtils.isEmpty(vehVehiclePO.getVehicleTrunkImg())){
                return 0;
            }
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("update vehicle_recruiting set modify_user = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "modify_user", Types.VARCHAR,TmsTransportConstant.TMS_DEFAULT_USERNAME);
            if(!StringUtils.isEmpty(vehVehiclePO.getNetTansCtfctImg())){
                sqlStr.append(", net_tans_ctfct_img = ? ");
                parameters.set(i++, "net_tans_ctfct_img", Types.VARCHAR,vehVehiclePO.getNetTansCtfctImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleCertiImg())){
                sqlStr.append(", vehicle_certi_img = ? ");
                parameters.set(i++, "vehicle_certi_img", Types.VARCHAR,vehVehiclePO.getVehicleCertiImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleFullImg())){
                sqlStr.append(", vehicle_full_img = ? ");
                parameters.set(i++, "vehicle_full_img", Types.VARCHAR,vehVehiclePO.getVehicleFullImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleFrontImg())){
                sqlStr.append(", vehicle_front_img = ? ");
                parameters.set(i++, "vehicle_front_img", Types.VARCHAR,vehVehiclePO.getVehicleFrontImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleBackImg())){
                sqlStr.append(", vehicle_back_img = ? ");
                parameters.set(i++, "vehicle_back_img", Types.VARCHAR,vehVehiclePO.getVehicleBackImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleTrunkImg())){
                sqlStr.append(", vehicle_trunk_img = ? ");
                parameters.set(i++, "vehicle_trunk_img", Types.VARCHAR,vehVehiclePO.getVehicleTrunkImg());
            }
            sqlStr.append(" where vehicle_id = ? ");
            parameters.set(i++, "vehicle_id", Types.BIGINT, vehVehiclePO.getVehicleId());
            builder.setTemplate(sqlStr.toString());
            return vehicleRecruitingRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("updateDrvImgQToC error", e);
        }
    }
}
