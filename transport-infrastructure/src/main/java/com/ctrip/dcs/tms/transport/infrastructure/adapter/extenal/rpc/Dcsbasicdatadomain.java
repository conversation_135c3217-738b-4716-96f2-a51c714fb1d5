package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.dcs.basicdatadomain.interfaces.DcsbasicdatadomainClient;
import com.ctrip.dcs.basicdatadomain.interfaces.message.QueryVehicleModelInfoRequestType;
import com.ctrip.dcs.basicdatadomain.interfaces.message.QueryVehicleModelInfoResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 * @Description  第三方服务soa
 * @Date 14:23 2020/9/25
 * @Param 
 * @return 
 **/
@ServiceClient(value = DcsbasicdatadomainClient.class,format = "json")
public interface Dcsbasicdatadomain {


    /**
     * 查询车型关系
     * @param request
     * @return
     */
    QueryVehicleModelInfoResponseType queryVehicleModelInfo(QueryVehicleModelInfoRequestType request);


}
