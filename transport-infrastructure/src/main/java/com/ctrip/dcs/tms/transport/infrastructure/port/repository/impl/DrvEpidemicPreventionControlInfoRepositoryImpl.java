package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

@Repository(value = "drvEpidemicPreventionControlInfoRepository")
public class DrvEpidemicPreventionControlInfoRepositoryImpl implements DrvEpidemicPreventionControlInfoRepository {

    private DalRepository<DrvEpidemicPreventionControlInfoPO> dalRepository;

    private DalRowMapper<DrvEpidemicPreventionControlInfoPO> dalRowMapper;

    public DrvEpidemicPreventionControlInfoRepositoryImpl() throws SQLException {
        this.dalRepository = new DalRepositoryImpl<>(DrvEpidemicPreventionControlInfoPO.class);
        this.dalRowMapper = new DalDefaultJpaMapper<>(DrvEpidemicPreventionControlInfoPO.class);
    }

    @Override
    public DrvEpidemicPreventionControlInfoPO queryByDrvId(Long drvId) {
        List<DrvEpidemicPreventionControlInfoPO> infoPOS = queryByDrvIdList(Lists.newArrayList(drvId));
        if (CollectionUtils.isEmpty(infoPOS)) {
            return null;
        }
        return infoPOS.get(0);
    }

    @Override
    public List<DrvEpidemicPreventionControlInfoPO> queryByDrvIdList(List<Long> drvIdList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            StringBuilder sqlStr = new StringBuilder("SELECT * FROM drv_epidemic_prevention_control_info WHERE drv_id IN (?)");
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvIdList);
            return dalRepository.getQueryDao().query(sqlStr.toString(), parameters, hints, DrvEpidemicPreventionControlInfoPO.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Long addEpidemicPreventionControlInfo(DrvEpidemicPreventionControlInfoPO infoPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        dalRepository.insert(new DalHints(), keyHolder, infoPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public Integer updateEpidemicPreventionControlInfo(DrvEpidemicPreventionControlInfoPO infoPO) throws SQLException {
        return dalRepository.update(infoPO);
    }
}