package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.util.*;

/**
 * 查询运力组绑定的商品
 * <AUTHOR>
 * @Date 2020/3/11 15:53
 */
public class QueryTransportGroupSkuInfoModel {

    private List<Long> transportGroupIds;

    private Long skuId;

    private List<Long> serviceAreaIds;

    private Integer serviceAreaType;

    private Boolean active;

    private List<Long> skuIds;

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public List<Long> getTransportGroupIds() {
        return transportGroupIds;
    }

    public void setTransportGroupIds(List<Long> transportGroupIds) {
        this.transportGroupIds = transportGroupIds;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public List<Long> getServiceAreaIds() {
        return serviceAreaIds;
    }

    public void setServiceAreaIds(List<Long> serviceAreaIds) {
        this.serviceAreaIds = serviceAreaIds;
    }

    public Integer getServiceAreaType() {
        return serviceAreaType;
    }

    public void setServiceAreaType(Integer serviceAreaType) {
        this.serviceAreaType = serviceAreaType;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }
}
