package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "")
public class TodoListCountPO implements DalPojo {

    /**
     * 供应商ID
     */
    @Id
    @Column(name = "supplier_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long supplierId;

    /**
     * 代办个数
     */
    @Column(name = "count")
    @Type(value = Types.INTEGER)
    private Integer count;

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}