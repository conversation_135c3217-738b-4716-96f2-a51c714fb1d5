package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/6
 */
@Data
public class DriverStatusTransitionPermissionDTO {

  // 判罚来源映射
  private Map<String, String> freezeFromUserMap;
  /**
   *  BD权限/供应商权限权限配置
   * key格式："sourceStatus:targetStatus:role"
   * value格式：["opfrom1", "opfrom2"]，表示哪些角色可以进行此操作
   */
  private Map<String, List<Integer>> permissionRuleList;
  // 超级管理员权限
  private List<Integer> superAdminPermissionList;
  // 超级管理员账号
  private List<String> superAdminAccountList;
  //采购派发
  private List<String> dispatchOpFromList;
  // 结算
  private List<String> settleMentOpFromList;
  // 司机风控
  private List<String> driverRiskOpFromList;
  // 司机安全分跌零
  public List<String> driverSafePointList;
}
