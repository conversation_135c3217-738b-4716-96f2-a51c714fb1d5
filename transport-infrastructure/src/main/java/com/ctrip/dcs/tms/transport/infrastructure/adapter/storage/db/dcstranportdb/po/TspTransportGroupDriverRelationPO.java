package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2020-02-20
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tsp_transport_group_driver_relation")
@ToString
@EqualsAndHashCode
public class TspTransportGroupDriverRelationPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 运力组ID
     */
	@Column(name = "transport_group_id")
	@Type(value = Types.BIGINT)
	private Long transportGroupId;

    /**
     * 司机ID
     */
	@Column(name = "drv_id")
	@Type(value = Types.BIGINT)
	private Long drvId;

    /**
     * 1表示启用，0表示删除
     */
	@Column(name = "active")
	@Type(value = Types.BIT)
	private Boolean active;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 变更人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 班次id
	 */
	@Column(name = "work_shift_id")
	@Type(value = Types.BIGINT)
	private Long workShiftId;

	/**
	 * 报名状态
	 */
	@Column(name = "apply_status")
	@Type(value = Types.INTEGER)
	private Integer applyStatus;

	public Long getWorkShiftId() {
		return workShiftId;
	}

	public void setWorkShiftId(Long workShiftId) {
		this.workShiftId = workShiftId;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getTransportGroupId() {
		return transportGroupId;
	}

	public void setTransportGroupId(Long transportGroupId) {
		this.transportGroupId = transportGroupId;
	}

	public Long getDrvId() {
		return drvId;
	}

	public void setDrvId(Long drvId) {
		this.drvId = drvId;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}
