package com.ctrip.dcs.tms.transport.infrastructure.common.dto;


/***
　* @description: 司机核验参数
　* <AUTHOR>
　* @date 2021/10/8 10:09
*/
public class DrvCertificateCheckParameterDTO {


    private Long checkId;
    private Integer checkType;
    private String drvIdCard;
    private String newDrvIdCard;
    private String drvName;
    private String newDrvName;
    private String modifyUser;
    private String netVehiclePeoImg;
    private String netAppealMaterials;
    private String drvPhone;

    public static DrvCertificateCheckParameterDTO drvCertificateCheck(Long checkId,Integer checkType,String drvIdCard,String newDrvIdCard,String drvName,String newDrvName,String modifyUser,String netVehiclePeoImg,String netAppealMaterials,String drvPhone) {
        DrvCertificateCheckParameterDTO auditDTO = new DrvCertificateCheckParameterDTO();
        auditDTO.setCheckId(checkId);
        auditDTO.setCheckType(checkType);
        auditDTO.setDrvIdCard(drvIdCard);
        auditDTO.setNewDrvIdCard(newDrvIdCard);
        auditDTO.setDrvName(drvName);
        auditDTO.setNewDrvName(newDrvName);
        auditDTO.setModifyUser(modifyUser);
        auditDTO.setNetVehiclePeoImg(netVehiclePeoImg);
        auditDTO.setNetAppealMaterials(netAppealMaterials);
        auditDTO.setDrvPhone(drvPhone);
        return auditDTO;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public String getDrvIdCard() {
        return drvIdCard;
    }

    public void setDrvIdCard(String drvIdCard) {
        this.drvIdCard = drvIdCard;
    }

    public String getNewDrvIdCard() {
        return newDrvIdCard;
    }

    public void setNewDrvIdCard(String newDrvIdCard) {
        this.newDrvIdCard = newDrvIdCard;
    }

    public String getDrvName() {
        return drvName;
    }

    public void setDrvName(String drvName) {
        this.drvName = drvName;
    }

    public String getNewDrvName() {
        return newDrvName;
    }

    public void setNewDrvName(String newDrvName) {
        this.newDrvName = newDrvName;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getNetVehiclePeoImg() {
        return netVehiclePeoImg;
    }

    public void setNetVehiclePeoImg(String netVehiclePeoImg) {
        this.netVehiclePeoImg = netVehiclePeoImg;
    }

    public String getNetAppealMaterials() {
        return netAppealMaterials;
    }

    public void setNetAppealMaterials(String netAppealMaterials) {
        this.netAppealMaterials = netAppealMaterials;
    }

    public String getDrvPhone() {
        return drvPhone;
    }

    public void setDrvPhone(String drvPhone) {
        this.drvPhone = drvPhone;
    }
}
