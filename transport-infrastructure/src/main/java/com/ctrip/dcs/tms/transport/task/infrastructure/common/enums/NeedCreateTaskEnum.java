package com.ctrip.dcs.tms.transport.task.infrastructure.common.enums;

import lombok.Getter;

@Getter
public enum NeedCreateTaskEnum {
  NOT_NEED(0),
  NEED(1),
  ;
  private final int code;

  NeedCreateTaskEnum(int code) {
    this.code = code;
  }

  public static NeedCreateTaskEnum getByCode(Integer code) {
    if (code == null) {
      return NEED;
    }
    for (NeedCreateTaskEnum needCreateTaskEnum : NeedCreateTaskEnum.values()) {
      if (needCreateTaskEnum.code == code) {
        return needCreateTaskEnum;
      }
    }
    throw new RuntimeException("Cde not found:" + code);
  }

}
