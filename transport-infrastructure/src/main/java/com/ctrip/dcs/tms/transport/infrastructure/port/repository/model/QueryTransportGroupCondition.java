package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.google.common.base.Strings;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class QueryTransportGroupCondition {

    /**
     * sku id 集
     */
    private List<Long> skuIdList;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 坐标系 WGS84/GCJ02/BD09
     */
    private String poiType;

    private Map<String, Boolean> paramAreaMap;

    /**
     * 车辆点位ID
     */
    private String carPlaceId;

    /**
     * poi参考
     */
    private String poiRef;

    /**
     * 0:全部 1:仅派单，不进单
     */
    public Integer filterDispatchOnly;

    public Integer getFilterDispatchOnly() {
        return filterDispatchOnly;
    }

    public void setFilterDispatchOnly(Integer filterDispatchOnly) {
        this.filterDispatchOnly = filterDispatchOnly;
    }

    public Map<String, Boolean> getParamAreaMap() {
        return paramAreaMap;
    }

    public void setParamAreaMap(Map<String, Boolean> paramAreaMap) {
        this.paramAreaMap = paramAreaMap;
    }

    public List<Long> getSkuIdList() {
        return skuIdList;
    }

    public void setSkuIdList(List<Long> skuIdList) {
        this.skuIdList = skuIdList;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getPoiType() {
        return poiType;
    }

    public void setPoiType(String poiType) {
        this.poiType = poiType;
    }

    public boolean isHasPointData() {
        if (!Strings.isNullOrEmpty(this.getPoiRef()) || !Strings.isNullOrEmpty(this.getCarPlaceId())) {
            return Boolean.TRUE;
        }
        if (Strings.isNullOrEmpty(this.getPoiType()) || this.getLongitude() == null || this.getLatitude() == null) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public String getCarPlaceId() {
        return carPlaceId;
    }

    public void setCarPlaceId(String carPlaceId) {
        this.carPlaceId = carPlaceId;
    }

    public String getPoiRef() {
        return poiRef;
    }

    public void setPoiRef(String poiRef) {
        this.poiRef = poiRef;
    }

}
