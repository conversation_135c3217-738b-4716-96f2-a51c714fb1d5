package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.basebiz.callcenter.splitservice.contract.BatchSplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.BatchSplitNumberResponseType;
import com.ctrip.basebiz.callcenter.splitservice.contract.PhoneNumberSplitServiceClient;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * 拆码服务
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/7/3 16:24
 */
@ServiceClient(value = PhoneNumberSplitServiceClient.class, format = "json")
public interface PhoneNumberSplitServiceProxy {
    // 批量拆码服务
    BatchSplitNumberResponseType batchSplitNumber(BatchSplitNumberRequestType request);
    // 单个拆码接口
    SplitNumberResponseType splitNumber(SplitNumberRequestType request);
}
