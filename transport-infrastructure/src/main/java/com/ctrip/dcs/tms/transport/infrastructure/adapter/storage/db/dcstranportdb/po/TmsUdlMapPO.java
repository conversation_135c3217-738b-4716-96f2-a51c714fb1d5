package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2025/6/5
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_udl_map")
@Data
public class TmsUdlMapPO  implements DalPojo {

  public TmsUdlMapPO() {
  }

  public TmsUdlMapPO(String udl, Long rrdId, Integer rrdType) {
    this.udl = udl;
    this.rrdId = rrdId;
    this.rrdType = rrdType;
  }

  /**
   * 主键
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * udl
   */
  @Column(name = "udl")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.VARCHAR)
  private String udl;

  /**
   * 实体主键
   */
  @Column(name = "rrd_id")
  @Type(value = Types.BIGINT)
  private Long rrdId;

  /**
   * 实体类别(1运力组,2司机,3车辆,4招募)
   */
  @Column(name = "rrd_type")
  @Type(value = Types.TINYINT)
  private Integer rrdType;

}
