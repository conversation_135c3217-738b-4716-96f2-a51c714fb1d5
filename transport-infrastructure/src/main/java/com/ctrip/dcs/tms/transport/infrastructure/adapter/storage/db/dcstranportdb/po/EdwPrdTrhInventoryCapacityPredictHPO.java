package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2022-11-10
 */
@Entity
@Database(name = "igtbidb_w")
@Table(name = "edw_prd_trh_inventory_capacity_predict_h")
public class EdwPrdTrhInventoryCapacityPredictHPO implements DalPojo {

    /**
     * 自增主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 用车日期
     */
	@Column(name = "use_date")
	@Type(value = Types.VARCHAR)
	private String useDate;

    /**
     * 用车时段
     */
	@Column(name = "use_time")
	@Type(value = Types.VARCHAR)
	private String useTime;

    /**
     * 城市id
     */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 城市名称
     */
	@Column(name = "city_name")
	@Type(value = Types.VARCHAR)
	private String cityName;

    /**
     * 省份id
     */
	@Column(name = "province_id")
	@Type(value = Types.BIGINT)
	private Long provinceId;

    /**
     * 省份名称
     */
	@Column(name = "province_name")
	@Type(value = Types.VARCHAR)
	private String provinceName;

    /**
     * 大区id
     */
	@Column(name = "region_id")
	@Type(value = Types.BIGINT)
	private Long regionId;

    /**
     * 大区名称
     */
	@Column(name = "region_name")
	@Type(value = Types.VARCHAR)
	private String regionName;

    /**
     * 车型id
     */
	@Column(name = "car_type_id")
	@Type(value = Types.BIGINT)
	private Long carTypeId;

    /**
     * 车型名称
     */
	@Column(name = "car_type_name")
	@Type(value = Types.VARCHAR)
	private String carTypeName;

    /**
     * 用车时段标签
     */
	@Column(name = "use_time_label")
	@Type(value = Types.VARCHAR)
	private String useTimeLabel;

    /**
     * 运力数
     */
	@Column(name = "inventory_capacity")
	@Type(value = Types.BIGINT)
	private Long inventoryCapacity;

    /**
     * 已支付单量
     */
	@Column(name = "pay_order_cnt")
	@Type(value = Types.BIGINT)
	private Long payOrderCnt;

    /**
     * 人效
     */
	@Column(name = "avg_human_effect")
	@Type(value = Types.DOUBLE)
	private Double avgHumanEffect;

    /**
     * 运力需求
     */
	@Column(name = "capacity_need")
	@Type(value = Types.DOUBLE)
	private Double capacityNeed;

    /**
     * 运力剩余库存数
     */
	@Column(name = "unused_capacity")
	@Type(value = Types.DOUBLE)
	private Double unusedCapacity;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUseDate() {
		return useDate;
	}

	public void setUseDate(String useDate) {
		this.useDate = useDate;
	}

	public String getUseTime() {
		return useTime;
	}

	public void setUseTime(String useTime) {
		this.useTime = useTime;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public Long getRegionId() {
		return regionId;
	}

	public void setRegionId(Long regionId) {
		this.regionId = regionId;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public Long getCarTypeId() {
		return carTypeId;
	}

	public void setCarTypeId(Long carTypeId) {
		this.carTypeId = carTypeId;
	}

	public String getCarTypeName() {
		return carTypeName;
	}

	public void setCarTypeName(String carTypeName) {
		this.carTypeName = carTypeName;
	}

	public String getUseTimeLabel() {
		return useTimeLabel;
	}

	public void setUseTimeLabel(String useTimeLabel) {
		this.useTimeLabel = useTimeLabel;
	}

	public Long getInventoryCapacity() {
		return inventoryCapacity;
	}

	public void setInventoryCapacity(Long inventoryCapacity) {
		this.inventoryCapacity = inventoryCapacity;
	}

	public Long getPayOrderCnt() {
		return payOrderCnt;
	}

	public void setPayOrderCnt(Long payOrderCnt) {
		this.payOrderCnt = payOrderCnt;
	}

	public Double getAvgHumanEffect() {
		return avgHumanEffect;
	}

	public void setAvgHumanEffect(Double avgHumanEffect) {
		this.avgHumanEffect = avgHumanEffect;
	}

	public Double getCapacityNeed() {
		return capacityNeed;
	}

	public void setCapacityNeed(Double capacityNeed) {
		this.capacityNeed = capacityNeed;
	}

	public Double getUnusedCapacity() {
		return unusedCapacity;
	}

	public void setUnusedCapacity(Double unusedCapacity) {
		this.unusedCapacity = unusedCapacity;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}