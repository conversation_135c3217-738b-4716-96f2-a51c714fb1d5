package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.igt.framework.infrastructure.context.*;
import com.ctriposs.baiji.rpc.server.*;
import com.google.common.base.*;

/**
 * 平台工具 用于区分 CQ请求
 * <AUTHOR>
 */
public class PlatformUtil {

    /**
     * Q 侧请求位于 headers 中 含有 qapp-name=Q侧项目名称
     */
    private static final String QUNAR_HEAD_KEY_NAME = "qapp-name";

    /**
     * 判断该请求是否来自 Q
     * 兜底认定为 Q
     */
    public static boolean isQReq() {
        return !Strings.isNullOrEmpty(getQunarAppName());
    }

    /**
     * 获取当前请求的服务名称
     * Q： 服务名称
     * C： clientId
     */
    public static String getAppName() {
        String resServiceName = getQunarAppName();
        if (!Strings.isNullOrEmpty(resServiceName)) {
            return resServiceName;
        }
        return getClientAppId();
    }

    /**
     * 获取Q侧服务名称
     *
     * @describe Q 侧请求 headers中含有服务名称
     * 例子:qapp-name=Q侧项目名称
     */
    public static String getQunarAppName() {
        if (HttpRequestContext.getInstance() == null) {
            return "";
        }
        HttpRequestWrapper requestWrapper = HttpRequestContext.getInstance().request();
        if (requestWrapper == null) {
            return "";
        }
        return requestWrapper.getHeader(QUNAR_HEAD_KEY_NAME);
    }

    /**
     * 获取C侧服务名称 <clientId>
     *
     * @describe 通过公共组件获取
     * 注：假如服务来自Q则返回 “webapi”
     * 参考自：http://conf.ctripcorp.com/pages/viewpage.action?pageId=147175746#id-5.1Java%E6%9C%8D%E5%8A%A1%E7%AB%AF%E9%85%8D%E7%BD%AE-14%E8%8E%B7%E5%8F%96%E8%AF%B7%E6%B1%82%E4%B8%8A%E4%B8%8B%E6%96%87%EF%BC%8C%E5%AE%A2%E6%88%B7%E7%AB%AFIP/AppId/Header%E7%AD%89%E4%BF%A1%E6%81%AF
     */
    public static String getClientAppId() {
        if (HttpRequestContext.getInstance() == null) {
            return "";
        }
        HttpRequestWrapper requestWrapper = HttpRequestContext.getInstance().request();
        if (requestWrapper == null) {
            return "";
        }
        return requestWrapper.clientAppId();
    }

    /**
     * 获取服务名称
     *
     * @describe 服务名称 = XXXXXRequestType = xxxxx
     * 例：QueryDrvSafetyCenterSOARequestType = querydrvsafetycenter
     */
    public static String getMethodName() {
        ServiceExecuteContext context = ServiceExecuteContext.getCurrent();
        return (context != null) ? context.getOperationName() : "";
    }

    /**
     * 获取接口名称
     *
     *
     *
     */
    public static String getOperation() {
        HttpRequestContext context = HttpRequestContext.getInstance();
        if (context == null) {
            return "";
        }

        HttpRequestWrapper requestWrapper = context.request();
        return (requestWrapper != null) ? requestWrapper.operationName() : "";
    }

}
