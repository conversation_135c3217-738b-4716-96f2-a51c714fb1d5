package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.Optional;

import org.springframework.stereotype.Component;

import qunar.tc.qconfig.client.spring.QMapConfig;

@Component
@QMapConfig("transport.common.properties")
public class TransportCommonQconfig {

    private String qmqOrderMessageSwitch;
    private String driverPwdToken;
    private String registerDriverAccountSwitch;
    private String registerDriverAccountWithNewInterfaceSwitch;
    public void setRegisterDriverAccountWithNewInterfaceSwitch(String registerDriverAccountWithNewInterfaceSwitch) {
        this.registerDriverAccountWithNewInterfaceSwitch = registerDriverAccountWithNewInterfaceSwitch;
    }

    public String getRegisterDriverAccountWithNewInterfaceSwitch() {
        return Optional.ofNullable(registerDriverAccountWithNewInterfaceSwitch).orElse("ON");
    }

    public String getQmqOrderMessageSwitch() {
        return qmqOrderMessageSwitch;
    }

    public void setQmqOrderMessageSwitch(String qmqOrderMessageSwitch) {
        this.qmqOrderMessageSwitch = qmqOrderMessageSwitch;
    }

    public String getDriverPwdToken() {
        return driverPwdToken;
    }

    public void setDriverPwdToken(String driverPwdToken) {
        this.driverPwdToken = driverPwdToken;
    }

    public String getRegisterDriverAccountSwitch() {
        return Optional.ofNullable(registerDriverAccountSwitch).orElse("ON");
    }

    public void setRegisterDriverAccountSwitch(String registerDriverAccountSwitch) {
        this.registerDriverAccountSwitch = registerDriverAccountSwitch;
    }
}
