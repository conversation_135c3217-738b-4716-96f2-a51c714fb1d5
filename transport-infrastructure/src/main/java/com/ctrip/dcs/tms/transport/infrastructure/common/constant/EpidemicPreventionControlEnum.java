package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

/**
 * 疫情防控相关枚举
 *
 * <AUTHOR>
 * 2021-04-14 17:34:21
 */
public class EpidemicPreventionControlEnum {

    /**
     * 要求疫苗报告状态
     * 0： 无需上传核酸报告 & 疫苗报告
     * 1：必须上传核酸报告
     * 2：必须上传疫苗报告
     * 3：满足 上传核酸报告 || 疫苗报告 二选一即可
     * 4：必须上传核酸报告 & 疫苗报告
     */
    public enum AskReportStatusEnum {

        NOT_NEED("NOT_NEED", 0),
        NEED_NUCLEIC_ACID_REPORT("NEED NUCLEIC ACID REPORT", 1),
        NEED_VACCINE_REPORT("NEED VACCINE REPORT", 2),
        SATISFY_ONE("SATISFY ONE", 3),
        ALL_NEED("ALL NEED", 4);

        private String message;
        private Integer code;

        AskReportStatusEnum(String message, Integer code) {
            this.message = message;
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }
    }

    /**
     * 报告类型
     */
    public enum DetectionTypeEnum {

        NUCLEIC_ACID_TEST(1),
        VACCINE(2);

        private Integer value;

        DetectionTypeEnum(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public static boolean isVaccineType(int detectionType) {
            return VACCINE.getValue().intValue() == detectionType;
        }
    }

    /**
     * 核酸结果
     */
    public enum ResultStatusEnum {

        NEGATIVE("阴"),
        POSITIVE("阳"),
        UNKNOWN("未知");

        private String message;

        ResultStatusEnum(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

    }

    /**
     * 报告结果状态
     */
    public enum ReportResultEnum {
        INIT("初始值",0),
        PASS("通过", 1),
        NOT_ONESELF("上传报告非本人信息", 2),
        INFORMATION_NOT_COMPLETE("无法读取，非报告信息", 3),
        OVERDUE("核酸报告已过期", 4),
        POSITIVE("核酸报告呈阳性", 5),
        FAILURE("核酸报告已失效", 6),
        PARTIAL_INFORMATION_AVAILABLE("需要人工审核", 7),
        HUMAN_AUDIT_FAILURE("人工审核失败", 8);

        private String message;
        private Integer code;

        ReportResultEnum(String message, Integer code) {
            this.message = message;
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public static ReportResultEnum getReportResultEnum(int code) {
            for (ReportResultEnum resultEnum : ReportResultEnum.values()) {
                if (resultEnum.getCode().intValue() == code) {
                    return resultEnum;
                }
            }
            return null;
        }
    }

    /**
     * 报告上传状态
     */
    public enum ReportUpdateStatusEnum {
        NOT_UPLOAD("未上传",0),
        UPLOADED("已上传", 1);

        private String message;
        private Integer code;

        ReportUpdateStatusEnum(String message, Integer code) {
            this.message = message;
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }
    }

    /**
     * 报告通知临近过期状态
     */
    public enum ReportInformStatusEnum {
        NOT_INFORM("未通知",0),
        INFORMED("已通知", 1);

        private String message;
        private Integer code;

        ReportInformStatusEnum(String message, Integer code) {
            this.message = message;
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }
    }

    /**
     * 依据报告冻结状态
     */
    public enum ReportFreezeStatusEnum {
        NOT_FREEZE("未因报告冻结",0),
        FROZE("已因报告冻结", 1);

        private String message;
        private Integer code;

        ReportFreezeStatusEnum(String message, Integer code) {
            this.message = message;
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }
    }

}