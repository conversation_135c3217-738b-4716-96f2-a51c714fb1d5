package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OCRConfig.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.LocationTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NewOCRDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrRecognitionResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class NewOcrFiledConfig {

    private static final Logger logger = LoggerFactory.getLogger(NewOcrFiledConfig.class);

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private OcrConfig ocrConfig;

    public List<RequiredFieldDTO> getFieldList(Long locationId, String locationType, List<String> sceneList) {
        Map<String, List<RequiredFieldDTO>> current = ocrConfig.getBackFieldMap();

        List<RequiredFieldDTO> list = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(sceneList)) {
            sceneList.forEach(scene -> {
                List<RequiredFieldDTO> requiredFieldDTOList = current.get(scene);
                if (CollectionUtils.isNotEmpty(requiredFieldDTOList)) {
                    List<RequiredFieldDTO> collect = requiredFieldDTOList.stream().filter(item -> item.getLocationId().contains(locationId) && item.getLocationType().equals(locationType)).collect(Collectors.toList());
                    collect.forEach(item -> item.setScene(scene));
                    if (CollectionUtils.isNotEmpty(collect)) {
                        list.addAll(collect);
                    }

                }
            });
        }
        return list;
    }

    public OcrRecognitionResultDTO getOcrRecognitionResultList(Long locationId, String locationType, String imgType) {
        List<OcrRecognitionResultDTO> current = ocrConfig.getOcrRecognitionResultList();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType) && a.getImgType().equals(imgType)).findFirst().orElse(null);
    }

    /*
    * 自动合规的范围
    * */
    public OcrComplianceDTO getOcrComplianceList(Long locationId, String locationType) {
        List<OcrComplianceDTO> current = ocrConfig.getOcrComplianceList();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType)).findFirst().orElse(null);
    }


    public NewOCRDTO getNewOcr(Long locationId, String locationType) {
        List<NewOCRDTO> current = ocrConfig.getNewOcrUseList();
        return current.stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType)).findFirst().orElse(null);
    }

    // ========== 使用枚举的新方法 ==========

    /**
     * 获取字段列表（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @param sceneList 场景列表
     * @return 字段列表
     */
    public List<RequiredFieldDTO> getFieldList(Long locationId, LocationTypeEnum locationType, List<String> sceneList) {
        return getFieldList(locationId, locationType.getCode(), sceneList);
    }

    /**
     * 获取OCR识别结果（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @param imgType 图片类型
     * @return OCR识别结果
     */
    public OcrRecognitionResultDTO getOcrRecognitionResultList(Long locationId, LocationTypeEnum locationType, String imgType) {
        return getOcrRecognitionResultList(locationId, locationType.getCode(), imgType);
    }

    /**
     * 获取OCR合规列表（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @return OCR合规DTO
     */
    public OcrComplianceDTO getOcrComplianceList(Long locationId, LocationTypeEnum locationType) {
        return getOcrComplianceList(locationId, locationType.getCode());
    }

    /**
     * 获取新OCR配置（使用枚举）
     * @param locationId 位置ID
     * @param locationType 位置类型枚举
     * @return 新OCR配置
     */
    public NewOCRDTO getNewOcr(Long locationId, LocationTypeEnum locationType) {
        return getNewOcr(locationId, locationType.getCode());
    }

    // ========== 收口查询方法：先查城市再查国家 ==========

    /**
     * 获取字段列表（先查城市再查国家）
     * @param cityId 城市ID
     * @param sceneList 场景列表
     * @return 字段列表
     */
    public List<RequiredFieldDTO> getFieldListByCityWithFallback(Long cityId, List<String> sceneList) {
        logger.info("getFieldListByCityWithFallback开始执行，入参: cityId={}, sceneList={}", cityId, sceneList);

        List<RequiredFieldDTO> resultList = new ArrayList<>();

        // 先查城市
        logger.debug("getFieldListByCityWithFallback开始查询城市维度配置，cityId={}", cityId);
        List<RequiredFieldDTO> cityFieldList = getFieldList(cityId, LocationTypeEnum.CITY, sceneList);
        if (CollectionUtils.isNotEmpty(cityFieldList)) {
            logger.info("getFieldListByCityWithFallback城市维度查询成功，cityId={}, 查询到字段数量={}", cityId, cityFieldList.size());
            resultList.addAll(cityFieldList);
        } else {
            logger.info("getFieldListByCityWithFallback城市维度未查询到配置，cityId={}", cityId);
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        logger.debug("getFieldListByCityWithFallback开始查询国家维度配置，cityId={}, countryId={}", cityId, countryId);
        List<RequiredFieldDTO> countryFieldList = getFieldList(countryId, LocationTypeEnum.COUNTRY, sceneList);
        if (CollectionUtils.isNotEmpty(countryFieldList)) {
            logger.info("getFieldListByCityWithFallback国家维度查询成功，cityId={}, countryId={}, 查询到字段数量={}", cityId, countryId, countryFieldList.size());
            resultList.addAll(countryFieldList);
        } else {
            logger.info("getFieldListByCityWithFallback国家维度未查询到配置，cityId={}, countryId={}", cityId, countryId);
        }

        logger.info("getFieldListByCityWithFallback执行完成，cityId={}, 最终返回字段数量={}", cityId, resultList.size());
        return resultList;
    }

    /**
     * 获取OCR识别结果（先查城市再查国家）
     * @param cityId 城市ID
     * @param imgType 图片类型
     * @return OCR识别结果
     */
    public OcrRecognitionResultDTO getOcrRecognitionResultByCityWithFallback(Long cityId, String imgType) {
        logger.info("getOcrRecognitionResultByCityWithFallback开始执行，入参: cityId={}, imgType={}", cityId, imgType);

        // 先查城市
        logger.debug("getOcrRecognitionResultByCityWithFallback开始查询城市维度配置，cityId={}, imgType={}", cityId, imgType);
        OcrRecognitionResultDTO cityResult = getOcrRecognitionResultList(cityId, LocationTypeEnum.CITY, imgType);
        if (Objects.nonNull(cityResult)) {
            logger.info("getOcrRecognitionResultByCityWithFallback城市维度查询成功，cityId={}, imgType={}", cityId, imgType);
            return cityResult;
        } else {
            logger.info("getOcrRecognitionResultByCityWithFallback城市维度未查询到配置，cityId={}, imgType={}", cityId, imgType);
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        logger.debug("getOcrRecognitionResultByCityWithFallback开始查询国家维度配置，cityId={}, countryId={}, imgType={}", cityId, countryId, imgType);
        OcrRecognitionResultDTO countryResult = getOcrRecognitionResultList(countryId, LocationTypeEnum.COUNTRY, imgType);
        if (Objects.nonNull(countryResult)) {
            logger.info("getOcrRecognitionResultByCityWithFallback国家维度查询成功，cityId={}, countryId={}, imgType={}", cityId, countryId, imgType);
        } else {
            logger.info("getOcrRecognitionResultByCityWithFallback国家维度未查询到配置，cityId={}, countryId={}, imgType={}", cityId, countryId, imgType);
        }

        logger.info("getOcrRecognitionResultByCityWithFallback执行完成，cityId={}, imgType={}, 返回结果={}", cityId, imgType, countryResult != null ? "有配置" : "无配置");
        return countryResult;
    }

    /**
     * 获取OCR合规配置（先查城市再查国家）
     * @param cityId 城市ID
     * @return OCR合规DTO
     */
    public OcrComplianceDTO getOcrComplianceByCityWithFallback(Long cityId) {
        logger.info("getOcrComplianceByCityWithFallback开始执行，入参: cityId={}", cityId);

        // 先查城市
        logger.debug("getOcrComplianceByCityWithFallback开始查询城市维度配置，cityId={}", cityId);
        OcrComplianceDTO cityCompliance = getOcrComplianceList(cityId, LocationTypeEnum.CITY);
        if (Objects.nonNull(cityCompliance)) {
            logger.info("getOcrComplianceByCityWithFallback城市维度查询成功，cityId={}, complianceType={}", cityId, cityCompliance.getComplianceType());
            return cityCompliance;
        } else {
            logger.info("getOcrComplianceByCityWithFallback城市维度未查询到配置，cityId={}", cityId);
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        logger.debug("getOcrComplianceByCityWithFallback开始查询国家维度配置，cityId={}, countryId={}", cityId, countryId);
        OcrComplianceDTO countryCompliance = getOcrComplianceList(countryId, LocationTypeEnum.COUNTRY);
        if (Objects.nonNull(countryCompliance)) {
            logger.info("getOcrComplianceByCityWithFallback国家维度查询成功，cityId={}, countryId={}, complianceType={}", cityId, countryId, countryCompliance.getComplianceType());
        } else {
            logger.info("getOcrComplianceByCityWithFallback国家维度未查询到配置，cityId={}, countryId={}", cityId, countryId);
        }

        logger.info("getOcrComplianceByCityWithFallback执行完成，cityId={}, 返回结果={}", cityId, countryCompliance != null ? "有配置" : "无配置");
        return countryCompliance;
    }

    /**
     * 获取新OCR配置（先查城市再查国家）
     * @param cityId 城市ID
     * @return 新OCR配置
     */
    public NewOCRDTO getNewOcrByCityWithFallback(Long cityId) {
        logger.info("getNewOcrByCityWithFallback开始执行，入参: cityId={}", cityId);

        // 先查城市
        logger.debug("getNewOcrByCityWithFallback开始查询城市维度配置，cityId={}", cityId);
        NewOCRDTO cityOcr = getNewOcr(cityId, LocationTypeEnum.CITY);
        if (Objects.nonNull(cityOcr)) {
            logger.info("getNewOcrByCityWithFallback城市维度查询成功，cityId={}, ocrFieldList大小={}", cityId,
                       cityOcr.getOcrFieldList() != null ? cityOcr.getOcrFieldList().size() : 0);
            return cityOcr;
        } else {
            logger.info("getNewOcrByCityWithFallback城市维度未查询到配置，cityId={}", cityId);
        }

        // 再查国家
        Long countryId = enumRepository.getCountryId(cityId);
        logger.debug("getNewOcrByCityWithFallback开始查询国家维度配置，cityId={}, countryId={}", cityId, countryId);
        NewOCRDTO countryOcr = getNewOcr(countryId, LocationTypeEnum.COUNTRY);
        if (Objects.nonNull(countryOcr)) {
            logger.info("getNewOcrByCityWithFallback国家维度查询成功，cityId={}, countryId={}, ocrFieldList大小={}", cityId, countryId,
                       countryOcr.getOcrFieldList() != null ? countryOcr.getOcrFieldList().size() : 0);
        } else {
            logger.info("getNewOcrByCityWithFallback国家维度未查询到配置，cityId={}, countryId={}", cityId, countryId);
        }

        logger.info("getNewOcrByCityWithFallback执行完成，cityId={}, 返回结果={}", cityId, countryOcr != null ? "有配置" : "无配置");
        return countryOcr;
    }
}
