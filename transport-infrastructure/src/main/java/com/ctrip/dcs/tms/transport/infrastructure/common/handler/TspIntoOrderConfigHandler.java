package com.ctrip.dcs.tms.transport.infrastructure.common.handler;

import com.ctrip.arch.canal.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.google.common.collect.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 进单配置表变更记录处理
 * <AUTHOR>
 * @Date 2020/10/30 16:10
 */
@Component(ModRecordConstant.TableName.tspIntoOrderConfig)
public class TspIntoOrderConfigHandler extends TmsModRecordHandler {

    @Override
    List<TmsModContent> initModContent(DataChange dataChange) {
        String tspIntoOrderConfigFormat = "编号：%s，点位：%s，点位类型：%s，配置信息：%s";
        List<TmsModContent> contentList = Lists.newArrayList();
        TmsModContent tmsModContent = new TmsModContent();
        tmsModContent.setAttributeKey("into_order_config");
        tmsModContent.setAttributeName("into_order_config");
        tmsModContent.setAttributeSharkKey("transport.tspTransportGroup.intoOrderConfig");
        tmsModContent.setValueSharkKey("transport.tspTransportGroup.intoOrderConfig.value");
        Map<String,String> beforeColumnValue = Maps.newHashMap();
        for (ColumnData columnData : dataChange.getBeforeColumnList()) {
            if ("id".equals(columnData.getName())){
                beforeColumnValue.put("id",columnData.getValue());
            }else if ("location_code".equals(columnData.getName())){
                beforeColumnValue.put("location_code",columnData.getValue());
            }else if ("config".equals(columnData.getName())){
                beforeColumnValue.put("config",columnData.getValue());
            }else if ("location_type".equals(columnData.getName())){
                beforeColumnValue.put("location_type",columnData.getValue());
            }
        }
        tmsModContent.setOriginalValue(JsonUtil.toJson(beforeColumnValue));

        Map<String,String> afterColumnValue = Maps.newHashMap();
        for (ColumnData columnData : dataChange.getAfterColumnList()) {
            if ("id".equals(columnData.getName())){
                afterColumnValue.put("id",columnData.getValue());
            }else if ("location_code".equals(columnData.getName())){
                afterColumnValue.put("location_code",columnData.getValue());
            }else if ("config".equals(columnData.getName())){
                afterColumnValue.put("config",columnData.getValue());
            }else if ("location_type".equals(columnData.getName())){
                afterColumnValue.put("location_type",columnData.getValue());
            }
        }
        tmsModContent.setChangeValue(JsonUtil.toJson(afterColumnValue));
        contentList.add(tmsModContent);
        return contentList;
    }

    @Override
    public Integer initModType(DataChange dataChange) {
        if (dataChange.isInsert()) {
            return CommonEnum.ModTypeEnum.CREATE.getValue();
        }else if (dataChange.isUpate()){
            for (ColumnData columnData : dataChange.getAfterColumnList()) {
                if ("active".equals(columnData.getName()) && columnData.isUpdated()){
                    if ("1".equals(columnData.getValue())) {
                        return CommonEnum.ModTypeEnum.CREATE.getValue();
                    }else if ("0".equals(columnData.getValue())){
                        return CommonEnum.ModTypeEnum.DELETE.getValue();
                    }
                }
            }
            return CommonEnum.ModTypeEnum.UPDATE.getValue();
        }else if (dataChange.isDelete()){
            return CommonEnum.ModTypeEnum.DELETE.getValue();
        }else {
            return null;
        }
    }
}
