package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDispatchRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDispatchRelationRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalRowMapper;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaMapper;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeSelectSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeUpdateSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.HashMap;
import java.util.List;

@Repository(value = "drvDispatchRelationRepository")
public class DrvDispatchRelationRepositoryImpl implements DrvDispatchRelationRepository {

    private static final Logger logger = LoggerFactory.getLogger(DrvDispatchRelationRepositoryImpl.class);

    private DalRepository<DrvDispatchRelationPO> drvDispatchRelationRepo;

    private DalRowMapper<DrvDispatchRelationPO> drvDispatchRelationRowMapper;

    public DrvDispatchRelationRepositoryImpl() throws SQLException {
        this.drvDispatchRelationRepo = new DalRepositoryImpl<>(DrvDispatchRelationPO.class);
        this.drvDispatchRelationRowMapper = new DalDefaultJpaMapper<>(DrvDispatchRelationPO.class);
    }

    public DalRepository<DrvDispatchRelationPO> getDrvDispatchRelationRepo() {
        return drvDispatchRelationRepo;
    }

    @Override
    public Long insert(DrvDispatchRelationPO relationPO) throws SQLException{
        KeyHolder keyHolder = new KeyHolder();
        drvDispatchRelationRepo.insert(new DalHints(), keyHolder, relationPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public List<DrvDispatchRelationPO> queryDrvDisPatchRecord(Long drvId, Long supplierId, Boolean active) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);

            SelectSqlBuilder sqlBuilder = new SelectSqlBuilder();
            sqlBuilder.selectAll();
            sqlBuilder.equal("drv_id",drvId, Types.BIGINT,false);
            sqlBuilder.and().equal("supplier_id",supplierId, Types.BIGINT,false);
            sqlBuilder.and().equal("relation_type", TmsTransportConstant.DrvDispatchRelationTypeEnum.DISPATCH.getCode(),Types.TINYINT);
            if(active != null){
                sqlBuilder.and().equal("active",active, Types.BIT,false);
            }
            sqlBuilder.orderBy("id",false);
            return drvDispatchRelationRepo.getDao().query(sqlBuilder,hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int operationDrvDispatchUnBing(Long drvId, Long supplierId,String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        try {
            builder.setTemplate("update tms_drv_dispatch_relation set active = ?,modify_user = ? where drv_id = ? and supplier_id = ? and active = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "active", Types.BIT,Boolean.FALSE);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR,modifyUser);
            parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);
            parameters.setSensitive(i++, "supplier_id", Types.BIGINT, supplierId);
            return drvDispatchRelationRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvDispatchRelationPO> queryDrvDispatchSupplierIds(List<Long> drvIds) {
        List<List<Long>> drvIdListArr = Lists.partition(drvIds, Constant.SQL_REQUEST_ID_LIMIT_ROW_COUNT);
        List<DrvDispatchRelationPO> res = Lists.newArrayListWithCapacity(drvIds.size());
        for (List<Long> drvIdList : drvIdListArr) {
            res.addAll(doQueryDrvDispatchSupplierIds(drvIdList));
        }
        return res;
    }

    @Override
    public List<Long> queryDispatchRelationDrvBySupplierId(Long supplierId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
            StatementParameters parameters = new StatementParameters();
            parameters.set(1, "supplier_id", Types.BIGINT, supplierId);
            builder.setTemplate("SELECT drv_id FROM tms_drv_dispatch_relation WHERE active = 1 and supplier_id = ?");
            builder.mapWith(drvDispatchRelationRowMapper);
            builder.simpleType().nullable();
            return drvDispatchRelationRepo.getQueryDao().query(builder, parameters, hints);
        } catch (Exception e) {
            logger.error("queryDispatchRelationDrvBySupplierIdError", "supplierId:{} error:{}", supplierId, e);
            return Lists.newArrayList();
        }
    }

    private List<DrvDispatchRelationPO> doQueryDrvDispatchSupplierIds(List<Long> drvIds) {
        if(CollectionUtils.isEmpty(drvIds)){
            return Lists.newArrayList();
        }
        SelectSqlBuilder builder = new SelectSqlBuilder();
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            builder.selectAll();
            builder.in("drv_id",drvIds,Types.BIGINT);
            builder.and().equal("relation_type", TmsTransportConstant.DrvDispatchRelationTypeEnum.DISPATCH.getCode(),Types.TINYINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("id",false);
            return drvDispatchRelationRepo.getDao().query(builder, hints);
        } catch (SQLException e) {
            logger.error("queryDrvDispatchSupplierIds",e);
        }
        return Lists.newArrayList();
    }
}
