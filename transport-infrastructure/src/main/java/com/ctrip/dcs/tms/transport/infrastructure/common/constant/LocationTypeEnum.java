package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

/**
 * 位置类型枚举
 * 用于替换代码中硬编码的"city"和"country"字符串
 */
public enum LocationTypeEnum {
    
    CITY("city", "城市"),
    COUNTRY("country", "国家");
    
    private final String code;
    private final String description;
    
    LocationTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据code获取枚举
     * @param code 代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static LocationTypeEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (LocationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
