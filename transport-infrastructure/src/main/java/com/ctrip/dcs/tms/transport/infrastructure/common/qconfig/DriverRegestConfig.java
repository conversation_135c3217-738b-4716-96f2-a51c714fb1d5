package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import java.util.List;

import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverTaxiDTO;

import qunar.tc.qconfig.client.JsonConfig;

@Component
public class DriverRegestConfig {
    JsonConfig<List<DriverTaxiDTO>> config1 = JsonConfig.get("tms.driver.taxi.json", JsonConfig.ParameterizedClass.of(List.class, DriverTaxiDTO.class));

    public DriverTaxiDTO getRequiredFieldList(Long locationId, String locationType, Long supplierId) {
        return config1.current().stream().filter(a -> a.getLocationId().equals(locationId) && a.getLocationType().equals(locationType) && a.getSupplierId().equals(supplierId)).findFirst().orElse(null);
    }

}
