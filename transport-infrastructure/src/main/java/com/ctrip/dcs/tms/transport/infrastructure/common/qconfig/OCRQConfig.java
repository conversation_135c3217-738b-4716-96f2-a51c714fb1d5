package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Data
public class OCRQConfig {

  @QMapConfig( value = "ocr.properties", key = "ocr_illegal_color_list", defaultValue = "\u5176\u5b83")
  private List<String> ocrIllegalColorList;

  @QMapConfig( value = "ocr.properties", key = "useDriverBindVehicleVerify", defaultValue = "false")
  private Boolean useDriverBindVehicleVerify;

  //无需人脸验证，抽查与换设备登录的司机白名单
  @QMapConfig( value = "ocr.properties", key = "exemptVerifyDrvIdList", defaultValue = "1")
  private String exemptVerifyDrvIdList;
}
