package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.dcs.order.dcsorderhybridsearchservice.DCSOrderHybridSearchServiceClient;
import com.ctrip.dcs.order.es.api.QueryOrderCommonInfoRequestType;
import com.ctrip.dcs.order.es.api.QueryOrderCommonInfoResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = DCSOrderHybridSearchServiceClient.class,format = "json")
public interface DcsOrderHybridSearchServiceProxy {

    QueryOrderCommonInfoResponseType queryOrderCommonInfo(QueryOrderCommonInfoRequestType request);
}
