package com.ctrip.dcs.tms.transport.infrastructure.service;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;

import java.util.List;
import java.util.Map;

/**
 * IVR电话呼叫服务接口
 * 负责处理IVR电话呼叫相关的业务逻辑
 */
public interface IVRCallService {

    /**
     * 发起IVR电话验证
     * 如果Redis中已存在该手机号的taskId，会先检查该taskId的状态
     * 如果状态是success或pending，直接返回该taskId
     * 如果状态是fail，删除Redis中的taskId，重新发起IVR呼叫
     * 如果Redis中不存在taskId，直接发起IVR呼叫
     *
     * @param mobilePhone 手机号
     * @param countryCode 国家码
     * @param channel 渠道，如"tms_driver_register"
     * @param language 语言，如 CN
     * @return IVR任务ID
     */
    Long callPhoneForVerify(String mobilePhone, String countryCode, String channel, String language);

    /**
     * 获取配置的N次可发起IVR呼叫的时间(单位:分钟)
     * @param cityId 城市ID
     * @return N次可发起IVR呼叫的时间(单位:分钟)
     */
    List<Long> getLocalTimeForIvrCall(Long cityId);

    /**
     * 根据当地时间异步发起（配置）指定次数的Ivr外呼(单位:分钟)
     *
     * @param cityId   城市ID
     * @param phoneDTO 手机号
     * @param language
     */
    void asyncSendIvrByLocalTimeForIvrCall(Long cityId, PhoneDTO phoneDTO, String language);

    /**
     * 获取配置的N次可发起IVR呼叫的时间(单位:分钟)
     * @param phoneDTOList 手机号列表
     * @return map<手机号, 是否经过验证>
     */
    Map<String, Boolean> isPhoneVerified(List<PhoneDTO> phoneDTOList);

    /**
     * 手机号是否验证通过
     * @return map<手机号, 是否经过验证>
     */
    Boolean isPhoneVerified(PhoneDTO phoneDTO);

    /**
     * 发送irv校验通过消息
     * @param phoneDTO 手机号
     */
    void sendIvrVerifiedMessage(PhoneDTO phoneDTO);

    /**
     * 异步消息处理irv校验结果
     * @param delayMin 延迟分钟数
     * @param phoneDTO 手机号
     */
    void sendIvrVerifyDelayMessage(int delayMin, PhoneDTO phoneDTO);


}
