package com.ctrip.dcs.tms.transport.infrastructure.service.impl;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.driver.domain.account.PhoneInfo;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.GeoGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.model.CallPhoneForVerifyRequestType;
import com.ctrip.model.CallPhoneForVerifyResponseType;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * IVR电话呼叫服务实现类
 */
@Service
public class IVRCallServiceImpl implements IVRCallService {
    private static final Logger logger = LoggerFactory.getLogger(IVRCallServiceImpl.class);
    private static final String IVR_VERIFICATION_KEY_PREFIX = "tms_ivr_result_";

    @Autowired
    private DriverDomainService driverDomainService;

    @Autowired
    private EnumRepositoryHelper enumRepositoryHelper;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    GeoGateway geoGateway;

    @Autowired
    TmsQmqProducer tmsQmqProducer;

    @Autowired
    BusinessQConfig businessQConfig;

    @Autowired
    EnumRepository enumRepository;

    private static final List<String> SUPPORTED_LANGUAGES = Arrays.asList("CN", "EN", "FR", "JP", "TH", "ES");

    /**
     * 发起IVR电话验证 如果Redis中已存在该手机号的taskId，会先检查该taskId的状态 如果状态是success或pending，直接返回该taskId
     * 如果状态是fail，删除Redis中的taskId，重新发起IVR呼叫 如果Redis中不存在taskId，直接发起IVR呼叫
     *
     * @param mobilePhone 手机号
     * @param countryCode 国家码
     * @param channel     渠道，如"tms_driver_register"
     * @param language    语言，如 CN
     * @return IVR任务ID
     */
    @Override
    public Long callPhoneForVerify(String mobilePhone, String countryCode, String channel,
        String language) {
        Cat.logEvent(CatEventType.IVR_VOICE_CALL, "method_entry");
        language = getLanguage(language);

        if (StringUtils.isEmpty(mobilePhone) || StringUtils.isEmpty(countryCode)) {
            logger.error("callPhoneForVerify", "Invalid parameters: mobilePhone={}, countryCode={}",
                mobilePhone, countryCode);
            return null;
        }
        // 创建Redis键，使用国家码和手机号
        String redisKey = IVR_VERIFICATION_KEY_PREFIX + countryCode + "_" + mobilePhone;

        // 从Redis中查询是否已有IVR任务信息
        IVRTaskInfo existingTaskInfo = RedisUtils.get(redisKey);

        if (existingTaskInfo != null && existingTaskInfo.getTaskId() != null) {
            Cat.logEvent(CatEventType.IVR_VOICE_CALL, "existing_task_found", Event.SUCCESS, "taskId:" + existingTaskInfo.getTaskId());
            logger.info("callPhoneForVerify", "Found existing IVR task for phone: {} with country code: {}, taskId: {}",
                mobilePhone, countryCode, existingTaskInfo.getTaskId());

            // 检查taskId是否在有效期内
            long currentTime = System.currentTimeMillis();
            long taskValidityMillis = commonConfig.getIvrTaskIdValidityMinutes() * 60 * 1000L;
            boolean isTaskValid = (currentTime - existingTaskInfo.getCreateTime()) <= taskValidityMillis;

            if (!isTaskValid) {
                // taskId已过期，删除Redis中的记录
                Cat.logEvent(CatEventType.IVR_VOICE_CALL, "task_expired", "WARN",
                    "taskId:" + existingTaskInfo.getTaskId() + ",age:" + (currentTime - existingTaskInfo.getCreateTime()));
                logger.info("callPhoneForVerify", "Existing IVR task expired, will initiate new call. taskId: {}, age: {} ms",
                    existingTaskInfo.getTaskId(), (currentTime - existingTaskInfo.getCreateTime()));

                // 删除Redis中的过期taskId
                RedisUtils.del(redisKey);
            } else {
                // taskId仍在有效期内，查询IVR结果
                String ivrResult = queryIVRResult(existingTaskInfo.getTaskId());

                // 如果结果是success或pending，直接返回现有taskId
                if ("success".equals(ivrResult) || "pending".equals(ivrResult)) {
                    Cat.logEvent(CatEventType.IVR_VOICE_CALL, "using_existing_task", Event.SUCCESS,
                        "taskId:" + existingTaskInfo.getTaskId() + ",result:" + ivrResult);
                    logger.info("callPhoneForVerify", "Using existing IVR task with result: {}, taskId: {}",
                        ivrResult, existingTaskInfo.getTaskId());

                    return existingTaskInfo.getTaskId();
                } else if ("fail".equals(ivrResult)) {
                    // 如果结果是fail，删除Redis中的taskId，重新发起IVR呼叫
                    Cat.logEvent(CatEventType.IVR_VOICE_CALL, "previous_task_failed", "WARN",
                        "taskId:" + existingTaskInfo.getTaskId());
                    logger.info("callPhoneForVerify", "Previous IVR task failed, will initiate new call. taskId: {}",
                        existingTaskInfo.getTaskId());

                    // 删除Redis中的taskId
                    RedisUtils.del(redisKey);
                }
            }
        }

        // 创建新的IVR呼叫请求
        CallPhoneForVerifyRequestType callPhoneForVerifyRequestType = new CallPhoneForVerifyRequestType();
        callPhoneForVerifyRequestType.setPhoneNumber(TmsTransUtil.encrypt(mobilePhone, KeyType.Phone));
        callPhoneForVerifyRequestType.setLocale(language);
        callPhoneForVerifyRequestType.setCountryCode(countryCode);
        callPhoneForVerifyRequestType.setChannel(channel);

        // 发起IVR呼叫
        Cat.logEvent(CatEventType.IVR_VOICE_CALL, "initiating_new_call");
        CallPhoneForVerifyResponseType callPhoneForVerifyResponseType = driverDomainService.callPhoneForVerify(callPhoneForVerifyRequestType);

        // 检查呼叫是否成功发起
        if (callPhoneForVerifyResponseType != null && callPhoneForVerifyResponseType.getCallTaskId() != null) {
            Long newTaskId = callPhoneForVerifyResponseType.getCallTaskId();

            // 创建新的IVR任务信息，包含taskId和当前时间
            IVRTaskInfo newTaskInfo = new IVRTaskInfo(newTaskId, System.currentTimeMillis());

            // 将新的任务信息存入Redis，使用配置的过期时间（默认24小时）
            int redisTtlSeconds = commonConfig.getIvrRedisTtlHours() * 60 * 60;
            RedisUtils.set(redisKey, redisTtlSeconds, newTaskInfo);

            Cat.logEvent(CatEventType.IVR_VOICE_CALL, "new_call_initiated", Event.SUCCESS, "taskId:" + newTaskId);
            logger.info("callPhoneForVerify", "Successfully initiated new IVR call for phone: {} with country code: {}, taskId: {}",
                mobilePhone, countryCode, newTaskId);

            return newTaskId;
        } else {
            Cat.logEvent(CatEventType.IVR_VOICE_CALL, "call_initiation_failed");
            logger.error("callPhoneForVerify", "Failed to initiate IVR call for phone: {} with country code: {}",
                mobilePhone, countryCode);

            return null;
        }
    }

    protected String getLanguage(String language) {
        if (StringUtils.isBlank(language)) {
            // 如果 language 为 null，返回默认语言代码
            return enumRepositoryHelper.getLocaleCode();
        }

        List<String> languageList = Arrays.asList(language.split(","));

        // 检查传入的语言是否在支持的语言列表中
        for (String supportedLanguage : SUPPORTED_LANGUAGES) {
            if (languageList.contains(supportedLanguage)) {
                return supportedLanguage;
            }
        }

        // 如果没有匹配的语言，返回默认语言代码
        return enumRepositoryHelper.getLocaleCode();
    }

    /**
     * 获取配置的N次可发起IVR呼叫的当地时间(单位:分钟)
     * @param cityId 城市ID
     * @return N次可发起IVR呼叫的当地时间(单位:分钟)
     */
    @Override
    public List<Long> getLocalTimeForIvrCall(Long cityId) {

        LocalDateTime currentDateTime = getLocationTime(cityId);

        // 解析可以呼叫的时间字符串
        LocalDateTime startTime = LocalTime.parse(commonConfig.getIrvCallAbleStartTime(), DateTimeFormatter.ofPattern(
            DateUtil.HHMMSS)).atDate(currentDateTime.toLocalDate());
        LocalDateTime endTime = LocalTime.parse(commonConfig.getIrvCallAbleEndTime(), DateTimeFormatter.ofPattern(DateUtil.HHMMSS)).atDate(currentDateTime.toLocalDate());

        // 返回N次可发起IVR呼叫的时间
        return generateTimePoints(startTime, endTime, currentDateTime, commonConfig.getIrvCallIntervalMin(), cityId);
    }

    /**
     * 根据当地时间异步发起（配置）指定次数的Ivr外呼(单位:分钟)
     *
     * @param cityId   城市ID
     * @param phoneDTO 手机号
     * @param language
     */
    @Override
    public void asyncSendIvrByLocalTimeForIvrCall(Long cityId, PhoneDTO phoneDTO, String language) {
        if (!businessQConfig.isVerifyPhone(enumRepository.isNotChinaMainLand(cityId) ? AreaScopeTypeEnum.OVERSEAS.getCode() : AreaScopeTypeEnum.DOMESTIC.getCode())) {
            Cat.logEvent(CatEventType.IVR_VOICE_CALL, "Not_over_sea_phone", Event.SUCCESS, "phone:" + phoneDTO.getMobilePhone());
            return;
        }
        // 发送异步呼叫IVR，校验手机号
        Cat.logEvent(CatEventType.IVR_VOICE_CALL, "callIVR_send_delay_message", Event.SUCCESS, "phone:" + phoneDTO.getMobilePhone());
        Map<String, Object> params = Maps.newHashMap();
        params.put("phoneNumber", phoneDTO.getMobilePhone());
        params.put("igtCode", phoneDTO.getCountryCode());
        params.put("modifyUser", phoneDTO.getModifyUser());
        params.put("language", language);
        // 根据实际规则，获取呼叫次数,在消息中呼叫IRV
        for (Long time : this.getLocalTimeForIvrCall(cityId)) {
            tmsQmqProducer.sendDelayMessage(
                TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL, null,
                time,
                TimeUnit.MINUTES,
                params);
        }
    }

    /**
     * 手机号是否验证通过
     *
     * @param phoneDTOList 手机号列表
     * @return map<区号-手机号, 是否经过验证>
     */
    @Override
    public Map<String, Boolean> isPhoneVerified(List<PhoneDTO> phoneDTOList) {
        try {
            if (CollectionUtils.isEmpty(phoneDTOList)) {
                return Maps.newHashMap();
            }

            phoneDTOList = phoneDTOList.stream().filter(phoneDTO -> StringUtils.isNotBlank(phoneDTO.getMobilePhone())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(phoneDTOList)) {
                return Maps.newHashMap();
            }
            //加密手机号
            phoneDTOList.forEach(phoneDTO -> phoneDTO.setMobilePhone(TmsTransUtil.encrypt(phoneDTO.getMobilePhone(), KeyType.Phone)));

            QueryCallPhoneForVerifyResultRequestType request = new QueryCallPhoneForVerifyResultRequestType();
            request.setPhoneInfoList(convert2PhoneData(phoneDTOList));
            QueryCallPhoneForVerifyResultResponseType result = driverDomainService.queryCallPhoneForVerifyResult(request);

            //如果查询报错，默认手机号过验证
            if (!result.responseResult.isSuccess()) {
                return phoneDTOList.stream().collect(Collectors.toMap(item -> contract(item.getCountryCode(),item.getMobilePhone()), phoneDTO -> true,
                    (existingValue, newValue) -> existingValue));
            }

            //获取IRV校验结果
            Map<String, Boolean> ivrResult = result.getCallResultList().stream().collect(
                Collectors.toMap(item -> contract(item.getCountryCode(), item.getPhoneNumber()),
                    item -> Objects.equals(item.isIsInPhoneCheck(), true) || Optional.ofNullable(item.getCallHangupCodeList())
                        .orElse(Lists.newArrayList()).stream().anyMatch(
                            hangupCode -> commonConfig.getCallResultStatus().contains(hangupCode)),
                    (existingValue, newValue) -> existingValue));
            //打印日志
            ivrResult.forEach((phone, verifyResult) -> Cat.logEvent(CatEventType.MOBILE_VERIFIED, String.valueOf(verifyResult)));

            return phoneDTOList.stream()
                .collect(Collectors
                    .toMap(item -> contract(item.getCountryCode(),item.getMobilePhone()),
                        item -> ivrResult.getOrDefault(contract(item.getCountryCode(),item.getMobilePhone()), false),
                        (existingValue, newValue) -> existingValue));
        } catch (Exception e) {
            logger.error("getLocalTimeForIvrCall", e);
            return phoneDTOList.stream().collect(Collectors.toMap(item -> contract(item.getCountryCode(),item.getMobilePhone()), phoneDTO -> true,
                (existingValue, newValue) -> existingValue));
        }
    }

    private String contract(String igtCode, String phone) {
        return igtCode + "-" + phone;
    }

    /**
     * 手机号是否验证通过
     *
     * @param phoneDTO 手机号
     * @return map<区号-手机号, 是否经过验证>
     */
    @Override
    public Boolean isPhoneVerified(PhoneDTO phoneDTO) {
        if(StringUtils.isBlank(phoneDTO.getCountryCode()) || StringUtils.isBlank(phoneDTO.getMobilePhone())) {
            return true;
        }
        return isPhoneVerified(Lists.newArrayList(phoneDTO)).getOrDefault(contract(phoneDTO.getCountryCode(), TmsTransUtil.encrypt(phoneDTO.getMobilePhone(), KeyType.Phone)), false);
    }

    /**
     * 发送irv校验通过消息
     *
     * @param phoneDTO 手机号
     */
    @Override
    public void sendIvrVerifiedMessage(PhoneDTO phoneDTO) {
        sendIvrVerifyDelayMessage(0, phoneDTO);
    }

    /**
     * 异步消息处理irv校验结果
     *
     * @param delayMin 延迟分钟数
     * @param phoneDTO 手机号
     */
    @Override
    public void sendIvrVerifyDelayMessage(int delayMin, PhoneDTO phoneDTO) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("phoneNumber", phoneDTO.getMobilePhone());
        params.put("igtCode", phoneDTO.getCountryCode());
        params.put("modifyUser", phoneDTO.getModifyUser());
        tmsQmqProducer.sendDelayMessage(
            TmsTransportConstant.QmqSubject.SUBJECT_IVR_CALL_VERIFICATION,
            Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_IVR_CALL_VERIFICATION),
            delayMin,
            TimeUnit.MINUTES,
            params);
    }

    private List<PhoneInfo> convert2PhoneData(List<PhoneDTO> phoneDTOList) {
        return phoneDTOList.stream().map(phoneDTO -> {
            PhoneInfo phoneInfo = new PhoneInfo();
            phoneInfo.setCountryCode(phoneDTO.getCountryCode());
            phoneInfo.setPhoneNumber(phoneDTO.getMobilePhone());
            return phoneInfo;
        }).collect(Collectors.toList());
    }

    protected LocalDateTime getLocationTime(Long cityId) {
        LocalDateTime localCurrentTime = geoGateway.getLocalCurrentTime(cityId);
        return localCurrentTime == null ? LocalDateTime.now() : localCurrentTime;
    }

    /**
     * 生成N个符合指定时间区间的当地时间点，并返这N个当前回距离的指定时间点的分钟数
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param currentDateTime 当地时间
     * @param intervals N个时间间隔
     * @param cityId 城市ID
     * @return 当前回距离的指定时间点的分钟数
     */
    public List<Long> generateTimePoints(LocalDateTime startTime, LocalDateTime endTime, LocalDateTime currentDateTime, List<Integer> intervals, Long cityId) {
        List<Long> timePoints = new ArrayList<>();
        LocalDateTime originCurrentDateTime = currentDateTime;

        for (int interval : intervals) {
            LocalDateTime nextDateTime = currentDateTime.plusMinutes(interval);
            //如果时间在开始结束前，则取开始时间
            if (nextDateTime.isBefore(startTime)) {
                nextDateTime = startTime;
            }

            // 如果时间在配置的结束时间之后，则跳到第二天
            if (nextDateTime.isAfter(endTime)) {
                // 如果超过结束时间，将时间顺延到第二天的开始时间
                startTime = startTime.plusDays(1);
                endTime = endTime.plusDays(1);
                nextDateTime = startTime;
            }
            timePoints.add(Duration.between(originCurrentDateTime, nextDateTime).toMinutes());
            currentDateTime = nextDateTime;
        }
        Cat.logEvent(CatEventType.IVR_CALL_TIME, cityId + ":" + JsonUtils.toJson(timePoints));
        return timePoints;
    }

    /**
     * 查询IVR电话验证结果
     * @param taskId IVR任务ID
     * @return 结果状态：success, pending, fail
     */
    private String queryIVRResult(Long taskId) {
        try {
            Cat.logEvent(CatEventType.IVR_VOICE_CALL, "query_ivr_result", Event.SUCCESS, "taskId:" + taskId);

            // 使用GetIVRVoiceResultExecutor的逻辑查询结果
            QueryCallPhoneForVerifyResultRequestType requestType = new QueryCallPhoneForVerifyResultRequestType();
            requestType.setCallTaskId(taskId);

            QueryCallPhoneForVerifyResultResponseType result = driverDomainService.queryCallPhoneForVerifyResult(requestType);
            String callResultStatus = result.getCallResultStatus();

            if (StringUtils.isBlank(callResultStatus)) {
                Cat.logEvent(CatEventType.IVR_VOICE_CALL, "pending_status");
                return "pending";
            } else {
                boolean isSuccess = commonConfig.getCallResultStatus().contains(callResultStatus);
                Cat.logEvent(CatEventType.IVR_VOICE_CALL, isSuccess ? "success_status" : "fail_status",
                        isSuccess ? Event.SUCCESS : "1", "callResultStatus:" + callResultStatus);
                return isSuccess ? "success" : "fail";
            }
        } catch (Exception e) {
            logger.error("queryIVRResult", "Error querying IVR result for taskId: {}", taskId, e);
            // 查询出错时，返回pending，让系统重新尝试
            return "pending";
        }
    }

    /**
     * IVR任务信息内部类
     */
    public static class IVRTaskInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long taskId;
        private Long createTime;

        public IVRTaskInfo() {}

        public IVRTaskInfo(Long taskId, Long createTime) {
            this.taskId = taskId;
            this.createTime = createTime;
        }

        public Long getTaskId() {
            return taskId;
        }

        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }

        public Long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Long createTime) {
            this.createTime = createTime;
        }
    }
}
