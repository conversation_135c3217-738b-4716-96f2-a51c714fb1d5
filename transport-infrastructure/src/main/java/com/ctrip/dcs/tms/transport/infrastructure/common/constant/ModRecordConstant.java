package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import com.google.common.collect.*;

import java.util.*;

/**
 * 变更记录常量信息
 * <AUTHOR>
 * @Date 2020/10/28 16:55
 */
public class ModRecordConstant {

    /**
     * 维护需要记变更记录的数据库表名
     */
    public class TableName{
        /**
         * 司机表
         */
        public static final String drvDriver = "drv_driver";
        /**
         * 请假记录表
         */
        public static final String drvDriverLeave = "drv_driver_leave";

        /**
         * 司机冻结记录表
         */
        public static final String tmsDrvFreeze = "tms_drv_freeze";
        /**
         * 车辆表
         */
        public static final String vehVehicle = "veh_vehicle";
        /**
         * 运力组表
         */
        public static final String tspTransportGroup = "tsp_transport_group";

        /**
         * 运力组进单配置
         */
        public static final String tspIntoOrderConfig = "tsp_into_order_config";

        /**
         * 运力组关联司机
         */
        public static final String tspTransportGroupDriverRelation = "tsp_transport_group_driver_relation";

        /**
         * 运力组关联sku
         */
        public static final String tspTransportGroupSkuAreaRelation = "tsp_transport_group_sku_area_relation";

        /**
         * 编辑后审批
         */
        public static final String tmsTransportApprove = "tms_transport_approve";
        /**
         * 司机车辆证件审核表
         */
        public static final String  tmsCertificateCheck = "tms_certificate_check";

        /**
         * 境外司机派遣
         */
        public static final String  tmsDrvDispatchRelation = "tms_drv_dispatch_relation";

    }


    /**
     * 维护表对应变更记录的RrdType
     */
    public static final Map<String, Integer> modRecordRrdTypeMap = Maps.newHashMap();

    /**
     * 维护表对应变更记录的RrdId取值字段
     */
    public static Map<String,String> modRecordRrdIdMap = Maps.newHashMap();

    static {

        /**
         * 初始化每张表对应变更记录的类型
         */
        modRecordRrdTypeMap.put(TableName.drvDriver,CommonEnum.RecordTypeEnum.DRIVER.getCode());
        modRecordRrdTypeMap.put(TableName.drvDriverLeave,CommonEnum.RecordTypeEnum.DRIVER.getCode());
        modRecordRrdTypeMap.put(TableName.tmsDrvFreeze,CommonEnum.RecordTypeEnum.DRIVER.getCode());
        modRecordRrdTypeMap.put(TableName.vehVehicle,CommonEnum.RecordTypeEnum.VEHICLE.getCode());
        modRecordRrdTypeMap.put(TableName.tspTransportGroup,CommonEnum.RecordTypeEnum.TRANSPORT_GROUP.getCode());
        modRecordRrdTypeMap.put(TableName.tspIntoOrderConfig,CommonEnum.RecordTypeEnum.TRANSPORT_GROUP.getCode());
        modRecordRrdTypeMap.put(TableName.tspTransportGroupDriverRelation,CommonEnum.RecordTypeEnum.TRANSPORT_GROUP.getCode());
        modRecordRrdTypeMap.put(TableName.tspTransportGroupSkuAreaRelation,CommonEnum.RecordTypeEnum.TRANSPORT_GROUP.getCode());
        modRecordRrdTypeMap.put(TableName.tmsTransportApprove,CommonEnum.RecordTypeEnum.DRIVER.getCode());
        modRecordRrdTypeMap.put(TableName.tmsDrvDispatchRelation,CommonEnum.RecordTypeEnum.DRV_DISPATCH.getCode());

        /**
         * 初始化每张表对应的变更记录实体id取值字段名称（如果不设置，则默认为表的主键）
         */
        modRecordRrdIdMap.put(TableName.drvDriver,"drv_id");
        modRecordRrdIdMap.put(TableName.drvDriverLeave,"drv_id");
        modRecordRrdIdMap.put(TableName.tmsDrvFreeze,"drv_id");
        modRecordRrdIdMap.put(TableName.vehVehicle,"vehicle_id");
        modRecordRrdIdMap.put(TableName.tspTransportGroup,"transport_group_id");
        modRecordRrdIdMap.put(TableName.tspIntoOrderConfig,"transport_group_id");
        modRecordRrdIdMap.put(TableName.tspTransportGroupDriverRelation,"transport_group_id");
        modRecordRrdIdMap.put(TableName.tspTransportGroupSkuAreaRelation,"transport_group_id");
        modRecordRrdIdMap.put(TableName.tmsTransportApprove,"approve_source_id");
        modRecordRrdIdMap.put(TableName.tmsDrvDispatchRelation,"drv_id");
    }

}
