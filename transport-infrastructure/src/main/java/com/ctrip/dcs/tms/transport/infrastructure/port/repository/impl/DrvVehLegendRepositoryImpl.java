package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvVehLegendPO;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvVehLegendRepository;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.List;

/**
 * 　* @description: 司机车辆图例
 * 　* <AUTHOR>
 * 　* @date 2023/6/8 14:53
 */
@Repository
public class DrvVehLegendRepositoryImpl implements DrvVehLegendRepository {

    private DalRepository<DrvVehLegendPO> drvVehLegendRepo;

    public DrvVehLegendRepositoryImpl() throws SQLException {
        drvVehLegendRepo = new DalRepositoryImpl<>(DrvVehLegendPO.class);
    }

    @Override
    public DrvVehLegendPO queryByPk(Long id) {
        return drvVehLegendRepo.queryByPk(id);
    }

    @Override
    public List<DrvVehLegendPO> queryDrvVehLegendList() {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.equal("active", Boolean.TRUE, Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            return drvVehLegendRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}
