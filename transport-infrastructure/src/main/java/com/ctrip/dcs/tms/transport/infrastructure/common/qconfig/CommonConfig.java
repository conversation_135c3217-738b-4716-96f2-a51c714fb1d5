package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import lombok.Data;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;

@Component
@Data
public class CommonConfig {

    @QMapConfig(value = "transport.oversea.properties", key = "cityIdList", defaultValue = "")
    private List<Long> cityIdList;

    @QMapConfig(value = "transport.oversea.properties", key = "overageGraySwitch", defaultValue = "true")
    private Boolean overageGraySwitch;

    @QMapConfig(value = "transport.oversea.properties", key = "whiteVehicleList", defaultValue = "")
    private List<Long> whiteVehicleList;

    @QMapConfig(value = "transport.oversea.properties", key = "filterOnlineDrv", defaultValue = "0,1,2,3")
    private List<Integer> filterOnlineDrv;

    @QMapConfig(value = "transport.oversea.properties", key = "filterHeadImg", defaultValue = "")
    private List<String> filterHeadImg;

    @QMapConfig(value = "transport.oversea.properties", key = "realNameAuthSwitch", defaultValue = "true")
    private Boolean realNameAuthSwitch;

    @QMapConfig(value = "transport.oversea.properties", key = "resultCodeList", defaultValue = "0")
    private List<Integer> resultCodeList;

    @QMapConfig(value = "transport.oversea.properties", key = "callResultStatus", defaultValue = "0")
    private List<String> callResultStatus;

    @QMapConfig(value = "transport.oversea.properties", key = "ivrVoiceResultFallbackEnabled", defaultValue = "false")
    private Boolean ivrVoiceResultFallbackEnabled;

    @QMapConfig(value = "transport.oversea.properties", key = "loginTypeList", defaultValue = "1,3")
    private List<Integer> loginTypeList;

    @QMapConfig(value = "transport.oversea.properties", key = "ivrVerificationExpirationMinutes", defaultValue = "10")
    private Integer ivrVerificationExpirationMinutes;

    @QMapConfig(value = "transport.oversea.properties", key = "ivrTaskIdValidityMinutes", defaultValue = "10")
    private Integer ivrTaskIdValidityMinutes;

    @QMapConfig(value = "transport.oversea.properties", key = "ivrRedisTtlHours", defaultValue = "24")
    private Integer ivrRedisTtlHours;

    @QMapConfig(value = "transport.oversea.properties", key = "irvCallAbleStartTime", defaultValue = "09:00:00")
    private String irvCallAbleStartTime;

    @QMapConfig(value = "transport.oversea.properties", key = "irvCallAbleEndTime", defaultValue = "21:00:00")
    private String irvCallAbleEndTime;

    // 呼叫间隔时间
    @QMapConfig(value = "transport.oversea.properties", key = "irvCallIntervalMin", defaultValue = "0,10,120,10")
    private List<Integer> irvCallIntervalMin;

    public Integer getIvrVerificationExpirationMinutes() {
        return ivrVerificationExpirationMinutes != null ? ivrVerificationExpirationMinutes : 10;
    }

    public Integer getIvrTaskIdValidityMinutes() {
        return ivrTaskIdValidityMinutes != null ? ivrTaskIdValidityMinutes : 10;
    }

    public Integer getIvrRedisTtlHours() {
        return ivrRedisTtlHours != null ? ivrRedisTtlHours : 24;
    }
}
