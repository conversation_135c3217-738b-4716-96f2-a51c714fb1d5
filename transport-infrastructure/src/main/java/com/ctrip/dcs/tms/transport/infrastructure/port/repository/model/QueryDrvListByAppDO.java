package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.util.*;

public class QueryDrvListByAppDO {

    private Long drvId;
    private Long drvIdFrom;
    private List<Integer> drvStatus;
    private Integer limit;

    public Long getDrvId() {
        return drvId;
    }

    public void setDrvId(Long drvId) {
        this.drvId = drvId;
    }

    public Long getDrvIdFrom() {
        return drvIdFrom;
    }

    public void setDrvIdFrom(Long drvIdFrom) {
        this.drvIdFrom = drvIdFrom;
    }

    public List<Integer> getDrvStatus() {
        return drvStatus;
    }

    public void setDrvStatus(List<Integer> drvStatus) {
        this.drvStatus = drvStatus;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }
}
