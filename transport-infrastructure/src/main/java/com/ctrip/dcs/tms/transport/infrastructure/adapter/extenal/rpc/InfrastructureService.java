package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.igt.framework.soa.client.*;
import com.ctrip.igt.infrastructureservice.api.IGTInfrastructureServiceClient;
import com.ctrip.igt.infrastructureservice.executor.contract.*;

@ServiceClient(value = IGTInfrastructureServiceClient.class,format = "json")
public interface InfrastructureService {

    SendMesByPhoneResponseType sendMessageByPhone(SendMesByPhoneRequestType request);

    CheckMobilePhoneCodeResponseType checkPhoneCode(CheckMobilePhoneCodeRequestType request);

    SendEmailResponseType sendEmail(SendEmailRequestType request);

    SendMessageResponseType sendMessage(SendMessageRequestType request);

    DrivingLicenseResponseType drivingLicense(DrivingLicenseRequestType requestType);

    VehicleLicenseResponseType vehicleLicense(VehicleLicenseRequestType requestType);

    GetFaceAuthSignResponseType getFaceAuthSign(GetFaceAuthSignRequestType request);

    GetFaceResultResponseType getFaceResult(GetFaceResultRequestType request);

    PlateLicenseResponseType plateLicense(PlateLicenseRequestType requestType);

    NameTranslateResponseType nameTranslate(NameTranslateRequestType request);

    CarOCRResponseType carOCR(CarOCRRequestType request);
}
