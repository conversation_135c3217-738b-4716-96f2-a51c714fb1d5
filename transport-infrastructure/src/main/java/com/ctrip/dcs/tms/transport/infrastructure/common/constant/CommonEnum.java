package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.*;

import java.util.*;

/**
 * 公用枚举
 */
public class CommonEnum {

    public enum RecordTypeEnum {

        TRANSPORT_GROUP("Transport", 1),
        DRIVER("Driver", 2),
        VEHICLE("Vehicle", 3),
        RECRUIT("Recruiting", 4),
        CHECK("Review", 5),
        DrVFREEZE("drvFreeze", 6),
        VEHRECRUIT("VehRecruiting", 7),
        VEH_CHECK("Veh_Review", 8),
        UPDATE_APPROVE("update_approve", 9),
        VERIFY_EVENT("update_approve", 10),
        DRV_SINGLE_APPROVE("drv_single_approve", 11),
        VEH_SINGLE_APPROVE("veh_single_approve", 12),
        DRV_DISPATCH("drv_dispatch", 13),
        //运力组审批（上云需要）
        TRANSPORT_APPROVE("transport_approve", 14),
        ;

        private String message;
        private Integer code;

        RecordTypeEnum(String message, Integer code) {
            this.message = message;
            this.code = code;
        }

        public static RecordTypeEnum findTypeByCode(Integer code) {
            for (RecordTypeEnum type : RecordTypeEnum.values()) {
                if (Objects.equals(type.getCode(), code)) {
                    return type;
                }
            }
            return null;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }
    }

    /**
     * 接收进单通知
     */
    public enum InformSwitchEnum {

        CLOSE(0, "transport.informSwitchEnum.close"),
        OPEN(1, "transport.informSwitchEnum.open");

        private Integer value;
        private String text;

        InformSwitchEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (InformSwitchEnum s : InformSwitchEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }

    }

    /**
     * 冻结状态
     */
    public enum FreezeStatusEnum {
        /**
         * 冻结
         */
        FREEZE(2, "transport.freezeStatusEnum.freeze"),

        /**
         * 解冻
         */
        UNFREEZE(1, "transport.freezeStatusEnum.unfreeze");

        private Integer value;
        private String text;

        FreezeStatusEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (FreezeStatusEnum s : FreezeStatusEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }

    }

    /**
     * 解冻后置操作
     */
    public enum UnfreezeActionEnum {
        /**
         * 解冻后上线
         */
        online(1, "transport.unfreezeActionEnum.online"),

        /**
         * 解冻后下线
         */
        offline(2, "transport.unfreezeActionEnum.offline");

        private Integer value;
        private String text;

        UnfreezeActionEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (UnfreezeActionEnum s : UnfreezeActionEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }

    }

    /**
     * 冻结订单操作
     */
    public enum FreezeOrderSetEnum {
        /**
         * 不改派
         */
        notDispatch(1, "transport.freezeOrderSetEnum.notDispatch"),

        /**
         * 改派
         */
        dispatch(2, "transport.freezeOrderSetEnum.dispatch");

        private Integer value;
        private String text;

        FreezeOrderSetEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (FreezeOrderSetEnum s : FreezeOrderSetEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }

    }

    // 修改类型
    public enum ModTypeEnum {

        CREATE(0, "transport.modTypeEnum.create"), UPDATE(1, "transport.modTypeEnum.update"), DELETE(2, "transport.modTypeEnum.delete");

        private Integer value;
        private String text;

        ModTypeEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (ModTypeEnum s : ModTypeEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    /**
     * 63燃油车辆，64纯电动车辆，65油电混合车辆
     * */
    public enum EnergyEnum {

        FUEL(63, "FUEL"), CELL(64, "CELL"), MIX(65, "MIX");

        private Integer value;
        private String text;

        EnergyEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return value;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * C 产线Code码
     */
    public enum ProductionLineCodeEnum {
        JNT("jnt"), DAY("day"), RTN("rtn"), PTP("point");

        private String value;

        ProductionLineCodeEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static ProductionLineCodeEnum getEnumByValue(String value) {
            for (ProductionLineCodeEnum lineCodeEnum : ProductionLineCodeEnum.values()) {
                if (lineCodeEnum.getValue().equals(value)) {
                    return lineCodeEnum;
                }
            }
            return null;
        }
    }

    /**
     * 冻结来源
     */
    public enum FreezeOPFromEnum {
        /**
         * 供应商(默认)
         */
        SUPPLIER(1, "SUPPLIER",7),

        /**
         * 运营、客服
         */
        BD(2, "BD",6),

        /**
         * 判罚
         */
        PUNISH(3, "PUNISH", 2),

        /**
         * 判罚下线
         */
        PUNISH_OFFLINE(4, "PUNISH_OFFLINE", 1),

        /**
         * 采购派发（车辆损毁冻结）
         */
        DISPATCH(5, "DISPATCH", 5),

        /**
         * 结算冻结
         */
        SETTLEMENT(6, "SETTLEMENT", 4),

        /**
         * 司机端风控
         */
        DRIVER_RISK_CONTROL(7, "DRIVER_RISK_CONTROL", 3),

        /**
         * 安全分跌零冻结
         */
        DRV_SAFE_POINT(8, "DRV_SAFE_POINT", 4);

        private Integer value;
        private String text;
        //优先级，值越小，优先级越高
        private int priority;

        FreezeOPFromEnum(Integer value, String text, int priority) {
            this.value = value;
            this.text = text;
            this.priority = priority;
        }

        public static int getPriority(Integer o1, Integer o2) {
            return getPriorityFromCode(o1) - getPriorityFromCode(o2);
        }

        private static int getPriorityFromCode(Integer code) {
            FreezeOPFromEnum opFrom = FreezeOPFromEnum.valueOfCode(code);
            return opFrom == null ? Integer.MAX_VALUE : opFrom.priority;
        }

        public static FreezeOPFromEnum valueOfCode(Integer code) {
            for (FreezeOPFromEnum opFrom : FreezeOPFromEnum.values()) {
                if (opFrom.getValue().equals(code)) {
                    return opFrom;
                }
            }
            return null;
        }

        public Integer getValue() {
            return value;
        }

        public String getText() {
            return text;
        }
    }

    public enum NewEnergyEnum {

        YES(1, "YES"), NO(0, "NO");

        private Integer value;
        private String text;

        NewEnergyEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return value;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * <AUTHOR> 2021-03-30 16:22
     * 用车产线存储枚举内部使用
     * 扩展产线，必须遵循二进制模式扩展
     * 00000001 1 接送机
     * 00000010 2 打车
     * 00000100 4 包车
     * 00001000 8 及其他产线
     * 参考：http://conf.ctripcorp.com/pages/viewpage.action?pageId=562791396
     */
    public enum UseProductionLineEnum {

        AIRPORT_TRANSPORTATION(1, ProductionLineCodeEnum.JNT.getValue()),
        CHARTER_TRANSPORTATION(4, ProductionLineCodeEnum.DAY.getValue()),
        RTN_TRANSPORTATION(2, ProductionLineCodeEnum.RTN.getValue()),
        PTP_TRANSPORTATION(8, ProductionLineCodeEnum.PTP.getValue());

        private Integer value;
        private String text;

        UseProductionLineEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return value;
        }

        public String getText() {
            return text;
        }

        private static Integer allLine() {
            int allLine = 0;
            for (UseProductionLineEnum lineEnum : UseProductionLineEnum.values()) {
                allLine += lineEnum.getValue();
            }
            return allLine;
        }

        public static UseProductionLineEnum getEnumByCode(Integer code) {
            for (UseProductionLineEnum lineEnum : UseProductionLineEnum.values()) {
                if (lineEnum.value.intValue() == code.intValue()) {
                    return lineEnum;
                }
            }
            return null;
        }

        public static List<Integer> getSeparateProductionLineList(Integer integratedLine) {
            if (integratedLine == null || integratedLine <= 0 || integratedLine > allLine()) {
                return Collections.emptyList();
            }
            List<Integer> lineList = Lists.newArrayListWithExpectedSize(UseProductionLineEnum.values().length);
            for (UseProductionLineEnum lineEnum : UseProductionLineEnum.values()) {
                if ((integratedLine & lineEnum.value) > 0) {
                    lineList.add(lineEnum.value);
                }
            }
            return lineList;
        }

        public static List<Integer> getIncludeProductionLineList(UseProductionLineEnum productionLineEnum) {
            if (productionLineEnum == null) {
                return Collections.emptyList();
            }
            List<Integer> includeProductionLineList = Lists.newArrayListWithExpectedSize(UseProductionLineEnum.values().length);
            for (int i = allLine(); i >= 1; i--) {
                if ((i & productionLineEnum.value) > 0) {
                    includeProductionLineList.add(i);
                }
            }
            return includeProductionLineList;
        }
    }

    /**
     * 审批来源(1.供应商,2.BD)
     */
    public enum ApproveFromEnum {
        /**
         * 供应商
         */
        vbk(1, "transport.vbk"),

        /**
         * BD
         */
        bd(2, "transport.bd");
        private Integer value;
        private String text;

        ApproveFromEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (ApproveFromEnum s : ApproveFromEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    /**
     * 审批状态(0.暂不可审批、1.待审批、2.审批通过、3.审批驳回)
     */
    public enum ApproveStatusEnum {
        /**
         * 待审批
         */
        WAIT_APPROVE(1, "transport.approveStatusEnum.waitApprove"),

        /**
         * 审批通过
         */
        APPROVED(2, "transport.approveStatusEnum.approved"),

        /**
         * 审批驳回
         */
        APPROVAL_REJECTED(3, "transport.approveStatusEnum.approvalRejected");

        private Integer value;
        private String text;

        ApproveStatusEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (ApproveStatusEnum s : ApproveStatusEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    /**
     * 单项审批记录类型(1.单项,2.子项)
     */
    public enum ApproveStepRecordTypeEnum {
        SINGLE(1, "SINGLE"), CHILD_SINGLE(2, "CHILD_SINGLE");

        private Integer value;
        private String text;

        ApproveStepRecordTypeEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }
    }


    public enum ApproveChildSharkEnum {
        /**
         * 文字与照片信息一致性审核（非OCR识别部分）
         */
        OCR_CAPACITYMGT(1, "capacitymgt.auditmgt.textphoto.cor"),

        /**
         * 文字与照片信息一致性审核（OCR识别部分）
         */
        NO_OCR_CAPACITYMGT(2, "capacitymgt.auditmgt.textphoto.noncor"),

        /**
         * 头像标准审核
         */
        HEAD_CAPACITYMGT(3, "capacitymgt.auditmgt.profilerule");

        private Integer value;
        private String text;

        ApproveChildSharkEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (ApproveChildSharkEnum s : ApproveChildSharkEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    public enum ApproveChildCertificateSharkEnum {
        /**
         * 驾驶证合规审核
         */
        DRIVER_LICENSE(1, "capacitymgt.auditmgt.drivinglicense.audit"),

        /**
         * 行驶证合规审核
         */
        VEHICLE_LICENSE(2, "capacitymgt.auditmgt.verhiclelicense.audit"),

        /**
         * 网约车车证合规审核
         */
        NET_VEHICLE_SHARK(4, "capacitymgt.auditmgt.onlineverhiclelicense.audit"),
        /**
         * 网约车人证合规审核
         */
        NET_DRIVER_SHARK(3, "capacitymgt.auditmgt.onlinedriverlicense.info"),
        /**
         * 身份与背景合规审核
         */
        IDCARD_SHARK(5, "capacitymgt.auditmgt.idandbackground.info");

        private Integer value;
        private String text;

        ApproveChildCertificateSharkEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (ApproveChildCertificateSharkEnum s : ApproveChildCertificateSharkEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    /**
    　* @description: 举牌接机
    　* <AUTHOR>
    　* @date 2022/6/16 19:08
    */
    public enum RaisingPickUpSharkEnum {
        /**
         *
         */
        YES(1, "YES"),

        /**
         *
         */
        NO(0, "NO");

        private Integer value;
        private String text;

        RaisingPickUpSharkEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }

        public static String getName(Integer type) {
            for (RaisingPickUpSharkEnum s : RaisingPickUpSharkEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    /**
    　* @description: 是否废弃
    　* <AUTHOR>
    　* @date 2022/8/22 10:43
    */
    public enum ActiveSharkEnum {
        /**
         *
         */
        NO(1, "NO"),

        /**
         *
         */
        YES(0, "YES");

        private Integer value;
        private String text;

        ActiveSharkEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }

        public static String getName(Integer type) {
            for (ActiveSharkEnum s : ActiveSharkEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    /**
     　* @description: 境外派遣状态(0.解绑,1.绑定)
     　* <AUTHOR>
     　* @date 2023/2/14 9:32
     */
    public enum DrvDispatchActiveSharkEnum {
        unbind(0, "drv.dispatch.active.unbind"),

        binding(1, "drv.dispatch.active.binding");

        private Integer value;
        private String text;

        DrvDispatchActiveSharkEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (DrvDispatchActiveSharkEnum s : DrvDispatchActiveSharkEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    /**
     * OCR识别类型枚举
     */
    public enum OcrTypeEnum {
        /**
         * 司机
         */
        Driver(1),

        /**
         * 车辆
         */
        Car(2);

        private Integer value;

        public Integer getValue() {
            return value;
        }

        OcrTypeEnum(Integer value) {
            this.value = value;
        }
    }

    /**
     　* @description: 车辆审核状态枚举
     　* <AUTHOR>
     　* @date 2023/2/14 9:32
     */
    public enum VehicleAuditStatusSharkEnum {

        UNDISPOSED(1, "vehicle.audit.status.undisposed"),

        COMPLIANCE(2, "vehicle.audit.status.compliance"),

        DISQUALIFICATION(3, "vehicle.audit.status.disqualification");

        private Integer value;
        private String text;

        VehicleAuditStatusSharkEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (VehicleAuditStatusSharkEnum s : VehicleAuditStatusSharkEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }

    }

    /**
     　* @description: 车辆状态  0.未激活,1.上线.3.下线
     　* <AUTHOR>
     　* @date 2024/12/14 9:32
     */
    public enum VehicleStatusEnum {

        UN_ACTIVE(0, "未激活"),

        ONLINE(1, "上线"),

        OFFLINE(3, "下线");

        private Integer value;
        private String desc;

        VehicleStatusEnum(Integer value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(desc, desc);
        }
    }

    /**
     * OCR识别结果枚举
     * http://contract.mobile.flight.ctripcorp.com/#/operation-detail/3392/3/carOCR?lang=zh-CN
     * http://contract.mobile.flight.ctripcorp.com/#/operation-detail/3392/3/drivingLicense?lang=zh-CN
     */
    public enum OcrResEnum {
        /**
         * 成功
         */
        Success("0"),
        /**
         * 请求错误
         */
        REQ_ERROR("11"),
        /**
         * 图片下载错误
         */
        DOWNLOAD_ERROR("12"),
        /**
         * 未知错误
         */
        UNKNOWN_ERROR("13");

        private String value;

        OcrResEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

    }

    /**
     * 查询司机缓存运力组模式
     */
    public enum QueryTransportModeEnum {
        // 全部允许以新模式请求
        ALL_ALLOW(1),
        // 部分允许以新模式请求
        PART_ALLOW(2),
        // 全部不允许以新模式请求
        ALL_REFUSE(3);

        private Integer value;

        QueryTransportModeEnum(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return this.value;
        }

        public static QueryTransportModeEnum getInstance(Integer statusCode) {
            if (statusCode == null) {
                return ALL_REFUSE;
            }
            for (QueryTransportModeEnum orderStatusEnum : QueryTransportModeEnum.values()) {
                if (orderStatusEnum.getValue().intValue() == statusCode.intValue()) {
                    return orderStatusEnum;
                }
            }
            return ALL_REFUSE;
        }

    }

    public enum TemporaryDispatchMarkEnum {
        OFFICIAL(0, "drv.temporary.dispatch.mark.official"),

        TEMPORARY(1, "drv.temporary.dispatch.mark.temporary");

        private Integer value;
        private String text;

        TemporaryDispatchMarkEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return SharkUtils.getSharkValue(text, text);
        }

        public static String getName(Integer type) {
            for (TemporaryDispatchMarkEnum s : TemporaryDispatchMarkEnum.values()) {
                if (s.getValue() == type.intValue()) {
                    return s.getText();
                }
            }
            return "";
        }
    }

    public enum ImageTypeEnum{
        DRIVERLICENSE(3, "drvcardImg"),
        VEHICLEFULLIMG(4,"vehicleFullImg"),
        VEHICLECERTIIMG(5,"vehicleCertiImg"),
        COMPLIANCEQUALIFICATIONCERTIFICATES(6,"complianceQualificationCertificates"),
                ;
        private Integer code;
        private String msg;

        private ImageTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public static String getText(Integer code) {
            for (ImageTypeEnum s : ImageTypeEnum.values()) {
                if (s.getCode().intValue() == code.intValue()) {
                    return s.msg;
                }
            }
            return "";
        }

        // 通过msg获取code
        public static Integer getCode(String msg) {
            for (ImageTypeEnum s : ImageTypeEnum.values()) {
                if (s.getMsg().equals(msg)) {
                    return s.code;
                }
            }
            return null;
        }

    }

}
