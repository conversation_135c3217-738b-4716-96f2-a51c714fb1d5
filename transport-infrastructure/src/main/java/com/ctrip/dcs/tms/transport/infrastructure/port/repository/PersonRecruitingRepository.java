package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.PersonRecruitingPO;
import com.ctrip.igt.framework.dal.DalRepository;

import java.sql.SQLException;

public interface PersonRecruitingRepository {

    DalRepository<PersonRecruitingPO> getPersonRecruitingRepo();

    Long insert(PersonRecruitingPO po) throws SQLException;

    int checkPhoneCount(String phone);
}