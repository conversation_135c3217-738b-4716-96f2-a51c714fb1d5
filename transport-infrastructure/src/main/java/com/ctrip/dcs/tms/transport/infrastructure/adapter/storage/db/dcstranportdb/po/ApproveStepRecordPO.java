package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2022-04-19
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_approve_step_record")
public class ApproveStepRecordPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 记录ID
     */
	@Column(name = "record_id")
	@Type(value = Types.BIGINT)
	private Long recordId;

    /**
     * 记录类别(1.单项,2.子项)
     */
	@Column(name = "record_type")
	@Type(value = Types.INTEGER)
	private Integer recordType;

    /**
     * 子项类别(1-OCR识别,2-非OCR识别,3-证件核验)
     */
	@Column(name = "child_item")
	@Type(value = Types.INTEGER)
	private Integer childItem;

    /**
     * 证件类别(1.驾驶证,2.行驶证,3.网约车驾驶证,4.网约车车辆许可证)
     */
	@Column(name = "certificate_type")
	@Type(value = Types.INTEGER)
	private Integer certificateType;

    /**
     * 记录内容
     */
	@Column(name = "record_content")
	@Type(value = Types.VARCHAR)
	private String recordContent;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 更新时间
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

    /**
     * 招募审批的记录ID
     */
	@Column(name = "recruiting_record_id")
	@Type(value = Types.BIGINT)
	private Long recruitingRecordId;

    /**
     * 招募ID
     */
	@Column(name = "recruiting_id")
	@Type(value = Types.BIGINT)
	private Long recruitingId;

    /**
     * 招募类型(1.司机-h5/工作台,2.车辆-工作台)
     */
	@Column(name = "recruiting_type")
	@Type(value = Types.INTEGER)
	private Integer recruitingType;

	public Long getRecruitingRecordId() {
		return recruitingRecordId;
	}

	public Long getRecruitingId() {
		return recruitingId;
	}

	public void setRecruitingId(Long recruitingId) {
		this.recruitingId = recruitingId;
	}

	public Integer getRecruitingType() {
		return recruitingType;
	}

	public void setRecruitingType(Integer recruitingType) {
		this.recruitingType = recruitingType;
	}

	public void setRecruitingRecordId(Long recruitingRecordId) {
		this.recruitingRecordId = recruitingRecordId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRecordId() {
		return recordId;
	}

	public void setRecordId(Long recordId) {
		this.recordId = recordId;
	}

	public Integer getRecordType() {
		return recordType;
	}

	public void setRecordType(Integer recordType) {
		this.recordType = recordType;
	}

	public Integer getChildItem() {
		return childItem;
	}

	public void setChildItem(Integer childItem) {
		this.childItem = childItem;
	}

	public Integer getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}

	public String getRecordContent() {
		return recordContent;
	}

	public void setRecordContent(String recordContent) {
		this.recordContent = recordContent;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

}