package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.ctrip.dcs.tms.transport.api.model.*;
import org.springframework.beans.*;

import java.util.*;

public class UpdateApproveStatusDO {

    private List<Long> ids;
    private Integer approveStatus;
    private String modifyUser;
    private String remarks;
    private String certificateCheckResult;

    public String getCertificateCheckResult() {
        return certificateCheckResult;
    }

    public void setCertificateCheckResult(String certificateCheckResult) {
        this.certificateCheckResult = certificateCheckResult;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public static UpdateApproveStatusDO buildDO(TransportApproveStatusUpdateSOARequestType requestType,String updateCertificateChecInfo){
        UpdateApproveStatusDO statusDO = new UpdateApproveStatusDO();
        BeanUtils.copyProperties(requestType,statusDO);
        statusDO.setCertificateCheckResult(updateCertificateChecInfo);
        return statusDO;
    }
}
