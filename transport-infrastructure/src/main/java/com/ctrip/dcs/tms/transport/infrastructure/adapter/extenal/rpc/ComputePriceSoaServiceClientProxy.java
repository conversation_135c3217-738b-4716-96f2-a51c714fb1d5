package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.dcs.price.compute.price.facade.ComputePriceSoaServiceClient;
import com.ctrip.dcs.price.compute.price.facade.param.QueryPoiAreaInfoRequestType;
import com.ctrip.dcs.price.compute.price.facade.param.QueryPoiAreaInfoResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 * @Date 2020/3/10 16:26
 */
@ServiceClient(value = ComputePriceSoaServiceClient.class, format = "json")
public interface ComputePriceSoaServiceClientProxy {

    /**
     * 查询城市和点位一级区域与点位的包含关系
     */
    QueryPoiAreaInfoResponseType queryPoiAreaInfo(QueryPoiAreaInfoRequestType request);


}
