package com.ctrip.dcs.tms.transport.infrastructure.service;

import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleBrandDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleColorDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleSeriesDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleTypeDTO;

public interface IQueryVehicleAttributeService {
    public VehicleBrandDTO queryVehicleBrand(Long vehicleBrandId);
    public VehicleColorDTO queryVehicleColor(Long vehicleColorId);
    public VehicleSeriesDTO queryVehicleSeries(Long vehicleSeriesId);
    public VehicleTypeDTO queryVehicleType(Long vehicleTypeId);
}
