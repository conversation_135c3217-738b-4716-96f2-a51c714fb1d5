package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.springframework.stereotype.*;
import qunar.tc.qconfig.client.spring.*;

import java.util.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 * 疫情防控城市配置
 * 2021-04-14 17:40:21
 */
@Component
public class EpidemicPreventionControlQconfig {

    public static final Integer DEFAULT_EFFECTIVE_DAYS = 7;

    private static Map<Long, EpidemicPreventionControlCityInfoDTO> epidemicPreventionControlCityMap;

    public static EpidemicPreventionControlCityInfoDTO defaultCityInfo;

    static {
        defaultCityInfo = new EpidemicPreventionControlCityInfoDTO(EpidemicPreventionControlEnum.AskReportStatusEnum.NOT_NEED.getCode(), DEFAULT_EFFECTIVE_DAYS);
    }

    public List<Long> getNeedInformCityIdList() {
        List<Long> cityIdList = Lists.newArrayList();
        cityIdList.addAll(getAllNeedCityIdList());
        cityIdList.addAll(getSatisfyOneCityIdList());
        cityIdList.addAll(getNeedVaccineReportCityIdList());
        cityIdList.addAll(getNeedNucleicAcidReportCityIdList());
        return cityIdList;
    }

    public List<Long> getNeedNucleicAcidReportCityIdList() {
        return getNeedReportCityIdListByAskReportModel(EpidemicPreventionControlEnum.AskReportStatusEnum.NEED_NUCLEIC_ACID_REPORT.getCode());
    }

    public List<Long> getNeedVaccineReportCityIdList() {
        return getNeedReportCityIdListByAskReportModel(EpidemicPreventionControlEnum.AskReportStatusEnum.NEED_VACCINE_REPORT.getCode());
    }

    public List<Long> getSatisfyOneCityIdList() {
        return getNeedReportCityIdListByAskReportModel(EpidemicPreventionControlEnum.AskReportStatusEnum.SATISFY_ONE.getCode());
    }

    public List<Long> getAllNeedCityIdList() {
        return getNeedReportCityIdListByAskReportModel(EpidemicPreventionControlEnum.AskReportStatusEnum.ALL_NEED.getCode());
    }

    public EpidemicPreventionControlCityInfoDTO getEpidemicPreventionControlCityMap(Long cityId) {
        return epidemicPreventionControlCityMap.getOrDefault(cityId, EpidemicPreventionControlQconfig.defaultCityInfo);
    }

    private List<Long> getNeedReportCityIdListByAskReportModel(int reportConstraintModel) {
        List<Long> res = Lists.newArrayListWithExpectedSize(epidemicPreventionControlCityMap.size());
        for (EpidemicPreventionControlCityInfoDTO cityInfoDTO : epidemicPreventionControlCityMap.values()) {
            if (cityInfoDTO.getReportConstraintModel().intValue() == reportConstraintModel) {
                res.add(cityInfoDTO.getCityId());
            }
        }
        return res;
    }

    @QConfig(value = "epidemic.prevention.control.city.json")
    private void epidemicPreventionControlCityMap(String epidemicPreventionControlCityJson) {
        if (Strings.isNullOrEmpty(epidemicPreventionControlCityJson)) {
            EpidemicPreventionControlQconfig.epidemicPreventionControlCityMap = Maps.newHashMap();
        } else {
            List<EpidemicPreventionControlCityInfoDTO> list = JsonUtil.fromJson(epidemicPreventionControlCityJson, new TypeReference<List<EpidemicPreventionControlCityInfoDTO>>() {
            });
            EpidemicPreventionControlQconfig.epidemicPreventionControlCityMap = list.stream().collect(Collectors.toMap(EpidemicPreventionControlCityInfoDTO::getCityId, cityInfoDTO -> cityInfoDTO));
        }
    }

}