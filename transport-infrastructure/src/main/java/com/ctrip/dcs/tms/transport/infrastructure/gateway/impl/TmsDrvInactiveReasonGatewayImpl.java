package com.ctrip.dcs.tms.transport.infrastructure.gateway.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.TmsDrvInactiveReasonGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/6/11
 */
@Service
public class TmsDrvInactiveReasonGatewayImpl implements TmsDrvInactiveReasonGateway {

  private static final Logger logger = LoggerFactory.getLogger(TmsDrvInactiveReasonGatewayImpl.class);

  @Autowired
  TmsDrvInactiveReasonRepository tmsDrvInactiveReasonRepository;

  /**
   * 根据司机ID和原因代码删除未激活原因，并返回是否（已经消减掉所有未激活卡控原因）可以上线
   *
   * @param drvId      司机ID
   * @param reasonCode 原因代码
   * @param modifyUser 修改人
   * @return 是否（已经消减掉所有未激活卡控原因）可以上线
   */
  @Override
  public boolean enableActiveAfterDeleteReasonByCode(Long drvId, List<Integer> reasonCode,
      String modifyUser) {
    tmsDrvInactiveReasonRepository.deleteReasonByCode(drvId, reasonCode, "system");
    List<TmsDrvInactiveReasonPO> result =
        tmsDrvInactiveReasonRepository.query(Lists.newArrayList(drvId));
    if (CollectionUtils.isEmpty(result)) {
      // 没有其他未激活原因，上线司机
      Cat.logEvent(CatEventType.DRIVER_ACTIVE_ENABLE, "true", Event.SUCCESS, "drvId:" + drvId);
      logger.info("handleIVRCallResult", "Driver {} activated successfully, no other inactive reasons found", drvId);
      return true;
    }
    Cat.logEvent(CatEventType.DRIVER_ACTIVE_ENABLE, "false", Event.SUCCESS, "drvId:" + drvId);
    return false;
  }

  /**
   * 插入未激活原因
   *
   * @param po 未激活原因
   */
  @Override
  public void insertWhenNotExist(TmsDrvInactiveReasonPO po) {
    List<TmsDrvInactiveReasonPO> result = tmsDrvInactiveReasonRepository.query(Lists.newArrayList(po.getDrvId()));
    if (!result.stream().filter(reason -> Objects.equals(reason.getReasonCode(), po.getReasonCode())).isParallel()) {
      Cat.logEvent(CatEventType.DRIVER_INACTIVE_REASON, po.getReasonCode() + ": " + po.getReasonDesc());
      tmsDrvInactiveReasonRepository.insert(po);
    }
  }
}
