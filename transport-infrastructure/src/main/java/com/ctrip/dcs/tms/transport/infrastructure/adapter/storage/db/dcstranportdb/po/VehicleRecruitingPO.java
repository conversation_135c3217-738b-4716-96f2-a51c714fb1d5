package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2020-04-20
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "vehicle_recruiting")
public class VehicleRecruitingPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "vehicle_id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long vehicleId;

    /**
     * 供应商id
     */
	@Column(name = "supplier_id")
	@Type(value = Types.BIGINT)
	private Long supplierId;

    /**
     * 城市id
     */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 车牌号
     */
	@Column(name = "vehicle_license")
	@Type(value = Types.VARCHAR)
	private String vehicleLicense;

    /**
     * 车型id
     */
	@Column(name = "vehicle_type_id")
	@Type(value = Types.BIGINT)
	private Long vehicleTypeId;

    /**
     * 车辆品牌id
     */
	@Column(name = "vehicle_brand_id")
	@Type(value = Types.BIGINT)
	private Long vehicleBrandId;

    /**
     * 车系id
     */
	@Column(name = "vehicle_series")
	@Type(value = Types.BIGINT)
	private Long vehicleSeries;

    /**
     * 车身颜色id
     */
	@Column(name = "vehicle_color_id")
	@Type(value = Types.TINYINT)
	private Integer vehicleColorId;

    /**
     * 车辆能源类型：1燃油车辆，2纯电动车辆，3油电混合车辆
     */
	@Column(name = "vehicle_energy_type")
	@Type(value = Types.TINYINT)
	private Integer vehicleEnergyType;

    /**
     * VIN码
     */
	@Column(name = "vin")
	@Type(value = Types.VARCHAR)
	private String vin;

    /**
     * 车辆首次注册时间
     */
	@Column(name = "regst_date")
	@Type(value = Types.TIMESTAMP)
	private Timestamp regstDate;

    /**
     * 1营运、2非营运、3租赁、4出租客运、5预约出租客运、6营转非
     */
	@Column(name = "using_nature")
	@Type(value = Types.TINYINT)
	private Integer usingNature;

    /**
     * 网约车运输证
     */
	@Column(name = "net_tans_ctfct_img")
	@Type(value = Types.VARCHAR)
	private String netTansCtfctImg;

    /**
     * 车辆行驶证照片
     */
	@Column(name = "vehicle_certi_img")
	@Type(value = Types.VARCHAR)
	private String vehicleCertiImg;

    /**
     * 车身照片
     */
	@Column(name = "vehicle_full_img")
	@Type(value = Types.VARCHAR)
	private String vehicleFullImg;

    /**
     * 车前排照片
     */
	@Column(name = "vehicle_front_img")
	@Type(value = Types.VARCHAR)
	private String vehicleFrontImg;

    /**
     * 车后排照片
     */
	@Column(name = "vehicle_back_img")
	@Type(value = Types.VARCHAR)
	private String vehicleBackImg;

    /**
     * 后备箱照片
     */
	@Column(name = "vehicle_trunk_img")
	@Type(value = Types.VARCHAR)
	private String vehicleTrunkImg;

    /**
     * 0:招募司机初始提交状态 1:供应商驳回 4:供应商提交/供应商通过/待运营审批 6:运营驳回 10:通过并落库
     */
	@Column(name = "approver_status")
	@Type(value = Types.TINYINT)
	private Integer approverStatus;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 操作人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	/**
	 * 正式车辆id
	 */
	@Column(name = "vehicle_official_id")
	@Type(value = Types.BIGINT)
	private Long vehicleOfficialId;

	/**
	 * 车辆来源1.司机自助,2.人工录入
	 */
	@Column(name = "vehicle_from")
	@Type(value = Types.TINYINT)
	private Integer vehicleFrom;

	/**
	 * 行驶证所有人
	 */
	@Column(name = "vehicle_license_owner")
	@Type(value = Types.VARCHAR)
	private String vehicleLicenseOwner;

	/**
	 * 校验状态 1.已完成,2.未完成
	 */
	@Column(name = "check_status")
	@Type(value = Types.TINYINT)
	private Integer checkStatus;

	/**
	 * 逻辑产线
	 */
	@Column(name = "category_synthesize_code")
	@Type(value = Types.TINYINT)
	private Integer categorySynthesizeCode;

	/**
	 * 网约车申诉材料
	 */
	@Column(name = "net_appeal_materials")
	@Type(value = Types.VARCHAR)
	private String netAppealMaterials;


	/**
	 * 审核进度（1.已完成,0,未完成）
	 */
	@Column(name = "approve_schedule")
	@Type(value = Types.INTEGER)
	private Integer approveSchedule;

	/**
	 * 审核时效（1未超时，0.已超时）
	 */
	@Column(name = "approve_aging")
	@Type(value = Types.INTEGER)
	private Integer approveAging;

	/**
	 * 版本标识(1.第一版,2.第二版)
	 */
	@Column(name = "version_flag")
	@Type(value = Types.INTEGER)
	private Integer versionFlag;

	/**
	 * 车牌号所属城市ID
	 */
	@Column(name = "vehicle_license_city_id")
	@Type(value = Types.BIGINT)
	private Long vehicleLicenseCityId;

	/**
	 * 审批时间
	 */
	@Column(name = "approve_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp approveTime;

	/**
	 * 保存最初的证件配置值
	 */
	@Column(name = "certificate_config")
	@Type(value = Types.VARCHAR)
	private String certificateConfig;

	/**
	 * OCR识别值
	 */
	@Column(name = "ocr_field_value")
	@Type(value = Types.VARCHAR)
	private String ocrFieldValue;

	/**
	 * 是否经过BD审批(0-未经过,1-已经过)
	 */
	@Column(name = "bd_approve_status")
	@Type(value = Types.INTEGER)
	private Integer bdApproveStatus;

	/**
	 * 供应商审核进度（1.已完成,0,未完成）
	 */
	@Column(name = "supplier_approve_schedule")
	@Type(value = Types.INTEGER)
	private Integer supplierApproveSchedule;

	/**
	 * 三方参数请求快照
	 */
	@Column(name = "mod_snapshot_values")
	@Type(value = Types.VARCHAR)
	private String modSnapshotValues;

	/**
	 * 车辆行驶证申诉材料
	 */
	@Column(name = "vehicle_license_appeal_materials")
	@Type(value = Types.VARCHAR)
	private String vehicleLicenseAppealMaterials;

	/**
	 * 平台驳回次数
	 */
	@Column(name = "bd_turn_down_count")
	@Type(value = Types.INTEGER)
	private Integer bdTurnDownCount;

	/**
	 * 1表示启用，0表示删除
	 */
	@Column(name = "active")
	@Type(value = Types.BIT)
	private Boolean active;

	/**
	 * OCR是否通过,0.不通过,1-通过，默认不通过
	 */
	@Column(name = "ocr_pass_status")
	@Type(value = Types.INTEGER)
	private Integer ocrPassStatus;

	/**
	 * OCR各项校验结果
	 */
	@Column(name = "ocr_pass_status_json")
	@Type(value = Types.VARCHAR)
	private String ocrPassStatusJson;

	/**
	 * 审批状态
	 * 1.未处理
	 * 2.合规
	 * 3.不合规
	 */
	@Column(name = "audit_status")
	@Type(value = Types.TINYINT)
	private Integer auditStatus;

	/**
	 * 车辆保险单
	 */
	@Column(name = "vehicle_insurance_policy")
	@Type(value = Types.VARCHAR)
	public String vehicleInsurancePolicy;

	/**
	 * 合规资质证件
	 */
	@Column(name = "compliance_qualification_certificates")
	@Type(value = Types.VARCHAR)
	public String complianceQualificationCertificates;

	/**
	 * 新OCR识别值
	 */
	@Column(name = "new_ocr_field_value")
	@Type(value = Types.VARCHAR)
	public String newOcrFieldValue;


	public Integer getOcrPassStatus() {
		return ocrPassStatus;
	}

	public void setOcrPassStatus(Integer ocrPassStatus) {
		this.ocrPassStatus = ocrPassStatus;
	}

	public String getOcrPassStatusJson() {
		return ocrPassStatusJson;
	}

	public void setOcrPassStatusJson(String ocrPassStatusJson) {
		this.ocrPassStatusJson = ocrPassStatusJson;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public String getVehicleLicenseAppealMaterials() {
		return vehicleLicenseAppealMaterials;
	}

	public void setVehicleLicenseAppealMaterials(String vehicleLicenseAppealMaterials) {
		this.vehicleLicenseAppealMaterials = vehicleLicenseAppealMaterials;
	}

	public Integer getBdTurnDownCount() {
		return bdTurnDownCount;
	}

	public void setBdTurnDownCount(Integer bdTurnDownCount) {
		this.bdTurnDownCount = bdTurnDownCount;
	}

	public String getModSnapshotValues() {
		return modSnapshotValues;
	}

	public void setModSnapshotValues(String modSnapshotValues) {
		this.modSnapshotValues = modSnapshotValues;
	}

	public Integer getSupplierApproveSchedule() {
		return supplierApproveSchedule;
	}

	public void setSupplierApproveSchedule(Integer supplierApproveSchedule) {
		this.supplierApproveSchedule = supplierApproveSchedule;
	}

	public Integer getBdApproveStatus() {
		return bdApproveStatus;
	}

	public void setBdApproveStatus(Integer bdApproveStatus) {
		this.bdApproveStatus = bdApproveStatus;
	}

	public String getOcrFieldValue() {
		return ocrFieldValue;
	}

	public void setOcrFieldValue(String ocrFieldValue) {
		this.ocrFieldValue = ocrFieldValue;
	}

	public String getCertificateConfig() {
		return certificateConfig;
	}

	public void setCertificateConfig(String certificateConfig) {
		this.certificateConfig = certificateConfig;
	}

	public String getNetAppealMaterials() {
		return netAppealMaterials;
	}

	public void setNetAppealMaterials(String netAppealMaterials) {
		this.netAppealMaterials = netAppealMaterials;
	}

	public Integer getCategorySynthesizeCode() {
		return categorySynthesizeCode;
	}

	public void setCategorySynthesizeCode(Integer categorySynthesizeCode) {
		this.categorySynthesizeCode = categorySynthesizeCode;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Integer getVehicleFrom() {
		return vehicleFrom;
	}

	public void setVehicleFrom(Integer vehicleFrom) {
		this.vehicleFrom = vehicleFrom;
	}

	public Long getVehicleOfficialId() {
		return vehicleOfficialId;
	}

	public void setVehicleOfficialId(Long vehicleOfficialId) {
		this.vehicleOfficialId = vehicleOfficialId;
	}

	public Long getVehicleId() {
		return vehicleId;
	}

	public void setVehicleId(Long vehicleId) {
		this.vehicleId = vehicleId;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}

	public String getVehicleLicense() {
		return vehicleLicense;
	}

	public void setVehicleLicense(String vehicleLicense) {
		this.vehicleLicense = vehicleLicense;
	}

	public Long getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(Long vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	public Long getVehicleBrandId() {
		return vehicleBrandId;
	}

	public void setVehicleBrandId(Long vehicleBrandId) {
		this.vehicleBrandId = vehicleBrandId;
	}

	public Long getVehicleSeries() {
		return vehicleSeries;
	}

	public void setVehicleSeries(Long vehicleSeries) {
		this.vehicleSeries = vehicleSeries;
	}

	public Integer getVehicleColorId() {
		return vehicleColorId;
	}

	public void setVehicleColorId(Integer vehicleColorId) {
		this.vehicleColorId = vehicleColorId;
	}

	public Integer getVehicleEnergyType() {
		return vehicleEnergyType;
	}

	public void setVehicleEnergyType(Integer vehicleEnergyType) {
		this.vehicleEnergyType = vehicleEnergyType;
	}

	public String getVin() {
		return vin;
	}

	public void setVin(String vin) {
		this.vin = vin;
	}

	public Timestamp getRegstDate() {
		return regstDate;
	}

	public void setRegstDate(Timestamp regstDate) {
		this.regstDate = regstDate;
	}

	public Integer getUsingNature() {
		return usingNature;
	}

	public void setUsingNature(Integer usingNature) {
		this.usingNature = usingNature;
	}

	public String getNetTansCtfctImg() {
		return netTansCtfctImg;
	}

	public void setNetTansCtfctImg(String netTansCtfctImg) {
		this.netTansCtfctImg = netTansCtfctImg;
	}

	public String getVehicleCertiImg() {
		return vehicleCertiImg;
	}

	public void setVehicleCertiImg(String vehicleCertiImg) {
		this.vehicleCertiImg = vehicleCertiImg;
	}

	public String getVehicleFullImg() {
		return vehicleFullImg;
	}

	public void setVehicleFullImg(String vehicleFullImg) {
		this.vehicleFullImg = vehicleFullImg;
	}

	public String getVehicleFrontImg() {
		return vehicleFrontImg;
	}

	public void setVehicleFrontImg(String vehicleFrontImg) {
		this.vehicleFrontImg = vehicleFrontImg;
	}

	public String getVehicleBackImg() {
		return vehicleBackImg;
	}

	public void setVehicleBackImg(String vehicleBackImg) {
		this.vehicleBackImg = vehicleBackImg;
	}

	public String getVehicleTrunkImg() {
		return vehicleTrunkImg;
	}

	public void setVehicleTrunkImg(String vehicleTrunkImg) {
		this.vehicleTrunkImg = vehicleTrunkImg;
	}

	public Integer getApproverStatus() {
		return approverStatus;
	}

	public void setApproverStatus(Integer approverStatus) {
		this.approverStatus = approverStatus;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

	public String getVehicleLicenseOwner() {
		return vehicleLicenseOwner;
	}

	public void setVehicleLicenseOwner(String vehicleLicenseOwner) {
		this.vehicleLicenseOwner = vehicleLicenseOwner;
	}

	public Integer getApproveSchedule() {
		return approveSchedule;
	}

	public void setApproveSchedule(Integer approveSchedule) {
		this.approveSchedule = approveSchedule;
	}

	public Integer getApproveAging() {
		return approveAging;
	}

	public void setApproveAging(Integer approveAging) {
		this.approveAging = approveAging;
	}

	public Integer getVersionFlag() {
		return versionFlag;
	}

	public void setVersionFlag(Integer versionFlag) {
		this.versionFlag = versionFlag;
	}

	public Long getVehicleLicenseCityId() {
		return vehicleLicenseCityId;
	}

	public void setVehicleLicenseCityId(Long vehicleLicenseCityId) {
		this.vehicleLicenseCityId = vehicleLicenseCityId;
	}

	public Timestamp getApproveTime() {
		return approveTime;
	}

	public void setApproveTime(Timestamp approveTime) {
		this.approveTime = approveTime;
	}

	public Integer getAuditStatus() {
		return auditStatus;
	}

	public void setAuditStatus(Integer auditStatus) {
		this.auditStatus = auditStatus;
	}

	public String getVehicleInsurancePolicy() {
		return vehicleInsurancePolicy;
	}

	public void setVehicleInsurancePolicy(String vehicleInsurancePolicy) {
		this.vehicleInsurancePolicy = vehicleInsurancePolicy;
	}

	public String getComplianceQualificationCertificates() {
		return complianceQualificationCertificates;
	}

	public void setComplianceQualificationCertificates(String complianceQualificationCertificates) {
		this.complianceQualificationCertificates = complianceQualificationCertificates;
	}

	public String getNewOcrFieldValue() {
		return newOcrFieldValue;
	}

	public void setNewOcrFieldValue(String newOcrFieldValue) {
		this.newOcrFieldValue = newOcrFieldValue;
	}
}
