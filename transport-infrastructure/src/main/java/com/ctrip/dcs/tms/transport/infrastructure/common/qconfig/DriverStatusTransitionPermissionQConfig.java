package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverStatusTransitionPermissionDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.JsonConfig;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
@Data
public class DriverStatusTransitionPermissionQConfig {

  private final JsonConfig<DriverStatusTransitionPermissionDTO>
      config = JsonConfig.get("drv.status.transition.permission.json", Feature.create().setFailOnNotExists(false).build(), DriverStatusTransitionPermissionDTO.class);

  private final String freezeFromUser = "{\"driver_safe_point_drop_to_zero\":\"司机安全分跌零(100030504)\",\"100031301\":\"\\u5224\\u7f5a(100031301)\",\"qb_biz\":\"\\u91c7\\u8d2d\\u6d3e\\u53d1(qb_biz)\",\"self_dsp\":\"\\u91c7\\u8d2d\\u6d3e\\u53d1(self_dsp)\",\"*********\":\"\\u7ed3\\u7b97(*********)\", \"dcs_driver_terminal\":\"司机安全(dcs_driver_terminal)\"}";

  public Map<String, String> getFreezeFromUserMap() {
    return Optional.ofNullable(config.current().getFreezeFromUserMap()).orElse(
        JsonUtil.fromJson(freezeFromUser, new TypeReference<Map<String, String>>() { }));
  }

  /**
   * 获取权限规则
   * @param sourceStatus com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.DrvStatusEnum
   * @param targetStatus com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.DrvStatusEnum
   * @param role 角色 @sse com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.AccountTypeEnum
   * @return true;false
   * @see CommonEnum.FreezeOPFromEnum
   */
  public List<Integer> getPermissionRule(String sourceStatus, String targetStatus, String role) {
    return Optional.ofNullable(config.current().getPermissionRuleList()).orElse(JsonUtil.fromJson("{\n" + "\t\"2:1:1\": [1],\n" + "\t\"2:1:2\": [1, 2, 5, 6, 8],\n" + "\t\"2:3:1\": [1],\n" + "    \"2:3:2\": [1, 2, 5, 6, 8],\n" + "    \"2:2:1\": [1],\n" + "    \"2:2:2\": [1, 2, 5, 6, 8],\n" + "    \"3:1:1\": [1],\n" + "    \"3:1:2\": [1, 2, 5, 6, 8]\n" + "}", new TypeReference<Map<String,List<Integer>>>() { })).get(sourceStatus + ":" + targetStatus + ":" + role);
  }

  public List<String> getSuperAdminAccountList() {
    return Optional.ofNullable(config.current().getSuperAdminAccountList()).orElse(
        JsonUtil.fromJson("[\"陈品良\",\"冯家琼\"]", new TypeReference<List<String>>() { }));
  }

  public List<String> getDispatchOpFromList() {
    return Optional.ofNullable(config.current().getDispatchOpFromList()).orElse(
        JsonUtil.fromJson("[\"采购派发(qb_biz)\",\"qb_biz\",\"采购派发(self_dsp)\",\"self_dsp\"]", new TypeReference<List<String>>() { }));
  }

  public List<String> getSettleMentOpFromList() {
    return Optional.ofNullable(config.current().getSettleMentOpFromList()).orElse(
        JsonUtil.fromJson("[\"结算(*********)\",\"*********\"]", new TypeReference<List<String>>() { }));
  }

  public List<String> getDriverRiskOpFromList() {
    return Optional.ofNullable(config.current().getDriverRiskOpFromList()).orElse(
        JsonUtil.fromJson("[\"司机安全(dcs_driver_terminal)\",\"dcs_driver_terminal\"]", new TypeReference<List<String>>() { }));
  }

  public List<String> getDriverSafePointList() {
    return Optional.ofNullable(config.current().getDriverSafePointList()).orElse(
        JsonUtil.fromJson("[\"司机安全分跌零(driver_safe_point_drop_to_zero)\",\"driver_safe_point_drop_to_zero\"]", new TypeReference<List<String>>() { }));
  }

}
