package com.ctrip.dcs.tms.transport.infrastructure.common.handler;

import com.ctrip.arch.canal.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import org.springframework.stereotype.*;

import java.util.*;


@Component(ModRecordConstant.TableName.tmsCertificateCheck)
public class TmsCertificateCheckHandler extends TmsModRecordHandler{

    @Override
    List<TmsModContent> initModContent(DataChange dataChange) {
        return null;
    }

    @Override
    public Integer initModType(DataChange dataChange) {
        if (dataChange.isInsert()) {
            return CommonEnum.ModTypeEnum.CREATE.getValue();
        }else if (dataChange.isUpate()){
            return CommonEnum.ModTypeEnum.UPDATE.getValue();
        }else {
            return null;
        }
    }
}
