package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2020-09-02
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_certificate_check")
public class TmsCertificateCheckPO implements DalPojo {

    /**
     * 主锓ID
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 核验id(招募,司机,车辆)
     */
	@Column(name = "check_id")
	@Type(value = Types.BIGINT)
	private Long checkId;

    /**
     * 核验类别(1.招募,2司机,3车辆)
     */
	@Column(name = "check_type")
	@Type(value = Types.TINYINT)
	private Integer checkType;

    /**
     * 证件类别(1.驾驶证,2.行驶证,3.网约车驾驶证,4.网约车车辆许可证)
     */
	@Column(name = "certificate_type")
	@Type(value = Types.TINYINT)
	private Integer certificateType;

    /**
     * 核验关键字
     */
	@Column(name = "check_keyword")
	@Type(value = Types.VARCHAR)
	private String checkKeyword;

    /**
     * 第三方返回结果
     */
	@Column(name = "check_content")
	@Type(value = Types.VARCHAR)
	private String checkContent;

    /**
     * 核验状态(1.通过，2.复核，3.错误)
     */
	@Column(name = "check_status")
	@Type(value = Types.TINYINT)
	private Integer checkStatus;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 操作人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 核验结果
     */
	@Column(name = "check_result")
	@Type(value = Types.VARCHAR)
	private String checkResult;

	/**
	 * 操作人
	 */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	/**
	 * 1表示启用，0表示删除
	 */
	@Column(name = "active")
	@Type(value = Types.BIT)
	private Boolean active;


	/**
	 * 第三方系统核验状态(1.通过，2.复核，3.错误,4.审核中)
	 */
	@Column(name = "third_check_status")
	@Type(value = Types.TINYINT)
	private Integer thirdCheckStatus;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCheckId() {
		return checkId;
	}

	public void setCheckId(Long checkId) {
		this.checkId = checkId;
	}

	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}

	public Integer getCertificateType() {
		return certificateType;
	}

	public void setCertificateType(Integer certificateType) {
		this.certificateType = certificateType;
	}

	public String getCheckKeyword() {
		return checkKeyword;
	}

	public void setCheckKeyword(String checkKeyword) {
		this.checkKeyword = checkKeyword;
	}

	public String getCheckContent() {
		return checkContent;
	}

	public void setCheckContent(String checkContent) {
		this.checkContent = checkContent;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getCheckResult() {
		return checkResult;
	}

	public void setCheckResult(String checkResult) {
		this.checkResult = checkResult;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Integer getThirdCheckStatus() {
		return thirdCheckStatus;
	}

	public void setThirdCheckStatus(Integer thirdCheckStatus) {
		this.thirdCheckStatus = thirdCheckStatus;
	}
}