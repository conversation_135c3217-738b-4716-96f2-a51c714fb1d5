package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2020-02-20
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tsp_into_order_config")
@ToString
@EqualsAndHashCode
public class TspIntoOrderConfigPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 国家码
     */
	@Column(name = "country_id")
	@Type(value = Types.BIGINT)
	private Long countryId;

    /**
     * 国家名称
     */
	@Column(name = "country_name")
	@Type(value = Types.VARCHAR)
	private String countryName;

    /**
     * 城市Id
     */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 点位code
     */
	@Column(name = "location_code")
	@Type(value = Types.VARCHAR)
	private String locationCode;

    /**
     * 运力组ID
     */
	@Column(name = "transport_group_id")
	@Type(value = Types.BIGINT)
	private Long transportGroupId;

    /**
     * 点位类型(1.机场，2.火车站)
     */
	@Column(name = "location_type")
	@Type(value = Types.TINYINT)
	private Integer locationType;

    /**
     * 进单配置(json:[{time:08:00~12:00,orderCount:20}])
     */
	@Column(name = "config")
	@Type(value = Types.VARCHAR)
	private String config;

    /**
     * 删除标志(1表示启用，0表示删除)
     */
	@Column(name = "active")
	@Type(value = Types.BIT)
	private Boolean active;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 变更人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCountryId() {
		return countryId;
	}

	public void setCountryId(Long countryId) {
		this.countryId = countryId;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}

	public String getLocationCode() {
		return locationCode;
	}

	public void setLocationCode(String locationCode) {
		this.locationCode = locationCode;
	}

	public Long getTransportGroupId() {
		return transportGroupId;
	}

	public void setTransportGroupId(Long transportGroupId) {
		this.transportGroupId = transportGroupId;
	}

	public Integer getLocationType() {
		return locationType;
	}

	public void setLocationType(Integer locationType) {
		this.locationType = locationType;
	}

	public String getConfig() {
		return config;
	}

	public void setConfig(String config) {
		this.config = config;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}
