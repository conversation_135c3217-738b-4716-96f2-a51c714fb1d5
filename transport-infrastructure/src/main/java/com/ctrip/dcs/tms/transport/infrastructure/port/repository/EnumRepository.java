package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.dcs.scm.sdk.domain.supplier.Supplier;
import com.ctrip.dcs.tms.transport.api.model.LocationDTOSOA;
import com.ctrip.dcs.vehicle.domain.value.SceneVehicleModel;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.FixedLocation;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface EnumRepository {

    String getApplyStatusName(Integer applyStatus);

    /**
     * 查询可选意愿车型
     * @param cityId
     * @param vehicleTypeId
     * @param flag 是否返回同级车型(false不返回，true返回)
     * @return
     */
    Map<Long,String> getOptionalIntendVehicleType(Long cityId,Long vehicleTypeId,boolean flag);

    /**
     * 获取车身颜色
     * @return
     */
    public String getColorName(Long colorId);

    /**
     * 是否绑定司机
     * @param hasDrv
     * @return
     */
    public  String getHasDrvStr(Boolean hasDrv);


    /**
     * 通过车系id查车型
     * @param vehicleSeries
     * @param cityId
     * @return
     */
    Long getVehicleTypeIdBySeries(Long vehicleSeries,Long cityId);

    /**
     * 车型
     * @return
     */
    String getVehicleTypeName(Long vehicleTypeId);

    /**
     * 使用性质
     * @param usingNatureValue
     * @return
     */
    String getUsingNatureValue(Integer usingNatureValue);

    /**
     * 城市
     * @param cityId
     * @return
     */
    String getCityName(Long cityId);

    /**
     * 获取国家名称
     * @param countryId
     * @return
     */
    String getCountryName(Long countryId);

    /**
     * 通过cityId查询城市
     * @param cityId
     * @return
     */
    City getCityById(Long cityId);

    /**
     * 供应商
     * @param supplierId
     * @return
     */
    String getSupplierName(Long supplierId);

    Map<Long, Supplier> batchGetSupplier(List<Long> supplierIdList);

    /**
     * 车辆品牌
     * @param bandId
     * @return
     */
    String getBandName(Long bandId);

    /**
     * 车系
     * @return
     */
    String getVehicleSeriesName(Long VehicleSeriesId);

    /**
     * 车系映射（通过老id查询新id）
     * @param vehicleSeriesId
     * @return
     */
    Long getVehicleSeriesIdByOld(Long vehicleSeriesId);

    /**
     * 品牌映射（通过老id查询新id）
     * @param bandId
     * @return
     */
    Long getBandByOld(Long bandId);

    /**
     * 司机状态
     * @param
     * @return
     */
    Map<Integer,String> getDrvStatusName();

    /**
     * 司机语言
     * @param
     * @return
     */
    Map<String,String> getDrvLanguageMap();

    /**
     * 司机语言名称
     * @param
     * @return
     */
    String getDrvLanguageName(String drvLanguage, Map<String, String> languageMap);

    /**
     * 司机来源
     * @param
     * @return
     */
    Map<Integer,String> getDrvFrom();

    /**
     * 车型能源描述
     * */
    String getVehicleEnergyTypeName(Integer vehicleEnergyType);

    /**
     * 获取区域 0 国内 1 国外
     * */
    int getAreaScope(Long cityId);

    /**
     * 可接订单距离用车时间
     * @return
     */
    Map<Integer,String> getTakeOrderLimitTimeMap();

    /**
     * 意原车型
     * @return
     */
    String getIntendVehicleTypeName(String intendVehicleType);

    /**
     * 获取点位信息
     * @return
     */
    FixedLocation getFixedLocation(Integer locationType, String fixedLocationCode);

    /**
     * 获取SKU品牌信息
     * @return
     */
    ServiceProvider getSkuServiceProvider(Long serviceProviderId);

    /**
     * 运力组模式
     * @return
     */
    Map<Integer,String> getTransportGroupMode();

    /**
     * 查询供应商下的所有服务商ID
     * @param supplierId
     * @return
     */
    List<Long> queryServiceProviderIds(Long supplierId);

    /**
     * 招募审批状态
     * @return
     */
    Map<Integer,String> getRecruitingApproverStatus();

    /**
     * 查询服务商下服务的城市
     * @param serviceProviderIds
     * @param supplierId
     * @return
     */
    List<Long> queryCityListByServiceProviderIds(List<Long> serviceProviderIds,Long supplierId);

    /**
     * 合作模式
     * @return
     */
    Map<Integer,String> getDrvCoopMode();

    /**
     * 运力组状态
     * @return
     * */
    Map<Integer, String> getTransportGroupStatusMap();

    /**
     * 获取供应商ID
     * @param supplied
     * @return
     */
    String getSupplierEmail(Long supplied);

    /**
     * 根据合同Id，查询对应服务商信息
     * @param contractId
     * @return
     */
    Map<String,Object> queryServiceProvider(long contractId);

    /**
     * 获取点位信息
     * @param locationCode
     * @param locationType
     * @return
     */
    String getLocationName(String locationCode,Integer locationType);

    /**
     * 查询城市对应点位信息
     * @param cityId
     * @return
     */
    List<LocationDTOSOA> queryLocalByCity(Long cityId);


    /**
     * 车辆状态
     * @param
     * @return
     */
    Map<Integer,String> getVehicleStatus();

    /**
     * 查询合同idList 通过服务商id
     * */
    List<Long> queryContractByServiceProviderId(Long serviceProviderId);


    /**
     * 城市名称，逗号分隔
     * @param cityIds
     * @return
     */
    String getCityNameSplt(Collection<Long> cityIds);

    /**
     * 三方核验状态
     * @param
     * @return
     */
    Map<Integer,String> queryCheckStatusMap();

    /**
     * 三方核验标签状态
     * @param
     * @return
     */
    Map<Integer,String> queryTagsStatusMap();

    /**
    * 获取车型
     * */
    SceneVehicleModel getVehicleType(Long vehicleTypeId);

    /**
     * 根据合同id获取当前服务的产线与城市
     */
    Map<Integer, Set<Long>> getServedScopeMapByContractId(long contractId);

    /**
     * 审批节点
     * @return
     */
    Map<Integer,String> getApproveNodeName();

    /**
     * 事件类型
     * @return
     */
    Map<Integer,String> getApproveEnentType();

    /**
     * 审批流-审批状态
     * @return
     */
    Map<Integer,String> getTransportApproveStatus();

    Map<Long,List<Contract>> getContractRepository();

    /**
     * 根据合同id 查询服务商
     * */
    ServiceProvider getServiceProviderByContractId(Long contractId);

    /**
    　* @description: 审核时效名称
    　* <AUTHOR>
    　* @date 2021/8/17 11:13
    */
    String getApproveAgingName(Integer approveAging);

    /**
    　* @description: 审核进度名称
    　* <AUTHOR>
    　* @date 2021/8/17 11:13
    */
    String getApproveScheduleName(Integer approveSchedule);

    /**
     * 单项审批枚举
     * @return
     */
    Map<Integer,String> getSingleApproveItem();

    /**
    　* @description: 获取城市对应的国家ID
    　* <AUTHOR>
    　* @date 2023/3/27 14:00
    */
    Long getCountryId(Long cityId);

    String getColorKey(Long colorId);

    /**
     * 根据c的城市id查询q的城市id
     * @param cityId
     * @return
     */
    String getQCityIdByCCityId(Long cityId);

    /**
     * 是否是境外
     * @return
     */
    boolean isNotChinaMainLand(Long cityId);

    /**
     * 是否是境外
     * @param cityIdList cityIdList
     * @return Map<Long, Boolean>
     */
    Map<Long, Boolean> isNotChinaMainLandMap(List<Long> cityIdList);

    /**
     * 根据城市id查询城市信息
     * @param cityIds
     * @return
     */
    List<City> queryByCityIds(List<Long> cityIds);
}
