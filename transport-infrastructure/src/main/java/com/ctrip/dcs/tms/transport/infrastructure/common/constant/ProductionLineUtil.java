package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.scm.sdk.common.CategoryEnum;
import com.ctrip.dcs.scm.sdk.domain.CategoryRepository;
import com.ctrip.dcs.scm.sdk.domain.category.Category;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.igt.framework.common.base.GsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.sql.SQLException;
import java.util.*;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2021-03-31 11:48
 * 功能：产线前期定义为 1.接送机 2.打车 3.包车 对于库中数据扩展性的增长，为了更好定位数据需要做一层转换，对外仍是123，对内则是124
 * http://conf.ctripcorp.com/pages/viewpage.action?pageId=562791396&searchId=9F99ZMSRQ
 */
@Component
public class ProductionLineUtil {

    private static final Logger logger = LoggerFactory.getLogger(ProductionLineUtil.class);

    @Autowired
    private ProductionCategoryLineConfig productionCategoryLineConfig;

    @Autowired
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;
    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private BindRuleConfig bindRuleConfig;

    @Autowired
    private DriverGuideProxy guideProxy;

    /**
     * for:转换 -> 底层存储
     * 例：[1,2,3] -> 7
     */
    public Integer getIntegratedLine(List<Integer> lineCodeList) {
        Integer integratedLine = 0;
        if (CollectionUtils.isEmpty(lineCodeList)) {
            return CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue();
        }
        for (Integer lineCode : lineCodeList) {
            integratedLine += productionCategoryLineConfig.getShowToUseLineCodeMap().get(lineCode);
        }
        return integratedLine;
    }

    /**
     * for:转换 -> 关联包含 -> 底层查询
     * 例：[1] -> [1,3,5,7]
     */
    public List<Integer> getIncludeProductionLineList(List<Integer> lineCodeList) {
        List<Integer> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(lineCodeList)) {
            return result;
        }
        Set<Integer> lineSet = Sets.newHashSet();
        for (Integer lineCode : lineCodeList) {
            Integer useProductionLineCode = productionCategoryLineConfig.getShowToUseLineCodeMap().get(lineCode);
            if (useProductionLineCode == null) {
                continue;
            }
            lineSet.addAll(CommonEnum.UseProductionLineEnum.getIncludeProductionLineList(CommonEnum.UseProductionLineEnum.getEnumByCode(useProductionLineCode)));
        }
        return Lists.newArrayList(lineSet);
    }

    /**
     * for:转换 -> 展示 -> 对外展 & 前端对接
     * 例：7 -> [1,2,3]
     */
    public List<Integer> getShowProductionLineList(Integer integratedLine) {
        List<Integer> useLineCodeList = CommonEnum.UseProductionLineEnum.getSeparateProductionLineList(integratedLine);
        if (CollectionUtils.isEmpty(useLineCodeList)) {
            return Collections.emptyList();
        }
        List<Integer> result = Lists.newArrayListWithExpectedSize(useLineCodeList.size());
        for (Integer lineCode : useLineCodeList) {
            result.add(productionCategoryLineConfig.getUseToShowLineCodeMap().get(lineCode));
        }
        return result;
    }

    /**
     * for:转换 -> 展示
     * 例：7 -> 接送机,包车,打车
     */
    public String getProductionLineNames(Integer integratedLine) {
        List<Integer> lineCodeList = getShowProductionLineList(integratedLine);
        if (CollectionUtils.isEmpty(lineCodeList)) {
            return "";
        }
        Collections.sort(lineCodeList);
        StringBuilder res = new StringBuilder();
        for (Integer code : lineCodeList) {
            String categoryName = getCategoryName(code.longValue());
            if (Strings.isNullOrEmpty(categoryName)) {
                continue;
            }
            res.append(",");
            res.append(categoryName);
        }
        return res.toString().substring(1);
    }

    /**
     * for:查询 -> 展示
     * 例：1 -> 接送机
     */
    public String getCategoryName(Long categoryId) {
        if (categoryId == null) {
            return "";
        }
        Category category = categoryRepository.findOne(categoryId);
        if (category == null) {
            return "";
        }
        return category.getName();
    }

    /**
     * for:查询 -> 展示
     * 例：[1] -> [airport_pickup]
     */
    public List<String> getCategoryCodeList(List<Long> categoryIdList) {
        return Optional.ofNullable(categoryIdList).orElse(Lists.newArrayList()).stream().map(this::getCategoryCode).collect(Collectors.toList());
    }

    /**
     * for:查询 -> 展示
     * 例：1 -> airport_pickup
     */
    public String getCategoryCode(Long categoryId) {
        if (categoryId == null) {
            return "";
        }
        Category category = categoryRepository.findOne(categoryId);
        if (category == null) {
            return "";
        }
        return category.getCode();
    }

    /**
     * for:查询 -> 使用
     * 例：1 -> [airport_pickup,airport_dropoff,station_pickup,station_dropoff]
     */
    public List<String> getSubCategoryCode(Long categoryCode) {
        String code = productionCategoryLineConfig.getUseToLineCodeMap().get(categoryCode.intValue());
        List<String> result = Lists.newArrayList();
        for (CategoryEnum categoryEnum : CategoryEnum.values()) {
            if (categoryEnum.getParent().getCode().equals(code)) {
                result.add(categoryEnum.getCode());
            }
        }
        return result;
    }


    /**
     * for:获取接送机展示产线id 产线Code码 production.category.line.json
     * 例： -> 1
     */
    public Integer getJNTProductLineCode() {
        return productionCategoryLineConfig.getShowProductCode(CommonEnum.ProductionLineCodeEnum.JNT);
    }

    /**
     * 获取包车展示产线id 产线Code码 production.category.line.json
     */
    public Integer getDAYProductLineCode() {
        return productionCategoryLineConfig.getShowProductCode(CommonEnum.ProductionLineCodeEnum.DAY);
    }

    /**
     * 获取打车展示产线id 产线Code码 production.category.line.json
     */
    public Integer getPTPProductLineCode() {
        return productionCategoryLineConfig.getShowProductCode(CommonEnum.ProductionLineCodeEnum.PTP);
    }

    /**
     * 获取接送机&包车展示产线id集合 产线Code码 production.category.line.json
     */
    public List<Integer> getJNTAndDAYProductLineCode() {
        return Lists.newArrayList(getJNTProductLineCode(), getDAYProductLineCode());
    }

    /**
     * 获取存储型 产线id UseProductionLineEnum
     */
    public Integer getDBProductLineCode(String strLineCode) {
        return productionCategoryLineConfig.getProductCode(CommonEnum.ProductionLineCodeEnum.getEnumByValue(strLineCode));
    }

    /**
     * 查询运力组绑定限制 - 司机车辆绑定组合限制
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=477552583
     * drvCategorySynthesizeCode 司机产线
     * vehCategorySynthesizeCode 车辆产线
     */
    public Result<String> bindTransportCheck(Integer drvCategorySynthesizeCode, Integer vehCategorySynthesizeCode) {
        List<Integer> drvProductLineCodes = getShowProductionLineList(drvCategorySynthesizeCode);
        List<Integer> vehProductLineCodes = getShowProductionLineList(vehCategorySynthesizeCode);
        if (CollectionUtils.isEmpty(drvProductLineCodes) || CollectionUtils.isEmpty(vehProductLineCodes)) {
            return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUnableToBind)).build();
        }
        if (isLineCodesJNTAndDAY(drvProductLineCodes) || isProductLineCodeCheck(drvProductLineCodes, getJNTProductLineCode())) {
            if (isProductLineCodeCheck(vehProductLineCodes, getDAYProductLineCode())) {
                return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.vehicleBindingRestrictions)).build();
            }
            return Result.Builder.<String>newResult().success().build();
        }
        if (isProductLineCodeCheck(drvProductLineCodes, getDAYProductLineCode())) {
            if (isLineCodesJNTAndDAY(vehProductLineCodes) || isProductLineCodeCheck(vehProductLineCodes, getJNTProductLineCode())) {
                return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.vehicleBindingRestrictions)).build();
            }
            return Result.Builder.<String>newResult().success().build();
        }
        return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportOperatingFail)).build();
    }

    public Result<String> checkBind(Long drvId, Integer driverCategorySynthesizeCode, Integer vehicleCategorySynthesizeCode) throws SQLException {
        List<TspTransportGroupDriverRelationPO> tspTransportGroupDriverRelationPOS = tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Collections.singletonList(drvId));
        if (CollectionUtils.isEmpty(tspTransportGroupDriverRelationPOS)) {
            return Result.Builder.<String>newResult().success().build();
        }
        List<TspTransportGroupPO> tspTransportGroupPOS = transportGroupRepository.queryTspTransportByIds(Optional.ofNullable(tspTransportGroupDriverRelationPOS).orElse(Lists.newArrayList()).stream().map(TspTransportGroupDriverRelationPO::getTransportGroupId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(tspTransportGroupPOS)) {
            return Result.Builder.<String>newResult().success().build();
        }

        logger.info("checkBind", "driverCategorySynthesizeCode:{} vehicleCategorySynthesizeCode:{},tspTransportGroupPOS :{}", driverCategorySynthesizeCode, vehicleCategorySynthesizeCode, GsonUtil.toJson(tspTransportGroupPOS));
        List<Integer> showProductionLineList = getShowProductionLineList(driverCategorySynthesizeCode);
        List<Integer> showProductionLineList1 = getShowProductionLineList(vehicleCategorySynthesizeCode);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(showProductionLineList) || org.apache.commons.collections.CollectionUtils.isEmpty(showProductionLineList1)) {
            return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUnableToBind)).build();
        }

        for (TspTransportGroupPO tspTransportGroupPO : tspTransportGroupPOS) {
            Integer categorySynthesizeCode = tspTransportGroupPO.getCategorySynthesizeCode();
            List<Integer> showProductionLineList2 = getShowProductionLineList(categorySynthesizeCode);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(showProductionLineList2)) {
                return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUnableToBind)).build();
            }
            boolean canBind = checkBind(tspTransportGroupPO.getCategorySynthesizeCode(), driverCategorySynthesizeCode, vehicleCategorySynthesizeCode);
            if (!canBind) {
                return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.UPDATE_REPLACE_VEHICLE_GROUP)).build();
            }
        }
        return Result.Builder.<String>newResult().success().build();
    }

    /**
     * 判断是否只有一种产线，并且是指定产线
     */
    public Boolean isProductLineCodeCheck(List<Integer> showProductLineCodes, Integer lineCode) {
        return showProductLineCodes.size() == 1 && showProductLineCodes.get(0).intValue() == lineCode.intValue();
    }

    public boolean isProductLineCodeNewAddDayCheck(Integer originCategoryCode, Integer newCategoryCode) {
        if (originCategoryCode == null || newCategoryCode == null) {
            return false;
        }
        return ((newCategoryCode & 4) == 4) && ((originCategoryCode & 4) != 4);
    }
    public boolean isOnlyDayProductLine(Integer categoryCode) {
        return Objects.equals(categoryCode, CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue());
    }

    /**
     * show category 1,2,3,4
     * @param supplierId
     * @param showCategoryCodeList
     * @return
     */
    public boolean isGraySupplierContainsDayProductLine(Long supplierId, List<Integer> showCategoryCodeList) {
        if (CollectionUtils.isEmpty(showCategoryCodeList)) {
            return false;
        }
        Integer integratedLine = getIntegratedLine(showCategoryCodeList);
        return Objects.equals((integratedLine & 4), CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()) && guideProxy.getGrayControl(supplierId);
    }

    /**
     * 判断传产线列表是否是接送机产线和包车产线
     */
    public Boolean isLineCodesJNTAndDAY(List<Integer> showCodesList) {
        if (CollectionUtils.isEmpty(showCodesList) || showCodesList.size() <= 1) {
            return false;
        }
        List<Integer> jntAndDayCodes = getJNTAndDAYProductLineCode();
        if (CollectionUtils.isEmpty(jntAndDayCodes) || jntAndDayCodes.size() != showCodesList.size()) {
            return false;
        }
        Collections.sort(showCodesList);
        for (int i = 0; i < showCodesList.size(); i++) {
            if (showCodesList.get(i).intValue() != jntAndDayCodes.get(i).intValue()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取同时包含产线集
     * for:转换 -> 关联包含 -> 取交集
     * 例：[1] -> [1,3,5,7]
     * 例：[4] -> [4,5,6,7]
     * 交集 [1 & 4] -> [5,7]
     */
    public List<Integer> getAllIncludeProductionLineList(List<Integer> lineCodeList) {
        List<Integer> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(lineCodeList)) {
            return result;
        }
        if (lineCodeList.size() == 1) {
            return getIncludeProductionLineList(lineCodeList);
        }
        List<List<Integer>> proLineIdFetchers = Lists.newArrayListWithExpectedSize(lineCodeList.size());
        for (Integer code : lineCodeList) {
            proLineIdFetchers.add(getIncludeProductionLineList(Lists.newArrayList(code)));
        }
        Set<Integer> driverIDs = Sets.newHashSet(proLineIdFetchers.get(0));
        for (int i = 1; i < proLineIdFetchers.size(); i++) {
            if (CollectionUtils.isEmpty(driverIDs)) {
                break;
            }
            driverIDs = Sets.intersection(driverIDs, Sets.newHashSet(proLineIdFetchers.get(i)));
        }
        return Lists.newArrayList(driverIDs);
    }

    public Integer insideProLineMerge(Set<Integer> lineCodeList){
        if (CollectionUtils.isEmpty(lineCodeList)) {
            return CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue();
        }
        if(lineCodeList.size() == 1){
            return lineCodeList.iterator().next();
        }
        Integer totalProLine = 0;
        for(Integer lineCode : lineCodeList){
            totalProLine += lineCode;
        }
        return totalProLine;
    }


    /**
     * 检查绑定关系
     *
     * @param transportCategorySynthesizeCode 交通类别综合码
     * @param driverCategorySynthesizeCode 驾驶员类别综合码
     * @param vehiCategorySynthesizeCode 车辆类别综合码
     * @return 返回一个布尔值，始终为true，表示绑定关系检查始终通过
     */
    public boolean checkBind(Integer transportCategorySynthesizeCode, Integer driverCategorySynthesizeCode, Integer vehiCategorySynthesizeCode) {
        return bindRuleConfig.canBind(transportCategorySynthesizeCode, driverCategorySynthesizeCode, vehiCategorySynthesizeCode);
    }

    public boolean checkBind(Integer transportCategorySynthesizeCode, Integer driverCategorySynthesizeCode) {
        return bindRuleConfig.canBind(transportCategorySynthesizeCode, driverCategorySynthesizeCode);
    }

}
