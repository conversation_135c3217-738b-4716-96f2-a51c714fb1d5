package com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ModRecordConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.AbstraceCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CacheBusinessQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.frt.xresource.framework.utility.redis.ResourceFillDataAction;
import com.dianping.cat.Cat;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 运力组缓存处理器
 */

@Service
public class TransportGroupCacheHandler extends AbstraceCacheHandler {

  @Autowired
  private TransportGroupRepository transportGroupRepository;

  @Autowired
  CacheBusinessQconfig cacheBusinessQconfig;

  @Override
  public void refreshCache(DataChange dataChange) {
    doRefresh(getBusinessId(dataChange));
  }

  @Override
  public boolean support(String tableName) {
    return ModRecordConstant.TableName.tspTransportGroup.equalsIgnoreCase(tableName);
  }

  @Override
  protected String getCacheName() {
    return "tsp";
  }

  @Override
  protected ResourceFillDataAction getFillDataAction() {
    return new ResourceFillDataAction<TspTransportGroupPO>() {
      @SneakyThrows
      @Override
      public Map<String, TspTransportGroupPO> refreshAction(List<String> keyList) {
        Cat.logEvent(Constant.EventType.CACHE, Constant.EventName.CACHE_EVENT_TRANSPORT_REFRESH);
        return transportGroupRepository.queryTspTransportByIds(keyList.stream().map(Long::valueOf).collect(
          Collectors.toList())).stream().collect(Collectors.toMap(data -> String.valueOf(data.getTransportGroupId()), data -> data));
      }

      @Override
      public String getVersion() {
        return null;
      }

      @Override
      public boolean enableDiff() {
        return cacheBusinessQconfig.getConfig().isTransportGroupCacheDiff();
      }

      @Override
      protected void diff(Map<String, TspTransportGroupPO> oldDataMap, Map<String, TspTransportGroupPO> newDataMap) {
        if (Objects.isNull(oldDataMap) || Objects.isNull(newDataMap)) {
          return;
        }
        for (Map.Entry<String, TspTransportGroupPO> entry : oldDataMap.entrySet()) {
          String key = entry.getKey();
          TspTransportGroupPO newData = newDataMap.get(key);
          if (newData == null) {
            Cat.logEvent(Constant.EventType.CACHE_DIFF, getCacheName());
            return;
          }
         if (!Objects.equals(entry.getValue(), newData)) {
           Cat.logEvent(Constant.EventType.CACHE_DIFF, getCacheName());
           return;
         }
        }
      }
    };
  }

  @SneakyThrows
  public List<TspTransportGroupPO> queryTspTransportByIds(List<Long> transportIds) {
    Cat.logEvent(Constant.EventType.CACHE, Constant.EventName.CACHE_EVENT_TRANSPORT_QUERY);
    return mgetHit(transportIds.stream().map(String::valueOf).collect(Collectors.toList()), TspTransportGroupPO.class);
  }
}
