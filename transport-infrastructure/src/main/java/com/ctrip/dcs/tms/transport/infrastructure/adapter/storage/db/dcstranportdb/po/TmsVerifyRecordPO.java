package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;

/**
 * <AUTHOR>
 * @date 2021-06-21
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_verify_record")
public class TmsVerifyRecordPO implements DalPojo {

    /**
     * 主键ID
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 校验来源ID(司机ID，车辆ID)
     */
	@Column(name = "verify_source_id")
	@Type(value = Types.BIGINT)
	private Long verifySourceId;

    /**
     * 验证类型(1.人脸验证,2.车辆验证)
     */
	@Column(name = "verify_type")
	@Type(value = Types.TINYINT)
	private Integer verifyType;

    /**
     * 请参
     */
	@Column(name = "request_content")
	@Type(value = Types.VARCHAR)
	private String requestContent;

    /**
     * 出参
     */
	@Column(name = "response_content")
	@Type(value = Types.VARCHAR)
	private String responseContent;

    /**
     * 公共返回验证结果
     */
	@Column(name = "verify_result_code")
	@Type(value = Types.VARCHAR)
	private String verifyResultCode;

    /**
     * 验证失败的原因
     */
	@Column(name = "verify_fail_reason")
	@Type(value = Types.VARCHAR)
	private String verifyFailReason;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 变更人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getVerifySourceId() {
		return verifySourceId;
	}

	public void setVerifySourceId(Long verifySourceId) {
		this.verifySourceId = verifySourceId;
	}

	public Integer getVerifyType() {
		return verifyType;
	}

	public void setVerifyType(Integer verifyType) {
		this.verifyType = verifyType;
	}

	public String getRequestContent() {
		return requestContent;
	}

	public void setRequestContent(String requestContent) {
		this.requestContent = requestContent;
	}

	public String getResponseContent() {
		return responseContent;
	}

	public void setResponseContent(String responseContent) {
		this.responseContent = responseContent;
	}

	public String getVerifyResultCode() {
		return verifyResultCode;
	}

	public void setVerifyResultCode(String verifyResultCode) {
		this.verifyResultCode = verifyResultCode;
	}

	public String getVerifyFailReason() {
		return verifyFailReason;
	}

	public void setVerifyFailReason(String verifyFailReason) {
		this.verifyFailReason = verifyFailReason;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

}