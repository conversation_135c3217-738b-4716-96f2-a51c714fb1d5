package com.ctrip.dcs.tms.transport.infrastructure.port.repository;


import java.sql.SQLException;
import java.util.List;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDispatchRelationPO;
import com.ctrip.igt.framework.dal.DalRepository;

public interface DrvDispatchRelationRepository {

    DalRepository<DrvDispatchRelationPO> getDrvDispatchRelationRepo();

    Long insert(DrvDispatchRelationPO relationPO) throws SQLException;

    List<DrvDispatchRelationPO> queryDrvDisPatchRecord(Long drvId, Long supplierId, Boolean active);

    int operationDrvDispatchUnBing(Long drvId, Long supplierId, String modifyUser);

    List<DrvDispatchRelationPO> queryDrvDispatchSupplierIds(List<Long> drvIds);

    /**
     * 根据供应商id 查询有效派遣关系合作司机
     */
    List<Long> queryDispatchRelationDrvBySupplierId(Long supplierId);

}
