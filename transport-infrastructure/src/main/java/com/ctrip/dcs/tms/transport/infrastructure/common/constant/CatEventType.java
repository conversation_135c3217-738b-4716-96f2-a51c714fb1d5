package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

public interface CatEventType {
    String CHECK_DRIVER_PHONE_ONLY = "Check.Driver.Phone.Only";
    String CHECK_MOBILE_VALID = "Check.Phone.Valid";
    String DRV_PRE_CHECK = "Driver.PreCheck";
    String VOICE_CODE = "Voice.Code";
    String SMS_MESSAGE = "SMS.Message";
    String IVR_VOICE_RESULT = "IVR.Voice.Result";
    String DRIVER_STATUS = "Driver.Status";
    String DRIVER_ACTIVATION = "Driver.Activation";
    String IVR_CALL_RESULT = "IVR.Call.Result";
    String DRIVER_LOGIN = "Driver.Login";
    String IVR_VOICE_CALL = "IVR.Voice.Call";
    String ONLINE_MOBILE_VERIFIED = "Online.Phone.verified";
    String MOBILE_VERIFIED = "Phone.verified";
    String DRIVER_ACTIVE_ENABLE = "Driver.active.enable";
    String DRIVER_INACTIVE_REASON = "Driver.inActive.reason";
    String IVR_CALL_TIME = "Ivr.call.time";
    String INTERNATIONAL_COMPLIANCE_CHECK = "International.Compliance.Check";
    String INTERNATIONAL_REQUIRED_FIELD_QUERY = "International.Required.Field.Query";
    String INTERNATIONAL_OCR_RECOGNITION = "International.OCR.Recognition";
    String INTERNATIONAL_COMPLIANCE_VERIFICATION = "International.Compliance.Verification";
    String INTERNATIONAL_NEW_OCR_CHECK = "International.New.OCR.Check";
}
