package com.ctrip.dcs.tms.transport.infrastructure.common.handler;

import com.ctrip.arch.canal.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.dcs.tms.transport.mq.model.TmsDataChangeMessage;
import com.google.common.base.*;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.sql.*;
import java.util.Objects;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 变更记录生成处理流程
 * <AUTHOR>
 * @Date 2020/10/30 11:47
 */
public abstract class TmsModRecordHandler {

    private static final String defaultUser = "system";

    private final String MODIFY_USER = "modify_user";

    @Resource
    private TmsQmqProducer tmsQmqProducer;

    private Long initRrdId(DataChange dataChange){
        String tableName = dataChange.getTableName();
        if (!ModRecordConstant.modRecordRrdIdMap.containsKey(tableName)) {
            return null;
        }
        String columnValue = dataChange.getAfterColumnValue(ModRecordConstant.modRecordRrdIdMap.get(tableName));
        return Strings.isNullOrEmpty(columnValue) ? null : Long.valueOf(columnValue);
    }

    private Integer initRrdType(DataChange dataChange){
        String tableName = dataChange.getTableName();
        if (!ModRecordConstant.modRecordRrdTypeMap.containsKey(tableName)) {
            return null;
        }
        if(Objects.equals(tableName, ModRecordConstant.TableName.tmsTransportApprove)){
            for (ColumnData columnData : dataChange.getAfterColumnList()) {
                if(Objects.equals("approve_source_type",columnData.getName())){
                    switch (TmsTransportConstant.ApproveSourceTypeEnum.getCode(Integer.parseInt(columnData.getValue()))){
                        case DRV:
                            return CommonEnum.RecordTypeEnum.DRIVER.getCode();
                        case VEHICLE:
                            return CommonEnum.RecordTypeEnum.VEHICLE.getCode();
                    }
                }
            }
        }
        return ModRecordConstant.modRecordRrdTypeMap.get(tableName);
    }

    abstract List<TmsModContent> initModContent(DataChange dataChange);

    public abstract Integer initModType(DataChange dataChange);

    private void initExtendInfo(DataChange dataChange,TmsModRecordPO tmsModRecordPO){
        for (ColumnData columnData : dataChange.getAfterColumnList()) {
            if (!Objects.isNull(tmsModRecordPO.getCreateUser()) && !Objects.isNull(tmsModRecordPO.getDatachangeCreatetime())) {
                break;
            }
            if("modify_user".equals(columnData.getName())){
                tmsModRecordPO.setCreateUser(columnData.getValue());
                tmsModRecordPO.setModifyUser(columnData.getValue());
            }else if ("datachange_lasttime".equals(columnData.getName())){
                Timestamp timestamp = DateUtil.string2Timestamp(columnData.getValue(), DateUtil.YYYYMMDDHHMMSSS);
                tmsModRecordPO.setDatachangeCreatetime(timestamp);
                tmsModRecordPO.setDatachangeLasttime(timestamp);
            }
        }
        if (Strings.isNullOrEmpty(tmsModRecordPO.getCreateUser())) {
            tmsModRecordPO.setCreateUser(defaultUser);
            tmsModRecordPO.setModifyUser(defaultUser);
        }
        if (Objects.isNull(tmsModRecordPO.getDatachangeCreatetime())) {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            tmsModRecordPO.setDatachangeCreatetime(timestamp);
            tmsModRecordPO.setDatachangeLasttime(timestamp);
        }
    }

    /**
     * 处理数据变更，生成TmsModRecordPO
     * @param dataChange
     * @return
     */
    public TmsModRecordPO handle(DataChange dataChange) {
        TmsModRecordPO tmsModRecordPO = new TmsModRecordPO();
        tmsModRecordPO.setActive(true);


        Long rrdId = initRrdId(dataChange);
        if (Objects.isNull(rrdId)){
            return null;
        }

        Integer rrdType = initRrdType(dataChange);
        if (Objects.isNull(rrdType)){
            return null;
        }

        Integer modType = initModType(dataChange);
        if (Objects.isNull(modType)){
            return null;
        }
        List<TmsModContent> contentList = initModContent(dataChange);
        if (CollectionUtils.isEmpty(contentList)) {
            return null;
        }
        tmsModRecordPO.setRrdId(rrdId);
        tmsModRecordPO.setRrdType(rrdType);
        tmsModRecordPO.setModType(modType);
        tmsModRecordPO.setModContent(JsonUtil.toJson(contentList));

        //初始化其他信息
        initExtendInfo(dataChange,tmsModRecordPO);
        return tmsModRecordPO;
    }

    //发送数据变更消息
    public void sendTmsDataChangeMsg(DataChange dataChange) {
        Long bizId = getBusinessId(dataChange);
        if (bizId == null || bizId <= 0L) {
            return;
        }
        String modifyUser = "";
        for (ColumnData columnData : dataChange.getAfterColumnList()) {
            if (MODIFY_USER.equals(columnData.getName())) {
                modifyUser = columnData.getValue();
                break;
            }
        }

        TmsDataChangeMessage dataChangeMessage = new TmsDataChangeMessage();
        dataChangeMessage.setBizId(bizId);
        dataChangeMessage.setModifyUser(modifyUser);
        List<String> changeColumns = dataChange.getAfterColumnList().stream()
                .filter(ColumnData::isUpdated)
                .map(c -> convertToHumpString(c.getName())).collect(Collectors.toList());
        dataChangeMessage.setUpdateColumns(changeColumns);
        dataChangeMessage.setEventType(dataChange.getEventType());
        Map<String, Object> params = Maps.newLinkedHashMapWithExpectedSize(1);

        params.put("content", JsonUtil.toJson(dataChangeMessage));
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_TRANSPORT_DATA_INFO_CHANGED, getDataChangeTags(), params);
    }

    protected Long getBusinessId(DataChange dataChange) {
        return null;
    }

    protected Set<String> getDataChangeTags() {
        return Sets.newHashSet();
    }

    protected String convertToHumpString(String columnName) {
        if (StringUtils.isBlank(columnName)) {
            return columnName;
        }
        StringBuilder camelCase = new StringBuilder();
        boolean capitalizeNext = false;

        for (int i = 0; i < columnName.length(); i++) {
            char c = columnName.charAt(i);
            if (c == '_') {
                capitalizeNext = true;
            } else {
                camelCase.append(capitalizeNext ? Character.toUpperCase(c) : c);
                capitalizeNext = false; // 重置为false，以便下一个字符不是大写
            }
        }

        return camelCase.toString();
    }
}
