package com.ctrip.dcs.tms.transport.infrastructure.gateway;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TransportgroupGateway {

  List<TspTransportGroupPO> queryTspTransportByIds(List<Long> transportIds);

  List<TspTransportGroupPO> queryTspTransportBaseByIds(@NotNull List<Long> transportIds);
}
