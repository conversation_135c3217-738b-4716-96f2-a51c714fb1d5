package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DrvMobileNumberAuthReqDTO {
    /**
     * 手机号
     */
    private String mobilePhone;
    /**
     * 手机号区号
     */
    private String igtCode;
    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 司机ID
     */
    private Long driverId;
    /**
     * 身份证正面
     */
    private String idCardImgUrl;
    /**
     * 身份证反面
     */
    private String idCardBackImgUrl;
    /**
     * 手持身份证照片
     */
    private String scenePhotoUrl;
    /**
     * 身份证号
     */
    private String idCard;
}
