package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.driver.domain.account.ParsePhoneNumberRequestType;
import com.ctrip.dcs.driver.domain.account.TelphoneNumberType;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.MOBLIE;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.SPLIT_INVALID;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventType.SPLIT_VALID;


/**
 * <AUTHOR>
 * 手机号码校验
 */
@Service
public class MobileHelper {

  private static final Logger logger = LoggerFactory.getLogger(MobileHelper.class);

  @Autowired
  TmsTransportQconfig tmsTransportQconfig;

  @Autowired
  CityRepository cityRepository;

  @Autowired
  BusinessQConfig businessQConfig;

  @Autowired
  DriverDomainServiceProxy driverDomainServiceProxy;

  @Autowired
  IVRCallService ivrCallService;

  /**
   * 注意，如果是后台请求过来的，直接放行，只校验由页面发起的操作
   * 判断手机号是否有效(经过短信或者ivr验证)
   * 1如果是H5入驻的，则直接通过
   * 2如果是境内的，本次也直接放过
   * @param igtCode 区号
   * @return true:有效
   */
  public Result<Boolean> isMobileVerified(String igtCode, String phone, Integer internalScope) {
    return isMobileVerified(igtCode, phone, internalScope, ErrorCodeEnum.TRANSPORT_DRIVER_MOBILE_NOT_VERIFIED);
  }

  /**
   * 注意，如果是后台请求过来的，直接放行，只校验由页面发起的操作
   * 判断手机号是否有效(经过短信或者ivr验证)
   * 1如果是H5入驻的，则直接通过
   * 2如果是境内的，本次也直接放过
   * @param igtCode 区号
   * @return true:有效
   */
  public Result<Boolean> isMobileVerified(String igtCode, String phone, Integer internalScope, ErrorCodeEnum errorCodeEnum) {
     // 如果是后台请求过来的，直接放行
     //举例：
    //1 运力组收到商品上线消息，上线运力组
    //2 判罚撤销，判罚域直接调用供应链接口上线司机

    if(StringUtils.isEmpty(SessionHolder.getRestSessionAccountType())) {
       return ResponseResultUtil.success();
     }
    //1 只校验格式
    if (!businessQConfig.isVerifyPhone(internalScope)) {
      return isMobileValid(igtCode, phone, null);
    }

    //2 判断手机号是否有效(经过短信或者ivr验证)
    Boolean verifyResult = ivrCallService.isPhoneVerified(
        PhoneDTO.builder().mobilePhone(phone).countryCode(igtCode).build());
    if (verifyResult) {
      Cat.logEvent(CatEventType.ONLINE_MOBILE_VERIFIED, "true");
      return ResponseResultUtil.success();
    }
    Cat.logEvent(CatEventType.ONLINE_MOBILE_VERIFIED, "false");
    return ResponseResultUtil.failed(errorCodeEnum.getCode(), SharkUtils.getSharkValue(errorCodeEnum.getMessage(), "Mobile not verified， Can not online"));

  }

  /**
   * 判断igtCode和phone是否合法
   * @param igtCode 区号
   * @param phone 电话
   * @return true:有效
   */
    public Result<Boolean> isMobileValid(String igtCode, String phone, Long cityId) {
      if (!businessQConfig.isOpen()) {
        return Result.Builder.<Boolean>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build();
      }

      //手机号为空不校验
      if (StringUtils.isBlank(phone) || StringUtils.isBlank(igtCode)) {
        return Result.Builder.<Boolean>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build();
      }

      final String decryptPhone = TmsTransUtil.decrypt(phone, KeyType.Phone);
      if (decryptPhone.startsWith("0")) {
        Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "startsWith 0");
        return Result.Builder.<Boolean>newResult().fail().withCode(ErrorCodeEnum.TRANSPORT_DRIVER_MOBILE_CAN_NOT_START_WITH_0.getCode()).withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_DRIVER_MOBILE_CAN_NOT_START_WITH_0.getMessage(), "Mobile not valid, please not start start with zero")).build();
      }

      //google lib 校验电话号码
      boolean valid = isValid(igtCode, decryptPhone, cityId);
      if (!valid) {
        Cat.logEvent(CatEventType.CHECK_MOBILE_VALID, "rule check fail");
        logger.info("isMobileValid", "Mobile not valid mobile {}, city: {}", decryptPhone, cityId);
        return Result.Builder.<Boolean>newResult().fail().withCode(ErrorCodeEnum.TRANSPORT_DRIVER_MOBILE_NOT_VALID.getCode()).withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_DRIVER_MOBILE_NOT_VALID.getMessage(), "Mobile not valid")).build();
      }
      return Result.Builder.<Boolean>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build();
    }

  protected boolean isValid(String igtCode, String decryptPhone, Long cityId) {
    if(!businessQConfig.isCheckMobileByGooglePhoneNumberUtil()) {
      return true;
    }

    ParsePhoneNumberRequestType requestType = new ParsePhoneNumberRequestType();
    requestType.setNumber(decryptPhone);
    requestType.setCountryCode(igtCode);
    requestType.setPhoneType(TelphoneNumberType.ALL);
    Result<String> result = driverDomainServiceProxy.parsePhoneNumber(requestType);
    if (!result.isSuccess()) {
      Cat.logEvent(MOBLIE, SPLIT_INVALID);
      return false;
    }
    Cat.logEvent(MOBLIE, SPLIT_VALID);
    return true;
  }

}
