package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

/**
 *
 */
@Repository(value = "tmsRecruitingApproveStepRepository")
public class TmsRecruitingApproveStepRepositoryImpl implements TmsRecruitingApproveStepRepository {

    private DalRepository<TmsRecruitingApproveStepPO> tmsRecruitingApproveStepRepo;

    public TmsRecruitingApproveStepRepositoryImpl() throws SQLException {
        tmsRecruitingApproveStepRepo = new DalRepositoryImpl<>(TmsRecruitingApproveStepPO.class);

    }

    @Override
    public TmsRecruitingApproveStepPO queryByPk(Long id) {
        return tmsRecruitingApproveStepRepo.queryByPk(id);
    }

    @Override
    public int update(TmsRecruitingApproveStepPO stepPO) {
        return tmsRecruitingApproveStepRepo.update(stepPO);
    }

    @Override
    public Long insert(TmsRecruitingApproveStepPO stepPO) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        tmsRecruitingApproveStepRepo.insert(new DalHints(), keyHolder, stepPO);
        return keyHolder.getKey().longValue();
    }

    @Override
    public List<TmsRecruitingApproveStepPO> queryApproveStepList(Long approveSourceId, Integer approveType, Integer approveFrom) {
        if (approveSourceId == null || approveSourceId <= 0) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_type", approveType, Types.INTEGER);
            builder.and().equal("approve_from", approveFrom, Types.INTEGER);
            builder.orderBy("approve_item", true);
            return tmsRecruitingApproveStepRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateApproveStatus(Long id, Integer approveStatus, String approveReason,Integer accountType,String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder("update tms_recruiting_approve_step set approve_status = ?,modify_user = ?,approve_time = now() ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "approve_status", Types.INTEGER, approveStatus);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            //单项审批通过,审批原因置为空
            if(Objects.equals(TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode(),approveStatus)){
                sqlStr.append(",approve_reason = ? ");
                parameters.setSensitive(i++, "approve_reason", Types.VARCHAR, "");
            }
            if (StringUtils.isNotEmpty(approveReason)) {
                sqlStr.append(",approve_reason = ? ");
                parameters.setSensitive(i++, "approve_reason", Types.VARCHAR, approveReason);
            }
            if(Objects.equals(approveStatus, TmsTransportConstant.SingleApproveStatusEnum.APPROVE_THROUGH.getCode())&&
                    Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                sqlStr.append(",final_pass_status = ? ");
                parameters.setSensitive(i++, "final_pass_status", Types.INTEGER, 1);
            }
            sqlStr.append(" where id = ? ");
            parameters.setSensitive(i++, "id", Types.BIGINT, id);
            builder.setTemplate(sqlStr.toString());
            return tmsRecruitingApproveStepRepo.getQueryDao().update(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateWaitApproveStatus(Long approveSourceId, Integer approveType, Integer approveFrom, String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder("update tms_recruiting_approve_step set approve_status = ?,modify_user = ? where approve_source_id = ? and approve_type = ? and approve_from = ? and approve_status = 2 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "approve_status", Types.INTEGER, TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode());
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "approve_source_id", Types.BIGINT, approveSourceId);
            parameters.setSensitive(i++, "approve_type", Types.INTEGER, approveType);
            parameters.setSensitive(i++, "approve_from", Types.INTEGER, approveFrom);
            builder.setTemplate(sqlStr.toString());
            return tmsRecruitingApproveStepRepo.getQueryDao().update(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsRecruitingApproveStepPO> queryApproveStepList(Long approveSourceId, Integer approveType) {
        if (approveSourceId == null || approveSourceId <= 0) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_type", approveType, Types.INTEGER);
            builder.orderBy("approve_item", true);
            return tmsRecruitingApproveStepRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsRecruitingApproveStepPO> queryNoThroughReason(List<Long> approveSourceId, Integer approveType, Integer approveFrom) {
        if (CollectionUtils.isEmpty(approveSourceId)) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("approve_source_id", approveSourceId, Types.BIGINT);
            if(approveType!=null){
                builder.and().equal("approve_type", approveType, Types.INTEGER);
            }
            builder.and().equal("approve_from", approveFrom, Types.INTEGER);
            builder.and().equal("approve_status", TmsTransportConstant.SingleApproveStatusEnum.APPROVE_NO_THROUGH.getCode(), Types.INTEGER);
            return tmsRecruitingApproveStepRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsRecruitingApproveStepPO> queryApproveStepByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("id", ids, Types.BIGINT);
            return tmsRecruitingApproveStepRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateWaitApproveStatusFromPass(Long approveSourceId, Integer approveType, Integer approveItem,Integer approveFrom, String modifyUser, String approveReason) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder("update tms_recruiting_approve_step set approve_status = ?,approve_reason = ?,final_pass_status = 0,modify_user = ? where approve_source_id = ? and approve_type = ? and approve_item = ? and approve_from = ? and approve_status = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "approve_status", Types.INTEGER, TmsTransportConstant.SingleApproveStatusEnum.WAITAPPROVE.getCode());
            parameters.setSensitive(i++, "approve_reason", Types.VARCHAR, approveReason);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "approve_source_id", Types.BIGINT, approveSourceId);
            parameters.setSensitive(i++, "approve_type", Types.INTEGER, approveType);
            parameters.setSensitive(i++, "approve_item", Types.INTEGER, approveItem);
            parameters.setSensitive(i++, "approve_from", Types.INTEGER, approveFrom);
            builder.setTemplate(sqlStr.toString());
            return tmsRecruitingApproveStepRepo.getQueryDao().update(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateSupplierFinalPassStatus(Long approveSourceId, Integer approveType, Integer approveItem) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder("update tms_recruiting_approve_step set final_pass_status = 0 where approve_source_id = ? and approve_type = ? and approve_item = ? and approve_from = ? and final_pass_status = 1 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "approve_source_id", Types.BIGINT, approveSourceId);
            parameters.setSensitive(i++, "approve_type", Types.INTEGER, approveType);
            parameters.setSensitive(i++, "approve_item", Types.INTEGER, approveItem);
            parameters.setSensitive(i++, "approve_from", Types.INTEGER, CommonEnum.ApproveFromEnum.vbk.getValue());
            builder.setTemplate(sqlStr.toString());
            return tmsRecruitingApproveStepRepo.getQueryDao().update(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TmsRecruitingApproveStepPO> queryApproveStepList(Long approveSourceId, Integer approveType, Integer approveItem, Integer approveFrom) {
        if (approveSourceId == null || approveSourceId <= 0) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_type", approveType, Types.INTEGER);
            builder.and().equal("approve_item", approveItem, Types.INTEGER);
            builder.and().equal("approve_from", approveFrom, Types.INTEGER);
            return tmsRecruitingApproveStepRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }


    @Override
    public List<TmsRecruitingApproveStepPO> queryApproveStepByItems(Long approveSourceId, Integer approveType, List<Integer> approveItems) {
        if (approveSourceId == null) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("approve_source_id", approveSourceId, Types.BIGINT);
            builder.and().equal("approve_type", approveType, Types.INTEGER);
            builder.and().in("approve_item", approveItems, Types.INTEGER);
            return tmsRecruitingApproveStepRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateApproveStatusByIds(List<Long> ids, Integer approveStatus,String modifyUser) {
        if(CollectionUtils.isEmpty(ids)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder("update tms_recruiting_approve_step set approve_status = ?,modify_user = ? where id in (?)");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "approve_status", Types.INTEGER, approveStatus);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setInParameter(i++, "id", Types.BIGINT, ids);
            builder.setTemplate(sqlStr.toString());
            return tmsRecruitingApproveStepRepo.getQueryDao().update(builder, parameters, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }
}
