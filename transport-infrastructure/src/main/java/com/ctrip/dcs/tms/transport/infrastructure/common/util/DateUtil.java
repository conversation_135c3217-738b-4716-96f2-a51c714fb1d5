package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DateRangeDTO;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.lang.*;
import org.apache.http.client.utils.*;

import java.sql.*;
import java.text.*;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;
import java.util.*;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DateUtil {

    public static final int SECOND_INTERVAL = 60;
    public static final int MINUTE_INTERVAL = 60;
    public static final int DAY_INTERVAL = 24;

    public static final String CH_MMDDHHMM = "MM月dd日 HH:mm";
    public static final String YYYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDDHHMMSSS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String YYYYMMDDHHMM = "yyyy-MM-dd HH:mm";
    public static final String YYYYMMDDHH = "yyyy-MM-dd HH";

    public static final String YYYYMMDD = "yyyy-MM-dd";
    public static final String yyyyMMdd = "yyyyMMdd";
    public static final String HHMM = "HH:mm";
    public static final String HH = "HH";
    public static final String HHMMSS = "HH:mm:ss";

    public static final Date defaultDate = stringToDate("1970-01-01",YYYYMMDD);

    public static String dateToString(Date date, String format) {
        if (date == null)
            return "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String timestampToString(Timestamp timestamp, String format) {
        if (timestamp == null) {
            return "";
        }
        try {
            return new SimpleDateFormat(format).format(timestamp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static Timestamp string2Timestamp(String time, String format) {
        if (Strings.isNullOrEmpty(time) || Strings.isNullOrEmpty(format)) {
            return null;
        }
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            return new Timestamp(simpleDateFormat.parse(time).getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Date stringToDate(String date, String format) {
        if (Strings.isNullOrEmpty(date) || Strings.isNullOrEmpty(format)) {
            return null;
        }
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 将日期进行按分钟移位
     *
     * @param date   date（为空时，按当天算）
     * @param minute
     * @return
     */
    public static Date getMinuteShift(Date date, int minute) {
        if (date == null) {
            date = new Date();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MINUTE, minute);
        return cal.getTime();
    }

    public static Timestamp getNow() {
        Date date = new Date();
        Timestamp nousedate = new Timestamp(date.getTime());
        return nousedate;
    }

    /**
     * 指定日期的时间戳
     * @param date
     * @return 指定日期的时间戳
     */
    public static Timestamp getTimestamp(Date date) {
       return new Timestamp(date.getTime());
     }

     public static Timestamp getTimestampDisMinu(Date date, int minute){
        return (getTimestamp(getMinuteShift(date,minute)));
     }

    /**
     * 两个日期相差的分钟数
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    public static long getMinOfDate(Date beginDate, Date endDate) {
        long beginTimes = (beginDate == null ? new Date() : beginDate).getTime();
        long endTimes = (endDate == null ? new Date() : endDate).getTime();
        long subMin = (endTimes - beginTimes) / 60000;
        return subMin;
    }

    /**
     * 两个日期相差的分钟数
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    public static int getNowHour(Date date) {
        return Integer.parseInt(dateToString(date,HH));
    }

    /**
     *
     * 将日期进行按小时移位
     *
     * @param date（为空时，按当天算）
     * @param hour
     * @return
     */
    public static Date getDayShift(Date date, int hour) {
        if (date == null) {
            date = new Date();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR, hour);
        return cal.getTime();
    }

    /**
     * 已冻结时长
     *
     * @param freezeTime
     * @return
     */
    public static String getAlreadyFreezeShow(Timestamp freezeTime) {
        if (freezeTime == null) {
            return "";
        }
        long subMin = DateUtil.getMinOfDate(freezeTime, new Date());
        long day = subMin / (60 * 24);
        long hour = (subMin % (60 * 24)) / 60;
        long min = subMin % 60;
        return day + "天" + hour + "小时" + min + "分";
    }

    /**
     * 累积冻结时长
     *
     * @param freezeHour
     * @return
     */
    public static String getFreezeDayHourShow(Integer freezeHour) {
        if (freezeHour != null && freezeHour != 0) {
            int day = freezeHour / 24;
            int hour = freezeHour % 24;
            return day + "天" + hour + "小时";
        }
        return "";
    }

    public static String getRemainingFreezeTime(Timestamp freezeTime, Integer freezeHour) {
        if (freezeTime != null && freezeHour != null) {
            long subMin = DateUtil.getMinOfDate(freezeTime, new Date());
            long delayMins = freezeHour * 60 - subMin;
            long day = delayMins / (60 * 24);
            long hour = (delayMins % (60 * 24)) / 60;
            long min = delayMins % 60;
            return day + "天" + hour + "小时" + min + "分";
        }
        return "";
    }

    public static String getEndfreezeTime(Timestamp freezeTime, Integer freezeHour) {
        if (freezeHour != null && freezeTime != null) {
            return dateToString(getDayShift(freezeTime, freezeHour),YYYYMMDDHHMMSS);
        }
        return null;
    }

    /**
     * 累积冻结天数
     *
     * @param freezeHour
     * @return
     */
    public static int getFreezeDay(Integer freezeHour) {
        if (freezeHour != null && freezeHour != 0) {
            int day = freezeHour / 24;
            int hour = freezeHour % 24;
            return day;
        }
        return 0;
    }

    public static String getTimeStr(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(YYYYMMDDHHMMSS).format(date);
    }

    public static String getDateStr(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(YYYYMMDD).format(date);
    }

    public static String getDateAndHourStr(Date date) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(YYYYMMDDHH).format(date);
    }

    public static String getTimeStr(Date date,String format) {
        if (date == null) {
            return "";
        }
        return new SimpleDateFormat(format).format(date);
    }

    /**
     * 剩余冻结天数
     * @param freezeTime
     * @param freezeHour
     * @return
     */
    public static long getRemainingDay(Timestamp freezeTime, Integer freezeHour) {
        long subMin = DateUtil.getMinOfDate(freezeTime, new Date());
        return freezeHour * 60 - subMin;
    }

    /**
     * 剩余冻结分钟数
     * @param freezeTime
     * @param freezeHour
     * @return
     */
    public static long getRemainingMins(Timestamp freezeTime, Integer freezeHour) {
        if (freezeTime != null && freezeHour != null) {
            long subMin = DateUtil.getMinOfDate(freezeTime, new Date());
            return freezeHour * 60 - subMin;
        }
        return -1;
    }

    /**
     * 计算2个日期之间相差的  以年、月、日为单位，各自计算结果是多少
     * 比如：2011-02-02 到  2017-03-02
     *                                以年为单位相差为：6年
     *                                以月为单位相差为：73个月
     *                                以日为单位相差为：2220天
     * @param fromDate
     * @param toDate
     * @return
     */
    public static int dayCompareMonth(Date fromDate,Date toDate){
        Calendar  from  =  Calendar.getInstance();
        from.setTime(fromDate);
        Calendar  to  =  Calendar.getInstance();
        to.setTime(toDate);
        //只要年月
        int fromYear = from.get(Calendar.YEAR);
        int fromMonth = from.get(Calendar.MONTH);

        int toYear = to.get(Calendar.YEAR);
        int toMonth = to.get(Calendar.MONTH);

        int month = toYear *  12  + toMonth  -  (fromYear  *  12  +  fromMonth);
        return month;
    }

    public static Date getDate(String datetime,String format){
        if (datetime == null || StringUtils.isEmpty(format)) {
            return null;
        }
        try {
            return new SimpleDateFormat(format).parse(datetime);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    public static long getDatePoor(Date endDate, Date nowDate) {

        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day;
    }

    public static Long getIntervalYears(Date now, Date beforeDate) {
        if (now == null || beforeDate == null) {
            return 0L;
        }
        return getDatePoor(now, beforeDate) / 365;
    }

    public static Date parseDate(String dateStr) {
        Date date;
        try {
            date = DateUtils.parseDate(dateStr, new String[]{YYYYMMDDHHMMSS, YYYYMMDDHHMMSSS, YYYYMMDDHHMM, YYYYMMDD, yyyyMMdd});
        } catch (Exception e) {
            return null;
        }
        return date;
    }

    public static Double getHm(String dateStr) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(stringToDate(dateStr, HHMM));
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        return hour + minute / 60.0;
    }

    public static List<String> getWorkPeriod(String workPeriod) {
        if (Strings.isNullOrEmpty(workPeriod)) {
            return Lists.newArrayList();
        }
        String[] periodList = workPeriod.split(",");
        if (periodList.length == 1) {
            return Lists.newArrayList(periodList[0]);
        }
        List<String> preList = Lists.newArrayListWithExpectedSize(periodList.length);
        for (String period : periodList) {
            String[] timePeriod = period.split("~");
            StringBuilder stringBuilder = new StringBuilder(timePeriod[0]);
            stringBuilder.append("~");
            String[] timeTail = timePeriod[1].split(":");
            Integer hour = Integer.valueOf(timeTail[0]);
            Integer minutes = Integer.valueOf(timeTail[1]);
            if (minutes == 29) {
                minutes = 30;
            } else if (minutes == 59) {
                if (hour == 23) {
                    hour = 0;
                } else {
                    hour++;
                }
                minutes = 0;
            }
            String hourStr = hour < 10 ? ("0" + hour) : (hour + "");
            String minutesStr = minutes == 0 ? "00" : minutes + "";
            stringBuilder.append(hourStr).append(":").append(minutesStr);
            preList.add(stringBuilder.toString());
        }
        for (int i = preList.size() - 1; i >= 1; i--) {
            String[] tail = preList.get(i).split("~");
            String[] head = preList.get(i - 1).split("~");
            if (tail[0].equals(head[1])) {
                String newPeriod = head[0] + "~" + tail[1];
                preList.remove(i);
                preList.remove(i - 1);
                preList.add(newPeriod);
            }
        }

        for (int i = preList.size() - 1; i >= 1; i--) {
            Collections.sort(preList);
            String[] tail = preList.get(i).split("~");
            String[] head = preList.get(i - 1).split("~");
            if (tail[0].equals(head[1])) {
                String newPeriod = head[0] + "~" + tail[1];
                preList.remove(i);
                preList.remove(i - 1);
                preList.add(newPeriod);
            }
        }
        if(preList.size() == 2){
            Collections.sort(preList);
            Collections.reverse(preList);
            for (int i = preList.size() - 1; i >= 1; i--) {
                String[] tail = preList.get(i).split("~");
                String[] head = preList.get(i - 1).split("~");
                if (tail[0].equals(head[1])) {
                    String newPeriod = head[0] + "~" + tail[1];
                    preList.remove(i);
                    preList.remove(i - 1);
                    preList.add(newPeriod);
                }
            }
        }
        for (int i = preList.size() - 1; i >= 0; i--) {
            String[] timePeriod = preList.get(i).split("~");
            StringBuilder stringBuilder = new StringBuilder(timePeriod[0]);
            stringBuilder.append("~");
            String[] timeTail = timePeriod[1].split(":");
            Integer hour = Integer.valueOf(timeTail[0]);
            Integer minutes = Integer.valueOf(timeTail[1]);
            if (minutes == 30) {
                minutes = 29;
            } else if (minutes == 0) {
                if (hour == 0) {
                    hour = 23;
                } else {
                    hour--;
                }
                minutes = 59;
            }
            String hourStr = hour < 10 ? ("0" + hour) : (hour + "");
            String minutesStr = minutes == 0 ? "00" : minutes + "";
            preList.set(i, stringBuilder.append(hourStr).append(":").append(minutesStr).toString());
        }
        return preList;
    }

    public static Date dateToDate(Date date, String format) {
        if (date == null)
            return new Date();
        try {
            String dateStr = dateToString(date,format);
            return stringToDate(dateStr,format);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new Date();
    }

    public static long getDifferenceHour(Date endDate, Date nowDate) {

        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return hour;
    }

    /**
     * 天数位移
     * @return
     */
    public static Date dayDisplacement(Date date,int day){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, day);
        return cal.getTime();
    }

    /**
     * 当前时间开始随机一个24小时内的时间点
     * @return
     */
    public static String calculateVerifyStartTime(int maxRandom) {
        //当前时间小时数
        int nowHour = DateUtil.getNowHour(new Date());
        int radom = StringUtil.randomNum(nowHour, maxRandom);
        String nowDate = DateUtil.dateToString(new Date(), DateUtil.YYYYMMDD);
        StringBuilder startTime = new StringBuilder(nowDate);
        startTime.append(" ").append(radom).append(":00:00");
        Date startDate = DateUtil.stringToDate(startTime.toString(),DateUtil.YYYYMMDDHHMMSS);
        if(startDate!=null && startDate.before(new Date())){
            return DateUtil.dateToString(DateUtil.getDayShift(startDate,2),DateUtil.YYYYMMDDHHMMSS);
        }
        return startTime.toString();
    }

    public static int getMonth(){
        Calendar cal = Calendar.getInstance();
        return cal.get(Calendar.MONTH) + 1;
    }

    public static java.sql.Date stringToSqlDate(String date, String format) {
        Date utilDate = stringToDate(date, format);
        if (utilDate == null) {
            return null;
        }
        return new java.sql.Date(utilDate.getTime());
    }

    public static Boolean isToday(String date) {
        if(StringUtils.isEmpty(date)){
            return Boolean.FALSE;
        }
        LocalDate nowDate = LocalDate.now();
        LocalDate paramsDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(YYYYMMDD));
        return Objects.equals(paramsDate,nowDate);
    }

    public static int nowHour() {
        LocalDateTime nowDate = LocalDateTime.now();
        return nowDate.getHour();
    }

    public static long compareHour(Timestamp begin,Timestamp end){
        if(begin == null || end == null){
            return 0;
        }
        LocalDateTime beginLocal = begin.toLocalDateTime();
        LocalDateTime endLocal = end.toLocalDateTime();
        Duration duration =  Duration.between(beginLocal,endLocal);
        return duration.toHours();
    }

    /**
     * 超过1s加一小时
     *
     * @param begin
     * @param end
     * @return
     */
    public static long compareHourRoundUp(Timestamp begin,Timestamp end){
        if(begin == null || end == null){
            return 0;
        }
        LocalDateTime beginLocal = begin.toLocalDateTime();
        LocalDateTime endLocal = end.toLocalDateTime();
        Duration duration =  Duration.between(beginLocal,endLocal);
        long remainingSeconds = duration.getSeconds() % 3600;

        // 超过1s加一小时
        if (remainingSeconds > 0) {
            return duration.toHours() + 1;
        }
        return duration.toHours();
    }

    public static long compareDay(Timestamp begin,Timestamp end){
        if(begin == null || end == null){
            return 0;
        }
        LocalDateTime beginLocal = begin.toLocalDateTime();
        LocalDateTime endLocal = end.toLocalDateTime();
        Duration duration =  Duration.between(beginLocal,endLocal);
        return duration.toDays();
    }

    public static Timestamp dateToTimestamp(Date date) {
        if(date == null){
            return getNow();
        }
        return new Timestamp(date.getTime());
    }

    public static Timestamp nowDatePlusHours(Integer hourCount) {
        LocalDateTime now = LocalDateTime.now();
        return Timestamp.valueOf(now.plusHours(hourCount));
    }

    public static Timestamp nowDatePlusMinus(Integer minuCount) {
        LocalDateTime now = LocalDateTime.now();
        return Timestamp.valueOf(now.plusMinutes(minuCount));
    }

    public static String getTimeStr(LocalDateTime operateTime) {
        if (operateTime == null) {
            return "";
        }
        return operateTime.format(DateTimeFormatter.ofPattern(YYYYMMDDHHMMSS));
    }

    public static String getTimeStr(LocalDate operateTime) {
        if (operateTime == null) {
            return "";
        }
        return operateTime.format(DateTimeFormatter.ofPattern(YYYYMMDD));
    }

  public static int getSecondsFromEndOffDay() {
      LocalDateTime now = LocalDateTime.now();
      LocalDateTime endOfDay = now.toLocalDate().atTime(LocalTime.MAX);
      Duration duration = Duration.between(now, endOfDay);
      return (int)duration.getSeconds();
  }
    public static LocalDateTime convertStringToDateTime(String queryDate, String yyyyMmDd) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(yyyyMmDd);

        // 将字符串转换为 LocalDate
        return LocalDateTime.parse(queryDate, formatter);
    }

    public static LocalDate convertStringToDate(String queryDate, String yyyyMmDd) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(yyyyMmDd);
        // 将字符串转换为 LocalDate
        return LocalDate.parse(queryDate, formatter);
    }

    public static String convertDateToString(LocalDateTime localDate, String pattern) {
        // 创建日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        // 格式化日期时间
        return localDate.format(formatter);
    }

    public static LocalDate parseDateWithOutFormat(String dateString) {
        if (dateString == null) {
            return null;
        }
        // 尝试解析日期，使用多种可能的格式
        String[] dateFormats = {YYYYMMDDHHMMSS, YYYYMMDDHHMMSSS, YYYYMMDDHHMM, YYYYMMDDHH};
        for (String format : dateFormats) {
            try {
                return LocalDate.parse(dateString, DateTimeFormatter.ofPattern(format));
            } catch (DateTimeParseException e) {
                // 忽略解析异常，尝试下一个格式
            }
        }
        return null;
    }
    public static boolean checkDateInRange(List<DateRangeDTO> ranges, String dateString) {
        LocalDate date = parseDateWithOutFormat(dateString);
        if (date == null) {
            return false;
        }

        if (ranges == null) {
            return false;
        }

        for (DateRangeDTO range : ranges) {
            if (!date.isBefore(range.getStart()) && !date.isAfter(range.getEnd())) {
                return true;
            }
        }
        return false;
    }
}
