package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

public class SharkKeyConstant {

    //驾驶证返回信息
    public static final String  drvLicenseResultInfo = "drvLicenseResultInfo";
    //驾驶证核验信息
    public static final String  drvLicenseCheckInfo = "drvLicenseCheckInfo";
    //姓名匹配不一致
    public static final String  drvLicenseNameNoAgree = "drvLicenseNameNoAgree";
    //司机状态_
    public static final String  drvStatus = "drvStatus";
    //驾驶证状态
    public static final String  drvLicenseStatus = "drvLicenseStatus";
    //行驶证返回信息
    public static final String  vehicleLicenseResultInfo = "vehicleLicenseResultInfo";
    //车辆状态_
    public static final String  vehicleStatus = "vehicleStatus";
    //存在
    public static final String  exist = "exist";
    //网约车状态
    public static final String  netVehicleStatus = "netVehicleStatus";
    //查询成功，无数据
    public static final String  querySueecessNoData = "querySueecessNoData";
    //请求姓名不标准
    public static final String  nameUnStandard = "nameUnStandard";
    //请求参数不标准
    public static final String  paramsUnStandard = "paramsUnStandard";
    //限制查询
    public static final String  limitTheQuery = "limitTheQuery";
    //查无记录
    public static final String  queryNoData = "queryNoData";
    //参数错误
    public static final String  paramsFail = "paramsFail";
    //第三方供应商服务异常
    public static final String  theThirdPartyServiceError = "theThirdPartyServiceError";
    //请求已达上限,30分钟后重试
    public static final String  queryCeiling = "queryCeiling";
    //车辆VIN码不一致
    public static final String  vinCodeNoAgree = "vinCodeNoAgree";
    //车辆VIN码一致
    public static final String  vinCodeAgree = "vinCodeAgree";
    //车辆正常可用
    public static final String  vehicleNormalUse = "vehicleNormalUse";
    //车辆已强制报废
    public static final String  vehicleScrap = "vehicleScrap";
    //调用soa服务异常
    public static final String  soaError = "soaError";
    //请求身份证号不标准：身份证号为空或者不符合身份证校验规范
    public static final String  requestParamsNotStandard = "requestParamsNotStandard";
    //车辆报废状态
    public static final String  vehicleRetirementStatus = "vehicleRetirementStatus";



    //查询点位与服务区域关系异常
    public static final String  transportMsgSelectPointServiceAreaInfoError="transport.query.point.and.service.area.error";
    //查询供应商id失败
    public static final String  transportQuerySupplierFail="transport.query.supplier.fail";
    //运力组不存在
    public static final String  transportMsgTransportGroupNotFound="transport.group.is.empty";
    //供应商下无可用服务
    public static final String  transportMsgServiceProviderNotFound="transport.service.provider.is.empty";
    //获取RPC商品列表失败
    public static final String  transportMsgRPCListFail="transport.rpc.sku.list.fail";
    //缺少必要的查询参数
    public static final String  transportMsgRequiredParameterMissing="transport.parameter.missing";
    //查询失败
    public static final String  transportQueryFail= "transport.query.fail";
    //账号不存在或账号对应多条司机数据
    public static final String  transportMsgAccountNotFoundOrMoreDriverData="transport.account.not.exists.more.drv.data";
    //车牌号已存在
    public static final String  transportVehicleLicenseAlreadyExists="transport.vehiclelicense.already.exists";
    //发送短信异常
    public static final String  transportSendMsgError= "transport.send.msg.error";

    public static final String TRANSPORT_SEND_MSG_OVER_LIMIT = "transport.send.msg.over.limit";
    //验证短信异常
    public static final String  transportVerifyError="transport.verify.error";
    //司机id个数为0
    public static final String  transportDrvIdIsZero="transport.drv.id.is.zero";
    //司机【%s】当前状态不能被变更为【%s】
    public static final String  transportMsgDriverStatusChangeError="transport.drv.status.change.error";
    //变更状态异常
    public static final String  transportStatusChangeError="transport.status.change.error";
    //请先去完成商品的创建
    public static final String  transportPleaseFinishSkuCreate="transport.please.finish.sku.create";
    //身份证号已存在
    public static final String  transportIdcardAlreadyExists="transport.idcard.already.exists";
    //手机号已存在
    public static final String  transportPhoneAlreadyExists="transport.phone.already.exists";
    // 驾驶证照片必填
    public static final String  transportDriverCardImgRequired="transport.drivercard.img.required";
    //账号已存在
    public static final String  transportAccountAlreadyExists="transport.account.already.exists";
    //车辆不存在或被占用
    public static final String  transportVehicleIsEmptyOrOccupied="transport.vehicle.is.empty.or.occupied";
    //司机不存在
    public static final String  transportDrvisEmpty="transport.drv.is.empty";
    //司机【%s】当前状态不能被冻结
    public static final String  transportMsgDriverFreezeNoExamsAllowed="transport.drv.frreze.no.allowed";
    //执行冻结异常
    public static final String  transportMsgFreezeError="transport.freeze.error";
    //请假时段重叠，请查看请假记录
    public static final String  transportLeaveTtimeRepeatPleaseCheck="transport.leave.time.repeat.please.check";
    //司机审批信息不存在
    public static final String  transportMsgDrvVehRecruitingDoesNotExist="transport.drvveh.recruiting.info.does.not.exist";
    //司机对应的车辆审批不存在
    public static final String  transportVehApprovalInfoNotExist="transport.vehicle.approval.info.not.exist";
    //招募司机账号已存在
    public static final String  transportMsgQunarAccountMustBeUniqueness="transport.drvveh.recruiting.account.already.exists";
    //点位/时段配置新增失败
    public static final String  transportMsgPointIsError="transport.point.time.add.error";
    //工作班次新增失败
    public static final String transportMsgWorkShiftAddError ="transport.work.shift.add.error";
    //工作班次更新失败
    public static final String  transportMsgWorkShiftUpdateError="transport.work.shift.update.error";
    //更新服务异常
    public static final String  transportMsgUpdateError="transport.update.error";
    //完成
    public static final String  transportMsgFinish="transport.finish";
    //未查询到有效数据
    public static final String  transportQueryDataIsEmpty="transport.query.data.is.empty";
    public static final String  VEHICLE_OCR_COLOR_ILLEGAL="transport.ocr.color.illegal";
    public static final String  VEHICLE_DISPATCH_PHOTO_MUST_NOT_EMPTY="vehicle.dispatch.photo.must.not.empty";
    //未更新有效数据
    public static final String  transportUpdateError="transport.update.error.error";
    //绑定失败
    public static final String  transportMsgBindingError="transport.binging.error";
    //解绑失败
    public static final String  transportMsgUnBindingError="transport.unbinging.error";
    //司机[%s]不满足当前运力组模式要求
    public static final String  transportMsgDriverNotMatchGroupMode="transport.drv.not.match.group.mode";
    //不支持的运力组状态
    public static final String  transportGroupStatusError="transport.group.status.error";
    //添加失败
    public static final String  transportMsgAddError="transport.add.error";
    //运力组数据为空
    public static final String  transportMsgTransportDataIsEmpty="transport.group.data.is.empty";
    //无可绑定司机
    public static final String  transportMsgNoOneDriverCanBind="transport.can.not.bind.the.drv";
    //无法绑定
    public static final String  transportMsgUnableToBind="transport.unable.to.bind";
    //请检查相应的司机配置(是否已配置工作时段，是否已绑定车辆)
    public static final String  transportCheckDrvConfig="transport.check.drv.config";
    //冻结司机状态错误
    public static final String  drvFreezeStatusError="transport.drv.freeze.status.error";
    //冻结信息不存在
    public static final String  drvFreezeNullError= "transport.freeze.info.is.empyt";
    //累积冻结时间必须大于1小时并且小于等于90天
    public static final String  drvFreezeHoursError="transport.accumulation.freeze.time.error";
    //新冻结截止时间不能小于等于当前时间
    public static final String  freezeHoursCompareError="transport.frreze.time.greater.or.equal.to.now";
    //批量冻结司机或冻结原因错误
    public static final String  batchFreezeError="transport.batch.freeze.or.reason.error";
    //冻结失败
    public static final String  transportFreezeError="transport.freeze.error";
    //自动解冻
    public static final String  transportAutoUnFreezeInfo="transport.auto.unfreeze.info";
    //解冻信息不存在或当前不是冻结状态
    public static final String  drvUnfreezeAndStatusError="transport.unfreeze.and.status.error";
    //冻结时间大于0并且小于90天
    public static final String  freezeTimeScopeNotMatching="transport.freeze.time.scope.not.matching";
    //冻结原因不为空
    public static final String  freezeReasonNullError="transport.freeze.reason.not.empty";
    //解冻原因不为空
    public static final String  unFreezeReasonNullError="transport.un.freeze.reason.not.empty";
    //用车时段与进单上限配置不能为空
    public static final String  transportUseTimeAndInOrderConfNotNull="transport.use.time.and.into.order.not.empty";
    //工作班次配置不能为空
    public static final String  transportWorkShiftDetailsNotNull="transport.work.shift.details.not.empty";
    //进单配置检查不通过，请确保点位/时段不重复
    public static final String  transportCheckIntoOrderConfFail="transport.check.into.order.conf.fail";
    //工作班次检查不通过
    public static final String  transportCheckWorkShiftDetailsFail="transport.check.work.shift.details.fail";
    //司机状态
    public static final String  transportDriverState="transport.driver.state";
    //证件照正面
    public static final String  transportDriverFrontPhoto="transport.driver.frontPhoto";
    //司机工作时段重叠
    public static final String  transportDriverWorkPeriodOverlap="transport.driver.workPeriod.overlap";
    //网约车人照
    public static final String  transportDriverPeopleoPhoto="transport.driver.Ride_hailing_photos_of_people";
    //初始领证日期
    public static final String  transportFirstIssuanceDate="transport.driver.Date_of_initial_issuance";
    //发送邮件失败
    public static final String  transportMsgSendEmailError="transport.send.email.error";
    //司机
    public static final String  transportDrvDrvier="transport.drv.driver";
    //无符合条件的改派订单
    public static final String  transportNoToSendOrder="transport.not.to.send.order";
    //请在该运力组下关联商品
    public static final String  driverCarMgtCapacityBindSku="drivercarmgt.capacity.bindsku";

    //请在该运力组下关联司机
    public static final String  driverCarMgtCapacityBindDriver="drivercarmgt.capacity.binddriver";

    //请在该运力组关联司机和商品
    public static final String  driverCarMgtCapacityBindDriverAndSku="drivercarmgt.capacity.binddriverandsku";
    //运力组名称已存在
    public static final String  capacityGroupNameExist="drivercarmgt.capacity.capacitygroupnameexist";
    //该司机不存在
    public static final String  driverNotExist="drivercarmgt.capacity.drivernotexist";
    //vin已存在
    public static final String  transportVinAlreadyExists="capacitymgt.addcar.vinexist";
    //车牌号唯一
    public static final String  transportVehicleLicenseUniqueness="transport.vehiclelicense.must.be.uniqueness";
    //黑名单车牌号
    public static final String  blackVehicleLicenseMessage ="transport.vehiclelicense.in.black.list";
    //该邮箱号已被使用，请更换邮箱进行注册
    public static final String mailboxUsedChangeForRegistration = "mailbox.used.change.for.registration";
    //该手机号已被使用，请更换手机号进行注册
    public static final String mobilePhoneUsedChangeForRegistration = "mobile.phone.used.change.for.registration";
    //城市已存在配置,请重新选择
    public static final String cityCertificateConfigExist = "transport.city.certificate.config.exist";
    //证件配置不存在
    public static final String certificateConfigExist = "transport.certificate.config.exist";
    //操作失败
    public static final String  transportOperatingFail= "transport.operating.fail";
    //该运力组有X个赛道没有报名成功司机
    public static final String  transportNotApplySuccessDrvFail= "transport.not.apply.success.drv";
    //未查询到对应的班次信息
    public static final String  transportWorkShiftNoExist= "transport.work.shift.no.exist";
    //班次无可操作的司机
    public static final String  transportWorkShiftNoAssociatedDrv= "transport.work.shift.noa.ssociated.drv";
    //没有可接单司机
    public static final String  transportNoServiceDrv= "transport.no.service.drv";
    //获取司机城市分排名异常
    public static final String  transportDriverPointFail= "transport.driver.point.fail";
    //SKU已存在同点位城市同车型的运力组中
    public static final String  transportSameLocalSkuIsExist= "transport.same.local.sku.is.exist";
    //车辆不存在
    public static final String  transportVehicleIsEmpty="transport.vehicle.is.empty";
    //根据国家防疫要求，当前司机不可进行上线/解冻/销假，需要联系超级账号处理
    public static final String  transportcapacitygroupmgt="capacitygroupmgt.beijingpolicy.epidemicprevention";
    //当前赛道名额已满,无需补位
    public static final String  transportWorkShiftIsFull= "transport.work.shift.is.full";
    //当前赛道名额已超,无需补位
    public static final String  transportWorkShiftIsBeyond= "transport.work.shift.is.beyond";
    //运力组不存在或未上线
    public static final String  transportGroupIsNotExistsOrNotLine= "transport.group.is.notexists.or.notline";
    //司机审批中
    public static final String  transportApproveIng= "transport.approveing";
    //司机审批名称
    public static final String  transportDrvUpdateApproveName= "transport.drv.update.approve.name";
    //车辆审批名称
    public static final String  transportVehicleUpdateApproveName= "transport.vehicle.update.approve.name";
    //不能操作自己发起的审批
    public static final String  transportNoOperationOneselfApprove= "transport.no.operation.oneself.approve";
    //用户未登录
    public static final String  transportUnLogin= "transport.user.unlogin";
    //审批状态错误
    public static final String  transporApproveStatusFail= "transport.approve.status.fail";
    //证件审核状态存在【复核】或【审核中】
    public static final String  transportCertificateStatusExist= "transport.certificate.status.exist.review.checking";
    // 请先设置合规内容
    public static final String  transportCompliantStatusExist= "transport.compliant.status.exist.review.checking";

    /**该司机车辆组合只可绑定包车运力组，服务包车订单*/
    public static final String vehicleBindingRestrictions = "key.driver.vehicle.bind.chartered.capacity.group";
    /**如需解绑车辆，请先解绑全职运力组*/
    public static final String untieVehicleCapacityGroup = "key.untie.vehicle.capacity.group";
    /**如需更换车辆，请先解绑 %s运力组*/
    public static final String replaceVehicleCapacityGroup = "key.replace.vehicle.capacity.group";

    /**核酸疫苗展示结果 上传报告需人工审核，请耐心等候，X小时内会通知您结果。*/
    public static final String REPORT_HUMAN_REVIEW = "key.wait.patiently";

    /***上传成功，距本次检测已过%s天*/
    public static final String UPLOAD_SUCCESS_OVER_DAYS = "key.uploaded.successfully.a";

    /**上传成功*/
    public static final String UPLOADED_SUCCESSFULLY = "key.uploaded.successfully.b";

    /**核酸疫苗展示结果 上传报告审核不通过。原因：核酸检测报告检测主体非司机本人，请重新上传清晰完整和真实有效的报告；*/
    public static final String NUCLEIC_ACID_REPORT_NOT_ME = "key.upload.report.fail.nucleic.not.driver";

    /**核酸疫苗展示结果 上传报告审核不通过。原因：疫苗接种报告接种主体非司机本人，请重新上传清晰完整和真实有效的报告；*/
    public static final String VACCINE_REPORT_NOT_ME = "key.upload.report.fail.vaccination.not.driver";

    /**核酸疫苗展示结果 核酸检测报告检测主体非司机本人；*/
    public static final String WORKBENCH_NUCLEIC_ACID_REPORT_NOT_ME = "key.subject.nucleic.not.driver";

    /**核酸疫苗展示结果 疫苗接种报告接种主体非司机本人；*/
    public static final String WORKBENCH_VACCINE_REPORT_NOT_ME = "key.upload.vaccination.not.driver";

    /**核酸疫苗展示结果 上传报告审核不通过。原因：核酸检测报告已超过%s天有效期，请重新上传清晰完整和真实有效的报告；*/
    public static final String NUCLEIC_ACID_REPORT_OVERDUE = "key.upload.report.fail.nucleic.validity.period";

    /**核酸疫苗展示结果 核酸检测报告已超过%s天有效期；*/
    public static final String WORKBENCH_NUCLEIC_ACID_REPORT_OVERDUE = "key.nucleic.acid.validity.period";

    /**核酸疫苗展示结果 上传报告审核不通过。原因：核酸检测结果呈阳性。请及时就医。您的司机状态将置为冻结，待服务订单会自动改派。如有异议，请联系客服；*/
    public static final String NUCLEIC_ACID_REPORT_RESULT_POSITIVE = "key.upload.report.fail.nucleic.positive";

    /**核酸疫苗展示结果 核酸检测结果呈阳性*/
    public static final String WORKBENCH_NUCLEIC_ACID_REPORT_RESULT_POSITIVE = "key.nucleic.acid.positive";

    /**核酸疫苗展示结果 上传报告审核不通过。原因：您上传的资料非核酸检测报告，请重新上传清晰完整和真实有效的报告；*/
    public static final String NUCLEIC_ACID_REPORT_NOT_TRUE = "key.upload.report.fail.nucleic.wrong";

    /**核酸疫苗展示结果 您上传的资料非核酸检测报告；*/
    public static final String WORKBENCH_NUCLEIC_ACID_REPORT_NOT_TRUE = "key.upload.nucleic.acid.wrong";

    /**核酸疫苗展示结果 上传报告审核不通过。原因：您上传的资料非疫苗接种报告，请重新上传清晰完整和真实有效的报告；*/
    public static final String VACCINE_REPORT_NOT_TRUE = "key.upload.report.fail.vaccination.wrong";

    /**核酸疫苗展示结果 您上传的资料非疫苗接种报告；*/
    public static final String WORKBENCH_VACCINE_REPORT_NOT_TRUE = "key.upload.not.vaccination.report";

    /**核酸疫苗展示结果 核酸检测报告返回信息*/
    public static final String WORKBENCH_NUCLEIC_ACID_REPORT_RETURN_INFO = "key.nucleic.acid.report.return.information";

    /**核酸疫苗展示结果 疫苗接种报告返回信息*/
    public static final String WORKBENCH_VACCINE_REPORT_RETURN_INFO = "key.vaccination.report.return.information";

    /**核酸报告审批标题*/
    public static final String REPORT_REVIEW_TITLE = "key.examine.driver.epidemic.prevention";

    /***已失效，距本次检测已过%s天，请上次最新有效报告	*/
    public static final String FAILURE_OVER_DAYS = "key.expired.latest.valid.report";

    /**上传报告审核不通过。原因：%s*/
    public static final String UPLOAD_FAIL_AND_RESULT = "key.upload.report.failed";

    /**核酸报告 - 阳性冻结原因 - 阳性*/
    public static final String DRV_FREEZE_REASON_POSITIVE = "key.nucleic.acid.test.result.positive";

    /**核酸报告 - 过期*/
    public static final String DRV_FREEZE_REASON_OVERDUE = "key.nucleic.acid.test.expired";

    /**上传凭证失败默认话术*/
    public static final String DEFAULT_REJECT_REPORT = "key.report.default.reject.describe";

    /**该司机处于判罚中的冻结，需等待自动解冻才可上线*/
    public static final String  capacitygroupmgtJudgmentFreezeHint="capacitygroupmgt.judgmentfreeze.hint";
    /**以下司机处于判罚中的冻结，需等待自动解冻才可上线，其他司机可正常上线*/
    public static final String  capacitygroupmgtJudgmentGreezebatchHint="capacitygroupmgt.judgmentfreezebatch.hint";
    /**以下司机由于判罚被操作下线，不可上线，其他司机可正常上线*/
    public static final String  capacitygroupmgtJudgmentofflineHint="capacitygroupmgt.judgmentoffline.hint";
    /**以下司机处于判罚中的冻结及下线，冻结司机需等待自动解冻才可上线，下线司机不可操作上线，其他司机可正常上线*/
    public static final String  capacitygroupmgtJudgmentfreezeofflineHint="capacitygroupmgt.judgmentfreezeoffline.hint";
    /**当前司机因判罚被冻结至%1$s，不可被消除，减少后冻结到期时间不可早于该时间*/
    public static final String  capacitygroupmgtJudgmentreduceHint="capacitygroupmgt.judgmentreduce.hint";
    /**通过*/
    public static final String  PASS="key.report.pass";

    //调取接口失败
    public static final String transportRequestInterfaceFail="transport.request.external.interface.fail";

    /**司机头像修改*/
    public static final String keyDrvHeadPortraitChange = "key.drv.head.portrait.change";
    /**
     * 请假 - 请假中，请在${假期结束时间}后正常服务订单 举例：请假中，请在4月26日 15:00 后正常服务订单
     * */
    public static final String drv_leave_status_describe = "transport.drv.leave.status.describe";

    /**
     * 冻结-上线 - 冻结中，${解冻时间} 解冻后可接单 举例：冻结中，3月25日 15:00 解冻后可接单
     * */
    public static final String drv_freeze_online_status_describe = "transport.drv.freeze.online.status.describe";

    /**
     * 冻结-下线 - 冻结中，${解冻时间} 下线 举例：冻结中，3月25日 15:00 下线
     * */
    public static final String drv_freeze_offline_status_describe = "transport.drv.freeze.offline.status.describe";

    /**请假时段超过最大有效期*/
    public static final String  transportDrvLeaveTimeExceedLimit = "transport.drv.leave.time.exceed.limit";

    /**
     * 证件不通过，系统自动驳回
     * */
    public static final String  transportSystemAutoRejectedCertificate= "transport.system.auto.rejected.certificate";

    /**
     * 身份证号变更，网约车人证重新核验，本单项需重新审核
     * */
    public static final String  rhbDriverguidemgtOnlinedrvlicenseReexamine= "rhb.driverguidemgt.onlinedrvlicense.reexamine";

    /**
     * 车牌号变更，网约车车证重新核验，本单项需重新审核
     * */
    public static final String  rhbDriverguidemgtOnlinetaxipermitReexamine= "rhb.driverguidemgt.onlinetaxipermit.reexamine";

    /**
     * 核酸姓名与司机姓名匹配无法确认一致
     * */
    public static final String  capacitymgtAuditmgtNucleicacidnameNotsame= "capacitymgt.auditmgt.nucleicacidname.notsame";

    /**
     * 核酸报告异常
     * */
    public static final String  capacitymgtAuditmgtNucleicacidAbnormal= "capacitymgt.auditmgt.nucleicacid.abnormal";

    /**
     * 疫苗姓名与司机姓名匹配无法确认一致
     * */
    public static final String  capacitymgtAuditmgtVaccinenameNotsame= "capacitymgt.auditmgt.vaccinename.notsame";

    /**
     * 疫苗上传信息有误
     * */
    public static final String  capacitymgtAuditmgtVaccineInfoerror= "capacitymgt.auditmgt.vaccine.infoerror";

    /**
     * 疫苗首次接种时间不一致
     * */
    public static final String  capacitymgtAuditmgtVaccinefirsttimeNotsame= "capacitymgt.auditmgt.vaccinefirsttime.notsame";

    /**
     * 疫苗第二次接种时间不一致
     * */
    public static final String  capacitymgtAuditmgtVaccinesecondtimeNotsame= "capacitymgt.auditmgt.vaccinesecondtime.notsame";

    /**
     * 疫苗第三次接种时间不一致
     * */
    public static final String  capacitymgtAuditmgtVaccinethirdtimeNotsame= "capacitymgt.auditmgt.vaccinethirdtime.notsame";

    /**
     * 疫苗接种时间小于当前时间
     * */
    public static final String  capacitymgtAuditmgtVaccinetimeEarlier= "capacitymgt.auditmgt.vaccinetime.earlier";

    /**
     * 当前操作无权限
     * */
    public static final String  capacitymgtAuditmgtCurrentoperationNopermit= "capacitymgt.auditmgt.currentoperation.nopermit";

    /**
     * 当前司机为境外司机，暂不可操作
     * */
    public static final String  capacitymgtAuditmgtOverseasdriverCannotoperat= "capacitymgt.auditmgt.overseasdriver.cannotoperat";

    /**
     * 当前手机号不匹配
     * */
    public static final String  capacitymgtAuditmgtPhonenumberNotmatch= "capacitymgt.auditmgt.phonenumber.notmatch";

    /**
     * 招募车辆不可为空
     * */
    public static final String  capacitymgtAuditmgtVehicleNotempty= "capacitymgt.auditmgt.vehicle.notempty";

    /**
     * 司机住址修改次数已达上限
     * */
    public static final String  drvAddrModCountUpperLimit= "driver.address.mod.count.upper.limit";
    /**
     * 网约车驾驶证号不能清空
     */
    public static final String  driverNetCertNoNotEmpty = "driver_lisence_blank";
    /**
     * 网约车运输证号不能清空
     */
    public static final String  vehicleNetCertNoNotEmpty = "vehicle_transportation_lisence_blank";
    /**
     * 网约车驾驶证号检验失败
     */
    public static final String driverNetCertNoCheckFalse = "driver_lisence_number_incorrect";
    /**
     * 网约车运输证号校验失败
     */
    public static final String vehicleNetCertNoCheckFalse = "transport_lisence_incorrect";

    /**
     * 当前车辆未下线,操作废弃前请先下线
     * */
    public static final String  listpageCardiscardtipWrongOnline= "listpage.cardiscardtip.wrong.online";

    /**
     * 该数据已废弃
     * */
    public static final String  driverlistpageDiscardtipWrongAlreadydiscard= "driverlistpage.discardtip.wrong.alreadydiscard";

    /**
     * 当前用户没有操作权限
     * */
    public static final String  driverlistpageDiscardtipWrongNoncapacity= "driverlistpage.discardtip.wrong.noncapacity";

    /**
     * 当前司机未下线,操作废弃前请先下线
     * */
    public static final String  listpageDriverdiscardtipWrongOnline= "listpage.driverdiscardtip.wrong.online";

    /**
     * 当前司机/车辆已成功入驻,无法废弃招募数据
     * */
    public static final String  rectutinglistpageCardiscardtipWrong= "rectutinglistpage.cardiscardtip.wrong";

    /**
     * 境外数据,暂不支持此功能
     * */
    public static final String  driverlistpageDiscardtipWronAbroad= "driverlistpage.discardtip.wrong.abroad";

    /**
     * 废弃
     * */
    public static final String  universalDiscard = "universal.discard";

    /**
     * 当前司机未下线，无法操作产线变更
     * */
    public static final String  drvWarning1 = "drv.warning1";

    /**
     * 当前车辆未下线或未解绑，无法操作产线变更
     * */
    public static final String  drvWarning2 = "drv.warning2";

    /**
     * 司机派遣关系只限于境外司机
     * */
    public static final String  drvDispatchRestrictionOverseas = "drv.dispatch.restriction.overseas";

    /**
     * 司机派遣关系已绑定过
     * */
    public static final String  drvDispatchHaveBeenBound = "drv.dispatch.have.been.bound";

    /**
     * 司机派遣关系未绑定过
     * */
    public static final String  drvDispatchUnbind = "drv.dispatch.unbind";

    /**
     * 该司机已与当前供应商属于关联关系
     * */
    public static final String  drvDispatchAlreadyBelongTo = "drv.dispatch.already.belong.to";

    /**
     * 证件配置车型重复
     * */
    public static final String  transportCertificateCartypeExist = "transport.certificate.cartype.exist";

    /**
     * 抱歉，司机密码为空
     * */
    public static final String  driverLoginPasswordEmpty = "driver.login.password.empty";

    /**
     * 抱歉，司机手机验证码验证失败
     * */
    public static final String  driverLoginAuthcodeFail = "driver.login.authcode.fail";

    /**
     * 抱歉，司机信息不唯一
     * */
    public static final String  driverLoginInformationDuplicate = "driver.login.information.duplicate";

    /**
     * 抱歉，司机登录密码验证失败
     * */
    public static final String  driverLoginAuthfail = "driver.login.authfail";

    /**
     * 抱歉，司机不存在
     * */
    public static final String  driverLoginNotexist = "driver.login.notexist";

    /**
     * 抱歉，密码格式不合法
     * */
    public static final String  driverLoginPassworderror = "driver.login.passworderror";

    /**
     * 抱歉，密码和确认密码不一致
     * */
    public static final String  driverLoginPassswordNotconsistent = "driver.login.passsword.notconsistent";

    /**
     * 抱歉，重置密码信息通知失败(短信或邮件)
     * */
    public static final String  driverLoginResetPasswordFail = "driver.login.reset.password.fail";

    /**
     * 【携程用车】忘记密码
     * */
    public static final String  driverLoginEmailForgetPassword = "driver.login.email.forget.password";

    /**
     * %sname，您好：您的新密码为%s，请注意保存，谨防泄露
     * */
    public static final String  driverLoginEmailNewpassword = "driver.login.email.newpassword";

    /**
     * ocr 识别错误图片
     */
    public static final String ocrError = "abroad_ocr_results_failed";

    /**
     * 进单配置最小值
     */
    public static final String enterNumerLimit = "transport.enterNumerLimit.notice";

    /**
     * 请及时补充临时司机和临时车辆相关信息，否则影响结算
     */
    public static final String drvAndVehSupplementInfoSubject = "Supplychain.Suppliermanagement.e-mailReminder.subject_01";

    /**
     * 请及时补充临时司机和临时车辆相关信息，否则影响结算 -- 内容
     */
    public static final String drvAndVehSupplementInfoContent = "Supplychain.Suppliermanagement.e-mailReminder.content_01";

    /**
     * 请及时补充临时司机相关信息，否则影响结算
     */
    public static final String drvSupplementInfoSubject = "Supplychain.Suppliermanagement.e-mailReminder.subject_02";

    /**
     * 请及时补充临时司机相关信息，否则影响结算 -- 内容
     */
    public static final String drvSupplementInfoContent = "Supplychain.Suppliermanagement.e-mailReminder.content_02";

    /**
     * 请及时补充临时车辆相关信息，否则影响结算
     */
    public static final String vehSupplementInfoSubject = "Supplychain.Suppliermanagement.e-mailReminder.subject_03";

    /**
     * 请及时补充临时车辆相关信息，否则影响结算 -- 内容
     */
    public static final String vehSupplementInfoContent = "Supplychain.Suppliermanagement.e-mailReminder.content_03";

    /**
     * 临时司机不能绑定派遣关系
     */
    public static final String dispatchdriverrelationship = "Supplychain.Suppliermanagement.Dispatchdriverrelationship";

    /**
     * 当前服务城市下无运力组可绑定
     */
    public static final String capacitygroupTemporary = "Supplychain.Suppliermanagement.Capacitygroup.Temporary";

    /**
     * 临时司机登录司机端的账号及密码
     */
    public static final String TemporaryMailSubject = "Supplychain.Suppliermanagement.e-mailReminder.subject_04";

    /**
     * 您好，您创建的临时司机（姓名：%s，ID：%s）可以登录并使用司机端了。登录账号：%s，登录密码：%s。请及时告知对应司机，谢谢。
     */
    public static final String TemporaryMailContent = "Supplychain.Suppliermanagement.e-mailReminder.content_04";

    /**
     * 如果是供应商操作编辑运力组并且运力组对应的服务商id是1001000
     */
    public static final String SHARK_SUPPLIER_NOT_UPDATE_TRANSPORT_GROUP = "Supplychain.CapacityGroup.Noteditable";
    /**
     * 如果是供应商操新增运力组并且运力组对应的服务商id是1001000
     */
    public static final String SHARK_SUPPLIER_NOT_ADD_TRANSPORT_GROUP = "Supplychain.CapacityGroup.Noteditable.01";
    /**
     * 如果是供应商操作运力组解绑sku并且运力组对应的服务商id是1001000
     */
    public static final String SHARK_SUPPLIER_NOT_DELETE_TRANSPORT_GROUP_SKU = "Supplychain.CapacityGroup.Noteditable.03";
    /**
     * 派安盈注册邮箱已存在
     */
    public static final String TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST = "transport.drvDriver.paiay.email.exist";
    /**
     * 派安盈账户已存在
     */
    public static final String TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST = "transport.drvDriver.paiay.account.exist";

    public static final String CAN_NOT_BIND_DRIVER_RELATION = "CapacityGroup.Association.Driver.Tips";

    public static final String UPDATE_REPLACE_VEHICLE_GROUP = "key.replace.vehicle.capacity.group";

    /**
     * 创建司机账号全部失败
     */
    public static final String TRANSPORT_DRIVER__CREATE_DRIVER_ACCOUNT_FAILED = "transport.drvDriver.create.driver.account.failed";

    /**
     * 创建司机账号部分失败
     */
    public static final String TRANSPORT_DRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED = "transport.drvDriver.create.driver.account.partial.failed";

    /**
     * 创建全局唯一车牌号失败
     */
    public static final String GENERATE_VEHICLE_GLOBAL_ID_FAILED = "generate.vehicle.global.id.failed";

    /**
     * 不支司机辆废弃
     */
    public static final String UN_SUPPORT_VEHICLE_DISCARD = "un.support.vehicle.discard";

    /**
     * 不支持车辆废弃
     */
    public static final String UN_SUPPORT_DRIVER_DISCARD = "un.support.driver.discard";

    /**
     * 不支持车牌号编辑
     */
    public static final String UN_SUPPORT_VEHICLE_LICENSE_EDIT = "un.support.vehicle.edit";

    public static final String DRIVER_MOBILE_CAN_NOT_EMPTY = "driver.mobile.can.not.be.empty";

    /**
     * 手机号不合法
     */
    public static final String DRIVER_MOBILE_NOT_VALID = "driver.mobile.not.valid";

    /**
     * 手机号没有经过验证，不能上线
     */
    public static final String DRIVER_MOBILE_NOT_VERIFIED = "driver.phoneVerification.result.failed";

    /**
     * 运力组调度电话
     */
    public static final String TRANSPORT_GROUP_DISPATCHER_MOBILE_NOT_VERIFIED = "transportgroup.contact.phoneVerification.result.failed";

    /**
     * 手机号不能以0开头
     */
    public static final String DRIVER_MOBILE_CAN_NOT_START_WITH_0 = "driver.mobile.can.not.start.with.0";

    /**
     * 包车产线已经迁移到司导平台，不支持新增包车产线
     */
    public static final String DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID = "day.product.line.move.to_driver.guide";

    /**
     * 当前供应商可服务城市、可服务产线不匹配，变更失败
     */
    public static final String SUPPLIER_CITY_CATEGORY_NOT_MATCH = "supplier.city.category.not.match";

    /**
     * 司机名称和驾驶证姓名不匹配
     */
    public static final String DRIVER_NAME_NOT_MATCH_LICENSE_NAME = "driver.name.not.match.license.name";

    /**
     * 更新司机账号失败
     */
    public static final String DRIVER_UPDATE_ACCOUNT_FAILED = "driver.update.account.failed";
    /**
     * 请假中
     */
    public static final String ON_LEVE = "driver.on.leve";
    /**
     * 非请假中
     */
    public static final String NOT_ON_LEVE = "driver.not.on.leve";

    /**
     * 当前司机状态不支持冻结
     */
    public static final String DRIVER_STATUS_NOT_SUPPORT_FREEZE = "driver.status.not.support.freeze";

    public static final String VEHICLE_OFFLINE_MESSAGE_PUSH = "vehicle.offline.message.push";

    public static final String VEHICLE_OVERAGE_OFFLINE_CONTENT = "vehicle.overage.offline.content";
    public static final String AGE_LIMIT_WARNING_PUSH = "age.limit.warning.push";
    public static final String VEHICLE_WARNING_INFORMATION_PUSH = "vehicle.warning.information.push";

    /**
     * 供应商鉴权失败
     */
    public static final String SUPPLIER_AUTHENTICATION_FAILD = "supplier.authentication.fail";

  public static final String EMAIL_HAS_ALREADY_BEEN_USED_IN_THE_ACCOUNT_CENTER = "driver.the.email.has.already.been.used.in.the.account.center";

    /**
     * 司机已经被别的短公里运力组绑定,详情见
     */
  public static final String SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_SHORT_TRANSPORT_GROUP = "driver.bind.by.other.short.transport.group";

  /**
     * 司机已经被非短公里运力组绑定,详情见
     */
  public static final String SHORT_TRANSPORT_GROUP_DRIVER_BIND_BY_OTHER_NOT_SHORT_TRANSPORT_GROUP = "driver.bind.by.other.transport.group";

    /**
     * 存在系统判罚，只有超级管理员可以操作
     */
    public static final String ONLY_SUPER_ADMIN_CAN_OPERATE = "only.super.admin.can.operate";
    /**
     * 存在判罚或者运营判罚，您无权限操作
     */
    public static final String EXIST_BD_OR_PENALTY_OPERATE = "exist.bd.or.penglty.operate.you.do.not.have.permission";

  public static final String DRV_INACTIVE_CAUSE_NOT_LOGIN = "driver.inactive.reason.cause.not.login";
  public static final String DRV_INACTIVE_CAUSE_REGISTER_ACCOUNT_ERROR = "driver.inactive.reason.cause.register.account.error";
  public static final String DRV_INACTIVE_CAUSE_IVR_AUTHENTICATION_FAILURE = "driver.inactive.reason.cause.ivr.authentication.failure";
}
