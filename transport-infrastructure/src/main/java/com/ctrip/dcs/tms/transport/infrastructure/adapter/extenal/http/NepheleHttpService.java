package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http;

import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.NephelpConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import lombok.Data;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;

import java.util.Base64;
import java.util.Map;

@Component
public class NepheleHttpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NepheleHttpService.class);

    @Autowired
    NephelpConfig config;

    QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    @SneakyThrows
    public String upload(byte[] bytes, String filename) {
        String uploadUrl = String.format("%s?channel=%s&public=1&internet_host=1&oversea=1&filename=%s", config.getFileUploadEndpoint(), config.getChannel(),
                Base64.getUrlEncoder().encodeToString(filename.getBytes()));
        Map<String, String> headers = FileProxy.getHeaders(FileType.Type.getType(filename.substring(filename.lastIndexOf(".") + 1)), bytes);
        QHttpOption option = new QHttpOption();
        option.setPostBodyData(bytes);
        headers.forEach(option::addHeader);
        ListenableFuture<Response> result = qunarAsyncClient.post(uploadUrl, option);
        Response response = result.get();
        String responseBody = response.getResponseBody("UTF-8");
        LOGGER.info("upload_response, url :{}, status :{} ,header:{} response :{}", uploadUrl, response.getStatusCode(), JsonUtil.toJson(response.getHeaders()), responseBody);
        FileUpLoadResponse downLoadResponse = JsonUtil.fromJson(responseBody, new TypeReference<FileUpLoadResponse>() {
        });
        return downLoadResponse.getUrl();
    }

    @Data
    private static class FileUpLoadResponse {
        String url;
    }
}
