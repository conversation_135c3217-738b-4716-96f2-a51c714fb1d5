package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;


import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.dal.*;
import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import com.ctriposs.baiji.exception.BaijiRemoteException;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.*;

import java.sql.*;
import java.util.*;

@Repository(value = "tmsDrvFreezeRepository")
public class TmsDrvFreezeRepositoryImpl implements TmsDrvFreezeRepository {

    private DalRepository<TmsDrvFreezePO> tmsDrvFreezeRepo;

    private DalRowMapper<TmsDrvFreezePO> tmsDrvFreezePORowMapper;

    @Autowired
    UDLHandler udlHandler;

    private static final String DEFAULT_TABLENAME = "tms_drv_freeze";

    private static final String DATA_BASE = "dcstransportdb_w";

    public TmsDrvFreezeRepositoryImpl() throws SQLException {
        tmsDrvFreezeRepo = new DalRepositoryImpl<>(TmsDrvFreezePO.class);
        this.tmsDrvFreezePORowMapper = new DalDefaultJpaMapper<>(TmsDrvFreezePO.class);
    }

    @Override
    public DalRepository<TmsDrvFreezePO> getTmsDrvFreezeRepo() {
        return tmsDrvFreezeRepo;
    }

    @Override
    public TmsDrvFreezePO queryByPk(Long drvId) {
        return tmsDrvFreezeRepo.queryByPk(drvId);
    }

    @Override
    public Integer addDrvFreeze(TmsDrvFreezePO po){
        po.setProviderDataLocation(udlHandler.getDrvUdl(po.getDrvId()));
        return tmsDrvFreezeRepo.insert(po);
    }

    @Override
    public Integer updateDrvFreeze(TmsDrvFreezePO doo) {
        return tmsDrvFreezeRepo.update(doo);
    }

    @Override
    public List<TmsDrvFreezePO> queryDrvFreezeByDrvIds(Set<Long> drvIds) {
        DalHints hints = DalHints.createIfAbsent(null);
        try{
            FreeSelectSqlBuilder<List<TmsDrvFreezePO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.tmsDrvFreezePORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where()
                    .in("drv_id", new ArrayList(drvIds), Types.BIGINT).ignoreNull();
            return tmsDrvFreezeRepo.getQueryDao().query(sqlBuilder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Integer drvUnFreeze(Long drvId, String unfreezeReason, String modifyUser) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_drv_freeze set first_freeze_time = null," +
                    "freeze_hour = 0,total_freeze_from = '',freeze_reason = '' ,freeze_status = 1," +
                    "unfreeze_reason = ?,freeze_count= 0,penalize_freeze_hour = 0,penalize_accumulated_hour = 0 ,report_freeze_status = 0, send_message_count = 0,modify_user = ?,confirm_online_status = 0 where drv_id = ?");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "unfreeze_reason", Types.VARCHAR, unfreezeReason);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "drv_id", Types.BIGINT, drvId);

            return tmsDrvFreezeRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Integer batchInsertDrvFreeze(List<TmsDrvFreezePO> list) {
        int[] resultList = tmsDrvFreezeRepo.batchInsert(list);
        return resultList.length;
    }

    @Override
    public Integer batchUpdateDrvFreeze(List<TmsDrvFreezePO> list) {
        int[] resultList = tmsDrvFreezeRepo.batchUpdate(list);
        return resultList.length;
    }

    @Override
    public List<TmsDrvFreezePO> queryDrvFreezeAll() {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("freeze_status", TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(), Types.INTEGER);
            return tmsDrvFreezeRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Integer batchUnFreezeByDrvIds(List<Long> drvids,String modifyUser) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_drv_freeze set first_freeze_time = null," +
                    "freeze_hour = 0,total_freeze_from = '',freeze_reason = '' ,freeze_status = 1," +
                    "unfreeze_reason = ?,freeze_count= 0 , send_message_count = 0,penalize_freeze_hour = 0,penalize_accumulated_hour = 0,report_freeze_status = 0, modify_user = ?,confirm_online_status = 0 where drv_id in (?) and freeze_status = 2 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "unfreeze_reason", Types.VARCHAR, "driver online or offline");
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvids);

            return tmsDrvFreezeRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<TmsDrvFreezePO> queryPenaltyFreezeDrvIds(List<Long> drvIds) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeSelectSqlBuilder<List<TmsDrvFreezePO>> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.mapWith(this.tmsDrvFreezePORowMapper)
                    .selectAll().from(DEFAULT_TABLENAME)
                    .where()
                    .in("drv_id", drvIds, Types.BIGINT).and().equal("freeze_status", CommonEnum.FreezeStatusEnum.FREEZE.getValue(),Types.TINYINT).and().isNotNull("penalize_freeze_hour");
            return tmsDrvFreezeRepo.getQueryDao().query(sqlBuilder, hints);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Integer drvUnfreezeConfirmOnline(List<Long> drvids, String modifyUser,Boolean confirmOnlineStatus) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update tms_drv_freeze set  modify_user = ?,confirm_online_status = ? where drv_id in (?) and freeze_status = 2 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++, "confirm_online_status", Types.BIT, confirmOnlineStatus);
            parameters.setInParameter(i++, "drv_id", Types.BIGINT, drvids);
            return tmsDrvFreezeRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }
}
