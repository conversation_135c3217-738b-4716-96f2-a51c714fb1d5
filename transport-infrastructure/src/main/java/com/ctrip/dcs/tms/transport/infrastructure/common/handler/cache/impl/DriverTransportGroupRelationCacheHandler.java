package com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ModRecordConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.AbstraceCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CacheBusinessQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.frt.xresource.framework.utility.json.JsonUtil;
import com.ctrip.frt.xresource.framework.utility.redis.ResourceFillDataAction;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 司机运力组关系缓存处理器
 */
@Service
public class DriverTransportGroupRelationCacheHandler extends AbstraceCacheHandler {

  @Autowired
  private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;

  @Autowired
  CacheBusinessQconfig cacheBusinessQconfig;

  @Override
  protected String getCacheName() {
    return "drv_tsp";
  }

  public List<TspTransportGroupDriverRelationPO> queryDriverGroupRelation(List<Long> drvIds) {
    Cat.logEvent(Constant.EventType.CACHE, Constant.EventName.CACHE_EVENT_DRIVER_TRANSPORT_QUERY);
    List<List<TspTransportGroupDriverRelationPO>> resultList =
      mgetHit(drvIds.stream().map(String::valueOf).collect(Collectors.toList()),
        JsonUtil.buildJavaType(new TypeReference<List<TspTransportGroupDriverRelationPO>>() {
        }.getType()));
    return Optional.ofNullable(resultList).orElse(Collections.emptyList()).stream().flatMap(List::stream).collect(
      Collectors.toList());
  }

  @Override
  public void refreshCache(DataChange dataChange) {
    doRefresh(getBusinessId(dataChange));
  }

  @Override
  protected ResourceFillDataAction getFillDataAction() {
    return new ResourceFillDataAction<List<TspTransportGroupDriverRelationPO>>() {
      @SneakyThrows
      @Override
      public Map<String, List<TspTransportGroupDriverRelationPO>> refreshAction(List<String> drvIdList) {
        Cat.logEvent(Constant.EventType.CACHE, Constant.EventName.CACHE_EVENT_DRIVER_TRANSPORT_REFRESH);
        return tspTransportGroupDriverRelationRepository.queryDriverGroupRelation(drvIdList.stream().map(Long::valueOf).collect(
          Collectors.toList())).stream().collect(Collectors.groupingBy(data -> String.valueOf(data.getDrvId())));
      }

      @Override
      public String getVersion() {
        return null;
      }

      @Override
      public boolean enableDiff() {
        return cacheBusinessQconfig.getConfig().isDrvTransportGroupRelationCacheDiff();
      }

      @Override
      protected void diff(Map<String, List<TspTransportGroupDriverRelationPO>> oldDataMap, Map<String, List<TspTransportGroupDriverRelationPO>> newDataMap) {
        if (Objects.isNull(oldDataMap) || Objects.isNull(newDataMap)) {
          return;
        }
        for (Map.Entry<String, List<TspTransportGroupDriverRelationPO>> entry : oldDataMap.entrySet()) {
          String key = entry.getKey();
          List<TspTransportGroupDriverRelationPO> newData = newDataMap.get(key);
          if (CollectionUtils.isEmpty(newData)) {
            Cat.logEvent(Constant.EventType.CACHE_DIFF, getCacheName());
            return;
          }
          if (!Objects.equals(entry.getValue(), newData)) {
            Cat.logEvent(Constant.EventType.CACHE_DIFF, getCacheName());
            return;
          }
        }
      }

    };
  }



  @Override
  public boolean support(String tableName) {
    return ModRecordConstant.TableName.tspTransportGroupDriverRelation.equalsIgnoreCase(tableName);
  }


}
