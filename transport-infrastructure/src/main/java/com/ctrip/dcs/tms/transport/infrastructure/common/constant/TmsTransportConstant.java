package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.google.common.collect.*;

import java.util.*;

/**
 * 运力管理常量值
 *
 * <AUTHOR>
 * @Date 2020/2/24 17:59
 */
public class TmsTransportConstant {

    //司机姓名：司机id(driver)表明变更人为具体的司机
    public static final String TMS_DEFAULT_USERNAME_DRIVER_FORMAT = "%s:%s(driver)";

    public static final String TMS_DEFAULT_USERNAME = "system";

    public static final String QMQ_CONSUMER_GROUP = "100025330";

    public static final String SUCCESS_CODE = "200";

    public static final String CTRIP_MESSAGE_CHANNEL = "Ctrip";

    public static final String CTRIP_EMAIL_CHANNEL = "ctrip";

    public static final String CONTENTTYPE = "text/html;charset=UTF-8";

    public static final String DEFAULTFROM = "<EMAIL>";

    public static final String TMS_TRANSPORT_DBNAME = "dcstransportdb_w";

    public static final String SALT_KEY="salt";
    public static final String PWD_KEY="pwd";

    //绑定关系枚举
    public static final String DRIVERBIND = "DriverBind";

    //报名状态
    public static final String APPLYSTATUS = "ApplyStatus";

    //司机状态枚举
    public static final String DRIVERSTATUS = "DriverStatus";

    //司机来源
    public static final String DRIVERSOURCE = "DriverSource";

    //车辆燃油类型
    public static final String VEHICLEFUELTYPE = "VehicleFuelType";

    //车辆颜色
    public static final String VEHICLECOLOR = "VehicleColor";

    //车辆状态枚举
    public static final String VEHICLESTATUS = "VehicleStatus";

    public static final String SPLIT = ",";

    public static final String JS_SPLIT = "<br/>";

    public static final String EMAILSPLIT = ";";

    //可接订单距离用车时间
    public static final String MINBOOKINGPERIOD = "MinBookingPeriod";

    //运力组模式
    public static final String TRANSPORTGROUPTYPE = "TransportGroupType";

    //审批状态
    public static final String APPROVERSTATUS = "ApproverStatus";

    //车辆使用性质
    public static final String VEHICLEOPERATIONTYPE = "VehicleOperationType";

    //车辆使用性质
    public static final String VEHICLETYPE = "vehicleType";

    //合作模式
    public static final String COOPMODE = "CoopMode";

    //三方核验状态
    public static final String CHECKSTATUS = "CheckStatus";

    //三方标签状态
    public static final String CERTIFICATE_TAG_STATUS = "CertificateTagStatus";


    //审批事件类型
    public static final String APPROVEEVENTTYPE = "ApproveEventType";

    //审批流中审批状态
    public static final String TRANSPORTAPPROVESTATUS = "TransportApproveStatus";

    //审批节点
    public static final String APPROVENODE = "ApproveNode";

    //司机解冻前24小时发送短信标识key
    public static final String DRV_UNFREEZE_24SENDMESSAGEKEY =  "drv_unfreeze_24_sendmessage_";

    //司机解冻前24小时发送短信标识key
    public static final String DRV_UNFREEZE_24SENDEMAILKEY =  "drv_unfreeze_24_sendemail_";

    //运力组状态
    public static final String TRANSPORTGROUPSTATUS = "TransportGroupStatus";

    //智行力接口入参
    public static final List<String> deptFields = Arrays.asList("id","name","expire_from","expire_to","parent_id");

    public static final List<String> postTypeFields = Arrays.asList("id","name","description");

    public static final List<String> positionFields = Arrays.asList("id","name","expire_from","expire_to","remark");

    public static final List<String> userFields = Arrays.asList("id","user_name","post_id","dept_id","position_id","working_status","entry_date","qualified_date","leave_date","mobile_phone","working_type","precincts","customer_openid","tags");

    //携程大学-第三方用户ID标识
    public static final String CTRIP_UNIVERSITY_USERID = "card";

    //携程大学-第三方部门ID标识
    public static final String CTRIP_UNIVERSITY_DEPTID = "carc";

    //携程大学-第三方部门父ID
    public static final String CTRIP_UNIVERSITY_PARENT_ID = "TripRideHailing";

    //携程大学-司机境内标签
    public static final String CTRIP_UNIVERSITY_DOMESTIC_TAGS = "102";

    //携程大学-司机境外标签
    public static final String CTRIP_UNIVERSITY_OVERSEAS_TAGS = "103";

    //号牌种类-燃油车
    public static final String VEHICLELICENSETYPE_FUEL = "02";

    //号牌种类-新能源
    public static final String VEHICLELICENSETYPE_ENERGY = "52";

    //人脸识别存储key
    public static final String DRV_SOURCE_ID_KEY = "drv_source_id";

    //车辆识别存储key
    public static final String VEHICLE_SOURCE_ID_KEY = "vehicle_source_id";

    public static final String DRV_UNFREEZE_CONFIRM_KEY =  "drv_unfreeze_confirm_key";

    public static final String OVERSEAS_DRV_OCR_COMPARISON_RES = "overseas_drv_ocr_comparison_res_";

    public static final String OVERSEAS_VEH_OCR_COMPARISON_RES = "overseas_veh_ocr_comparison_res_";

    public static String DRIVER_TEMP_REMARK_EXP_SECONDS = "driverTempRemarkExpSeconds";

    public static String DRIVER_TEMP_REMARK_EXP_RANDOM_SECONDS = "driverTempRemarkExpRandomSeconds";

    /**
     * 账户类型key
     *
     */
    public static final String ACCOUNT_TYPE_KEY = "accountTypeCode";

    /***
    　* @description: TODO 枚举Code
    　* <AUTHOR>
    　* @date 2021/8/17 11:22
    */
    public class DicCode{
        // 审核时效 Code
        public static final String APPROVEAGING = "ApproveAging";
        // 审核进度 Code
        public static final String APPROVESCHEDULE = "ApproveSchedule";
        // 单项审批元素枚举
        public static final String SINGLEAPPROVEITEM = "SingleApproveItem";
        // 司机派遣关系枚举
        public static final String DRVRELATIONTYPE = "DrvRelationType";
    }

    /**
     　* @description: 业务code
     　* <AUTHOR>
     　* @date 2023/2/13 14:40
     */
    public class ResultCode{
        //该司机与您有过派遣关系绑定，如需重新绑定，请联系平台操作，谢谢
        public static final String  code100020 = "100020";
        //司机+供应商已绑定过
        public static final String  code100021 = "100021";
        //司机+供应商未绑定过
        public static final String  code100022 = "100022";
        //该司机已注册，只能绑定派遣关系
        public static final String  code100023 = "100023";
        //司机不在灰度范围内
        public static final String  code100024 = "100024";
        //司机不能派遣关联关系供应商
        public static final String  code100025 = "100025";
        //该司机已绑定派遣关系
        public static final String  code100026 = "100026";
        //该司机绑定过派遣关系，请联系平台处理
        public static final String  code100027 = "100027";
        //新增临派司机-正式司机重复
        public static final String  code100028 = "100028";
        //新增临派司机-临派司机重复
        public static final String  code100029 = "100029";
        //新增临派司机-正式车辆重复
        public static final String  code100030 = "100030";
        //新增临派司机-临派车辆重复
        public static final String  code100031 = "100031";
    }

    /**
     * qmq subject
     */
    public class QmqSubject {

        //dcstransportdb库数据变更
        public static final String SUBJECT_DCSTRANSPORTDB_CHANGED = "dcs.canal.dcstransportdb.data.changed";

        /**
         * 供应链数据信息变更
         * 所有表信息变更
         * */
        public static final String SUBJECT_TRANSPORT_DATA_INFO_CHANGED = "dcs.tms.transport.data.info.changed";

        //司机信息变更
        public static final String SUBJECT_DRIVERINFO_CHANGED = "dcs.tms.transport.driver.info.changed";

        //司机状态变更
        public static final String SUBJECT_DRIVERSTATE_CHANGED = "dcs.tms.transport.driver.state.changed";

        //司机请假管理
        public static final String SUBJECT_DRIVERLEAVE_CONFIRMED = "dcs.tms.transport.driver.leave.confirmed";

        //车辆信息变更
        public static final String SUBJECT_DRIVERVEHICLE_CHANGED = "dcs.tms.transport.driver.vehicle.changed";

        //司机订单变更
        public static final String SUBJECT_DRIVERORDER_CHANGED = "dcs.tms.transport.driver.order.changed";

        //运力组信息变更
        public static final String SUBJECT_GROUPINFO_CHANGED = "dcs.tms.transport.group.info.changed";

        //运力组绑定/解绑司机
        public static final String SUBJECT_GROUPDRIVER_CHANGED = "dcs.tms.transport.group.driver.changed";

        //运力组绑定/解绑商品
        public static final String SUBJECT_GROUPSKU_CHANGED = "dcs.tms.transport.group.sku.changed";

        //运力组状态变更
        public static final String SUBJECT_GROUPSTATE_CHANGED = "dcs.tms.transport.group.state.changed";

        //sku上下线服务区域
        public static final String SUBJECT_SERVEDSCOPE_CHANGE = "dcs.scm.merchant.servedscope.change";
        public static final String SUBJECT_VPHONE_IDENTIFICATION_RESULT_NOTIFY = "dcs.vphone.identification.result.notify";

        public static final String SUBJECT_IDCARDBACKGROUND = "dcs.tms.transport.idcard.back.ground.check";

        //司机运力组变更
        public static final String SUBJECT_DRIVERTRANSPORTGROUP_CHANGED = "dcs.tms.transport.driver.transportgroup.changed";

        //有效司机率发送短信
        public static final String SUBJECT_DRIVERVALIDRATETHRESHOLD = "dcs.tms.transport.driver.validrate.threshold";

        //司机开始服务
        public static final String SUBJECT_DRIVER_START_SERVICE = "car.order.driver_service_location";

        //身份证核验结果qmq
        public static final String SUBJECT_IDCARDAPPROVE_RESULT = "dcs.tms.transport.idcard.approve.result";

        //司机/车辆审批qmq
        public static final String SUBJECT_APPROVECHECK_CHANGE = "dcs.scm.drv.vehicle.approve.check.changed";

        /**
         * 司机DSP状态变更
         * drvId ：司机id
         * changeType：1 : 司机信息变更
         * changeType：2：司机请假信息变更
         * changeType：3：司机冻结信息变更
         * */
        public static final String SUBJECT_DSP_DRIVER_STATUS_CHANGED = "dcs.tms.transport.driver.dsp.status.changed";

        public static final String SUBJECT_DRIVER_VIRTUALNUMBER_AUTH_CHANGED = "dcs.tms.transport.driver.virtualnumber.auth.changed";

        //招募司机车辆审批时效qmq
        public static final String SUBJECT_RECRUITING_APPROVE_AGING = "dcs.tms.transport.recruiting.approve.aging.qmq";

        //司机、车辆招募证件核验qmq
        public static final String SUBJECT_RECRUITING_CERTIFICATE_CHECK = "dcs.tms.transport.recruiting.certificate.check.qmq";

        //司机、车辆招募核验状态qmq
        public static final String SUBJECT_RECRUITING_CHECK_STATUS = "dcs.tms.transport.recruiting.check.status.qmq";

        //系统自动驳回招募信息
        public static final String SUBJECT_SYSTEM_AUTO_REJECTED_RECRUITING = "dcs.tms.transport.system.auto.rejected.recruiting";

        //招募审批进度
        public static final String SUBJECT_RECRUITING_APPROVE_SCHEDULE = "dcs.tms.transport.recruiting.approve.schedule";

        public static final String SUBJECT_BACKGROUND_INFORM = "dcs.gms.transport.guide.identity.result.inform";

        //注册成为正式司机
        public static final String SUBJECT_DRIVER_REGISTER= "dcs.tms.transport.driver.register.qmq";


        //临派司机转正式(司机/车辆)qmq
        public static final String SUBJECT_TEMPORARY_TO_OFFICIAL= "dcs.tms.transport.temporary.to.official.qmq";

        //编辑后审核通过后，判断是否成为正式司机或车辆
        public static final String SUBJECT_APPROVE_TO_OFFICIAL= "dcs.tms.transport.approve.to.official.qmq";

        //临派司机或车辆补充信息通知qmq
        public static final String SUBJECT_TEMPORARY_REPLENISH_INFO = "dcs.tms.transport.temporary.replenish.info.qmq";
        /**
         * Diff subject
         */
        public static final String DATA_COMPARE_TASK_TOPIC = "dcs.data.compare.async.task.topic";


        //境外司机和车辆OCRqkq
        public static final String SUBJECT_OVERSEAS_DRVVEH_OCR = "dcs.overseas.drv.veh.ocr.qmq";

        //司机手机和邮箱修改
        public static final String SUBJECT_DCS_TMS_TRANSPORT_PHONE_EMAIL_MODIFY = "dcs.tms.transport.phone.email.modify.qmq";

        //接收司机公共信息变更消息
        public static final String DCS_DRIVER_ACCOUNT_BASE_INFO_UPDATE_CONSUMER = "dcs.driver.account.base.info.update";

        //推送司机消息
        public static final String DCS_DRIVER_PUSH_MESSAGE = "dcs.driver.push.message";
        //司机登录
        public static final String DCS_DRIVER_LOGIN_MESSAGE = "dcs.driver.login.info";

        public static final String DCS_TMS_DRV_OUT_CHECK ="dcs.tms.drv.out.check";

        public static final String DCS_VPHONE_IDENTIFICATION_ASYNC_NOTIFY ="dcs.vphone.identification.async.notify";

        //IVR电话验证
        public static final String SUBJECT_IVR_CALL_VERIFICATION = "dcs.tms.transport.ivr.call.verification";

        //IVR电话呼起
        public static final String SUBJECT_IVR_CALL = "dcs.tms.transport.ivr.call";
    }

    /**
     * qmq tag
     */
    public class QmqTag {

        //司机新增
        public static final String TAG_DRIVERINFO_ADD = "tag_driverinfo_add";

        //司机信息修改
        public static final String TAG_DRIVERINFO_MODIFY = "tag_driverinfo_modify";

        //司机上线
        public static final String TAG_DRIVERSTATE_ONLINE = "tag_driverstate_online";

        //司机下线
        public static final String TAG_DRIVERSTATE_OFFLINE = "tag_driverstate_offline";

        //司机订单批量改派
        public static final String TAG_DRIVERORDER_BATCHCHANGE = "tag_driverorder_batchchange";

        //司机冻结
        public static final String TAG_DRIVERSTATE_FREEZE = "tag_driverstate_freeze";

        //司机解冻
        public static final String TAG_DRIVERSTATE_UNFREEZE = "tag_driverstate_unfreeze";

        //司机请假
        public static final String TAG_DRIVERLEAVE_ADD = "tag_driverleave_add";

        //司机销假
        public static final String TAG_DRIVERLEAVE_CLOSE = "tag_driverleave_close";

        //司机换车
        public static final String TAG_DRIVERVEHICLE_CHANGE = "tag_drivervehicle_change";

        //车辆信息修改
        public static final String TAG_DRIVERVEHICLE_MODIFY = "tag_drivervehicle_modify";

        //车辆信息修改
        public static final String TAG_DRIVERVEHICLETYPE_MODIFY = "tag_drivervehicletype_modify";

        //运力组信息修改
        public static final String TAG_GROUPINFO_MODIFY = "tag_groupinfo_modify";

        //运力组绑定司机
        public static final String TAG_GROUPDRIVER_BIND = "tag_groupdriver_bind";
        //运力组解绑司机
        public static final String TAG_GROUPDRIVER_UNBIND = "tag_groupdriver_unbind";
        //运力组绑定商品
        public static final String TAG_GROUPSKU_BIND = "tag_groupsku_bind";
        //运力组解绑商品
        public static final String TAG_GROUPSKU_UNBIND = "tag_groupsku_unbind";
        //运力组下线
        public static final String TAG_GROUPSTATE_OFFLINE = "tag_groupstate_offline";
        //运力组上线
        public static final String TAG_GROUPSTATE_ONLINE = "tag_groupstate_online";
        //司机供应商变更
        public static final String TAG_DRIVERSUPPLIER_CHANGE = "tag_driver_supplier_change";
        //司机城市变更
        public static final String TAG_DRIVERCITY_CHANGE = "tag_driver_city_change";
        //司机账号变更
        public static final String TAG_DRIVER_ACCOUNT_CHANGE = "tag_driver_account_change";

        public static final String TAG_IDCARDBACKGROUND = "tag_idcard_back_ground_check";

        public static final String TAG_DRV_TRANSPORTGROUP_CHANGE = "tag_driver_transportgroup_change";

        public static final String TAG_DRIVER_START_SERVICE = "tag_driver_start_service";

        //身份证核验
        public static final String TAG_IDCARD_APPROVE = "tag_idcard_approve";
        //司机/车辆审批
        public static final String TAG_APPROVECHECK = "tag_drv_vehicle_approve_change";

        //司机 dsp 状态变更
        public static final String TAG_DRV_DSP_STATUS = "tag_drv_dsp_status_change";

        //携程大学数据推送
        public static final String 	TAG_PUSH_CTRIP_UNIVERSITY = "tag_push_ctrip_university";

        //招募司机车辆审批时效qmq
        public static final String TAG_RECRUITING_APPROVE_AGING = "tag_recruiting_aging";

        //司机、车辆招募证件核验qmq
        public static final String TAG_RECRUITING_CERTIFICATE_CHECK = "tag_recruiting_certificate_check";

        //司机、车辆招募核验状态qmq
        public static final String TAG_RECRUITING_CHECK_STATUS = "tag_recruiting_check_status";

        //系统自动驳回招募
        public static final String TAG_RECRUITING_REJECTED = "tag_recruiting_rejected";

        //系统自动驳回招募
        public static final String TAG_RECRUITING_APPROVE_SCHEDULE = "tag_recruiting_approve_schedule";

        //注册新司机
        public static final String TAG_REGISTER_DRIVER = "tag_register_driver";

        //临派转正式
        public static final String TAG_TEMPORARY_TO_OFFICIAL = "tag_temporary_to_official";

        //IVR电话验证
        public static final String TAG_IVR_CALL_VERIFICATION = "tag_ivr_call_verification";

        //临派转正式
        public static final String TAG_APPROVE_TO_OFFICIAL = "tag_approve_to_official";

        //临派补充信息通过
        public static final String TAG_TEMPORARY_REPLENISH_INFO = "tag_temporary_replenish_info";

        //境外OCR
        public static final String TAG_OVERSEAS_OCR = "tag_overseas_ocr";

        //司机表数据信息变更
        public static final String TAG_DRV_INFO_CHANGE = "tag_drv_info_change";
    }

    /**
     * 司机报名状态
     */
    public enum ApplyStatusEnum{

        NOT(0, "NOT"),
        APPLIED(1, "APPLIED"),
        SUCCESS(2, "SUCCESS"),
        ELIMINATE(3, "eliminate"),
        APPLY_FAILED(4, "APPLY_FAILED"); //报名失败（已报名，但是没有选上）

        private Integer code;
        private String text;

        ApplyStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return text;
        }

        public static String getText(String code) {
            for (ApplyStatusEnum s : ApplyStatusEnum.values()) {
                if (s.getCode().equals(code)) {
                    return s.text;
                }
            }
            return "";
        }
    }

    public enum LeaveAndFreezeTypeEmue{
        LEAVE("LEAVE", "LEAVE"), // 请假
        FREEZE("FREEZE", "FREEZE"), // 冻结
        LEAVE_AND_FREEZE("LEAVE_AND_FREEZE", "LEAVE_AND_FREEZE"), // 请假+冻结
        ;

        private String code;
        private String text;

        LeaveAndFreezeTypeEmue(String code, String text) {
            this.code = code;
            this.text = text;
        }
    }

    public enum ApplyFailedTypeEnum {
        LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT(1, UNICODE.TRIGGER_DRIVER_LEAVE_AND_FREEZE_LIMIT), // 触发请假冻结时长限制规则
        DRIVER_POINT_IS_RANKED_LOW(2, UNICODE.TRIGGER_DRIVER_SCORE_RESTRICTION_RULE), // 触发司机分限制规则
        ;

        private int code;
        private String text;

        ApplyFailedTypeEnum(int code, String text) {
            this.code = code;
            this.text = text;
        }

        public int getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    public enum TransportDriverApplyRuleEnum{

        A("A", "\u8bf7\u5047+\u51bb\u7ed3\u65f6\u957f%s\u5c0f\u65f6,\u8d85\u8fc7\u89c4\u5b9a%s\u5c0f\u65f6"), // 运力组全局赛道更新前A天请假冻结时长阈值
        B("B", "\u8bf7\u5047+\u51bb\u7ed3\u65f6\u957f%s\u5c0f\u65f6,\u8d85\u8fc7\u89c4\u5b9a%s\u5c0f\u65f6"), // 运力组全局赛道更新后B天请假冻结时长阈值
        C("C", "\u8bf7\u5047+\u51bb\u7ed3\u65f6\u957f%s\u5c0f\u65f6,\u8d85\u8fc7\u89c4\u5b9a%s\u5c0f\u65f6"), // Job 删除规则从报名成功到赛道更新时间内请假冻结时长阈值
        D("D", "\u8bf7\u5047+\u51bb\u7ed3\u65f6\u957f%s\u5c0f\u65f6,\u8d85\u8fc7\u89c4\u5b9a%s\u5c0f\u65f6"), // 运力组赛道运力缺失补位从当前时间到赛道更新+30天内请假冻结时长阈值
        DRIVER_POINT("DRIVER_POINT", "\u53f8\u673a\u5206%s,\u4f4e\u4e8e\u6392\u540d"), // 司机分低于排名
        ;

        private String code;
        private String text;

         TransportDriverApplyRuleEnum(String code, String text) {
            this.code = code;
            this.text = text;
        }

        public String getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * canal监听数据源变化事件类型
     */
    public enum CanalEventTypeEnum{

        INSERT("INSERT", "INSERT"),
        UPDATE("UPDATE", "UPDATE");

        private String code;
        private String text;

        CanalEventTypeEnum(String code, String text) {
            this.code = code;
            this.text = text;
        }

        public String getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(String code) {
            for (CanalEventTypeEnum s : CanalEventTypeEnum.values()) {
                if (s.getCode().equals(code)) {
                    return s.text;
                }
            }
            return "";
        }
    }

    /**
     * sku对接方式
     */
    public enum SkuConnectModeEnum {
        VBK(1, "VBK"),
        API(2, "API");

        private Integer code;
        private String text;

        SkuConnectModeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 服务区域类型
     */
    public enum ServiceAreaTypeEnum {
        CITY(1, "In the city"),
        CUSTOM(2, "DIY");

        private Integer code;
        private String text;

        ServiceAreaTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 进单配置状态
     */
    public enum IntoOrderConfigActiveEnum {
        INVALID(0, "Invalid"),
        VALID(1, "Effective");

        private Integer code;
        private String text;

        IntoOrderConfigActiveEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 进单配置状态
     */
    public enum WorkShiftActiveEnum {
        INVALID(0, "Invalid"),
        VALID(1, "Effective");

        private Integer code;
        private String text;

        WorkShiftActiveEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 请假记录状态
     */
    public enum DrvLeaveStatusEnum {
        INVALID(0, "非请假中", SharkKeyConstant.NOT_ON_LEVE),
        VALID(1, "请假中", SharkKeyConstant.ON_LEVE);

        private Integer code;
        private String text;
        private String sharkKey;

        DrvLeaveStatusEnum(Integer code, String text, String sharkKey) {
            this.code = code;
            this.text = text;
            this.sharkKey = sharkKey;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public String getSharkKey() {
            return this.sharkKey;
        }

        public static String getText(Integer code) {
            for (DrvLeaveStatusEnum s : DrvLeaveStatusEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return SharkUtils.getSharkValue(s.getSharkKey(), s.getText());
                }
            }
            return "";
        }
    }

    /**
     * 请假类型
     */
    public enum DrvLeaveTypeEnum {

        OPERATION(1, "Operating-leave"),
        DRIVER(2, "Driver-leave"),
        SUPPLIER(3, "Supplier-leave");

        private Integer code;
        private String text;

        DrvLeaveTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 站点类型
     */
    public enum LocationTypeEnum {
        AIRPORT(1, "Airport"),
        STATION(2, "Railway station");

        private Integer code;
        private String text;

        LocationTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 司机状态
     */
    public enum DrvStatusEnum {
        UNACT(0, "Inactive"),
        ONLINE(1, "Online"),
        FREEZE(2, "Freeze"),
        OFFLINE(3, "Offline");

        private Integer code;
        private String text;

        DrvStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (DrvStatusEnum s : DrvStatusEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }

        public static DrvStatusEnum getEnum(Integer code) {
            for (DrvStatusEnum statusEnum : DrvStatusEnum.values()) {
                if (statusEnum.getCode().intValue() == code.intValue()) {
                    return statusEnum;
                }
            }
            return null;
        }
    }


    /**
     * 车辆状态
     */
    public enum VehStatusEnum {
        UNACT(0, "Inactive"),
        ONLINE(1, "Online"),
        OFFLINE(3, "Offline");

        private Integer code;
        private String text;

        VehStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (DrvStatusEnum s : DrvStatusEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }
    }

    /**
     * 运力组状态
     */
    public enum TransportGroupStatusEnum {
        ONLINE(0, "Online"),
        OFFLINE(1, "Offline");

        private Integer code;
        private String text;

        TransportGroupStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (TransportGroupStatusEnum s : TransportGroupStatusEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }
    }

    /**
     * 运力组模式
     * 1001-全职司机指派
     * 1002-兼职司机播报
     * 1003-人工调度
     * 1004-优先人工调度
     * 1005-兜底人工调度
     * 1006-全职报名
     */
    public enum TransportGroupModeEnum {
        QZSJ(1001, "Full-time driver-assigned"),
        JZSJ(1002, "Part-time driver-assigned"),
        RGDD(1003, "Artificial scheduling"),
        YXRGDD(1004, "Give priority to Artificial scheduling"),
        DDRGDD(1005, "Out Artificial scheduling"),
        QZSJA(1006, "Full-time driver-assigned apply");

        private Integer code;
        private String text;

        TransportGroupModeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (TransportGroupModeEnum s : TransportGroupModeEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }

        public static TransportGroupModeEnum getModeEnum(Integer mode) {
            for (TransportGroupModeEnum s : TransportGroupModeEnum.values()) {
                if (s.getCode() == mode.intValue()) {
                    return s;
                }
            }
            return null;
        }

    }

    /**
     * 司机需要判断的唯一属性
     */
    public enum DrvOnlyTypeEnum {
        PHONE(1, "Phone Number"),
        IDCARD(2, "Id Card"),
        LOGIN_ACCOUNT(3, "Login Account"),
        EMIAL(4,"Emial"),
        PAIAY_ACCOUNT(5,"paiay account"),
        PAIAY_EMAIL(6,"paiay email");

        private Integer code;
        private String text;

        DrvOnlyTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }


    /**
     * 车辆需要判断的唯一属性
     */
    public enum VehOnlyTypeEnum {
        vehicle_license(1, "vehicle_license"),
        vehicle_vin(2, "vehicle_vin");

        private Integer code;
        private String text;

        VehOnlyTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 司机来源
     */
    public enum DrvFromEnum {
        DRV_AUTO(1, "Driver DIY"),
        DRV_MANUAL(2, "Manually");

        private Integer code;
        private String text;

        DrvFromEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 车辆绑定状态
     */
    public enum VehicleHasDrvEnum {
        IS_HASDRV(1, "Is binding"),
        NO_HASDRV(0, "Unbounded");

        private Integer code;
        private String text;

        VehicleHasDrvEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 状态返回值
     */
    public enum ResultStatusTypeEnum {
        SEND_SUCCESS_CODE("0"),
        SUCCESS_CODE("200");
        private String code;

        ResultStatusTypeEnum(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 招募审批状态
     * 0:招募司机初始提交状态 1:供应商驳回 4:供应商提交/供应商通过/待运营审批 6:运营驳回 10:通过并落库
     */
    public enum RecruitingApproverStatusEnum {
        wait_Approve(0, "Pending"),
        supplier_turnDown(1, "Supplier-rejected"),
        supplier_Approve_finish(4, "Supplier-pass"),
        operating_turnDown(6, "Operating-rejected"),
        finish(10, "Pass"),;

        private Integer code;
        private String text;

        RecruitingApproverStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (RecruitingApproverStatusEnum s : RecruitingApproverStatusEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }
    }

    /**
     * 供应商资质
     * none:无匹配,domestic:境内,overseas:境外,all:全匹配
     */
    public enum SupplierQualificationEnum {
        none("none", "No match"),
        domestic("domestic", "China"),
        overseas("overseas", "Overseas"),
        all("overseas", "ALL");

        private String code;
        private String text;

        SupplierQualificationEnum(String code, String text) {
            this.code = code;
            this.text = text;
        }

        public String getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 审批动作
     * 1:供应商通过 2:供应商驳回 3:运营驳回 4:运营通过并激活 5:运营通过未激活
     */
    public enum RecruitingApproverActionEnum {
        supplier_pass(1, "Supplier-pass"),
        supplier_refuse(2, "Supplier-rejected"),
        operating_refuse(3, "Operating-rejected"),
        operating_finish_online(4, "Operating-activation"),
        operating_finish_offline(5, "Operating-not activation");

        private Integer code;
        private String text;

        RecruitingApproverActionEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (RecruitingApproverActionEnum s : RecruitingApproverActionEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }
    }

    /**
     * 司机合作模式
     * 0-无合作模式
     * 1-全职
     * 2-兼职
     * 4-全职指派
     * 5-兼职指派
     * 6-兼职播报
     * 7-人工调度
     */
    public enum DrvCoopModeEnum {
        NO(0, "NO"),
        FULL_TIME(1, "Full-time"),
        PART_TIME(2, "part-time"),
        DRV_QJJP(4, "DRV_QJJP"),
        DRV_JJJP(5, "DRV_JJJP"),
        DRV_JJBB(6, "DRV_JJBB"),
        DRV_RGDD(7, "DRV_RGDD");


        private Integer code;
        private String text;

        DrvCoopModeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    // 冻结类型0.正常,1.增加,2.减少
    public enum FrzTypeEnum {
        NORMAL(0, "normal"), PLUS(1, "increase"), MINUS(2, "to-reduce");

        private Integer value;
        private String text;

        private FrzTypeEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }
    }

    // 冻结状态1.解冻,2.冻结
    public enum FreezeStatusEnum {
        FREEZE(2, "freeze"), UNFRREZE(1, "unfreeze");

        private Integer value;
        private String text;

        private FreezeStatusEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }
    }

    // 操作角色1.供应商,2.BD
    public enum AccountTypeEnum {
        B_SYSTEM(1, "B_SYSTEM"), OFFLINE(2, "OFFLINE");

        private Integer value;
        private String text;

        private AccountTypeEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }
        

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }
    }

    // 到期解冻后置操作（1.解冻后自动上线,2.解冻后自动下线）
    public enum UnfreezeActionEnum {
        UNFREEZEONLINE(1, "online"), UNFREEZEOFFLINE(2, "offLine");

        private Integer value;
        private String text;

        private UnfreezeActionEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }
    }

    //是否改派 1.不改派,2.自动改派
    public enum FreezeOrderSetEnum {
        FREEZEORDERNOCHG(1, "FreezeOrderNoChg"), FREEZEORDERCHG(2, "FreezeOrderchg");

        private Integer value;
        private String text;

        private FreezeOrderSetEnum(Integer value, String text) {
            this.value = value;
            this.text = text;
        }

        public Integer getValue() {
            return this.value;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 状态禁用按钮(0.正常,1.禁用)
     */
    public enum StatusDisableEnum {
        Available(0, "available"),
        Disable(1, "Disable");

        private Integer code;
        private String text;

        StatusDisableEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 车辆来源1.H5招募,2.工作台录入
     */
    public enum VehicleFromEnum {
        Veh_AUTO(1, "Veh DIY"),
        Veh_MANUAL(2, "Manually");

        private Integer code;
        private String text;

        VehicleFromEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (VehicleFromEnum s : VehicleFromEnum.values()) {
                if (s.getCode().intValue() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }
    }

    /**
     * 招募列表类型.1.司机.2车辆
     */
    public enum RecruitingTypeEnum {
        drv(1, "drv"),
        vehicle(2, "vehicle");

        private Integer code;
        private String text;

        RecruitingTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     *  四证校验-核验类别(1.招募,2司机,3车辆)
     */
    public enum CertificateCheckTypeEnum {
        RECRUITING_DRV(1, "招募司机"),
        DRV(2, "司机"),
        VEHICLE(3, "车辆"),
        RECRUITING_VEHICLE(4, "招募车辆");
        private Integer code;
        private String msg;

        private CertificateCheckTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    /**
     *  四件校验-证件类别(1.驾驶证,2.行驶证,3.网约车驾驶证,4.网约车车辆许可证,11.车牌号,12.车身颜色,13.车身照片)
     */
    public enum CertificateTypeEnum {
        DRIVERLICENSE(1, "驾驶证"),
        CARCERTILICENSE(2, "行驶证"),
        NETDRVCTFCT(3, "网约车驾驶证"),
        NETTANSCTFCT(4, "网约车运输证"),
        IDCARD(5, "身份证"),
        NUCLEIC_ACID(6, "核酸报告"),
        VACCINE(7, "疫苗报告"),
        EPIDEMIC_REPORT(8,"疫情报告"),
        HEAD_PORTRAIT_COMPLIANCE(9,"合规司机头像"),
        OCR_HEAD_PORTRAIT(10,"OCR 头像识别校验"),
        VEHICLELICENSE(11,"vehicleLicense"),
        VEHICLECOLORID(12,"vehicleColorId"),
        VEHICLEFULLIMG(13,"vehicleFullImg"),
        DRVNAME(14,"drvName"),
        DRVCARDIMG(15,"drvcardImg"),
        VEHICLE_AUDITSTATUS(16,"auditStatus");
        private Integer code;
        private String msg;

        private CertificateTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public static String getText(Integer code) {
            for (CertificateTypeEnum s : CertificateTypeEnum.values()) {
                if (s.getCode().intValue() == code.intValue()) {
                    return s.msg;
                }
            }
            return "";
        }


    }

    /**
     *  四证核验状态(1.信息通过，2.复核，3.信息错误,4.审核中)
     */
    public enum CheckStatusEnum {
        INIT(0,"初始值"),
        THROUGH(1, "通过"),
        REVIEW(2, "复核"),
        ERROR(3, "不通过"),
        CHECKING(4, "审核中");
        private Integer code;
        private String msg;

        private CheckStatusEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public static String getText(Integer code) {
            for (CheckStatusEnum s : CheckStatusEnum.values()) {
                if (s.getCode().intValue() == code.intValue()) {
                    return s.msg;
                }
            }
            return "";
        }

    }
    /**
     *  是否听播报(0-不听,1.听)
     */
    public enum BroadcastEnum {
        NO(0, "NO"),
        YES(1, "YES");
        private Integer code;
        private String msg;

        private BroadcastEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    /**
     *  是否传工作时段(0-不传,1.传)
     */
    public enum SendWorkPeriodEnum {
        NO(0, "NO"),
        YES(1, "YES");
        private Integer code;
        private String msg;

        private SendWorkPeriodEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }

    /**
     *  兼容合作模式(1-全职,2.兼职)
     */
    public enum CompatibleCoopModeEnum {
        QZ(1, "QZ"),
        JZ(2, "JZ");
        private Integer code;
        private String msg;

        private CompatibleCoopModeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }

    /**
     * <AUTHOR>
     * @Date 2020/11/27 14:59
     */
    public enum SaleModeEnum {

        DL(1, "代理"),
        LS(2, "零售"),
        ZY(5,"自营");

        private Integer code;

        private String name;

        SaleModeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public Integer getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    /**
     *  审批流中审批状态(0.暂不可审批、1.待审批、2.审批通过、3.审批驳回)
     */
    public enum TransportApproveStatusEnum {
        UNAPPROVABLE(0, "Unapprovable"),
        WAITAPPROVE(1, "wait approve"),
        APPROVED(2, "Approved"),
        APPROVAL_REJECTED(3, "Approval rejected");
        private Integer code;
        private String msg;

        private TransportApproveStatusEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    /**
     *  审批事件类型(1.司机/车辆修改审核)
     */
    public enum EnentTypeEnum {
        drvAndVehicleUpdate(1, "drv and vehicle update"),
        nucleicAcidTest(2,"nucleic acid test"),
        vaccine(3,"vaccine"),
        headPortraitCompliance(4,"headPortrait"),
        OVERSEASVEHMOD(5,"overseasVehMod"),
        TEMPORARYDISPATCH(6,"temporarydispatch");
        private Integer code;
        private String msg;

        private EnentTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public static EnentTypeEnum getCode(Integer code) {
            for (EnentTypeEnum s : EnentTypeEnum.values()) {
                if (s.getCode().intValue() == code.intValue()) {
                    return s;
                }
            }
            return null;
        }

    }

    /**
     *  审批来源类型(1.司机,2.车辆)
     */
    public enum ApproveSourceTypeEnum {
        DRV(1, "drv"),
        VEHICLE(2, "vehicle");
        private Integer code;
        private String msg;

        private ApproveSourceTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public static ApproveSourceTypeEnum getCode(Integer code){
            for (ApproveSourceTypeEnum s : ApproveSourceTypeEnum.values()) {
                if (s.getCode().intValue() == code.intValue()) {
                    return s;
                }
            }
            return null;
        }
    }



    /**
     * 运力组行政区域类型(1.行政区域,2.自定义区域)
     */
    public enum AreaTypeTypeEnum {

        ADMINISTRATIVE_REGION(1, "administrative region"), DIY_REGION(2, "diy_region");
        private Integer code;
        private String msg;

        private AreaTypeTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    /**
     * 校验事件类型
     * 1常规事件 2 抽查事件,3.主动事件
     */
    public enum VerifyEventTypeEnum {
        CONVENTIONAL_EVENT(1, "CONVENTIONAL_EVENT"),
        SPOT_CHECK_EVENT(2, "SPOT_CHECK_EVENT"),
        INDEPENDENT_EVENT(3, "INDEPENDENT_EVENT");


        private Integer code;
        private String text;

        VerifyEventTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 事件状态
     * 1未验证,2已验证
     */
    public enum VerifyStatusEnum {
        NO_VERIFY(1, "NO_VERIFY"),
        VERIFYED(2, "VERIFYED-time");


        private Integer code;
        private String text;

        VerifyStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 认证标识 1不需要认证(没有可以认证的事件) |2 需要人脸认证 | 3需要车辆认证 | 4需要人车认证（有人脸事件和车辆事件）
     */
    public enum VerifyFlagEnum {
        NO_VERIFY(1, "NO_VERIFY"),
        FACE_VERIFY(2, "FACE_VERIFY-time"),
        VEHICLE_VERIFY(3, "VEHICLE_VERIFY-time"),
        FACE_VEHICLE_VERIFY(4, "FACE_VEHICLE_VERIFY-time");


        private Integer code;
        private String text;

        VerifyFlagEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 校验来源类型(1.司机,2.车辆)
     */
    public enum VerifySourceTypeEnum {
        DRV(1, "DRV"),
        VEHICLE(2, "VEHICLE");


        private Integer code;
        private String text;

        VerifySourceTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }
    }

    /**
     * 验证类型(1.人脸验证,2.车辆验证)
     */
    public enum VerifyTypeEnum {
        FACE(1, "FACE"),
        VEHICLE(2, "VEHICLE");


        private Integer code;
        private String text;

        VerifyTypeEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static List<Integer> getList(){
            List<Integer> list = Lists.newArrayList();
            for (VerifyTypeEnum verifyTypeEnum : VerifyTypeEnum.values()){
                list.add(verifyTypeEnum.getCode());
            }
            return list;
        }
    }


    public final static String HAVE_INTO_ORDER_CONFIG = "存在城市已添加进单配置的点位，不可删除";
    public static final String HAVE_RELEVANCE_SKU = "存在城市已关联相同点位城市的资源，不可删除";
    public static final String BOTH_HAVE = "存在城市已添加进单配置的点位并已关联相同点位城市的资源，不可删除";
    public static final String HAVE_POINT_SKU = "存在被删除的点位已关联相同枢纽点位的资源，请检查后重试";

    //四证核验状态(1.信息通过，2.复核，3.信息错误)
    public static final String CHECKSTATUS_KEY = "checkStatusKey";
    //返回标识  true 通过，false 未通过
    public static final String RESULTFLAG_KEY = "resultFlag";
    //是否调用第三方标识
    public static final String REQUEST_THIRD_INTERFACE_KEY = "request_third_interface_key";
    //证件核验 错误key值
    public static final String CERTIFICATE_CHECK_ERROR_INFO_KEY = "certificate_check_error_info";
    //返回信息
    public static final String RESULTINFO_KEY = "resultInfo";

    // taskId = -1 代表手机号ivr已验证
    public static final Long IVR_PHONE_VERIFIED = -1L;

    //查询成功，有数据
    public static final String CODE_1001 = "1001";
    //查无记录
    public static final String CODE_3001 = "3001";
    //参数错误
    public static final String CODE_3002 = "3002";
    //查询成功，无数据
    public static final String CODE_2001 = "2001";
    //限制查询
    public static final String CODE_2002 = "2002";
    //请求参数不标准
    public static final String CODE_1005 = "-1005";
    //查询异常
    public static final String CODE_1 = "-1";
    //驾驶证已达上限
    public static final String CODE_1018 = "-1018";
    //行驶证已达上限
    public static final String CODE_1017 = "-1017";
    //行驶证已达上限
    public static final String CODE_9999 = "9999";
    //网约车返加正常码
    public static final String STATE_CODE = "1";
    //行驶证已达上限
    public static final String CODE_1002 = "-1002";
    //请求姓名不标准：姓名为空或者包含特殊字符
    public static final String _CODE_1001 = "-1001";

    //驾驶证状态返回信息属性值key
    public static final String DrvLicenseStatusColKey = "drvLicenseStatusKey";
    //驾驶证状态返回信信结果key
    public static final String DrvLicenseStatusResultKey = "drvLicenseStatusResultKey";
    //行驶证VIN码返回信信属性key
    public static final String VehLicenseVinColKey = "vehLicenseVinColKey";
    //行驶证VIN码返回信信结果key
    public static final String VehLicenseVinResultKey = "vehLicenseVinResultKey";
    //行驶证报废期返回信信属性key
    public static final String VehLicenseScrapColKey = "vehLicenseScrapColKey";
    //行驶证报废期返回信信结果key
    public static final String VehLicenseScrapResultKey = "vehLicenseScrapResultKey";
    //行驶证使用性质返回信信属性key
    public static final String VehLicenseUseTypeColKey = "vehLicenseUseTypeColKey";
    //行驶证使用性质返回信信结果key
    public static final String VehLicenseUseTypResultKey = "vehLicenseUseTypeResultKey";
    //车辆报废状态返回信信属性key
    public static final String VehRetirementColKey = "VehRetirementColKey";
    //车辆报废状态返回信信结果key
    public static final String VehRetirementResultKey = "VehRetirementResultKey";
    //网约车驾驶证状态返回信息属性值key
    public static final String NetDrvLicenseStatusColKey = "netDrvLicenseStatusKey";
    //网约车驾驶证状态返回信信结果key
    public static final String NetDrvLicenseStatusResultKey = "netDrvLicenseStatusResultKey";
    //行驶证状态属性key
    public static final String VehLicenseStatusColKey = "VehLicenseStatusColKey";
    //行驶证状态信信结果key
    public static final String VehLicenseStatusResultKey = "VehLicenseStatusResultKey";

    //核酸疫苗单项类型
    public static final String NUCLEICACIDVACCINE_ITEM = "NucleicAcidVaccine_ITEM_";

    //核酸疫苗单项类型描述
    public static final String NUCLEICACIDVACCINE_DESC = "NUCLEICACIDVACCINE_DESC_";

    //灰度表示全量
    public static final Long allData = -1L;


    /**
     * 司机安全中心使用
     * 司机逻辑状态
     */
    public enum DrvCenterStatusEnum {
        UNACT(-1, "未激活"),
        NORMAL(0, "正常"),
        LEAVE(1, "请假"),
        FREEZE_ONLINE(2, "冻结-上线"),
        FREEZE_OFFLINE(3,"冻结-下线");
        private Integer code;
        private String text;

        DrvCenterStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     * 司机安全中心使用
     * 司机疫情报告状态
     */
    public enum DrvEpidemicReportStatusEnum {
        NOT_NEED(0, "无需上传"),
        UPLOADED(1, "已上传"),
        HAVE_NOT_UPLOADED(2, "未上传");
        private Integer code;
        private String text;

        DrvEpidemicReportStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     * 司机安全中心使用
     * 司机疫情报告状态
     */
    public enum DrvVerifyStatusEnum {
        UNVERIFIED(1, "未验证"),
        VERIFIED(2, "已验证"),
        TIMEOUT(3, "已超时");
        private Integer code;
        private String text;

        DrvVerifyStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     * 司机DSP状态
     */
    public enum DrvDspStatusEnum {
        DRV(1),
        DRV_LEAVE(2),
        DRV_FREEZE(3);

        private Integer code;

        DrvDspStatusEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     *
     * 招募审批时效
     */
    public enum ApproveAgingEnum {
        HASTIMEOUT(0, "已超时"),
        NOTIMEOUT(1, "未超时");
        private Integer code;
        private String text;

        ApproveAgingEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     *
     * 招募核验状态(1.已完成,2.未完成)
     */
    public enum RecruitingCheckStatusEnum {
        HASCOMPLETED(1, "已完成"),
        NOTCOMPLETED(2, "未完成");
        private Integer code;
        private String text;

        RecruitingCheckStatusEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     *  单项审批状态(0.待审核,1.审批通过,2.审批不通过,3.不审批)
     */
    public enum SingleApproveStatusEnum {
        WAITAPPROVE(0, "WAITAPPROVE"),
        APPROVE_THROUGH(1, "APPROVE_THROUGH"),
        APPROVE_NO_THROUGH(2, "APPROVE_NO_THROUGH"),
        NO_APPROVE(3, "NO_APPROVE");
        private Integer code;
        private String msg;

        private SingleApproveStatusEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    /**
     *  审批单项(1.身份与背景信息,2.驾驶证信息,3.头像信息,4.网约车人证信息,5.疫苗信息,6.核酸信息,7.司机基础信息,101.行驶证信息,102.网约车车证信息,103.车辆基础信息)
     */
    public enum ApproveItemEnum {
        identity(1, "身份与背景信息"),
        driverlicense(2, "驾驶证信息"),
        HeadPortrait(3, "头像信息"),
        net_people(4, "网约车人证信息"),
        vaccine(5, "疫苗信息"),
        nucleicAcid(6, "核酸信息"),
        drv_basis(7, "司机基础信息"),
        Vehicle_icense(101, "行驶证信息"),
        net_vehile(102, "网约车车证信息"),
        veh_basis(103, "车辆基础信息"),
        drv_name(8, "drv_name"),
        vehicle_number(104, "vehicle_number"),
        vehicle_color(105, "vehicle_color"),
        vehicle_compliance(106,"vehicle_compliance"),// 车辆合规
        ;

        private Integer code;
        private String msg;

        private ApproveItemEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public static String getText(Integer code) {
            for (ApproveItemEnum s : ApproveItemEnum.values()) {
                if (s.getCode().intValue() == code.intValue()) {
                    return s.msg;
                }
            }
            return "";
        }

    }


    /**
     *  单项审批类别(1.司机,2.车辆)
     */
    public enum SingleApproveTypeEnum {
        DRV(1, "DRV"),
        VEH(2, "Veh");
        private Integer code;
        private String msg;

        private SingleApproveTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    /**
     *  子项类别(1-OCR识别,2-非OCR识别,3-证件核验)
     */
    public enum ApproveChildItemEnum {
        OCR(1, "OCR"),
        NON_OCR(2, "NON_OCR"),
        COMPLIANCE(3, "COMPLIANCE");
        private Integer code;
        private String msg;

        private ApproveChildItemEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    /**
     * 招募审批操作
     *
     */
    public enum ApproverAperationEnum {
        H5_register_submit(0, "H5注册提交"),
        H5_mod_submit(1, "H5修改提交"),
        approve_pass(2, "审批通过"),
        approve_rejected(3, "审批驳回"),
        save_audit(4, "保存审核"),
        work_register_submit(5, "工作台注册提交"),
        pass_no_activation(6, "审批通过不激活"),
        system_auto_audit(7, "系统自动驳回");

        private Integer code;
        private String text;

        ApproverAperationEnum(Integer code, String text) {
            this.code = code;
            this.text = text;
        }

        public Integer getCode() {
            return code;
        }

        public String getText() {
            return this.text;
        }

        public static String getText(Integer code) {
            for (ApproverAperationEnum s : ApproverAperationEnum.values()) {
                if (s.getCode() == code.intValue()) {
                    return s.text;
                }
            }
            return "";
        }
    }

    /**
     * 1.冻结期累计总时长
     * 2.解冻前的小时配置
     *
     */
    public enum DrvFreezeTimeConfigEnum {
        drvFreezeRemindDays(1),
        unfreezeBeforeHour(2);

        private Integer code;

        DrvFreezeTimeConfigEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
    　* @description: 报名制操作类型(1.全局赛道,2.运力缺失补位)
    　* <AUTHOR>
    　* @date 2022/12/29 15:48
    */
    public enum ApplyTransPortOperationTypeEnum {
        GLOBAL_TRACK(1),
        CAPACITY_REPLACEMENT(2),

        ELIMINATE(3);

        private Integer code;

        ApplyTransPortOperationTypeEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }

        public static ApplyTransPortOperationTypeEnum getEnum(Integer code) {
            return Arrays.stream(ApplyTransPortOperationTypeEnum.values()).filter(e -> Objects.equals(e.getCode(),code)).findFirst().orElse(null);
        }

    }

    /**
     　* @description: 司机派遣类型(1.关联关系,2.派遣关系)
     　* <AUTHOR>
     　* @date 2023/2/13 15:00
     */
    public enum DrvDispatchRelationTypeEnum {
        CORRELATION(1),DISPATCH(2);

        private Integer code;

        DrvDispatchRelationTypeEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     　* @description: 司机绑定/解绑派遣(0.解绑,1.绑定)
     　* <AUTHOR>
     　* @date 2023/2/13 15:00
     */
    public enum DrvDispatchOperationTypeEnum {
        unbind(0),
        binding(1);

        private Integer code;

        DrvDispatchOperationTypeEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
    　* @description: 编辑审核状态 ：0.未完成,1.已完成
    　* <AUTHOR>
    　* @date 2023/4/21 16:00
    */
    public enum ApproveScheduleStatusEnum {
        undone(0),done(1);

        private Integer code;

        ApproveScheduleStatusEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     * 　* @description: 境外OCR通过标识
     * 　* <AUTHOR>
     * 　* @date 2023/6/9 11:15
     */
    public enum OverseasOCRPassStatusEnum {
        no_pass(0), pass(1);

        private Integer code;

        OverseasOCRPassStatusEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
     * 　* @description: 境外供应商编辑-保存/审批通过 标记
     * 　* <AUTHOR>
     * 　* @date 2023/6/25 17:03
     */
    public enum OverseasApproveMarkEnum {
        save(0), approve(1);

        private Integer code;

        OverseasApproveMarkEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
    　* @description: 灰度开关判断,1.H5入注,2.其它(工作台创建,编辑招募信息)
    　* <AUTHOR>
    　* @date 2023/7/4 14:51
    */
    public enum BusinessTypeEnum {
        H5(1), OTHER(2);

        private Integer code;

        BusinessTypeEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    /**
    　* @description: 临派标识-0:正式,1:临派
    　* <AUTHOR>
    　* @date 2023/7/4 14:51
    */
    public enum TemporaryDispatchMarkEnum {
        OFFICIAL(0), TEMPORARY(1);

        private Integer code;

        TemporaryDispatchMarkEnum(Integer code) {
            this.code = code;
        }

        public Integer getCode() {
            return code;
        }
    }

    public enum AutoDiscardTemporaryTypeEnum {
        DRV(1, "DRV"),
        VEH(2, "Veh"),
        DRV_VEH(3,"DRV_VEH");
        private Integer code;
        private String msg;

        private AutoDiscardTemporaryTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    public enum PayAccountTypeEnum {
        PPM(1, "PPM"),
        PAIAY(2, "PAIAY");
        private Integer code;
        private String msg;

        private PayAccountTypeEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

    }

    public static class UNICODE {
        //司机分
        public static final String DRIVER_POINT = "\u53f8\u673a\u5206";
        //司机上限
        public static final String DRIVER_UP_LIMIT = "\u53f8\u673a\u4e0a\u9650";
        //全局赛道更新司机排名
        public static final String GLOBAL_REFRESH_DRIVER_RANKING = "\u5168\u5c40\u8d5b\u9053\u66f4\u65b0\u53f8\u673a\u6392\u540d";
        // 运力补位需求
        public static final String CAPACITY_REPLACEMENT_LIMIT =  "\u8fd0\u529b\u8865\u4f4d\u9700\u6c42";
        //运力补位司机分排名
        public static final String CAPACITY_REPLACEMENT_DEMAND_RANKING = "\u8fd0\u529b\u8865\u4f4d\u53f8\u673a\u5206\u6392\u540d";

        //触发司机分限制
        public static final String TRIGGER_DRIVER_SCORE_RESTRICTION_RULE = "\u89e6\u53d1\u53f8\u673a\u5206\u9650\u5236";

        //触发请假冻结时长限制
        public static final String TRIGGER_DRIVER_LEAVE_AND_FREEZE_LIMIT = "\u89e6\u53d1\u8bf7\u5047\u51bb\u7ed3\u65f6\u957f\u9650\u5236";

        //请假及冻结时长共计
        public static final String LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT = "\u8bf7\u5047\u53ca\u51bb\u7ed3\u65f6\u957f\u5171\u8ba1";

        // 冻结时长明细
        public static final String FREEZE_DETAIL = "\u51bb\u7ed3\u65f6\u957f\u660e\u7ec6:";
        //请假时长明细
        public static final String LEAVE_DETAIL = "\u8bf7\u5047\u65f6\u957f\u660e\u7ec6:";

        //共计
        public static final String TOTAL = "\u5171\u8ba1:";

        //小时
        public static final String HOUR = "\u5c0f\u65f6";
        //执行时间
        public static final String OPERATE_TIME = "\u6267\u884c\u65f6\u95f4:";
    }

}
