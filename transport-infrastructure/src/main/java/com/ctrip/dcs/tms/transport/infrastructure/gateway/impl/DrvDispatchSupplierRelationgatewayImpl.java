package com.ctrip.dcs.tms.transport.infrastructure.gateway.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDispatchRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.DrvDispatchRelationHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl.DriverDispatchSupplierCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CacheBusinessQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TrafficDiverter;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.DrvDispatchSupplierRelationgateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDispatchRelationRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class DrvDispatchSupplierRelationgatewayImpl implements DrvDispatchSupplierRelationgateway {

  private static final Logger logger = LoggerFactory.getLogger(DrvDispatchSupplierRelationgatewayImpl.class);

  @Autowired
  DrvDispatchRelationRepository drvDispatchRelationRepository;

  @Autowired
  TrafficDiverter trafficDiverter;

  @Autowired
  CacheBusinessQconfig cacheBusinessQconfig;

  @Autowired
  DriverDispatchSupplierCacheHandler handler;


  @Override
  public List<DrvDispatchRelationPO> queryDrvDispatchSupplierIds(ArrayList<Long> drvIds) {
    try {
      if (trafficDiverter.greatThan(cacheBusinessQconfig.getConfig().getDrvDispatchSupplierRate())) { // 走DB
        return drvDispatchRelationRepository.queryDrvDispatchSupplierIds(drvIds);
      }
      // 走缓存
      return handler.queryDrvDispatchSupplierIds(drvIds);
    } catch (Exception e) {
      logger.error("queryDrvDispatchSupplierIds_excep", e);
      return drvDispatchRelationRepository.queryDrvDispatchSupplierIds(drvIds);
    }
  }
}
