package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;

import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.QueryDiscardVehicleListSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryVehicleSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailDTO;
import com.ctrip.dcs.tms.transport.api.model.VehicleListSOADTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleGlobalIdRecordRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalRowMapper;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaMapper;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeSelectSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeUpdateSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.UpdateSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.DRIVER_SOURCE;

@Repository(value = "vehicleRepository")
public class VehicleRepositoryImpl implements VehicleRepository {

    private static final Logger logger = LoggerFactory.getLogger(VehicleRepositoryImpl.class);

    private DalRepository<VehVehiclePO> vehVehicleRepo;

    private DalRowMapper<VehVehiclePO> vehVehiclePODalRowMapper;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Autowired
    EnumRepository enumRepository;
    @Autowired
    DrvDrvierRepository drvDrvierRepository;

    @Autowired
    VehicleGlobalIdRecordRepository vehicleGlobalIdRecordRepository;
    @Autowired
    OverageQConfig overageQConfig;

    @Autowired
    private CommonConfig config;

    private static final String DATA_BASE = "dcstransportdb_w";

    public VehicleRepositoryImpl() throws SQLException {
        this.vehVehiclePODalRowMapper = new DalDefaultJpaMapper<>(VehVehiclePO.class);
        vehVehicleRepo = new DalRepositoryImpl<>(VehVehiclePO.class);
    }

    public DalRepository<VehVehiclePO> getVehVehicleRepo() {
        return vehVehicleRepo;
    }

    @Override
    public VehVehiclePO queryByPk(Long vehicleId) {
        return vehVehicleRepo.queryByPk(vehicleId);
    }

    @Override
    public Long addVehicle(VehVehiclePO vehVehiclePO) {
        try {
            KeyHolder keyHolder = new KeyHolder();
            Result<Long> generateGlobalId =
              vehicleGlobalIdRecordRepository.generateGlobalId(vehVehiclePO.getVehicleLicense(), DRIVER_SOURCE);
            vehVehiclePO.setVehicleId(generateGlobalId.getData());
            setOverAgeType(vehVehiclePO);
            Integer id = vehVehicleRepo.insert(new DalHints(), keyHolder, vehVehiclePO);
            if (id <= 0) {
                throw new RuntimeException(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgAddError));
            }
            return keyHolder.getKey().longValue();
        } catch (SQLException e) {
            throw new RuntimeException(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgAddError), e);
        }
    }

    private void setOverAgeType(VehVehiclePO vehVehiclePO) {
        try {
            City cityById = enumRepository.getCityById(vehVehiclePO.getCityId());
            if (cityById.isChineseMainland()) {
                vehVehiclePO.setVehicleAgeType(VehicleAgeTypeEnum.NOT_OVERAGE.getCode());
            }
        } catch (Exception e) {
            logger.error("setOverAgeType error", e);
        }
    }

    @Override
    public int updateVehicle(VehVehiclePO vehVehiclePO) {
        int count;
        try {
            count = vehVehicleRepo.update(vehVehiclePO);
        } catch (Exception e) {
            throw new RuntimeException(SharkUtils.getSharkValue(SharkKeyConstant.transportUpdateError), e);
        }
        return count;
    }

    @Override
    public int batchUpdateVehicle(List<VehVehiclePO> vehVehicleList) {
        int[] count = vehVehicleRepo.batchUpdate(vehVehicleList);
        if(count.length > 0){
            return 1;
        }
        return 0;
    }

    @Override
    public VehicleDetailDTO queryVehicleDetail(Long vehicleId) {
        VehVehiclePO vehVehiclePO = vehVehicleRepo.queryByPk(vehicleId);
        if(vehVehiclePO == null) {
            return null;
        }
        Map<Integer,String> vehicleStatusMap = enumRepository.getVehicleStatus();
        VehicleDetailDTO result = new VehicleDetailDTO();
        BeanUtils.copyProperties(vehVehiclePO, result);
        result.setSupplierName(enumRepository.getSupplierName(result.getSupplierId()));
        result.setHasDrvValue(enumRepository.getHasDrvStr(vehVehiclePO.getHasDrv()));
        result.setVehicleBrandName(enumRepository.getBandName(result.getVehicleBrandId()));
        result.setVehicleSeriesName(enumRepository.getVehicleSeriesName(result.getVehicleSeries()));
        result.setVehicleTypeName(enumRepository.getVehicleTypeName(result.getVehicleTypeId()));
        result.setCityName(enumRepository.getCityName(result.getCityId()));
        result.setSupplierName(enumRepository.getSupplierName(result.getSupplierId()));
        result.setUsingNatureValue(enumRepository.getUsingNatureValue(result.getUsingNature()));
        result.setVehicleColorName(enumRepository.getColorName(result.getVehicleColorId()));
        result.setHasDrv(vehVehiclePO.getHasDrv() ? 1 : 0);
        result.setCreateUser(vehVehiclePO.getCreateUser());
        result.setVehicleEnergyType(vehVehiclePO.getVehicleEnergyType());
        result.setVehiclePowerModeValue(enumRepository.getVehicleEnergyTypeName(vehVehiclePO.getVehicleEnergyType()));
        result.setRegstDate(getRegstDate(vehVehiclePO.getRegstDate()));
        result.setAreaScope(enumRepository.getAreaScope(vehVehiclePO.getCityId()));
        result.setDatachangeCreatetime(new SimpleDateFormat("YYYY-MM-dd HH:mm:ss").format(new Date(vehVehiclePO.getDatachangeCreatetime().getTime())));
        result.setVehicleStatus(vehVehiclePO.getVehicleStatus());
        result.setVehicleStatusName(vehicleStatusMap.get(vehVehiclePO.getVehicleStatus()));
        result.setTemporaryDispatchEndDatetime(DateUtil.timestampToString(vehVehiclePO.getTemporaryDispatchEndDatetime(),DateUtil.YYYYMMDDHHMMSS));
        return result;
    }

    private String getRegstDate(Date regstDate) {
        return new SimpleDateFormat("YYYY-MM-dd").format(regstDate);
    }

    @Override
    public List<VehicleListSOADTO> queryVehicleList(QueryVehicleSOARequestType queryVehicleRequestType, List<Integer> productionLineIdList) {
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equalNullable("supplier_id", queryVehicleRequestType.getSupplierId(), Types.BIGINT);
            builder.and();
            builder.inNullable("vehicle_license", inStr(queryVehicleRequestType.getVehicleLicense()), Types.VARCHAR);
            builder.and();
            builder.equalNullable("city_id", queryVehicleRequestType.getCityId(), Types.BIGINT);
            builder.and();
            builder.equalNullable("vehicle_brand_id", queryVehicleRequestType.getVehicleBrandId(), Types.BIGINT);
            builder.and();
            builder.equalNullable("vehicle_series", queryVehicleRequestType.getVehicleSeriesId(), Types.BIGINT);
            builder.and();
            builder.equalNullable("vehicle_type_id", queryVehicleRequestType.getVehicleTypeId(), Types.BIGINT);
            builder.and();
            builder.inNullable("vehicle_id", queryVehicleRequestType.getVehicleIdList(), Types.VARCHAR);
            builder.and();
            builder.equalNullable("has_drv", getHasDrv(queryVehicleRequestType.getHasDrv()), Types.BIT);
            builder.and();
            builder.equalNullable("vehicle_status", queryVehicleRequestType.getVehicleStatus(), Types.INTEGER);
            builder.and();
            builder.inNullable("category_synthesize_code", productionLineIdList, Types.INTEGER);
            builder.and();
            builder.equalNullable("temporary_dispatch_mark", queryVehicleRequestType.getTemporaryDispatchMark(), Types.TINYINT);
            builder.and().equal("active", CtripCommonUtils.queryActiveChoose(queryVehicleRequestType.isActive()),Types.BIT);
            builder.and().equalNullable("audit_status", queryVehicleRequestType.getVehicleAuditStatus(), Types.TINYINT);
            builder.and().equalNullable("vehicle_age_type", queryVehicleRequestType.getVehicleAgeType(), Types.VARCHAR);
            int page = 1;
            int size = 15;
            if (queryVehicleRequestType.getPaginator() != null) {
                page = queryVehicleRequestType.getPaginator().getPageNo();
                size = queryVehicleRequestType.getPaginator().getPageSize();
            }
            builder.orderBy("datachange_lasttime",false);
            builder.atPage(page, size);
            return queryVehicleList(queryPage(builder));
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryVehicleList error", e);
        }
    }

    /**
     * 先查出车辆ID列表再根据车辆ID查出车辆信息
     * @param builder builder
     * @return List<DrvDriverPO>
     * @throws SQLException SQLException
     */
    protected List<VehVehiclePO> queryPage(SelectSqlBuilder builder) throws SQLException {
        builder.select("vehicle_id");
        List<VehVehiclePO> resultList = vehVehicleRepo.getDao().query(builder, DalHints.createIfAbsent(null));
        return queryVehicleByIds(resultList.stream().map(VehVehiclePO::getVehicleId).collect(Collectors.toList())).stream().sorted(Comparator.comparing(VehVehiclePO::getDatachangeLasttime).reversed()).collect(Collectors.toList());
    }

    private List<VehicleListSOADTO> queryVehicleList(List<VehVehiclePO> vehiclePOList) {
        if (CollectionUtils.isEmpty(vehiclePOList)) {
            return null;
        }
        List<VehicleListSOADTO> result = Lists.newArrayListWithCapacity(vehiclePOList.size());
        Map<Integer,String> vehicleStatusMap = enumRepository.getVehicleStatus();
        for (VehVehiclePO vehiclePO : vehiclePOList) {
            VehicleListSOADTO tmp = new VehicleListSOADTO();
            BeanUtils.copyProperties(vehiclePO, tmp);
            tmp.setVehicleLicense(SecretUtil.decode(tmp.getVehicleLicense(), CommonEnum.RecordTypeEnum.VEHICLE));
            tmp.setHasDrv(vehiclePO.getHasDrv() ? 1 : 0);
            tmp.setHasDrvValue(enumRepository.getHasDrvStr(vehiclePO.getHasDrv()));
            tmp.setVehicleSeriesId(vehiclePO.getVehicleSeries());
            tmp.setCityName(enumRepository.getCityName(tmp.getCityId()));
            tmp.setCreateUser(vehiclePO.getCreateUser());
            tmp.setModifyUser(vehiclePO.getModifyUser());
            tmp.setSupplierName(enumRepository.getSupplierName(tmp.getSupplierId()));
            tmp.setVehicleTypeName(enumRepository.getVehicleTypeName(tmp.getVehicleTypeId()));
            tmp.setVehicleBrandName(enumRepository.getBandName(tmp.getVehicleBrandId()));
            tmp.setVehicleColorName(enumRepository.getColorName(tmp.getVehicleColorId()));
            tmp.setVehicleSeriesName(enumRepository.getVehicleSeriesName(tmp.getVehicleSeriesId()));
            tmp.setVehicleStatus(vehiclePO.getVehicleStatus());
            tmp.setVehicleStatusName(vehicleStatusMap.get(vehiclePO.getVehicleStatus()));
            tmp.setProLineName(productionLineUtil.getProductionLineNames(vehiclePO.getCategorySynthesizeCode()));
            tmp.setAreaScope(enumRepository.getAreaScope(tmp.getCityId()));
            processVehicleAge(vehiclePO, tmp);
            result.add(tmp);
        }
        processVehicleAge(result);
        return result;
    }

    private void processVehicleAge(VehVehiclePO vehiclePO, VehicleListSOADTO tmp) {
        List<Long> cityIdList = config.getCityIdList();
        String vehicleAgeType = vehiclePO.getVehicleAgeType();
        VehicleAgeTypeEnum enumByCode = VehicleAgeTypeEnum.getEnumByCode(vehicleAgeType);
        tmp.setVehicleAgeTypeName(Objects.nonNull(enumByCode) ? enumByCode.getName() : null);
        if ((!CollectionUtils.isEmpty(cityIdList) && BooleanUtils.isTrue(config.getOverageGraySwitch()) && !cityIdList.contains(vehiclePO.getCityId()))|| (BooleanUtils.isTrue(config.getOverageGraySwitch())&&CollectionUtils.isEmpty(cityIdList))) {
            tmp.setVehicleAgeType(null);
            tmp.setVehicleAge(null);
            tmp.setVehicleAgeTypeName(null);
            return;
        }
        LocalDateTime regstDate = getRegisterTime(vehiclePO);
        // 计算日期之间的天数差
        long daysBetween = ChronoUnit.DAYS.between(regstDate, LocalDateTime.now());
        BigDecimal divide = BigDecimal.valueOf(daysBetween).divide(BigDecimal.valueOf(365), 1, RoundingMode.HALF_UP);
        tmp.setVehicleAge(String.format("%s年", divide.doubleValue()));
    }

    private static LocalDateTime getRegisterTime(VehVehiclePO vehiclePO) {
        LocalDateTime regstDate;
        if (Objects.isNull(vehiclePO.getRegstDate()) || org.apache.commons.lang3.StringUtils.equals(Constant.DEFAULT_YEAR, vehiclePO.getRegstDate().toString())) {
            regstDate = vehiclePO.getDatachangeCreatetime().toLocalDateTime();
        } else {
            LocalDate localDate = DateUtil.convertStringToDate(vehiclePO.getRegstDate().toString(), DateUtil.YYYYMMDD);
            LocalTime localTime = LocalTime.of(23, 59, 59);
            regstDate = LocalDateTime.of(localDate, localTime);
        }
        return regstDate;
    }

    private void processVehicleAge(List<VehicleListSOADTO> vehicleListSOADTOS) {
        List<Long> cityIdList = vehicleListSOADTOS.stream().filter(Objects::nonNull).map(VehicleListSOADTO::getCityId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (BooleanUtils.isTrue(config.getOverageGraySwitch())) {
            cityIdList = cityIdList.stream().filter(item -> config.getCityIdList().contains(item)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(cityIdList)) {
            return;
        }
        List<City> cities = enumRepository.queryByCityIds(cityIdList);
        if (CollectionUtils.isEmpty(cities)) {
            return;
        }
        List<Long> mainLandCityId = cities.stream().filter(item -> BooleanUtils.isTrue(item.isChineseMainland())).map(City::getId).collect(Collectors.toList());
        vehicleListSOADTOS.forEach(vehicleListSOADTO -> {
            boolean isNotManLand = CollectionUtils.isEmpty(mainLandCityId) || !mainLandCityId.contains(vehicleListSOADTO.getCityId());
            if (isNotManLand) {
                vehicleListSOADTO.setVehicleAgeType(null);
                vehicleListSOADTO.setVehicleAgeTypeName(null);
                vehicleListSOADTO.setVehicleAge(null);
            }
        });
    }

    @Override
    public int queryVehicleCount(QueryVehicleSOARequestType queryVehicleRequestType, List<Integer> productionLineIdList) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equalNullable("supplier_id", queryVehicleRequestType.getSupplierId(), Types.BIGINT);
            builder.and();
            builder.inNullable("vehicle_license", inStr(queryVehicleRequestType.getVehicleLicense()), Types.VARCHAR);
            builder.and();
            builder.equalNullable("city_id", queryVehicleRequestType.getCityId(), Types.BIGINT);
            builder.and();
            builder.equalNullable("vehicle_brand_id", queryVehicleRequestType.getVehicleBrandId(), Types.BIGINT);
            builder.and();
            builder.equalNullable("vehicle_series", queryVehicleRequestType.getVehicleSeriesId(), Types.BIGINT);
            builder.and();
            builder.equalNullable("vehicle_type_id", queryVehicleRequestType.getVehicleTypeId(), Types.BIGINT);
            builder.and();
            builder.inNullable("vehicle_id", queryVehicleRequestType.getVehicleIdList(), Types.BIGINT);
            builder.and();
            builder.equalNullable("has_drv", getHasDrv(queryVehicleRequestType.getHasDrv()), Types.BIT);
            builder.and();
            builder.equalNullable("vehicle_status", queryVehicleRequestType.getVehicleStatus(), Types.INTEGER);
            builder.and();
            builder.inNullable("category_synthesize_code", productionLineIdList, Types.INTEGER);
            builder.and();
            builder.equalNullable("temporary_dispatch_mark", queryVehicleRequestType.getTemporaryDispatchMark(), Types.TINYINT);
            builder.and().equal("active",CtripCommonUtils.queryActiveChoose(queryVehicleRequestType.isActive()),Types.BIT);
            builder.and().equalNullable("audit_status", queryVehicleRequestType.getVehicleAuditStatus(), Types.TINYINT);
            builder.and().equalNullable("vehicle_age_type", queryVehicleRequestType.getVehicleAgeType(), Types.VARCHAR);
            return vehVehicleRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryVehicleCount error", e);
        }
    }

    private List<String> inStr(String idStrList) {
        if (Strings.isNullOrEmpty(idStrList)) {
            return null;
        }
        String[] list = idStrList.split(",|，");
        return Arrays.asList(list);
    }

    private Boolean getHasDrv(Integer hasDrv) {
        if (hasDrv == null) {
            return null;
        }
        return hasDrv.intValue() == 2 ? false : true;
    }

    @Override
    public boolean isVehicleLicenseUniqueness(Long vehicleId, String vehicleLicense) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.notEqualNullable("vehicle_id", vehicleId, Types.BIGINT);
            builder.and();
            builder.equal("vehicle_license", vehicleLicense, Types.VARCHAR);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            int result = vehVehicleRepo.getDao().count(builder, hints).intValue();
            return result == 0;
        } catch (Exception e) {
            throw new BaijiRuntimeException("isVehicleLicenseUniqueness error", e);
        }
    }

    @Override
    public Boolean checkVehAvailAndHasNoDrv(Long vehicleId,Long supplierId) {
        if(vehicleId == null || vehicleId <= 0){
            return Boolean.FALSE;
        }
        VehVehiclePO vehVehiclePO = vehVehicleRepo.queryByPk(vehicleId);
        if(vehVehiclePO == null || vehVehiclePO.getHasDrv()){
            return Boolean.TRUE;
        }
        if(supplierId != null && !Objects.equals(vehVehiclePO.getSupplierId(),supplierId)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public int updateVehicleHasDrv(Long VehicleId, Integer hasDrv,String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update veh_vehicle set has_drv = ?,modify_user = ? where vehicle_id = ? and active = 1 ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "has_drv", Types.BIT, hasDrv);
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?"":modifyUser);
        parameters.setSensitive(i++, "vehicle_id", Types.BIGINT, VehicleId);

        return vehVehicleRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<VehVehiclePO> queryVehVehicleByIds(List<Long> vehVehicleIds) throws SQLException {
        if (CollectionUtils.isEmpty(vehVehicleIds)) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.in("vehicle_id",vehVehicleIds,Types.BIGINT);
        builder.and().equal("active", Boolean.TRUE, Types.BIT);
        return vehVehicleRepo.getDao().query(builder,hints);
    }

    @Override
    public List<VehVehiclePO> queryVehVehicleBySupplierId(Long supplierId, Integer hasDrv,Long cityId,List<Integer> productionLineIdList) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equalNullable("supplier_id", supplierId, Types.BIGINT);
            builder.and();
            builder.equalNullable("has_drv", hasDrv == 0 ? false : true, Types.BIT);
            builder.and();
            builder.equalNullable("city_id", cityId, Types.BIGINT);
            builder.and();
            builder.inNullable("category_synthesize_code", productionLineIdList, Types.TINYINT);
            builder.and().equalNullable("vehicle_status", TmsTransportConstant.VehStatusEnum.ONLINE.getCode(),Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().query(builder, hints);
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public int updateVehicleStatus(List<Long> vehVehicleIds, Integer vehicleStatus,String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        StringBuilder sqlStr = new StringBuilder("update veh_vehicle set vehicle_status = ?,modify_user = ?");
        parameters.setSensitive(i++, "vehicle_status", Types.INTEGER, vehicleStatus);
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?"":modifyUser);
        if(Objects.equals(TmsTransportConstant.VehStatusEnum.OFFLINE.getCode(),vehicleStatus)){
            sqlStr.append(",has_drv = ?");
            parameters.set(i++, "has_drv", Types.BIT, Boolean.FALSE);
        }
        sqlStr.append(" WHERE vehicle_id in (?) and active = 1");
        if(Objects.equals(TmsTransportConstant.VehStatusEnum.ONLINE.getCode(),vehicleStatus)){
            sqlStr.append(" and vehicle_age_type != 'overage'");
        }
        parameters.setInParameter(i++, "vehicle_id", Types.BIGINT, vehVehicleIds);
        builder.setTemplate(sqlStr.toString());
        return vehVehicleRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public Boolean checkVehOnly(String str, Integer type) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        if (Objects.equals(type, TmsTransportConstant.VehOnlyTypeEnum.vehicle_license.getCode())) {
            builder.and();
            builder.equal("vehicle_license", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())) {
            // vin 码不再校验唯一性
            return Boolean.FALSE;
        }
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        if (vehVehicleRepo.getDao().count(builder, hints).intValue() > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<VehVehiclePO> queryVehVehicleBySupplierIdAndId(List<Long> vehVehicleIds, Long supplierId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("supplier_id", supplierId, Types.BIGINT);
            builder.and().in("vehicle_id", vehVehicleIds, Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int countAllVehVehicleByIds(List<Long> vehVehicleIds, List<Long> cityIds){
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(!CollectionUtils.isEmpty(vehVehicleIds)){
                builder.in("vehicle_id", vehVehicleIds, Types.BIGINT);
            }
            if(!CollectionUtils.isEmpty(cityIds)){
                builder.and().in("city_id", cityIds, Types.BIGINT);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().count(builder,hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<VehVehiclePO> queryAllVehVehicleByIds(List<Long> vehVehicleIds, List<Long> cityIds, int beginNo, int pageSize) throws SQLException {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(!CollectionUtils.isEmpty(vehVehicleIds)){
                builder.in("vehicle_id", vehVehicleIds, Types.BIGINT);
            }
            if(!CollectionUtils.isEmpty(cityIds)){
                builder.and().in("city_id", cityIds, Types.BIGINT);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage(beginNo,pageSize);
            return vehVehicleRepo.getDao().query(builder,hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<VehVehiclePO> queryAllVehVehicleByIds(List<Long> vehVehicleIds, Integer vehicleStatus, int beginNo, int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(!CollectionUtils.isEmpty(vehVehicleIds)){
                builder.in("vehicle_id", vehVehicleIds, Types.BIGINT);
            }
            if (vehicleStatus != null) {
                builder.and().equal("vehicle_status", vehicleStatus, Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage(beginNo,pageSize);
            return vehVehicleRepo.getDao().query(builder,hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<VehVehiclePO> queryIllegalCertiDateVehicle(List<Long> vehVehicleIds, int page, int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(vehVehicleIds)) {
                builder.in("vehicle_id", vehVehicleIds, Types.BIGINT);
            }
            builder.and().isNotNull("vehicle_certi_img");
            builder.and().equal("regst_date", DateUtil.stringToDate("1777-01-01",DateUtil.YYYYMMDD), Types.DATE);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage(page,pageSize);
            return vehVehicleRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    @Override
    public int countAllVehVehicleByIds(List<Long> vehVehicleIds, Integer vehicleStatus) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if (!CollectionUtils.isEmpty(vehVehicleIds)) {
                builder.in("vehicle_id", vehVehicleIds, Types.BIGINT);
            }
            if (vehicleStatus != null) {
                builder.and().equal("vehicle_status", vehicleStatus, Types.INTEGER);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int countIllegalCertiDateVehicle(List<Long> vehVehicleIds) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(vehVehicleIds)) {
                builder.in("vehicle_id", vehVehicleIds, Types.BIGINT);
            }
            builder.and().isNotNull("vehicle_certi_img");
            builder.and().equal("regst_date", DateUtil.stringToDate("1777-01-01",DateUtil.YYYYMMDD), Types.DATE);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }


    private List<VehVehiclePO> doQueryVehInfo4Cache(List<Long> vehIdList) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("vehicle_id", "vehicle_license", "vehicle_type_id", "vehicle_brand_id", "vehicle_series",
                    "vehicle_color_id", "vehicle_energy_type", "vin", "regst_date", "datachange_createtime", "vehicle_status", "category_synthesize_code", "vehicle_full_img","vehicle_net_cert_no",
                    "temporary_dispatch_mark", "temporary_dispatch_end_datetime","city_id");
            builder.and();
            builder.in("vehicle_id", vehIdList, Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("queryVehInfo4CacheError", "vehIdSet:{} error:{}", JsonUtil.toJson(vehIdList), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<VehVehiclePO> queryVehInfo4Cache(Set<Long> vehIdSet) {
        List<List<Long>> vehIdListArr = Lists.partition(Lists.newArrayList(vehIdSet), Constant.SQL_REQUEST_ID_LIMIT_ROW_COUNT);
        List<VehVehiclePO> res = Lists.newArrayListWithCapacity(vehIdSet.size());
        for (List<Long> idList : vehIdListArr) {
            res.addAll(doQueryVehInfo4Cache(idList));
        }
        return res;
    }

    @Override
    public Long queryOnlineVehicleId(String vehicleNo) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("vehicle_id");
            builder.equalNullable("vehicle_license", vehicleNo, Types.VARCHAR);
            builder.and();
            builder.equalNullable("vehicle_status", 1, Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            int page = 1;
            int size = 15;
            builder.orderBy("datachange_lasttime",false);
            builder.atPage(page, size);
            List<VehVehiclePO> vehiclePOList = vehVehicleRepo.getDao().query(builder, hints);
            if(CollectionUtils.isEmpty(vehiclePOList)){
                return null;
            }
            return vehiclePOList.get(0).getVehicleId();
        } catch (Exception e) {
            Map<String,String> tags = new HashMap<>();
            tags.put("vehicleNo",vehicleNo);
            logger.info("VehicleRepositoryImpl_queryOnlineVehicleId_excep",vehicleNo,tags);
            logger.error("VehicleRepositoryImpl_queryOnlineVehicleId_excep",e);
            throw new BizException("VehicleRepositoryImpl_queryOnlineVehicleId_excep");
        }
    }

    @Override
    public List<VehVehiclePO> queryRandomVehicleId(Long supplierId,Long cityId,int page,int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("vehicle_id");
            builder.equalNullable("supplier_id", supplierId, Types.INTEGER);
            builder.and();
            builder.equalNullable("vehicle_status", 1, Types.INTEGER);
            builder.and();
            builder.equalNullable("city_id", cityId, Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("datachange_lasttime",false);
            builder.atPage(page, pageSize);
            List<VehVehiclePO> vehiclePOList = vehVehicleRepo.getDao().query(builder, hints);
            return vehiclePOList;
        } catch (Exception e) {
            Map<String,String> tags = new HashMap<>();
            tags.put("supplierId",supplierId.toString());
            logger.info("VehicleRepositoryImpl_queryRandomVehicleId_excep",supplierId,tags);
            logger.error("VehicleRepositoryImpl_queryRandomVehicleId_excep",e);
            throw new BizException("VehicleRepositoryImpl_queryRandomVehicleId_excep");
        }
    }

    @Override
    public List<VehVehiclePO> queryVehicleByPage(Long supplierId, List<Long> cityIdList, long id) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equalNullable("supplier_id", supplierId, Types.INTEGER);
            if (!CollectionUtils.isEmpty(cityIdList)) {
                builder.and().in("city_id", cityIdList, Types.INTEGER);
            }
            builder.and().greaterThan("vehicle_id", id, Types.BIGINT);
            builder.orderBy("vehicle_id", true);
            builder.and().equal("active", Boolean.TRUE, Types.BIT);
            builder.atPage(1, 100);
            return vehVehicleRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            Map<String, String> tags = new HashMap<>();
            tags.put("supplierId", supplierId.toString());
            logger.info("VehicleRepositoryImpl_queryRandomVehicleId_excep", supplierId, tags);
            logger.error("VehicleRepositoryImpl_queryRandomVehicleId_excep", e);
            throw new BizException("VehicleRepositoryImpl_queryRandomVehicleId_excep");
        }
    }

    @Override
    public int queryRandomVehicleIdCount(Long supplierId, Long cityId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equalNullable("supplier_id", supplierId, Types.INTEGER);
            builder.and();
            builder.equalNullable("vehicle_status", 1, Types.INTEGER);
            builder.and();
            builder.equalNullable("city_id", cityId, Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            Map<String,String> tags = new HashMap<>();
            tags.put("supplierId",supplierId.toString());
            tags.put("cityId",cityId.toString());
            logger.info("VehicleRepositoryImpl_queryRandomVehicleIdCount_excep",supplierId,tags);
            logger.error("VehicleRepositoryImpl_queryRandomVehicleIdCount_excep",e);
            throw new BizException("VehicleRepositoryImpl_queryRandomVehicleIdCount_excep");
        }
    }

    @Override
    public List<VehVehiclePO> queryVehByVehicleLicense(String vehicleLicense) {
        if(StringUtils.isEmpty(vehicleLicense)){
            return Collections.emptyList();
        }
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("vehicle_license",vehicleLicense,Types.VARCHAR);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return vehVehicleRepo.getDao().query(builder,hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int queryCountQunarImgList(List<Long> vehicleIds) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<Long> builder = new FreeSelectSqlBuilder<>();
        builder.simpleType().requireSingle().nullable();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT count(1) FROM veh_vehicle WHERE active = 1 and vehicle_id <= 366309 ");
            StatementParameters parameters = new StatementParameters();
            builder.setTemplate(sqlStr.toString());
            return vehVehicleRepo.getQueryDao().query(builder, parameters, hints).intValue();
        }catch (Exception e){
            return 0;
        }
    }

    @Override
    public List<VehVehiclePO> queryQunarImgList(List<Long> vehicleIds, int pageNo,int pagesise) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("vehicle_id","net_tans_ctfct_img","vehicle_certi_img","vehicle_full_img","vehicle_front_img","vehicle_back_img","vehicle_trunk_img");
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            if(!CollectionUtils.isEmpty(vehicleIds)){
                builder.and().in("vehicle_id",vehicleIds,Types.BIGINT);
            }else {
                builder.and().lessThanEquals("vehicle_id",366309L,Types.BIGINT);
            }
            builder.atPage(pageNo,pagesise);
            return vehVehicleRepo.getDao().query(builder,hints);
        }catch (Exception e){
            return Lists.newArrayList();
        }
    }

    @Override
    public int updateDrvImgQToC(VehVehiclePO vehVehiclePO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if(StringUtils.isEmpty(vehVehiclePO.getNetTansCtfctImg()) && StringUtils.isEmpty(vehVehiclePO.getVehicleCertiImg()) && StringUtils.isEmpty(vehVehiclePO.getVehicleFullImg()) &&
                    StringUtils.isEmpty(vehVehiclePO.getVehicleFrontImg()) && StringUtils.isEmpty(vehVehiclePO.getVehicleBackImg())&& StringUtils.isEmpty(vehVehiclePO.getVehicleTrunkImg())){
                return 0;
            }
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("update veh_vehicle set modify_user = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "modify_user", Types.VARCHAR,TmsTransportConstant.TMS_DEFAULT_USERNAME);
            if(!StringUtils.isEmpty(vehVehiclePO.getNetTansCtfctImg())){
                sqlStr.append(", net_tans_ctfct_img = ? ");
                parameters.set(i++, "net_tans_ctfct_img", Types.VARCHAR,vehVehiclePO.getNetTansCtfctImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleCertiImg())){
                sqlStr.append(", vehicle_certi_img = ? ");
                parameters.set(i++, "vehicle_certi_img", Types.VARCHAR,vehVehiclePO.getVehicleCertiImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleFullImg())){
                sqlStr.append(", vehicle_full_img = ? ");
                parameters.set(i++, "vehicle_full_img", Types.VARCHAR,vehVehiclePO.getVehicleFullImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleFrontImg())){
                sqlStr.append(", vehicle_front_img = ? ");
                parameters.set(i++, "vehicle_front_img", Types.VARCHAR,vehVehiclePO.getVehicleFrontImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleBackImg())){
                sqlStr.append(", vehicle_back_img = ? ");
                parameters.set(i++, "vehicle_back_img", Types.VARCHAR,vehVehiclePO.getVehicleBackImg());
            }
            if(!StringUtils.isEmpty(vehVehiclePO.getVehicleTrunkImg())){
                sqlStr.append(", vehicle_trunk_img = ? ");
                parameters.set(i++, "vehicle_trunk_img", Types.VARCHAR,vehVehiclePO.getVehicleTrunkImg());
            }
            sqlStr.append(" where vehicle_id = ? ");
            parameters.set(i++, "vehicle_id", Types.BIGINT, vehVehiclePO.getVehicleId());
            builder.setTemplate(sqlStr.toString());
            return vehVehicleRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("updateDrvImgQToC error", e);
        }
    }

    @Override
    public int countDiscardVeh(QueryDiscardVehicleListSOARequestType requestType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.equal("supplier_id", requestType.getSupplierId(), Types.BIGINT);
            if(!StringUtils.isEmpty(requestType.getVehicleLicense())){
                builder.and().equal("vehicle_license", requestType.getVehicleLicense(), Types.VARCHAR);
            }
            builder.and().equal("active",Boolean.FALSE,Types.BIT);
            return vehVehicleRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e) {
            throw new BaijiRuntimeException("countDiscardVeh error", e);
        }
    }

    @Override
    public List<VehVehiclePO> queryDiscardVeh(QueryDiscardVehicleListSOARequestType requestType) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.equal("supplier_id", requestType.getSupplierId(), Types.BIGINT);
            if(!StringUtils.isEmpty(requestType.getVehicleLicense())){
                builder.and().equal("vehicle_license", requestType.getVehicleLicense(), Types.VARCHAR);
            }
            builder.and().equal("active",Boolean.FALSE,Types.BIT);
            builder.orderBy("datachange_lasttime",false);
            builder.atPage(requestType.getPaginator().getPageNo(), requestType.getPaginator().getPageSize());
            return vehVehicleRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            throw new BaijiRuntimeException("queryDiscardVeh error", e);
        }
    }

    @Override
    public int updateTemporaryDispatchMark(List<Long> vehicleIds,Boolean active, String modifyUser) {
        if(CollectionUtils.isEmpty(vehicleIds)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update veh_vehicle set temporary_dispatch_mark = ?,modify_user = ? where vehicle_id in (?) and active = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "temporary_dispatch_mark", Types.INTEGER, TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
            parameters.setInParameter(i++, "vehicle_id", Types.BIGINT, vehicleIds);
            parameters.set(i++, "active", Types.BIT, active);
            return vehVehicleRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<Long> queryDiscardTemVeh(String vehicleLicense) {
        if(StringUtils.isEmpty(vehicleLicense)){
            return Lists.newArrayList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT vehicle_id FROM veh_vehicle WHERE vehicle_license = ? and temporary_dispatch_mark = 1 and active = 0 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "vehicle_license", Types.VARCHAR,vehicleLicense);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(vehVehiclePODalRowMapper);
            builder.simpleType().nullable();
            return vehVehicleRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            logger.error("queryDiscardTemVeh", "error:{}", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<Long> queryTemporaryDispatchVehicle(Timestamp nowDate) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<Long>> builder = new FreeSelectSqlBuilder<>();
        try {
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT vehicle_id FROM veh_vehicle WHERE temporary_dispatch_mark = 1 and active = 1 and  temporary_dispatch_end_datetime <= ?");
            StatementParameters parameters = new StatementParameters();
            parameters.set(1, "temporary_dispatch_end_datetime", Types.TIMESTAMP,nowDate);
            builder.setTemplate(sqlStr.toString());
            builder.mapWith(vehVehiclePODalRowMapper);
            builder.simpleType().nullable();
            return vehVehicleRepo.getQueryDao().query(builder, parameters, hints);
        }catch (Exception e){
            logger.error("queryTemporaryDispatchVehicle", "error:{}", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<Long> queryVehicleId(List<String> vehicleLicense, Long supplierId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<List<Long>> selectSqlBuilder = new FreeSelectSqlBuilder();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("SELECT vehicle_id FROM veh_vehicle WHERE vehicle_license in(?) and supplier_id=? and active = ?");

            StatementParameters parameters = new StatementParameters();
            parameters.setInParameter(1, "vehicle_license", Types.VARCHAR,vehicleLicense);
            parameters.set(2,"supplier_id",Types.BIGINT,supplierId);
            parameters.set(3,"active", Types.BIT, Boolean.TRUE);

            selectSqlBuilder.setTemplate(sqlStr.toString());
            selectSqlBuilder.mapWith(vehVehiclePODalRowMapper);
            selectSqlBuilder.simpleType().nullable();
            List<Long> result = vehVehicleRepo.getQueryDao().query(selectSqlBuilder,parameters,hints);
            return result;
        }catch (Exception e){
            logger.error("queryVehicleId_ex", "queryVehicleId_ex", e,new HashMap<>());
            return Lists.newArrayList();
        }
    }

    @Override
    public List<VehVehiclePO> queryVehicleByIds(List<Long> vehicleIds) {
        try{
            if (CollectionUtils.isEmpty(vehicleIds)) {
                return Collections.emptyList();
            }
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("vehicle_id",vehicleIds,Types.BIGINT);
            return vehVehicleRepo.getDao().query(builder,hints);
        }catch (Exception e){
            logger.error("queryVehicleByIds_ex", "queryVehicleByIds_ex", e,new HashMap<>());
            return Lists.newArrayList();
        }
    }

    @SneakyThrows
    @Override
    public List<VehVehiclePO> queryVehicleListByVehicleIdFromAndPage(Long vehicleId, int pageNo, int pageSize) {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.greaterThan("vehicle_id", vehicleId, Types.BIGINT);
        builder.atPage(pageNo, pageSize);
        builder.orderBy("vehicle_id", true);
        return vehVehicleRepo.query(builder);
    }

    @Override
    public int updateVehicleAgeType(Long vehicleId, String code, String modifyUser) {
        if (vehicleId == null || StringUtil.isEmpty(code)) {
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update veh_vehicle set vehicle_age_type = ?,modify_user = ? where vehicle_id in (?) and active = ?  ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "vehicle_age_type", Types.VARCHAR, code);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser) ? TmsTransportConstant.TMS_DEFAULT_USERNAME : modifyUser);
            parameters.setInParameter(i++, "vehicle_id", Types.BIGINT, Arrays.asList(vehicleId));
            parameters.set(i++, "active", Types.BIT, 1);
            return vehVehicleRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int initVehicleAgeType(List<Long> vehicleIdList) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("UPDATE veh_vehicle SET vehicle_age_type = '' WHERE vehicle_id IN ( ?)");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setInParameter(i++, "vehicle_id", Types.BIGINT, vehicleIdList);
            return vehVehicleRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<VehVehiclePO> queryVehicleById(long id) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.select("vehicle_id");
            builder.and().greaterThan("vehicle_id", id, Types.BIGINT);
            builder.and().in("vehicle_age_type", Arrays.asList("not_overage","unknown"), Types.VARCHAR);
            builder.orderBy("vehicle_id", true);
            builder.atPage(1, 100);
            return vehVehicleRepo.getDao().query(builder, hints);
        } catch (Exception e) {
            logger.error("VehicleRepositoryImpl_queryRandomVehicleId_excep", e);
            throw new BizException("VehicleRepositoryImpl_queryRandomVehicleId_excep");
        }
    }

    @Override
    public boolean updateOfflineVehicleById(VehVehiclePO vehVehiclePO) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            UpdateSqlBuilder updateSqlBuilder = new UpdateSqlBuilder()
                    .update("vehicle_type_id", vehVehiclePO.getVehicleTypeId(), Types.BIGINT);
            if (!StringUtils.isEmpty(vehVehiclePO.getModifyUser())) {
                updateSqlBuilder.update("modify_user", vehVehiclePO.getModifyUser(), Types.VARCHAR);
            }

            updateSqlBuilder
                    .equal("vehicle_id", vehVehiclePO.getVehicleId(), Types.BIGINT).and()
                    .equal("vehicle_status", CommonEnum.VehicleStatusEnum.OFFLINE.getValue(), Types.TINYINT);

            return vehVehicleRepo.getDao().update(updateSqlBuilder, hints) == 1;
        } catch (Exception e) {
            throw new BaijiRuntimeException("updateVehicleById error", e);
        }
    }

}
