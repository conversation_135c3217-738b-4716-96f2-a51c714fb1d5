package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.dal.*;

import java.util.*;

public interface TspTransportGroupDriverRelationRepository {

    /**
     * 更新报名状态
     * @param workShiftIds
     * @param applyStatus
     * @param modifyUser
     * @return
     */
    int updateApplyStatus(List<Long> workShiftIds,Integer applyStatus,String modifyUser);

    /**
     * 更新报名状态
     * @param workShiftId
     * @param drvIds
     * @param applyStatus
     * @return
     */
    int updateApplyStatus(Long workShiftId,List<Long> drvIds,Integer applyStatus,String modifyUser);

    /**
     * 查询班次的司机报名情况
     * @param workShiftIds
     * @return
     */
    List<ApplyDriverRelationDetailPO> queryRelationDrvList(List<Long> workShiftIds,List<Integer> applyStatus);

    /**
     * 查询已关联司机列表
     * @param param
     * @return
     */
    List<DriverRelationDetailPO> queryRelationDrvList(QueryDriverRelationDetailParam param);

    /**
     * 统计已关联司机数量
     * @param param
     * @return
     */
    int queryRelationDrvCount(QueryDriverRelationDetailParam param);

    /**
     * 查询未关联司机列表
     * @param param
     * @return
     */
    List<DriverRelationDetailPO> queryNotRelationDrvList(QueryDriverRelationDetailParam param);

    /**
     * 统计未关联司机数量
     * @param param
     * @return
     */
    int queryNotRelationDrvCount(QueryDriverRelationDetailParam param);

    /**
     * 批量绑定关系
     * @param param
     * @return
     */
    int insetBatch(InsertBatchRelationParam param);

    /**
     * 批量解绑关系
     * @param param
     * @return
     */
    int updateRelationStatus(UpdateRelationStatusParam param);

    /**
     * 获取已绑定该运力组的车辆
     * */
    List<Long> queryRelationDrvIdListByTransportGroupId(Long transportGroupId);

    /**
     * 司机对应的运力组ID
     * @param drvIds
     * @return
     */
    List<TspTransportGroupDriverRelationPO> queryTransportGroupIdByDrvIds(List<Long> drvIds) ;

    /**
     * 查询有效的司机与运力组绑定关系（这里的有效为运力组为上线状态）
     * @param drvIds
     * @return
     */
    List<TspTransportGroupDriverRelationPO> queryTransportGroupDriverRelation(List<Long> drvIds) ;
    /**
     * 司机解绑运力组
     * @param drvIds
     * @param operator
     * @return
     */
    int unFreezeTransportGroup(List<Long> drvId,List<Long> transportGroupIds, String operator);

    /**
     * 运力组绑定的司机
     * */
    List<TspTransportGroupDriverRelationPO> queryRelationListByTransportGroupId(Long transportGroupId);

    int updateDriverRelationApplyStatus(Long transportGroupId,String modifyUser,Integer applyStatus);

    /**
     * 通过班次查询报名成功的司机
     * @param workIds
     * @return
     */
    List<TspTransportGroupDriverRelationPO> queryRelationListByWorkShiftIds(List<Long> workIds);

    /**
     * 查询已绑定该运力组的司机id
     * */
    List<Long> queryRelationDrvListByTransportGroupIdAndDrvIdList(Long transportGroupId, List<Long> drvIdList,Long workShiftId);

    /**
     * 运力组查询绑定的司机
     * */
    List<TspTransportGroupDriverRelationPO> queryRelationListByTransportGroupIds(List<Long> transportGroupId,Long workId);

    /**
     * 查询有效运力已绑定运力id
     * */
    List<Long> queryValidRelationTransportGroupId();

    /**
     * 查询司机运力组关系
     * */
    List<TspTransportGroupDriverRelationPO> queryDriverGroupRelation(QueryDriverGroupRelationConditionDTO conditionDTO);

    /**
     * 根据司机ID列表查询运力组关系
     * @param drvIds
     * @return
     */
    List<TspTransportGroupDriverRelationPO> queryDriverGroupRelation(List<Long> drvIds);

    /**
     * 查询运力组关系司机
     * */
    List<Long> queryRelationDrvIdList(QueryDriverRelationDetailParam param);

    /**
    　* @description: 查询报名成功的数据
    　* <AUTHOR>
    　* @date 2022/12/29 17:26
    */
    int queryApplySuccessTransportCount(List<Long> drvIds);


    /**
     　* @description: 查询报名成功的数据
     　* <AUTHOR>
     　* @date 2022/12/29 17:26
     */
    List<TspTransportGroupDriverRelationPO> queryApplySuccessTransportList(List<Long> drvIds,Integer pageNo,Integer pageSize);

    /**
    　* @description: 剔除司机，将报名状态置为剔除
    　* <AUTHOR>
    　* @date 2022/12/30 13:50
    */
    int eliminateApplySuccessDrv(List<Long> ids,String modifyUser,Integer applyStatus);

    /**
    　* @description: 剔除置为已报名
    　* <AUTHOR>
    　* @date 2023/1/31 9:35
    */
    int updatEeliminateStatusByDrvId(List<Long> drvIds,String modifyUser);

    /**
     * 查询已关联司机列表-派遣
     * @param param
     * @return
     */
    List<DriverRelationDetailPO> queryDispatchRelationDrvList(QueryDriverRelationDetailParam param);

    /**
     * 统计已关联司机数量-派遣
     * @param param
     * @return
     */
    int queryDispatchRelationDrvCount(QueryDriverRelationDetailParam param);

    /**
     * 查询未关联司机列表-派遣
     * @param param
     * @return
     */
    List<DriverRelationDetailPO> queryDispatchNotRelationDrvList(QueryDriverRelationDetailParam param);

    /**
     * 统计未关联司机数量-派遣
     * @param param
     * @return
     */
    int queryDispatchNotRelationDrvCount(QueryDriverRelationDetailParam param);

    //查询派遣司机
    List<Long> queryDispatchRelationDrvIdList(QueryDriverRelationDetailParam param);

    /**
     * 查询司机id
     * */
    List<Long> queryDrvIdListByTransportGroups(List<Long> transportGroupIdList);

    int batchRelationGroup(List<Long> transportIds,Long drvId);

}
