package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OCRConfig;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NewOCRDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrRecognitionResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import org.springframework.stereotype.Component;

import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;
import java.util.Map;

@Component
@Data
public class OcrConfig {

    @QMapConfig(value = "ocr.new.compliance.properties", key = "ocrBackConfig", defaultValue = "")
    private String ocrBackConfig;

    @QMapConfig(value = "ocr.new.compliance.properties", key = "ocrRecognitionConfig", defaultValue = "")
    private String ocrRecognitionConfig;

    @QMapConfig(value = "ocr.new.compliance.properties", key = "ocrComplianceConfig", defaultValue = "")
    private String ocrComplianceConfig;

    @QMapConfig(value = "ocr.new.compliance.properties", key = "ocrUseConfig", defaultValue = "")
    private String ocrUseConfig;

    public Map<String, List<RequiredFieldDTO>> getBackFieldMap() {
        return JsonUtil.fromJson(ocrBackConfig, new TypeReference<Map<String, List<RequiredFieldDTO>>>() {});
    }

    public List<OcrRecognitionResultDTO> getOcrRecognitionResultList() {
        return JsonUtil.fromJson(ocrRecognitionConfig, new TypeReference<List<OcrRecognitionResultDTO>>() {});
    }


    public List<OcrComplianceDTO> getOcrComplianceList() {
        return JsonUtil.fromJson(ocrComplianceConfig, new TypeReference<List<OcrComplianceDTO>>() {});

    }

    public List<NewOCRDTO> getNewOcrUseList() {
        return JsonUtil.fromJson(ocrUseConfig, new TypeReference<List<NewOCRDTO>>() {});
    }
}
