package com.ctrip.dcs.tms.transport.infrastructure.gateway.impl;

import com.ctrip.dcs.geo.domain.repository.TimeZoneRepository;
import com.ctrip.dcs.geo.domain.value.TransformTimeZoneParameter;
import com.ctrip.dcs.geo.domain.value.TransformTimeZoneResult;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.GeoGateway;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Component
public class GeoGatewayImpl implements GeoGateway {
    public static final Long BEIJING_CITY_ID = 1L;

    @Resource
    TimeZoneRepository timeZoneRepository;

    @Override
    public LocalDateTime getLocalCurrentTime(Long targetCityId) {
        return convertToLocalTime(LocalDateTime.now(), targetCityId);
    }

    protected LocalDateTime convertToLocalTime(LocalDateTime sourceDateTime, Long targetCityId) {
        if (targetCityId == null) {
            return null;
        }
        TransformTimeZoneResult result = transform(sourceDateTime, BEIJING_CITY_ID, targetCityId);
        return result != null ? result.getTargetDateTime() : null;
    }

    protected TransformTimeZoneResult transform(LocalDateTime sourceDateTime, Long sourceCityId, Long targetCityId) {
        if (sourceDateTime == null) {
            return null;
        }
        TransformTimeZoneParameter request = TransformTimeZoneParameter.builder()
                .sourceCityId(sourceCityId)
                .sourceDateTime(sourceDateTime)
                .targetCityId(targetCityId)
                .build();
        return timeZoneRepository.transform(request);
    }

}
