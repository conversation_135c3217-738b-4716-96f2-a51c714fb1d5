package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import com.ctrip.dcs.tms.transport.api.model.PoiDTO;

import java.util.*;

/**
 * 运力组查询参数
 * <AUTHOR>
 * @Date 2020/3/10 17:40
 */
public class QueryTransportGroupModel {

    //运力组id
    private Long transportGroupId;
    //skuid
    private Long skuId;
    //运力组模式
    private Integer transportGroupMode;
    //供应商id
    private Long supplierId;
    //运力组状态
    private Integer groupStatus;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 坐标系类型
     */
    private String poiType;

    private List<Long> skuIds;

    private String carPlaceId;

    private String poiRef;
    /**
     * @see com.ctrip.dcs.tms.transport.infrastructure.common.constant.TransportDispatchEnum
     */
    private Integer filterDispatchOnly;

    private List<PoiDTO> poiList;

    public Integer getFilterDispatchOnly() {
        return filterDispatchOnly;
    }

    public List<PoiDTO> getPoiList() {
        return poiList;
    }

    public void setPoiList(List<PoiDTO> poiList) {
        this.poiList = poiList;
    }

    public void setFilterDispatchOnly(Integer filterDispatchOnly) {
        this.filterDispatchOnly = filterDispatchOnly;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getPoiType() {
        return poiType;
    }

    public void setPoiType(String poiType) {
        this.poiType = poiType;
    }

    public Integer getGroupStatus() {
        return groupStatus;
    }

    public void setGroupStatus(Integer groupStatus) {
        this.groupStatus = groupStatus;
    }

    public Long getTransportGroupId() {
        return transportGroupId;
    }

    public void setTransportGroupId(Long transportGroupId) {
        this.transportGroupId = transportGroupId;
    }

    public Integer getTransportGroupMode() {
        return transportGroupMode;
    }

    public void setTransportGroupMode(Integer transportGroupMode) {
        this.transportGroupMode = transportGroupMode;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getCarPlaceId() {
        return carPlaceId;
    }

    public void setCarPlaceId(String carPlaceId) {
        this.carPlaceId = carPlaceId;
    }

    public String getPoiRef() {
        return poiRef;
    }

    public void setPoiRef(String poiRef) {
        this.poiRef = poiRef;
    }
}
