package com.ctrip.dcs.tms.transport.infrastructure.gateway;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;

import java.util.List;

public interface TmsDrvInactiveReasonGateway {

  /**
   * 根据司机ID和原因代码删除未激活原因，并返回是否（已经消减掉所有未激活卡控原因）可以上线
   *
   * @param drvId      司机ID
   * @param reasonCode 原因代码
   * @param modifyUser 修改人
   * @return 是否（已经消减掉所有未激活卡控原因）可以上线
   */
  boolean enableActiveAfterDeleteReasonByCode(Long drvId, List<Integer> reasonCode, String modifyUser);

  /**
   * 插入未激活原因
   *
   * @param po 未激活原因
   */
  void insertWhenNotExist(TmsDrvInactiveReasonPO po);

}
