package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.annotation.*;

import javax.persistence.*;
import java.sql.*;


/**
 * <AUTHOR>
 * @date 2020-02-20
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_mod_record")
public class TmsModRecordPO implements DalPojo {

	/**
	 * 主键
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

	/**
	 * 实体主键
	 */
	@Column(name = "rrd_id")
	@Type(value = Types.BIGINT)
	private Long rrdId;

	/**
	 * 实体类别(1运力组,2司机,3车辆,4招募)
	 */
	@Column(name = "rrd_type")
	@Type(value = Types.TINYINT)
	private Integer rrdType;

	/**
	 * 变更类别(0新增,1修改)
	 */
	@Column(name = "mod_type")
	@Type(value = Types.TINYINT)
	private Integer modType;

	/**
	 * 变更内容
	 */
	@Column(name = "mod_content")
	@Type(value = Types.VARCHAR)
	private String modContent;

	/**
	 * 删除标志(1表示启用，0表示删除)
	 */
	@Column(name = "active")
	@Type(value = Types.BIT)
	private Boolean active;

	/**
	 * 创建时间
	 */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

	/**
	 * 创建人
	 */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

	/**
	 * 修改人
	 */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	/**
	 * 更新时间
	 */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 供应商ID
	 */
	@Column(name = "supplier_id")
	@Type(value = Types.BIGINT)
	private Long supplierId;

	/**
	 * 上云UDl
	 */
	@Column(name = "provider_data_location")
	@Type(value = Types.VARCHAR)
	private String providerDataLocation;

	public String getProviderDataLocation() {
		return providerDataLocation;
	}

	public void setProviderDataLocation(String providerDataLocation) {
		this.providerDataLocation = providerDataLocation;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRrdId() {
		return rrdId;
	}

	public void setRrdId(Long rrdId) {
		this.rrdId = rrdId;
	}

	public Integer getRrdType() {
		return rrdType;
	}

	public void setRrdType(Integer rrdType) {
		this.rrdType = rrdType;
	}

	public Integer getModType() {
		return modType;
	}

	public void setModType(Integer modType) {
		this.modType = modType;
	}

	public String getModContent() {
		return modContent;
	}

	public void setModContent(String modContent) {
		this.modContent = modContent;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public Long getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Long supplierId) {
		this.supplierId = supplierId;
	}
}
