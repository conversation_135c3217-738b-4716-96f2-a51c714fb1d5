package com.ctrip.dcs.tms.transport.infrastructure.port.repository.model;

import java.sql.*;
import java.util.*;

public class QueryDrvByMuSelDO {

    private List<Long> cityIdList;
    private List<Long> vehicleTypeIdList;
    private List<Integer> drvStatusList;
    private List<Integer> coopModeList;
    private List<Long> countryIdList;
    private List<Long> supplierIdList;
    private String drvLanguage;
    private List<Long> drvIdList;
    private List<Long> transportGroupIdList;
    private Timestamp registStartDate;
    private Timestamp registEndDate;
    private Integer page;
    private Integer pageSize;
    private List<Integer> proLineIdList;

    public List<Integer> getProLineIdList() {
        return proLineIdList;
    }

    public void setProLineIdList(List<Integer> proLineIdList) {
        this.proLineIdList = proLineIdList;
    }

    public List<Long> getTransportGroupIdList() {
        return transportGroupIdList;
    }

    public void setTransportGroupIdList(List<Long> transportGroupIdList) {
        this.transportGroupIdList = transportGroupIdList;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<Long> getCityIdList() {
        return cityIdList;
    }

    public void setCityIdList(List<Long> cityIdList) {
        this.cityIdList = cityIdList;
    }

    public List<Long> getVehicleTypeIdList() {
        return vehicleTypeIdList;
    }

    public void setVehicleTypeIdList(List<Long> vehicleTypeIdList) {
        this.vehicleTypeIdList = vehicleTypeIdList;
    }

    public List<Integer> getDrvStatusList() {
        return drvStatusList;
    }

    public void setDrvStatusList(List<Integer> drvStatusList) {
        this.drvStatusList = drvStatusList;
    }

    public List<Integer> getCoopModeList() {
        return coopModeList;
    }

    public void setCoopModeList(List<Integer> coopModeList) {
        this.coopModeList = coopModeList;
    }

    public List<Long> getCountryIdList() {
        return countryIdList;
    }

    public void setCountryIdList(List<Long> countryIdList) {
        this.countryIdList = countryIdList;
    }

    public List<Long> getSupplierIdList() {
        return supplierIdList;
    }

    public void setSupplierIdList(List<Long> supplierIdList) {
        this.supplierIdList = supplierIdList;
    }

    public String getDrvLanguage() {
        return drvLanguage;
    }

    public void setDrvLanguage(String drvLanguage) {
        this.drvLanguage = drvLanguage;
    }

    public List<Long> getDrvIdList() {
        return drvIdList;
    }

    public void setDrvIdList(List<Long> drvIdList) {
        this.drvIdList = drvIdList;
    }

    public Timestamp getRegistStartDate() {
        return registStartDate;
    }

    public void setRegistStartDate(Timestamp registStartDate) {
        this.registStartDate = registStartDate;
    }

    public Timestamp getRegistEndDate() {
        return registEndDate;
    }

    public void setRegistEndDate(Timestamp registEndDate) {
        this.registEndDate = registEndDate;
    }
}
