package com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache;

import com.ctrip.arch.canal.ColumnData;
import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ModRecordConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.frt.xresource.framework.utility.redis.RedisProxy;
import com.ctrip.frt.xresource.framework.utility.redis.ResourceFillDataAction;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import com.ctriposs.baiji.rpc.common.HasResponseStatus;
import com.ctriposs.baiji.rpc.common.types.ExtensionType;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.databind.JavaType;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.unidal.cat.traceContext.TraceContext;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public abstract class AbstraceCacheHandler implements CacheHandler {

  /**
   * 维护表对应变更记录的RrdId取值字段
   */
  public static Map<String,String> tableCacheIDMap = Maps.newHashMap();

  static {
    /**
     * 初始化每张表对应的变更记录实体id取值字段名称（如果不设置，则默认为表的主键）
     */
    tableCacheIDMap.put(ModRecordConstant.TableName.tspTransportGroup,"transport_group_id");
    tableCacheIDMap.put(ModRecordConstant.TableName.tspIntoOrderConfig,"transport_group_id");
    tableCacheIDMap.put(ModRecordConstant.TableName.tspTransportGroupDriverRelation,"drv_id");
    tableCacheIDMap.put(ModRecordConstant.TableName.tspTransportGroupSkuAreaRelation,"sku_id");
    tableCacheIDMap.put(ModRecordConstant.TableName.tmsDrvDispatchRelation,"drv_id");
    tableCacheIDMap.put(ModRecordConstant.TableName.drvDriverLeave,"drv_id");
  }

  protected String getBusinessId(DataChange dataChange) {
    for (ColumnData columnData : dataChange.getAfterColumnList()) {
      if (tableCacheIDMap.get(dataChange.getTableName()).equalsIgnoreCase(columnData.getName())) {
        return Objects.isNull(columnData.getValue()) ? null : String.valueOf(columnData.getValue());
      }
    }
    throw new RuntimeException("Business id not found");
  }

  /**
   * 获取缓存名称
   * @return
   */
  protected abstract String getCacheName();

  protected RedisProxy getProxy() {
    {
      try {
        return RedisProxy.getInstance("dcs_tms","dcs_tms", getCacheName());
      } catch (Exception e) {
        throw new RuntimeException(e);
      }
    }
  }

  protected void doRefresh(String key) {
    Cat.logEvent(Constant.EventType.CACHE, Constant.EventName.CACHE_EVENT_REFRESH + "_" + getCacheName() + "_" + key);
    if (StringUtil.isEmpty(key)) {
      return;
    }
    getProxy().refreshCache(Sets.newHashSet(key), getFillDataAction(), false);
  }

  protected abstract ResourceFillDataAction getFillDataAction();

  public <T> List<T> mgetHit(List<String> keyList, Class<?> cacheClazz) {
    setExtension();
    return getProxy().mgetHit(keyList, cacheClazz, getFillDataAction());
  }

  public <T> List<T> mgetHit(List<String> keys, final JavaType javaType) {
    setExtension();
    return getProxy().mgetHit(keys, javaType, getFillDataAction(), false);
  }

  protected void setExtension() {
    TraceContext context = Cat.getTraceContext(true);
    ServiceExecuteContext.getCurrent().getHickwallMetric().tagged(Constant.EventType.CACHE, getCacheName());
    context.add(Constant.EventType.CACHE, Optional.ofNullable(context.get(Constant.EventType.CACHE)).map(s -> s + "," + getCacheName()).orElse(getCacheName()));
  }

  public static void setExtension(HasResponseStatus resp) {
    String cache = Cat.getTraceContext(true).get(Constant.EventType.CACHE);
    if (StringUtils.isNotEmpty(cache)) {
      ExtensionType extensionType = new ExtensionType();
      extensionType.setContentType(Constant.EventType.CACHE);
      extensionType.setValue(cache);
      resp.getResponseStatus().getExtension().add(extensionType);
    }
  }
}
