package com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc;

import com.ctrip.igt.infrastructureservice.executor.contract.*;

public interface InfrastructureServiceClientProxy {

    SendMesByPhoneResponseType sendMessageByPhone(SendMesByPhoneRequestType request);

    CheckMobilePhoneCodeResponseType checkPhoneCode(CheckMobilePhoneCodeRequestType request);

    SendEmailResponseType sendEmail(SendEmailRequestType request);

    SendMessageResponseType sendMessage(SendMessageRequestType request);

    DrivingLicenseResponseType drivingLicense(DrivingLicenseRequestType requestType);

    VehicleLicenseResponseType vehicleLicense(VehicleLicenseRequestType requestType);

    GetFaceAuthSignResponseType getFaceAuthSign(GetFaceAuthSignRequestType request);

    GetFaceResultResponseType getFaceResult(GetFaceResultRequestType request);

    PlateLicenseResponseType plateLicense(PlateLicenseRequestType requestType);

    String nameTranslate(String driverName);

    CarOCRResponseType carOCR(CarOCRRequestType request);

}