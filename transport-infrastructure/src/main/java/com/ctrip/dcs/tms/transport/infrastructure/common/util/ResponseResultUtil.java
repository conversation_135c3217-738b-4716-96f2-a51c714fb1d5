package com.ctrip.dcs.tms.transport.infrastructure.common.util;

import com.ctrip.igt.HasIGTResponseResult;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.Collection;
import java.util.Objects;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.EMPTY;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.NOT_EMPTY;

/**
 * 返回值检查Util
 *
 * <AUTHOR> ZhangZhen
 * @create 2023/2/12 19:36
 */
public class ResponseResultUtil {

    /**
     * 判定此次Soa结果
     */
    public static boolean checkResponseResult(HasIGTResponseResult responseResult) {
        if (responseResult == null || responseResult.getResponseResult() == null) {
            return Boolean.FALSE;
        }
        if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equalsIgnoreCase(responseResult.getResponseResult().getReturnCode())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    public static <T> Result<T> failed(String errorCode, String errorMsg) {
        return Result.Builder.<T>newResult().fail().withCode(errorCode).withMsg(errorMsg).build();
    }

    public static <T> Result<T> success() {
        return Result.Builder.<T>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build();
    }

    public static <T> Result<T> success(T t) {
        return Result.Builder.<T>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).withData(t).build();
    }

    public static <T, R extends HasIGTResponseResult> R response(R response, Result<T> result) {
        if (result.isSuccess()) {
            return ServiceResponseUtils.success(response);
        }else {
            return ServiceResponseUtils.fail(response, result.getCode(),result.getMsg());
        }
    }

    public static void logQueryResult(Object obj) {
        Cat.logEvent(PlatformUtil.getMethodName(), isEmpty(obj) ? EMPTY : NOT_EMPTY);
    }

    /**
     * 判断对象是否为空、空字符串或空列表
     *
     * @param obj 要检查的对象
     * @return 如果对象为空、空字符串或空列表，则返回 true；否则返回 false
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }

        if (obj instanceof String) {
            return StringUtils.isBlank((String) obj);
        }

        if (obj instanceof Collection) {
            return CollectionUtils.isEmpty((Collection<?>) obj);
        }

        return false;
    }
}
