package com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-06-08
 */
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_overseas_ocr_record")
public class OverseasOcrRecordPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 请求图片
     */
	@Column(name = "request_img")
	@Type(value = Types.VARCHAR)
	private String requestImg;

    /**
     * 请求城市
     */
	@Column(name = "request_city_id")
	@Type(value = Types.BIGINT)
	private Long requestCityId;

    /**
     * 请求类别(1.司机,2.车辆)
     */
	@Column(name = "request_type")
	@Type(value = Types.TINYINT)
	private Integer requestType;

    /**
     * 返回值
     */
	@Column(name = "response_result")
	@Type(value = Types.VARCHAR)
	private String responseResult;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 操作时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 操作人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getRequestImg() {
		return requestImg;
	}

	public void setRequestImg(String requestImg) {
		this.requestImg = requestImg;
	}

	public Long getRequestCityId() {
		return requestCityId;
	}

	public void setRequestCityId(Long requestCityId) {
		this.requestCityId = requestCityId;
	}

	public Integer getRequestType() {
		return requestType;
	}

	public void setRequestType(Integer requestType) {
		this.requestType = requestType;
	}

	public String getResponseResult() {
		return responseResult;
	}

	public void setResponseResult(String responseResult) {
		this.responseResult = responseResult;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getCreateUser() {
		return createUser;
	}

	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getModifyUser() {
		return modifyUser;
	}

	public void setModifyUser(String modifyUser) {
		this.modifyUser = modifyUser;
	}

}