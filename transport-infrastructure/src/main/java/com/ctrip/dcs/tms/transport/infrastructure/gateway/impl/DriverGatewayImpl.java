package com.ctrip.dcs.tms.transport.infrastructure.gateway.impl;

import com.ctrip.dcs.go.domain.Pair;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.IThreadPoolService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.RedisUtils;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.DriverGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class DriverGatewayImpl implements DriverGateway {

  @Autowired
  DrvDrvierRepository drvDrvierRepository;

  @Autowired
  IThreadPoolService threadPoolService;

  @Autowired
  TmsTransportQconfig tmsTransportQconfig;

  Random random = new Random(1);

  @Override
  public List<Long> queryDrvIdByMarkPage(List<Long> drvIds, Integer temporaryDispatchMark) {
    if (CollectionUtils.isEmpty(drvIds)) {
      return Lists.newArrayList();
    }

    try {
      if (Objects.equals(temporaryDispatchMark, TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode())) {
        List<Long> result = new ArrayList<>();
        // 获取缓存中的正式司机
        List<Long> cachedOfficialResultList = getCachedOfficialDrivers(drvIds);

        Set<Long> cachedOfficialResultSet = new HashSet<>(cachedOfficialResultList);
        List<Long> notCachedOfficialResultList = drvIds.stream()
          .filter(driverId -> !cachedOfficialResultSet.contains(driverId))
          .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(notCachedOfficialResultList)) {
          // 从DB获取
          List<Long> dbResult = getResultData(notCachedOfficialResultList, temporaryDispatchMark);
          // 缓存正式司机
          cacheDrivers(dbResult);
          result.addAll(dbResult);
        }

        result.addAll(cachedOfficialResultList);
        logDriverRemarkResult(drvIds, result);
        return result;
      }
    } catch (Exception e) {
      Cat.logEvent(Constant.EventType.DRIVER, "getTemporaryMarkFailed");
    }

    return getResultData(drvIds, temporaryDispatchMark);
  }

  // 获取缓存中的正式司机
  protected List<Long> getCachedOfficialDrivers(List<Long> drvIds) {
    List<String> drvIdStrings =
      drvIds.stream().distinct().map(this::getTemporaryMarkDriverKey).collect(Collectors.toList());
    List<Long> results =
      RedisUtils.mGet2(drvIdStrings, tmsTransportQconfig.getRedisBatchSize()).stream().filter(NumberUtils::isNumber)
        .map(NumberUtils::toLong).collect(Collectors.toList());
    Cat.logEvent("Dcs-Driver",String.format("Cache-Driver-%s-%s",drvIds.size(),results.size()));
    return results;
  }

  // 异步缓存正式司机
  protected void cacheDrivers(List<Long> drvIds) {
    Transaction tx = Cat.newTransaction("DcsTms", "DriverCacheBatchAction");
    try {
    if (CollectionUtils.isEmpty(drvIds)) {
      return;
    }
      List<Long> distinctDriverIds = drvIds.stream().distinct().collect(Collectors.toList());
      Runnable cacheAction =
        () -> Lists.partition(distinctDriverIds, tmsTransportQconfig.getInQueryBatchSize()).forEach(batch -> {
          HashMap<String, String> map =
            batch.stream().map(e -> Pair.of(this.getTemporaryMarkDriverKey(e), String.valueOf(e)))
              .collect(Collectors.toMap(Pair::getLeft, Pair::getRight, (o1, o2) -> o1, HashMap::new));
      RedisUtils.mSet(map, tmsTransportQconfig.getDriverTempRemarkExpSeconds() + random.nextInt(tmsTransportQconfig.getDriverTempRemarkExpRandomSeconds()));
    });
      threadPoolService.getInQueryDriverPool().submit(cacheAction);
    } catch (Exception e) {
      tx.setStatus(e);
    } finally {
      tx.complete();
      tx.forceAdjustIfCompleted();
    }
  }

  protected String getTemporaryMarkDriverKey(Long drvId) {
    return Constant.DRIVER_TEMPORARY_REMARK + "_" + drvId;
  }

  // 记录日志
  protected void logDriverRemarkResult(List<Long> drvIds, List<Long> result) {
    String eventStatus = drvIds.size() == result.size() ? Constant.EVENT_SUCCESS : Constant.EVENT_FAILED;
    Map<String, String> eventData = new HashMap<>();
    eventData.put("drvIds", drvIds.toString());
    eventData.put("result", result.toString());
    Cat.logEvent(Constant.EventType.DRIVER, "DriverRemarkResult", eventStatus, eventData);
  }

  @SneakyThrows
  public List<Long> getResultData(List<Long> resultList,Integer temporaryDispatchMark) {

    //按公司要求，查询数据，限制在200以内，当前分页为200，查询出司机ID
    List<List<Long>> drvIdListPage = Lists.partition(resultList, tmsTransportQconfig.getInQueryBatchSize());

    List<CompletableFuture<List<Long>>> futures =
      drvIdListPage.stream().map(drvIds -> CompletableFuture.supplyAsync(() -> drvDrvierRepository.queryDrvIdByMarkPage(drvIds, temporaryDispatchMark), threadPoolService.getInQueryDriverPool())).collect(
        Collectors.toList());
    // 获取结果
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));

    return allFutures.thenApply(v ->
      futures.stream()
        .map(CompletableFuture::join)
        .flatMap(List::stream)
        .collect(Collectors.toList())
    ).get();
  }

}
