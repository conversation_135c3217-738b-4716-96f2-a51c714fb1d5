package com.ctrip.dcs.tms.transport.infrastructure.common.converter;

import com.ctrip.dcs.driver.domain.account.GenerateGlobalIdRequestType;
import com.ctrip.dcs.driver.domain.account.RegisterNewAccountRequestType;
import com.ctrip.dcs.driver.domain.account.SyncAccountRequestType;
import com.ctrip.dcs.driver.domain.account.UpdateAccountRequestType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverGuideDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.VehVehicleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidVehicleRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.vehicle.domain.value.SceneVehicleModel;
import com.ctrip.frt.product.soa.DriverBasicInfoType;
import com.ctrip.frt.product.soa.DriverCardInfo;
import com.ctrip.frt.product.soa.DriverInfoRequestType;
import com.ctrip.frt.product.soa.DriverInfoResponseType;
import com.ctrip.frt.product.soa.DriverInfoReturnOptionType;
import com.ctrip.frt.product.soa.DriverInfoType;
import com.ctrip.frt.product.soa.DriverKeyType;
import com.ctrip.frt.product.soa.FreezeRecordInfo;
import com.ctrip.frt.product.soa.SocialAccountInfoType;
import com.ctrip.frt.product.soa.SupplierInfo;
import com.ctrip.frt.product.soa.VehicleInfoRequestType;
import com.ctrip.frt.product.soa.VehicleInfoResponseType;
import com.ctrip.frt.product.soa.VehicleInfoType;
import com.ctrip.frt.product.soa.VehicleKeyType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.tour.driver.platform.api.contract.DriverBaseInfoType;
import com.ctrip.tour.driver.platform.api.contract.GetGrayControlRequestType;
import com.ctrip.tour.driver.platform.api.contract.QueryDriverIdBySupplierRequestType;
import com.ctrip.tour.driver.platform.api.contract.SearchDriverRequestType;
import com.ctrip.tour.driver.platform.api.contract.SearchDriverResponseType;
import com.ctrip.tour.driver.platform.api.contract.SearchVehicleRequestType;
import com.ctrip.tour.driver.platform.api.contract.SearchVehicleResponseType;
import com.ctrip.tour.driver.platform.api.contract.SupplierCondition;
import com.ctrip.tour.driver.platform.api.contract.VehicleType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.DRIVER_SOURCE;

@Component
public class DriverGuideConverter {

  private static final Logger logger = LoggerFactory.getLogger(DriverGuideConverter.class);

  @Autowired
  private EnumRepository enumRepository;

  @Autowired
  ProductionLineUtil productionLineUtil;

  /**
   * 包车产线
   */
  private static final Integer DAY = CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue();
  private static final String DAY_PRODUCT_LINE = "V";

  public SyncAccountRequestType buildSyncAccountRequestType(DrvDriverPO request) {
    SyncAccountRequestType syncAccountRequestType = new SyncAccountRequestType();
    syncAccountRequestType.setSource(DRIVER_SOURCE);
    syncAccountRequestType.setSourceId(String.valueOf(request.getDrvId()));
    syncAccountRequestType.setCountryCode(request.getIgtCode());
    syncAccountRequestType.setPhoneNumber(request.getDrvPhone());
    syncAccountRequestType.setEmail(request.getEmail());
    syncAccountRequestType.setName(request.getDrvName());
    syncAccountRequestType.setIdCardNo(request.getDrvIdcard());
    syncAccountRequestType.setPayoneerAccountId(request.getPaiayAccount());
    syncAccountRequestType.setPpmAccountId(request.getPpmAccount());
    syncAccountRequestType.setUid(request.getUid());
    syncAccountRequestType.setIsOversea(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(), request.getInternalScope()) ? true : false);
    syncAccountRequestType.setIsValid(Objects.equals(request.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode()) || Objects.equals(request.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.FREEZE.getCode()) ? true : false);
    return syncAccountRequestType;
  }

  public UpdateAccountRequestType buildUpdateAccountRequestType(DrvDriverPO request) {
    UpdateAccountRequestType requestType = new UpdateAccountRequestType();
    requestType.setSource(DRIVER_SOURCE);
    requestType.setUid(request.getUid());
    requestType.setCountryCode(request.getIgtCode());
    requestType.setPhoneNumber(request.getDrvPhone());
    requestType.setEmail(request.getEmail());
    requestType.setName(request.getDrvName());
    requestType.setModifyUser(request.getModifyUser());
    requestType.setPayoneerAccountId(request.getPaiayAccount());
    return requestType;
  }

  public RegisterNewAccountRequestType buildRegisterNewAccountRequestType(DrvDriverPO request) {
    RegisterNewAccountRequestType requestType = new RegisterNewAccountRequestType();
    requestType.setSource(DRIVER_SOURCE);
    requestType.setModifyUser(request.getModifyUser());
    requestType.setSourceId(String.valueOf(request.getDrvId()));
    requestType.setCountryCode(request.getIgtCode());
    requestType.setPhoneNumber(request.getDrvPhone());
    requestType.setEmail(request.getEmail());
    requestType.setName(request.getDrvName());
    requestType.setIdCardNo(request.getDrvIdcard());
    requestType.setPayoneerAccountId(request.getPaiayAccount());
    requestType.setIsOversea(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(), request.getInternalScope()));
    requestType.setIsValid(
      Objects.equals(request.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode()) || Objects.equals(
        request.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.FREEZE.getCode()) || Objects.equals(
        request.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.UNACT.getCode()));
    requestType.setCityId(request.getCityId());
    return requestType;
  }

  public GenerateGlobalIdRequestType buildGenerateGlobalIdRequestType(DrvDriverPO request) {
    GenerateGlobalIdRequestType requestType = new GenerateGlobalIdRequestType();
    requestType.setSource(DRIVER_SOURCE);
    requestType.setPhoneNumber(request.getDrvPhone());
    requestType.setCountryCode(request.getIgtCode());
    return requestType;
  }

  public List<DriverGuideDTO> convert(DriverInfoResponseType driverInfo, String defaultDriverPic) {
    if (driverInfo != null && CollectionUtils.isNotEmpty(driverInfo.getDriverInfoList())) {
      return convertDriverList(driverInfo.getDriverInfoList(), defaultDriverPic);
    }
    return Lists.newArrayList();
  }

  public List<DriverGuideDTO> convertDriverList(List<DriverInfoType> driverInfoList, String defaultDriverPic) {
    return driverInfoList.stream().map(driverInfoType -> convertDriverOne(driverInfoType, defaultDriverPic)).collect(Collectors.toList());
  }

  public DriverGuideDTO convertDriverOne(DriverInfoType driverInfoType, String defaultDriverPic) {

    DriverGuideDTO driverGuidDTO = new DriverGuideDTO();
    DriverBasicInfoType basicInfo = driverInfoType.getBasicInfo();

    VehicleInfoType vehicleInfo = getDriverCar(driverInfoType.getVehicleInfos());

    driverGuidDTO.setUid(basicInfo.getCtripUID());
    driverGuidDTO.setBroadcast(TmsTransportConstant.BroadcastEnum.NO.getCode());
    driverGuidDTO.setPaiayAccount(null);
    driverGuidDTO.setPaiayEmail(null);
    driverGuidDTO.setAccountType(null);
    driverGuidDTO.setTemporaryDispatchMark(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
    driverGuidDTO.setTemporaryDispatchEndDatetime(null);
    driverGuidDTO.setOcrPassStatus(true);
    driverGuidDTO.setOcrPassStatusJson(null);
    driverGuidDTO.setActive(true);
    driverGuidDTO.setIdcardAppealMaterials(null);
    driverGuidDTO.setDrvLicenseAppealMaterials(null);
    driverGuidDTO.setRaisingPickUp(false);
    driverGuidDTO.setChildSeat(false);
    driverGuidDTO.setCertificateConfig(null);
    driverGuidDTO.setOcrFieldValue(null);
    driverGuidDTO.setVehBindTime(null); // 司导没有绑车时间
    driverGuidDTO.setVersionFlag(null);
    driverGuidDTO.setCategorySynthesizeCode(DAY);  // 默认包车
    driverGuidDTO.setDrvProductionLineCodeList(productionLineUtil.getShowProductionLineList(DAY));
    driverGuidDTO.setVehProductionLineCodeList(productionLineUtil.getShowProductionLineList(DAY));
    driverGuidDTO.setNetAppealMaterials(null);
    driverGuidDTO.setNucleicAcidReportImg(null);
    driverGuidDTO.setVaccineReportImg(null);
    driverGuidDTO.setDrvId(basicInfo.getDriverId());
    driverGuidDTO.setDrvName(basicInfo.getDrvName());
    driverGuidDTO.setDrvEnglishName(basicInfo.getDrvEnglishName());
    driverGuidDTO.setSupplierId(basicInfo.getSupplierId());
    driverGuidDTO.setDispatchSupplierIdList(getDispatchSupplierList(basicInfo.getDispatchSupplierList()));
    driverGuidDTO.setDrvLanguage(getDrvLanguage(basicInfo.getDrvLanguages()));
    driverGuidDTO.setCountryId(basicInfo.getCountryId());
    driverGuidDTO.setCountryName(null);
    driverGuidDTO.setCityId(basicInfo.getServiceCityId());
    driverGuidDTO.setCityName(enumRepository.getCityName(basicInfo.getServiceCityId()));
    driverGuidDTO.setIgtCode(basicInfo.getIgtCode());
    driverGuidDTO.setDrvPhone(basicInfo.getDrvPhone());
    driverGuidDTO.setEmail(basicInfo.getEmail());
    driverGuidDTO.setLoginAccount(null);
    driverGuidDTO.setLoginPwd(null);
    driverGuidDTO.setPpmAccount(null);
    driverGuidDTO.setQunarAccount(null);
    driverGuidDTO.setImAccount(null);
    driverGuidDTO.setVehicleId(vehicleInfo.getVehicleId());
    driverGuidDTO.setVehicleLicense(vehicleInfo.getLicense());
    driverGuidDTO.setVehicleTypeId(vehicleInfo.getTypeId());
    driverGuidDTO.setCarTypeId(getCarTypeId(vehicleInfo.getTypeId()));
    driverGuidDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehicleInfo.getTypeId()));
    driverGuidDTO.setCarTypeName(enumRepository.getVehicleTypeName(vehicleInfo.getTypeId()));
    driverGuidDTO.setCarBrandId(vehicleInfo.getBrandId());
    driverGuidDTO.setCarBrandName(enumRepository.getBandName(vehicleInfo.getBrandId()));
    driverGuidDTO.setCarColorId(vehicleInfo.getColorId());
    driverGuidDTO.setCarColor(enumRepository.getColorName(vehicleInfo.getColorId()));
    driverGuidDTO.setCarSeriesId(vehicleInfo.getSeriesId());
    driverGuidDTO.setCarSeriesName(enumRepository.getVehicleSeriesName(vehicleInfo.getSeriesId()));
    SceneVehicleModel vehicleModel = enumRepository.getVehicleType(vehicleInfo.getTypeId());
    if (vehicleModel != null) {
      driverGuidDTO.setCarTypeName(vehicleModel.getTranslationName());
      driverGuidDTO.setMaxLuggages(vehicleModel.getLargeBaggageCount());
      driverGuidDTO.setMaxPassengers(vehicleModel.getPassengerCount());
    }
    DriverCardInfo driving = getCertiFicate("driving", driverInfoType.getCardInfos());
    driverGuidDTO.setCertificateNumber(driving.getCardNo());
    driverGuidDTO.setCertiDate(getCertiDate(driving)); // 驾驶证初次申领日期
    //      driverGuidDTO.setQuasiDrivingType(); // 无意向车型
    driverGuidDTO.setExpiryBeginDate(getDate(driving.getEffectDate()));
    driverGuidDTO.setExpiryEndDate(getDate(driving.getEndDate()));
    driverGuidDTO.setDrvcardImg(driving.getFrontImg()); // 给全身照
    driverGuidDTO.setDrvAddr(null);
    driverGuidDTO.setWechat(getWechat(driverInfoType.getSocialAccountInfo()));
    driverGuidDTO.setWorkPeriod(getDriverGuideWorkPeriod(basicInfo.getWorkPeriods())); // basicInfo.getWorkPeriods()
    DriverCardInfo idCard = getIDCard("idCard", driverInfoType.getCardInfos());
    driverGuidDTO.setIdcardImg(idCard.getFrontImg()); // 驾驶证图片
    driverGuidDTO.setDrvIdcard(idCard.getCardNo());
    driverGuidDTO.setPeopleVehicleImg(vehicleInfo.getFullImg()); // 给车身照
    driverGuidDTO.setNetVehiclePeoImg(null); // 网约车人证照片
    driverGuidDTO.setDrvStatus(basicInfo.getDrvStatus());
    getFreezeRecord(driverGuidDTO, driverInfoType.getFreezeRecordInfo());
//    driverGuidDTO.setFreezeHour(null); // 冻结小时数 司导先不返回了，暂时没有上游使用
//    driverGuidDTO.setFreezeReason(null); // 司导提供 司导先不返回了，暂时没有上游使用
//    driverGuidDTO.setFreezeTime(null); // 冻结时间 司导先不返回了，暂时没有上游使用
    driverGuidDTO.setDrvFrom(null); // 不需要
    driverGuidDTO.setOpFrom(null); // 不需要
    driverGuidDTO.setInternalScope(basicInfo.getIsAbroad()); // 境内境外
    driverGuidDTO.setDatachangeCreatetime(null); //
    driverGuidDTO.setCreateUser(null); //
    driverGuidDTO.setModifyUser(null); //
    driverGuidDTO.setDatachangeLasttime(getTimeStamp(basicInfo.getRegisterTime())); // 司导提供
    driverGuidDTO.setSalt(null);
    driverGuidDTO.setCoopMode(TmsTransportConstant.DrvCoopModeEnum.FULL_TIME.getCode()); // 默认全职
    driverGuidDTO.setNoCriminalProofImg(null);
    driverGuidDTO.setIdcardBackImg(null);
    driverGuidDTO.setOnlineTime(getTimeStamp(basicInfo.getFirstOnlineTime())); // 上线时间
    driverGuidDTO.setSex(basicInfo.getGender()); // 司导提供
    driverGuidDTO.setNation(null); // 默认给null
    driverGuidDTO.setBirthday(null); // 默认给null
    driverGuidDTO.setIdcardValidity(getDate(driving.getEndDate())); //
    driverGuidDTO.setDrvLicenseNumber(driving.getCardNo()); //
    driverGuidDTO.setDrvLicenseName(driving.getCardName()); //
    driverGuidDTO.setOtherCertificateImg(null); //
    driverGuidDTO.setCheckStatusList(null); //
    driverGuidDTO.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode()); // 审核状态
    driverGuidDTO.setAddrModCount(null); // 地址修改次数统计
    driverGuidDTO.setDriverNetCertNo(null); // 网约车证
    DriverCardInfo lifePhoto = getCertiFicate("lifePhoto", driverInfoType.getCardInfos());
    String headImg = getHeadImg(basicInfo.getHeadImg(), lifePhoto, defaultDriverPic);
    driverGuidDTO.setPicUrl(headImg);
    driverGuidDTO.setRealPicUrl(headImg);
    driverGuidDTO.setDrvHeadImg(headImg);
    driverGuidDTO.setSupplierName(enumRepository.getSupplierName(basicInfo.getSupplierId()));
    driverGuidDTO.setTemporaryVehicleMark(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
    driverGuidDTO.setChildSeat(true);
    driverGuidDTO.setRaisingPickUp(true);
    driverGuidDTO.setVehicleStatus(vehicleInfo.getVehicleStatus());
    return driverGuidDTO;
  }

  private void getFreezeRecord(DriverGuideDTO driverGuideDTO, FreezeRecordInfo freezeRecordInfo) {
    if (freezeRecordInfo == null) {
      return;
    }
    driverGuideDTO.setFreezeHour(freezeRecordInfo.getFreezeHour());
    driverGuideDTO.setFreezeReason(freezeRecordInfo.getFreezeReason());
    driverGuideDTO.setFreezeTime(getTimeStamp(freezeRecordInfo.getStartFreezeTime()));
  }

  protected String getHeadImg(String headImg, DriverCardInfo lifePhoto, String defaultDriverPic) {
    return StringUtil.isEmpty(lifePhoto.getFrontImg()) ? (StringUtils.isEmpty(headImg) ? defaultDriverPic : headImg) : lifePhoto.getFrontImg();
  }

  private List<Long> getDispatchSupplierList(List<SupplierInfo> dispatchSupplierList) {
    return Optional.ofNullable(dispatchSupplierList).orElse(Lists.newArrayList()).stream().filter(Objects::nonNull).map(SupplierInfo::getSupplierId).collect(
      Collectors.toList());
  }

  private String getDriverGuideWorkPeriod(List<String> workPeriods) {
    return String.join(", ", Optional.ofNullable(workPeriods).orElse(new ArrayList<>()));
  }

  private Integer getCarTypeId(Long typeId) {
    return Optional.ofNullable(typeId).orElse(0L).intValue();
  }

  /**
   *   目前查询司机只有返回司机ID这一种场景
    */
  public DriverGuideDTO convert2DriverGuidDTOBDriverBaseInfo(DriverBaseInfoType driverBaseInfoType) {
      DriverGuideDTO driverGuidDTO = new DriverGuideDTO();
      driverGuidDTO.setDispatchSupplierIdList(driverBaseInfoType.getDispatchSupplierIdList());
      driverGuidDTO.setDrvId(driverBaseInfoType.getDriverId());
      driverGuidDTO.setDrvName(driverBaseInfoType.getDrvName());
      driverGuidDTO.setSupplierId(driverBaseInfoType.getSupplierId());
      driverGuidDTO.setIgtCode(driverBaseInfoType.getIgtCode());
      driverGuidDTO.setDrvPhone(driverBaseInfoType.getDrvPhone());
      driverGuidDTO.setVehicleId(driverBaseInfoType.getVehicleId());
      driverGuidDTO.setVehicleLicense(driverBaseInfoType.getVehicleLicense());
      driverGuidDTO.setVehicleTypeId(driverBaseInfoType.getVehicleTypeId());
      driverGuidDTO.setEmail(driverBaseInfoType.getEmail());
      driverGuidDTO.setWorkPeriod(driverBaseInfoType.getWorkPeriod());
      driverGuidDTO.setDrvStatus(driverBaseInfoType.getDrvStatus());
      driverGuidDTO.setSupplierName(enumRepository.getSupplierName(driverBaseInfoType.getSupplierId()));
    driverGuidDTO.setTemporaryDispatchMark(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
      return driverGuidDTO;
  }

  public QueryDriverIdBySupplierRequestType convert2QueryDriverIdBySupplierRequestType(DriverGuidDriverRequestDTO request) {
    QueryDriverIdBySupplierRequestType requestType = new QueryDriverIdBySupplierRequestType();
    requestType.setGeoId(request.getCityId());
    requestType.setSupplierId(request.getSupplierId());
    requestType.setProductLine(DAY_PRODUCT_LINE);
    return requestType;
  }

  public Timestamp getTimeStamp(Calendar registerTime) {
    return Optional.ofNullable(registerTime)
      .map(calendar -> {
        Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());
        timestamp.setNanos(0);
        return timestamp;
      })
      .orElse(null);
  }

  public VehicleInfoType getDriverCar(List<VehicleInfoType> cardInfos) {
    return Optional.ofNullable(cardInfos)
      .orElseGet(Collections::emptyList)
      .stream().findFirst().orElse(new VehicleInfoType());
  }

  public String getWechat(SocialAccountInfoType getSocialAccountInfo) {
    return Optional.ofNullable(getSocialAccountInfo)
      .map(SocialAccountInfoType::getWechat)
      .orElse("");
  }

  public String getDrvLanguage(List<String> drvLanguages) {
    return Optional.ofNullable(drvLanguages).orElse(Lists.newArrayList()).stream().reduce((a, b) -> a + "," + b).orElse("");
  }

  public DriverCardInfo getIDCard(String type, List<DriverCardInfo> cardInfos) {
    return Optional.ofNullable(cardInfos)
      .orElseGet(Collections::emptyList)
      .stream()
      .filter(cardInfo -> type.equals(cardInfo.getCardType()))
      .findFirst()
      .orElse(new DriverCardInfo());
  }

  /**
   * 初次领证日期
   * @param cardInfo
   * @return
   */
  public Date getCertiDate(DriverCardInfo cardInfo) {
    Calendar calendar = Optional.ofNullable(cardInfo).map(DriverCardInfo::getInitialDate).orElse(null);
    return Optional.ofNullable(calendar).map(calendar1 -> new java.sql.Date((calendar1.getTime()).getTime())).orElse(null);
  }

  public Date getDate(Calendar calendar) {
    return Optional.ofNullable(calendar).map(calendar1 -> new java.sql.Date((calendar1.getTime()).getTime())).orElse(null);
  }

  public DriverCardInfo getCertiFicate(String type, List<DriverCardInfo> cardInfos) {
    return Optional.ofNullable(cardInfos).orElseGet(Collections::emptyList).stream()
      .filter(cardInfo -> type.equals(cardInfo.getCardType())).findFirst()
      .orElse(new DriverCardInfo());
  }

  public DriverInfoRequestType buildDriverInfoRequestType(DriverGuidDriverRequestDTO request) {
    DriverInfoRequestType remoteRequestType = new DriverInfoRequestType();
    List<DriverKeyType> keys = Lists.newArrayList();
    if (CollectionUtils.isNotEmpty(request.getDriverIdList())) {
      keys.addAll(request.getDriverIdList().stream().map(drvId -> {
        DriverKeyType driverKeyType = new DriverKeyType();
        driverKeyType.setDriverId(drvId);
        return driverKeyType;
      }).collect(Collectors.toList()));
    }

    if (CollectionUtils.isNotEmpty(request.getDriverPhoneList())) {
      keys.addAll(request.getDriverPhoneList().stream().map(drvPhone -> {
        DriverKeyType driverKeyType = new DriverKeyType();
        driverKeyType.setPhone(drvPhone);
        return driverKeyType;
      }).collect(Collectors.toList()));
    }
    remoteRequestType.setKeys(keys);
    logger.info("DriverGuidProxyImpl.getDriverInfo", "request:{}", JsonUtil.toJson(remoteRequestType));
    DriverInfoReturnOptionType returnOption = new DriverInfoReturnOptionType(true, true, true,true,true, true,true,true);
    remoteRequestType.setReturnOption(returnOption);

    return remoteRequestType;
  }

  public List<VehVehicleDTO> convertVehicleList(VehicleInfoResponseType vehicleInfoResponseType) {
    return Optional.ofNullable(vehicleInfoResponseType.getVehicleInfoList()).orElse(Lists.newArrayList()).stream().map(vehicleInfo -> convertVehicle(vehicleInfo)).collect(
      Collectors.toList());
  }

  public VehicleInfoRequestType buildVehicleInfoRequestType4GetVehicleInfo(DriverGuidVehicleRequestDTO request) {
    VehicleInfoRequestType vehicleInfoRequestType = new VehicleInfoRequestType();
    List<VehicleKeyType> keys = Lists.newArrayList();
      Optional.ofNullable(request.getVehicleIdList()).orElse(Lists.newArrayList())
      .forEach(vehicleId -> {
        VehicleKeyType vehicleKeyType = new VehicleKeyType();
        vehicleKeyType.setVehicleId(vehicleId);
        keys.add(vehicleKeyType);
      });
      Optional.ofNullable(request.getVehicleLicenseList()).orElse(Lists.newArrayList())
      .forEach(vehicleLicense -> {
        VehicleKeyType vehicleKeyType = new VehicleKeyType();
        vehicleKeyType.setLicense(vehicleLicense);
        keys.add(vehicleKeyType);
      });

    vehicleInfoRequestType.setKeys(keys);
    return vehicleInfoRequestType;
  }

  public List<VehVehicleDTO> convert2VehVehicleDTO(SearchVehicleResponseType responseType) {
    return Optional.ofNullable(responseType.getVehicleList())
      .orElse(Collections.emptyList())
      .stream()
      .map(this::convertVehicleForSearch)
      .collect(Collectors.toList());
  }

  public SearchVehicleRequestType buildVehicleInfoRequestType(DriverGuidVehicleRequestDTO request, int pageNo, int pageSize) {
    SearchVehicleRequestType searchVehicleRequestType = new SearchVehicleRequestType();
    searchVehicleRequestType.setSupplierId(request.getSupplierId());
    searchVehicleRequestType.setVehicleLicenseList(request.getVehicleLicenseList());
    searchVehicleRequestType.setHasDrv(Objects.equals(request.getHasDrv(),0) ? "F" : "T");
    searchVehicleRequestType.setPageIndex(pageNo); // 循环取
    searchVehicleRequestType.setPageSize(pageSize);
    searchVehicleRequestType.setVehicleStatusList(Lists.newArrayList(TmsTransportConstant.VehStatusEnum.ONLINE.getCode()));
    return searchVehicleRequestType;
  }

  public VehVehicleDTO convertVehicle(VehicleInfoType vehicleInfo) {
    VehVehicleDTO vehVehicleDTO = new VehVehicleDTO();
    vehVehicleDTO.setTemporaryDispatchMark(0);
    vehVehicleDTO.setTemporaryDispatchEndDatetime(null);
    vehVehicleDTO.setOcrPassStatus(true);
    vehVehicleDTO.setOcrPassStatusJson(null); // 默认不返回
    vehVehicleDTO.setActive(true);
    vehVehicleDTO.setVehicleLicenseAppealMaterials(null); // 默认不返回
    vehVehicleDTO.setCertificateConfig(null);
    vehVehicleDTO.setOcrFieldValue(null); // 默认不返回
    vehVehicleDTO.setVersionFlag(null); // 默认不返回
    vehVehicleDTO.setNetAppealMaterials(null); // 默认不返回
    vehVehicleDTO.setCategorySynthesizeCode(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()); // 转成枚举
    vehVehicleDTO.setVehicleId(vehicleInfo.getVehicleId());
    vehVehicleDTO.setVehicleLicense(vehicleInfo.getLicense());
    vehVehicleDTO.setSupplierId(getSupplierId(vehicleInfo.getSupplierIdList())); //
    vehVehicleDTO.setSupplierName(enumRepository.getSupplierName(vehVehicleDTO.getSupplierId()));
    vehVehicleDTO.setCountryId(null); // 不返回
    vehVehicleDTO.setCountryName(null); //
    vehVehicleDTO.setCityId(vehicleInfo.getLicenseCityId()); //
    vehVehicleDTO.setVehicleTypeId(vehicleInfo.getTypeId());
    vehVehicleDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehicleInfo.getTypeId()));
    vehVehicleDTO.setVehicleBrandId(vehicleInfo.getBrandId());
    vehVehicleDTO.setVehicleBrandName(enumRepository.getBandName(vehicleInfo.getBrandId()));
    vehVehicleDTO.setVehicleSeries(vehicleInfo.getSeriesId());
    vehVehicleDTO.setVehicleSeriesName(enumRepository.getVehicleSeriesName(vehicleInfo.getSeriesId()));
    vehVehicleDTO.setVehicleColorId(vehicleInfo.getColorId());
    vehVehicleDTO.setVehicleColorName(enumRepository.getColorName(vehicleInfo.getColorId()));
    vehVehicleDTO.setVehicleEnergyType(vehicleInfo.getEnergyType()); // 要不要映射
    vehVehicleDTO.setVin(null); // 不返回
    vehVehicleDTO.setRegstDate(null); //  不返回
    vehVehicleDTO.setUsingNature(null); //  不返回
    vehVehicleDTO.setNetTansCtfctImg(null); //  网约车运输证图片
    vehVehicleDTO.setVehicleCertiImg(null); //  车辆行驶证照片
    vehVehicleDTO.setVehicleFullImg(vehicleInfo.getFullImg()); //  车身照片
    vehVehicleDTO.setVehicleFrontImg(vehicleInfo.getFrontImg()); //  车辆前身照
    vehVehicleDTO.setVehicleBackImg(vehicleInfo.getBackImg()); //  车辆后身照
    vehVehicleDTO.setVehicleTrunkImg(vehicleInfo.getTrunkImg()); // 后备箱照片
    vehVehicleDTO.setHasDrv(CollectionUtils.isNotEmpty(vehicleInfo.getDriverIdList()));
    vehVehicleDTO.setComments(null); // 不返回
    vehVehicleDTO.setDatachangeCreatetime(null); //
    vehVehicleDTO.setCreateUser(null); //
    vehVehicleDTO.setModifyUser(null); //
    vehVehicleDTO.setDatachangeLasttime(null); //
    vehVehicleDTO.setVehicleStatus(vehicleInfo.getVehicleStatus()); //
    vehVehicleDTO.setVehicleLicenseOwner(null); // 默认不返回
    //审批状态(0.暂不可审批、1.待审批、2.审批通过、3.审批驳回)
    vehVehicleDTO.setApproveStatus(2); // 默认审核通过
    vehVehicleDTO.setVehicleLicenseCityId(null); // 司导返回
    vehVehicleDTO.setVehicleNetCertNo(null); // 默认不返回
    vehVehicleDTO.setCategorySynthesizeCode(DAY);

    /**
     * 审批状态
     * 1.未处理
     * 2.合规
     * 3.不合规
     */
    vehVehicleDTO.setAuditStatus(2); // 审核状态

    return vehVehicleDTO;
  }

  public VehVehicleDTO convertVehicleForSearch(VehicleType vehicleInfo) {
    VehVehicleDTO vehVehicleDTO = new VehVehicleDTO();
    vehVehicleDTO.setTemporaryDispatchMark(0);
    vehVehicleDTO.setTemporaryDispatchEndDatetime(null);
    vehVehicleDTO.setOcrPassStatus(true);
    vehVehicleDTO.setOcrPassStatusJson(null); // 默认不返回
    vehVehicleDTO.setActive(true);
    vehVehicleDTO.setVehicleLicenseAppealMaterials(null); // 默认不返回
    vehVehicleDTO.setCertificateConfig(null);
    vehVehicleDTO.setOcrFieldValue(null); // 默认不返回
    vehVehicleDTO.setVersionFlag(null); // 默认不返回
    vehVehicleDTO.setNetAppealMaterials(null); // 默认不返回
    vehVehicleDTO.setCategorySynthesizeCode(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()); // 转成枚举
    vehVehicleDTO.setVehicleId(vehicleInfo.getVehicleId());
    vehVehicleDTO.setVehicleLicense(vehicleInfo.getVehicleLicense());
    vehVehicleDTO.setSupplierId(getSupplierId(vehicleInfo.getSupplierIdList())); //
    vehVehicleDTO.setSupplierName(enumRepository.getSupplierName(vehVehicleDTO.getSupplierId()));
    vehVehicleDTO.setCountryId(null); // 不返回
    vehVehicleDTO.setCountryName(null); //
//    vehVehicleDTO.setCityId(vehicleInfo.getl()); //
    vehVehicleDTO.setVehicleTypeId(vehicleInfo.getVehicleTypeId());
    vehVehicleDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehicleInfo.getVehicleTypeId()));
    vehVehicleDTO.setVehicleBrandId(vehicleInfo.getVehicleBrandId());
    vehVehicleDTO.setVehicleBrandName(enumRepository.getBandName(vehicleInfo.getVehicleBrandId()));
    vehVehicleDTO.setVehicleSeries(vehicleInfo.getVehicleSeries());
    vehVehicleDTO.setVehicleSeriesName(enumRepository.getVehicleSeriesName(vehicleInfo.getVehicleSeries()));
    vehVehicleDTO.setVehicleColorId(vehicleInfo.getVehicleColorIdNew());
    vehVehicleDTO.setVehicleColorName(enumRepository.getColorName(vehicleInfo.getVehicleColorIdNew()));

    //    vehVehicleDTO.setVehicleEnergyType(vehicleInfo.ggetvehicleEnergyType()); //
    vehVehicleDTO.setVin(null); // 不返回
    vehVehicleDTO.setRegstDate(null); //  不返回
    vehVehicleDTO.setUsingNature(null); //  不返回
    vehVehicleDTO.setNetTansCtfctImg(null); //  网约车运输证图片
    vehVehicleDTO.setVehicleCertiImg(null); //  车辆行驶证照片
    vehVehicleDTO.setVehicleFullImg(null); //  车身照片
    vehVehicleDTO.setVehicleFrontImg(null); //  车辆前身照
    vehVehicleDTO.setVehicleBackImg(null); //  车辆后身照
    vehVehicleDTO.setVehicleTrunkImg(null); // 后备箱照片
    vehVehicleDTO.setHasDrv(vehicleInfo.getVehicleId() != null); //
    vehVehicleDTO.setComments(null); // 不返回
    vehVehicleDTO.setDatachangeCreatetime(null); //
    vehVehicleDTO.setCreateUser(null); //
    vehVehicleDTO.setModifyUser(null); //
    vehVehicleDTO.setDatachangeLasttime(null); //
    vehVehicleDTO.setVehicleStatus(vehicleInfo.getVehicleStatus()); //
    vehVehicleDTO.setVehicleLicenseOwner(null); // 默认不返回
    //审批状态(0.暂不可审批、1.待审批、2.审批通过、3.审批驳回)
    vehVehicleDTO.setApproveStatus(2); // 默认审核通过
    vehVehicleDTO.setVehicleLicenseCityId(null); // 司导返回
    vehVehicleDTO.setVehicleNetCertNo(null); // 默认不返回

    /**
     * 审批状态
     * 1.未处理
     * 2.合规
     * 3.不合规
     */
    vehVehicleDTO.setAuditStatus(2); // 审核状态

    return vehVehicleDTO;
  }

  /**
   * 产品逻辑，默认返回第一个
   * @param supplierIdList
   * @return
   */
  public Long getSupplierId(List<Long> supplierIdList) {
    return Optional.ofNullable(supplierIdList).orElse(Lists.newArrayList()).stream().findFirst().orElse(null);
  }

  public GetGrayControlRequestType buildGrayControlRequestType(Long supplierId) {
    GetGrayControlRequestType request = new GetGrayControlRequestType();
    request.setSupplierId(supplierId);
    return request;
  }

}
