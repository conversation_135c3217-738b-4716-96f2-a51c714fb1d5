package com.ctrip.dcs.tms.transport.infrastructure.common.qconfig;

import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.google.common.collect.Lists;
import qunar.tc.qconfig.client.spring.QMapConfig;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@QMapConfig("overseas.config.properties")
public class OverseasQconfig {

    //境外灰度供应商(多个供应商ID逗 号分隔，-1代表开全量)
    String overseasGrayScaleSupplierConf;
    //境外OCR总开关(true:开,false:关)
    Boolean overseasOcrTotalSwitch;
    //境外灰度供应商是否OCR校验(多个供应商ID逗 号分隔，-1代表开全量)
    String overseasOcrVerifyConf;
    //cor超时阀值(单位:分钟)
    Integer overseasOcrTimeoutThreshold;
    //境外车辆审核通过未激活
    String vehicleUnactCountryConf;

    //#临时派遣结束时间配置(单位：小时)
    Integer temporaryDispatchEndDatetimeConfig;
    //临派车辆或车辆补充信息通达时间
    Integer temporaryReplenishInfohreshold;
    //境外临时派遣供应商
    String overseasTemporarySupplierConf;

    String filtrationServiceproviderid;

    //登录激活白名单
    private String loginActiveWithedSupplierList;

    //登录激活灰度白名单
    private String loginActiveGrayCityList;

    public List<Long> getFiltrationServiceprovideridList() {
        return  JsonUtil.fromJson(filtrationServiceproviderid, new TypeReference<List<Long>>() { });
    }

    public String getFiltrationServiceproviderid() {
        return filtrationServiceproviderid;
    }

    public void setFiltrationServiceproviderid(String filtrationServiceproviderid) {
        this.filtrationServiceproviderid = filtrationServiceproviderid;
    }

    public String getVehicleUnactCountryConf() {
        return vehicleUnactCountryConf;
    }

    public void setVehicleUnactCountryConf(String vehicleUnactCountryConf) {
        this.vehicleUnactCountryConf = vehicleUnactCountryConf;
    }

    public String getOverseasTemporarySupplierConf() {
        return overseasTemporarySupplierConf;
    }

    public void setOverseasTemporarySupplierConf(String overseasTemporarySupplierConf) {
        this.overseasTemporarySupplierConf = overseasTemporarySupplierConf;
    }

    public Integer getTemporaryNotificationHour(){
        return temporaryDispatchEndDatetimeConfig - temporaryReplenishInfohreshold;
    }

    public Integer getTemporaryReplenishInfohreshold() {
        return temporaryReplenishInfohreshold;
    }

    public void setTemporaryReplenishInfohreshold(Integer temporaryReplenishInfohreshold) {
        this.temporaryReplenishInfohreshold = temporaryReplenishInfohreshold;
    }

    public Integer getTemporaryDispatchEndDatetimeConfig() {
        return temporaryDispatchEndDatetimeConfig;
    }

    public void setTemporaryDispatchEndDatetimeConfig(Integer temporaryDispatchEndDatetimeConfig) {
        this.temporaryDispatchEndDatetimeConfig = temporaryDispatchEndDatetimeConfig;
    }

    public String getOverseasGrayScaleSupplierConf() {
        return overseasGrayScaleSupplierConf;
    }

    public void setOverseasGrayScaleSupplierConf(String overseasGrayScaleSupplierConf) {
        this.overseasGrayScaleSupplierConf = overseasGrayScaleSupplierConf;
    }

    public Boolean getOverseasOcrTotalSwitch() {
        return overseasOcrTotalSwitch;
    }

    public void setOverseasOcrTotalSwitch(Boolean overseasOcrTotalSwitch) {
        this.overseasOcrTotalSwitch = overseasOcrTotalSwitch;
    }

    public String getOverseasOcrVerifyConf() {
        return overseasOcrVerifyConf;
    }

    public void setOverseasOcrVerifyConf(String overseasOcrVerifyConf) {
        this.overseasOcrVerifyConf = overseasOcrVerifyConf;
    }

    public Integer getOverseasOcrTimeoutThreshold() {
        return overseasOcrTimeoutThreshold;
    }

    public void setOverseasOcrTimeoutThreshold(Integer overseasOcrTimeoutThreshold) {
        this.overseasOcrTimeoutThreshold = overseasOcrTimeoutThreshold;
    }

  public List<Long> getLoginActiveWithedSupplierList() {
        return Optional.ofNullable(JsonUtil.fromJson(loginActiveWithedSupplierList, new TypeReference<List<Long>>() { })).orElse(
          Lists.newArrayList(-1L));
  }

    public List<Long> getLoginActiveGrayCityList() {
        return Optional.ofNullable(JsonUtil.fromJson(loginActiveGrayCityList, new TypeReference<List<Long>>() { })).orElse(
                Lists.newArrayList(-1L));
    }
}
