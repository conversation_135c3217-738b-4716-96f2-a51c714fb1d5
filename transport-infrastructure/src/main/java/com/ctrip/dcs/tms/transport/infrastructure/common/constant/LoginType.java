package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2020/9/17 11:28
 */
public enum LoginType {

  /**
   *
   */
  PHONE_CODE(1),

  /**
   *
   */
  ACCOUNT_PWD(2);

  private static final Map<Integer, LoginType> MAP = new HashMap<>(4);

  static {
    LoginType[] types = values();

    for (int i = 0; i < types.length; ++i) {
      LoginType loginType = types[i];
      MAP.put(loginType.getCode(), loginType);
    }

  }

  private int code;

  LoginType(int code) {
    this.code = code;
  }

  public static LoginType of(int code) {
    LoginType type = MAP.get(code);
    if (type == null) {
      throw new IllegalArgumentException(String.format("no loginType of this code : %d.", code));
    }

    return type;
  }

  public int getCode() {
    return this.code;
  }
}
