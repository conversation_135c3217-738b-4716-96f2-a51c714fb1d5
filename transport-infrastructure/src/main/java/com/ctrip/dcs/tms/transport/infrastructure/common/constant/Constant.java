package com.ctrip.dcs.tms.transport.infrastructure.common.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

public class Constant {

    // null 空串
    public static final String NULL_STR = "null";

    // 中国国家码区号
    public static final String CHINA_COUNTRY_CODE = "86";

    public static final String DRIVER_CENTER_USER_NOT_EXISTS = "1517100";
    public static final String DRIVER_TEMPORARY_REMARK = "dtms_d";


    /**
     * 变更记录字段key
     */
    public class Fields {

        //司机状态
        public static final String drv_status = "drvStatus";

        //司机意愿车型
        public static final String drv_intendVehicleTypeId = "intendVehicleTypeId";

        //运力组名称
        public static final String transportGroup_name = "transportGroupName";

        //运力组状态
        public static final String transportGroup_status = "groupStatus";

        //运力组模式
        public static final String transportGroup_mode = "transportGroupMode";

        //运力组调度联系人
        public static final String transportGroup_dispatcher = "dispatcher";

        //运力组调度电话
        public static final String transportGroup_dispatcherPhone = "dispatcherPhone";

        //运力组调度语言
        public static final String transportGroup_dispatcherLanguage = "dispatcherLanguage";

        //运力组可接订单距离用车时间
        public static final String transportGroup_takeOrderLimitTime = "takeOrderLimitTime";

        //运力组点位时段配置
        public static final String transportGroup_inOrderConfigs = "inOrderConfigs";

        //审批状态
        public static final String approver_status = "approverStatus";
        //审批备注
        public static final String approver_remark = "approverRemark";

        //冻结时长
        public static final String freezeHour = "freezeHour";

        //冻结原因
        public static final String freezeReason = "freezeReason";

        //到期解冻后置操作（1.解冻后自动上线,2.解冻后自动下线）
        public static final String unfreezeAction = "unfreezeAction";

        //冻结状态
        public static final String freezeStatus = "freezeStatus";

        //操作来源
        public static final String freezeFrom = "freezeFrom";

        //冻结期间所有未服务订单(1.不改派,2.自动改派)
        public static final String freezeOrderSet = "freezeOrderSet";

        //解冻原因
        public static final String unfreezeReason = "unfreezeReason";

        //证件核验状态
        public static final String certificate_check_status = "certificateCheckStatus";

        //人车验证开始时间
        public static final String verifyevent_verify_start_time = "verifyStartTime";

        //单项审批
        public static final String single_approve_value = "single_approve_value";

        //审批操作
        public static final String approver_operation = "approverAperation";

    }

    public static final Map<String, String> vehiclePropertyMap = Maps.newHashMap();
    public static final Map<String, String> drvPropertyMap = Maps.newHashMap();
    public static final Map<String, String> transportGroupPropertyMap = Maps.newHashMap();
    public static final Map<String, String> drvVehRecruitingStatusPropertyMap = Maps.newHashMap();

    public static final Map<String, String> drvFreezePropertyMap = Maps.newHashMap();

    public static final String[] VEHICLE_NEED_KEY = new String[]{"vehicleLicense", "supplierId", "cityId", "vehicleTypeId", "vehicleBrandId", "vehicleSeries",
            "vehicleColorId", "vehicleEnergyType", "vin", "regstDate", "usingNature", "netTansCtfctImg", "vehicleCertiImg", "vehicleFullImg", "vehicleFrontImg",
            "vehicleBackImg", "vehicleTrunkImg", "modifyUser"};

    public static final String[] VEHICLE_FOREIGN_NEED_KEY = new String[]{"vehicleLicense", "supplierId", "cityId", "vehicleTypeId", "vehicleBrandId", "vehicleSeries",
            "vehicleColorId", "vehicleEnergyType", "vehicleCertiImg", "vehicleFullImg", "vehicleFrontImg",
            "vehicleBackImg", "vehicleTrunkImg", "modifyUser"};

    public static final String[] VEHICLE_SPECIAL_KEY = new String[]{"supplierId", "cityId", "vehicleTypeId", "vehicleBrandId", "vehicleSeries",
            "vehicleColorId", "vehicleEnergyType","usingNature"};

    public static final String[] DRV_NEED_KEY = new String[]{"drvName", "drvEnglishName", "drvLanguage", "vehicleLicense", "supplierId", "drvAddr",
            "workPeriod", "certiDate", "loginAccout", "drvcardImg", "idcardImg", "drvHeadImg", "peopleVehicleImg", "netVehiclePeoImg","intendVehicleTypeId"};

    public static final String[] DRV_RECRUITING_NEED_KEY = new String[]{"drvName", "drvEnglishName", "drvLanguage", "vehicleLicense", "supplierId", "drvAddr",
            "workPeriod", "certiDate", "loginAccout", "drvcardImg", "idcardImg", "drvHeadImg", "peopleVehicleImg", "netVehiclePeoImg","intendVehicleTypeId"};

    public static final String[] VEHICLE_RECRUITING_NEED_KEY = new String[]{"vehicleLicense", "supplierId", "cityId", "vehicleTypeId", "vehicleBrandId", "vehicleSeries",
            "vehicleColorId", "vehicleEnergyType", "vin", "regstDate", "usingNature", "netTansCtfctImg", "vehicleCertiImg", "vehicleFullImg", "vehicleFrontImg",
            "vehicleBackImg", "vehicleTrunkImg", "modifyUser"};

    //司机信息需求进审批流字段
    public static final String[] DRV_APPROVE_NEED_KEY = new String[]{ "drvcardImg", "netVehiclePeoImg"};
    public static final String[] DRV_APPROVE_NEED_KEY__EXCEPT_NET_CERT = new String[]{ "drvcardImg"};
    //车辆信息需求进审批流字段
    public static final String[] VEHICLE_APPROVE_NEED_KEY = new String[]{ "vehicleCertiImg","regstDate", "netTansCtfctImg"};
    public static final String[] VEHICLE_APPROVE_NEED_KEY_EXCEPT_NET_CERT = new String[]{ "vehicleCertiImg","regstDate"};

    //境外车辆进审批流字段
    //todo  境外进编辑后审核的字段
    public static final String[] VEHICLE_OVERSEAS_ALL_APPROVE_NEED_KEY = new String[]{ "vehicleLicense","vehicleFullImg"};
    //境外车辆进审批流字段-车牌
    public static final String[] VEHICLE_OVERSEAS_VEHICLELICENSE_APPROVE_NEED_KEY = new String[]{ "vehicleLicense", "vehicleFullImg"};
    //境外车辆进审批流字段-颜色
    public static final String[] VEHICLE_OVERSEAS_VEHICLECOLORID_APPROVE_NEED_KEY = new String[]{ "vehicleColorId", "vehicleFullImg"};

    public static final String[] VEHICLE_OVERSEAS_VEHICLEFULLIMG_NEED_KEY = new String[]{"vehicleFullImg"};

    public static final String[] OVERSEAS_TEMPORARY_DRV_APPROVE_NEED_KEY = new String[]{"drvcardImg","drvName"};

    public static final String[] OVERSEAS_TEMPORARY_DRV_DRVCARD_APPROVE_NEED_KEY = new String[]{"drvcardImg"};

    public static final String[] TRANSPORTGROUP_NEED_KEY = new String[]{
            Fields.transportGroup_name,
            Fields.transportGroup_mode,
            Fields.transportGroup_dispatcher,
            Fields.transportGroup_dispatcherPhone,
            Fields.transportGroup_dispatcherLanguage,
            Fields.transportGroup_takeOrderLimitTime,
            Fields.transportGroup_inOrderConfigs
    };

    static {
        vehiclePropertyMap.put("vehicleLicense", "vehicle-license");
        vehiclePropertyMap.put("supplierId", "supplier-Id");
        vehiclePropertyMap.put("cityId", "city-Id");
        vehiclePropertyMap.put("vehicleTypeId", "vehicle-type-Id");
        vehiclePropertyMap.put("vehicleBrandId", "vehicle-brand");
        vehiclePropertyMap.put("vehicleSeries", "vehicle-series");
        vehiclePropertyMap.put("vehicleColorId", "vehicle-color");
        vehiclePropertyMap.put("vehicleEnergyType", "vehicle-energy-type");
        vehiclePropertyMap.put("vin", "vin");
        vehiclePropertyMap.put("regstDate", "first-registered-date");
        vehiclePropertyMap.put("usingNature", "using-nature");
        vehiclePropertyMap.put("netTansCtfctImg", "netTansCtfctImg");
        vehiclePropertyMap.put("vehicleCertiImg", "vehicleCertiImg");
        vehiclePropertyMap.put("vehicleFullImg", "vehicleFullImg");
        vehiclePropertyMap.put("vehicleFrontImg", "vehicleFrontImg");
        vehiclePropertyMap.put("vehicleBackImg", "vehicleBackImg");
        vehiclePropertyMap.put("vehicleTrunkImg", "vehicleTrunkImg");
        vehiclePropertyMap.put("hasDrv", "hasDrv");
        vehiclePropertyMap.put("comments", "comments");
        vehiclePropertyMap.put("modifyUser", "modifyUser");
        vehiclePropertyMap.put("datachangeLasttime", "datachangeLasttime");
        drvPropertyMap.put("drvName", "drvName");
        drvPropertyMap.put("drvEnglishName", "drvEnglishName");
        drvPropertyMap.put("drvLanguage", "drvLanguage");
        drvPropertyMap.put("countryId", "countryId");
        drvPropertyMap.put("countryName", "countryName");
        drvPropertyMap.put("drvEnglishName", "drvEnglishName");
        drvPropertyMap.put("vehicleLicense", "vehicleLicense");
        drvPropertyMap.put("supplierId", "supplierId");
        drvPropertyMap.put("cityId", "cityId");
        drvPropertyMap.put("vehicleId", "vehicleId");
        drvPropertyMap.put("certificateNumber", "certificateNumber");
        drvPropertyMap.put("drvAddr", "drvAddr");
        drvPropertyMap.put("vehicleTypeId", "vehicleTypeId");
        drvPropertyMap.put("email", "email");
        drvPropertyMap.put("wechat", "wechat");
        drvPropertyMap.put("workPeriod", "workPeriod");
        drvPropertyMap.put("certiDate", "certiDate");
        drvPropertyMap.put("loginAccout", "loginAccout");
        drvPropertyMap.put("drvLanguage", "drvLanguage");
        drvPropertyMap.put("certificateNumber", "certificateNumber");
        drvPropertyMap.put("intendVehicleTypeId", "intendVehicleTypeId");
        drvPropertyMap.put("quasiDrivingType", "quasiDrivingType");
        drvPropertyMap.put("expiryBeginDate", "expiryBeginDate");
        drvPropertyMap.put("expiryEndDate", "expiryEndDate");
        drvPropertyMap.put("expiryEndDate", "expiryEndDate");
        drvPropertyMap.put("drvcardImg", "drvcardImg");
        drvPropertyMap.put("idcardImg", "idcardImg");
        drvPropertyMap.put("drvHeadImg", "drvHeadImg");
        drvPropertyMap.put("peopleVehicleImg", "peopleVehicleImg");
        drvPropertyMap.put("netVehiclePeoImg", "netVehiclePeoImg");
        drvPropertyMap.put(Fields.drv_status, "status");

        transportGroupPropertyMap.put(Fields.transportGroup_name,"transportGroup_name");
        transportGroupPropertyMap.put(Fields.transportGroup_mode,"transportGroup_mode");
        transportGroupPropertyMap.put(Fields.transportGroup_dispatcher,"transportGroup_dispatcher");
        transportGroupPropertyMap.put(Fields.transportGroup_dispatcherPhone,"transportGroup_dispatcherPhone");
        transportGroupPropertyMap.put(Fields.transportGroup_takeOrderLimitTime,"transportGroup_takeOrderLimitTime");
        transportGroupPropertyMap.put(Fields.transportGroup_inOrderConfigs,"transportGroup_inOrderConfigs");
        transportGroupPropertyMap.put(Fields.transportGroup_dispatcherLanguage,"transportGroup_dispatcherLanguage");
        transportGroupPropertyMap.put(Fields.transportGroup_status,"transportGroup_status");

        drvVehRecruitingStatusPropertyMap.put(Fields.approver_status,"approver_status");
        drvVehRecruitingStatusPropertyMap.put(Fields.approver_remark,"approver_remark");

        drvFreezePropertyMap.put(Fields.freezeHour,"freezeHour");
        drvFreezePropertyMap.put(Fields.freezeReason,"freezeReason");
        drvFreezePropertyMap.put(Fields.unfreezeAction,"unfreezeAction");
        drvFreezePropertyMap.put(Fields.freezeStatus,"freezeStatus");
        drvFreezePropertyMap.put(Fields.freezeFrom,"freezeFrom");
        drvFreezePropertyMap.put(Fields.freezeOrderSet,"freezeOrderSet");
        drvFreezePropertyMap.put(Fields.unfreezeReason,"unfreezeReason");



        drvVehRecruitingStatusPropertyMap.put("drvName", "drvName");
        drvVehRecruitingStatusPropertyMap.put("drvEnglishName", "drvEnglishName");
        drvVehRecruitingStatusPropertyMap.put("drvLanguage", "drvLanguage");
        drvVehRecruitingStatusPropertyMap.put("countryId", "countryId");
        drvVehRecruitingStatusPropertyMap.put("countryName", "countryName");
        drvVehRecruitingStatusPropertyMap.put("drvEnglishName", "drvEnglishName");
        drvVehRecruitingStatusPropertyMap.put("vehicleLicense", "vehicleLicense");
        drvVehRecruitingStatusPropertyMap.put("supplierId", "supplierId");
        drvVehRecruitingStatusPropertyMap.put("cityId", "cityId");
        drvVehRecruitingStatusPropertyMap.put("vehicleId", "vehicleId");
        drvVehRecruitingStatusPropertyMap.put("certificateNumber", "certificateNumber");
        drvVehRecruitingStatusPropertyMap.put("drvAddr", "drvAddr");
        drvVehRecruitingStatusPropertyMap.put("vehicleTypeId", "vehicleTypeId");
        drvVehRecruitingStatusPropertyMap.put("email", "email");
        drvVehRecruitingStatusPropertyMap.put("wechat", "wechat");
        drvVehRecruitingStatusPropertyMap.put("workPeriod", "workPeriod");
        drvVehRecruitingStatusPropertyMap.put("certiDate", "certiDate");
        drvVehRecruitingStatusPropertyMap.put("loginAccout", "loginAccout");
        drvVehRecruitingStatusPropertyMap.put("drvLanguage", "drvLanguage");
        drvVehRecruitingStatusPropertyMap.put("certificateNumber", "certificateNumber");
        drvVehRecruitingStatusPropertyMap.put("intendVehicleTypeId", "intendVehicleTypeId");
        drvVehRecruitingStatusPropertyMap.put("quasiDrivingType", "quasiDrivingType");
        drvVehRecruitingStatusPropertyMap.put("expiryBeginDate", "expiryBeginDate");
        drvVehRecruitingStatusPropertyMap.put("expiryEndDate", "expiryEndDate");
        drvVehRecruitingStatusPropertyMap.put("expiryEndDate", "expiryEndDate");
        drvVehRecruitingStatusPropertyMap.put("drvcardImg", "drvcardImg");
        drvVehRecruitingStatusPropertyMap.put("idcardImg", "idcardImg");
        drvVehRecruitingStatusPropertyMap.put("drvHeadImg", "drvHeadImg");
        drvVehRecruitingStatusPropertyMap.put("peopleVehicleImg", "peopleVehicleImg");
        drvVehRecruitingStatusPropertyMap.put("netVehiclePeoImg", "netVehiclePeoImg");
        drvVehRecruitingStatusPropertyMap.put(Fields.drv_status, "status");
    }

    public static final String SYSTEM = "SYSTEM";

    public static final Long MillisecondsPerDay = 86400000L;

    public static final String RESTRICT_PREFIX_STR = "restrictPrefix_";

    public static final String REJECTED_STATEMENT = "Rejected because of requested conditions， Please call transport team";

    public static final String shortUrlHeaderKey = "ctrip_shorturl";

    public static final String[] shortUrlReqStructure = new String[]{"AppID","Type"};

    public static final String[] shortUrlResStructure = new String[]{"IsSuccess","Detail"};

    public static final String H5_VALIDATION_PERMISSIONS_KEY="h5_validation_permissions_key_";

    //司机废弃操作code
    public static final String drvDiscardCode = "offical_drv_discard";

    //车辆废弃操作code
    public static final String vehDiscardCode = "offical_veh_discard";

    //招募司机车辆废弃操作code
    public static final String recruitingDiscardCode = "recruting_drv_n_veh_discard";

    /**
     * 默认IVR调度语言
     */
    public static final String DEFAULT_LANGUAGE = "EN";

    /**
     * SQL 请求id 限制行数
     */
    public static final int SQL_REQUEST_ID_LIMIT_ROW_COUNT = 150;

    /**
     * 中国特殊电话码
     * 852 香港
     * 853 澳门
     * 886 台湾
     */
    public static final List<String> CHINA_SPECIAL_NATION_IGT_CODE = Lists.newArrayList("852", "853", "886");

    public static final String EVENT_SUCCESS = "0";
    public static final String EVENT_FAILED = "-1";

    public static final String GENERATE_VEHICLE_GLOBAL_ID = "generate_vehicle_global_id";
    public static final String VEHICLE_EVENT = "vehicle";

    public static final String DRIVER_SOURCE = "Driver";
    public static final String DRIVER_GUIDE_SOURCE = "DriverGuide";

    // 列表权限
    public static final String ListJurisdiction = "ListJurisdiction";
    // 流程权限
    public static final String FlowJurisdiction = "FlowJurisdiction";
    // 境内的
    public static final String cisborder = "cisborder";
    // 海外的
    public static final String overseas = "overseas";
    // 自助的
    public static final String drvAuto = "drv_auto";
    // 工作台
    public static final String workbench = "workbench";
    public static final String DRIVER_REST_APP_100027977 = "100027977";
    public static final String QUERY_USE_CACHE_FLAG = "query_use_cache_flag";

    public static final String DEFAULT_YEAR = "1777-01-01";




    public static final class EventType {

        public static final String VEHICLE = "vehicle";
        public static final String DRIVER = "driver";
        public static final String PRODUCT_LINE_ROUTE = "product_line_route";
        public static final String BRIDGE = "bridge";
        public static final String MOBLIE = "mobile";
        public static final String SPLIT_VALID = "split-valid";
        public static final String SPLIT_INVALID = "split-invalid";

        public static final String OCR_RES = "ocr_pass";

        public static final String RECRUITING_APPROVE = "recApprove";
        public static final String CACHE = "cache";
        public static final String QUERY_RESULT = "query_result";
        
        public static final String CACHE_DIFF = Constant.EventType.CACHE_DIFF;

      public static final String QUERY_TRANSPORT_GROUP_SKU_LIST_SIZE = "query_transport_group_sku_size";
    }
    public static final class EventName {

        // 同步司机账号
        public static final String SYNC_DRIVER_ACCOUNT = "sync.driver.account";
        public static final String REGISTER_DRIVER_ACCOUNT = "register.driver.account";
        public static final String UPDATE_DRIVER_ACCOUNT = "update.driver.base.info";
        public static final String CLEAN_DRIVER_CACHE = "clean.driver.cache";
        public static final String UPDATE_DRIVER_ACCOUNT_HAS_UID = "update.driver.base.info.has.uid";
        public static final String GENERATE_DRIVER_GLOBAL_ID = "generate.driver.global.id";

        public static final String QUERY_DRIVER_WITH_NO_DRIVER_ID_LIST_AND_PHONE_LIST = "query.driver.with.no.driver.id.list.and.phone.list";

        public static final String QUERY_DRV_DETAIL_EMPTY = "query.drv.detail.empty";
        public static final String QUERY_DRV_DETAIL = "query.drv.detail";
        public static final String QUERY_VEHICLE_DETAIL_EMPTY = "query.drv.detail.empty";
        public static final String MERGE_PROCESS = "mergeProcess";
        public static final String EVENT_SPLIT = "-";
        public static final String RETURN_DRIVER_RESULT = "return_driver_result";
        public static final String RETURN_EMPTY_RESULT = "return_empty_result";
        public static final String RETURN_DRIVER_GUIDE_RESULT = "return_driver_guid_result";
        public static final String MERGE_FROM_DRIVER = "merge_from_driver";


        public static final String QUERY_DRIVER = "queryDriver";
        public static final String FILTER_BY_SUPPLIER_ID = "filter_by_supplier_id";
        public static final String QUERY_DRIVER_DETAIL = "queryDrvDetail";
        public static final String QUERY_VEHICLE_DETAIL = "queryVehicleDetail";
        public static final String QUERY_VEHICLE_LIST_BY_SUPPLIER_ID = "queryVehicleListBySupplierId";
        public static final String QUERY_DRV_ID_BY_TRANSPORT_GROUPS = "queryDrvIdByTransportGroups";
        public static final String QUERY_DRIVER_ID_FOR_SAAS = "queryDriverIdForSaas";
        public static final String QUERY_VEHICLE_ID_FOR_SAAS = "queryVehicleIdForSaas";
        public static final String QUERY_DRIVER_FOR_SAAS = "queryDriverForSaas";
        public static final String QUERY_VEHICLE_FOR_SAAS = "queryVehicleForSaas";

        //缓存相关
        public static final String CACHE_EVENT_TRANSPORT_QUERY = "cache.transport.query";
        public static final String CACHE_EVENT_TRANSPORT_REFRESH = "cache.transport.refresh";
        public static final String CACHE_EVENT_DRIVER_TRANSPORT_QUERY = "cache.driver.transport.query";
        public static final String CACHE_EVENT_DRIVER_LEAVE_QUERY = "cache.driver.leave.query";
        public static final String CACHE_EVENT_DRIVER_TRANSPORT_REFRESH = "cache.driver.transport.refresh";
        public static final String CACHE_EVENT_DRIVER_LEAVE_REFRESH = "cache.driver.leave.refresh";
        public static final String CACHE_EVENT_SKU_TRANSPORT_QUERY = "cache.sku.transport.query";
        public static final String CACHE_EVENT_SKU_TRANSPORT_REFRESH = "cache.sku.transport.refresh";
        public static final String CACHE_EVENT_TRANSPORT_INTO_ORDER_QUERY = "cache.transport.into.order.query";
        public static final String CACHE_EVENT_TRANSPORT_INTO_ORDER_REFRESH = "cache.transport.into.order.refresh";
        public static final String CACHE_EVENT_DRIVER_DISPATCH_SUPPLIER_TRANSPORT_QUERY = "cache.driver.dispatch.supplier.query";
        public static final String CACHE_EVENT_DRIVER_DISPATCH_SUPPLIER_TRANSPORT_REFRESH = "cache.driver.dispatch.supplier.refresh";

        public static final String EMPTY = "empty";
        public static final String NOT_EMPTY = "not_empty";
        public static final String CACHE_EVENT_REFRESH = "db_refresh";
      public static final String REFRESH_DRV_COOP_MODE = "refresh_drv_coop_mode";
      public static final String LOGIN_ACTIVE = "login_active";
    }

}
