package com.ctrip.dcs.tms.transport.infrastructure.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhoneDTO {
    private String mobilePhone;
    private String countryCode;
    private String modifyUser;

    public PhoneDTO(String mobilePhone, String countryCode) {
        this.mobilePhone = mobilePhone;
        this.countryCode = countryCode;
    }
}
