package com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl;
import com.ctrip.dcs.tms.transport.api.model.RecruitingSOARequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsUdlMapPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TodoListCountPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehicleRecruitingPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.udl.UDLHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.CtripCommonUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.UdlMapRepository;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalRowMapper;
import com.ctrip.platform.dal.dao.KeyHolder;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaMapper;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeSelectSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.FreeUpdateSqlBuilder;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.utils.StringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository(value = "drvRecruitingRepository")
public class DrvRecruitingRepositoryImpl implements DrvRecruitingRepository {

    private static final Logger logger = LoggerFactory.getLogger(DrvRecruitingRepositoryImpl.class);

    private DalRepository<DrvRecruitingPO> drvRecruitingRepo;

    @Autowired
    DriverGroupRelationRepository driverGroupRelationRepository;

    @Autowired
    UDLHandler udlHandler;

    @Autowired
    UdlMapRepository udlMapRepository;

    private DalRowMapper<DrvRecruitingPO> drvDriverPORowMapper;

    private DalRepository<TodoListCountPO> toduListCountRepo;

    private DalRowMapper<TodoListCountPO> toduListCountPORowMapper;

    private static final String DEFAULT_TABLENAME = "drv_recruiting";

    private static final String DATA_BASE = "dcstransportdb_w";

    public DrvRecruitingRepositoryImpl() throws SQLException {
        drvRecruitingRepo = new DalRepositoryImpl<>(DrvRecruitingPO.class);
        this.drvDriverPORowMapper = new DalDefaultJpaMapper<>(DrvRecruitingPO.class);
        toduListCountRepo = new DalRepositoryImpl<>(TodoListCountPO.class);
        this.toduListCountPORowMapper = new DalDefaultJpaMapper<>(TodoListCountPO.class);
    }

    public DalRepository<DrvRecruitingPO> getDrvRecruitingRepo() {
        return drvRecruitingRepo;
    }

    @Override
    public DrvRecruitingPO queryByPK(Long drvRecruitingId) {
        return drvRecruitingRepo.queryByPk(drvRecruitingId);
    }

    @Override
    public int update(DrvRecruitingPO drvRecruitingPO) {
        return drvRecruitingRepo.update(drvRecruitingPO);
    }

    @Override
    public DrvRecruitingPO queryOneByRecruitingPhone(String drvPhone) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            FreeSelectSqlBuilder<DrvRecruitingPO> sqlBuilder = new FreeSelectSqlBuilder<>();
            sqlBuilder.requireFirst().nullable();
            sqlBuilder.setTemplate("select * from drv_recruiting").where("1=1");
            sqlBuilder.and().equal("drv_phone",drvPhone,Types.VARCHAR);
            sqlBuilder.and().equal("active",Boolean.TRUE,Types.BIT);
            sqlBuilder.mapWith(drvDriverPORowMapper);
            return drvRecruitingRepo.getQueryDao().query(sqlBuilder, hints);
        } catch (Exception e){
            logger.error("queryOneByRecruitingPhone error", e);
        }
        return null;
    }

    @Override
    public Long addDrvRecruiting(DrvRecruitingPO po) throws SQLException {
        KeyHolder keyHolder = new KeyHolder();
        po.setProviderDataLocation(udlHandler.getDrvUdlByCityId(po.getCityId()));
        drvRecruitingRepo.insert(new DalHints(), keyHolder, po);
        udlMapRepository.insert(new TmsUdlMapPO(po.getProviderDataLocation(), keyHolder.getKey().longValue(),
            CommonEnum.RecordTypeEnum.RECRUIT.getCode()));
        return keyHolder.getKey().longValue();
    }

    @Override
    public Integer syncDrvFromVeh(Long vehicleId, Long vehicleTypeId, String vehicleLicense, String modifyUser) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_recruiting set vehicle_license = ? ,vehicle_type_id = ? ,datachange_lasttime = ?,modify_user = ?  where vehicle_id = ? and approver_status in (?)");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "vehicle_license", Types.VARCHAR, vehicleLicense);
        parameters.setSensitive(i++, "vehicle_type_id", Types.BIGINT, vehicleTypeId);
        parameters.setSensitive(i++, "datachange_lasttime", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
        parameters.setSensitive(i++, "vehicle_id", Types.BIGINT, vehicleId);
        parameters.setInParameter(i++,"approver_status",Types.INTEGER, Arrays.asList(
                TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),
                TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode()));
        return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public Integer updateDrvRecApproverStatus(List<Long> drvRecruitingIds, Integer approverStatus, String remark, String modifyUser,Integer checkStatus,Boolean approveTimeFlag,Integer bdTurnDownCount) throws SQLException{
        if(CollectionUtils.isEmpty(drvRecruitingIds)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        StringBuilder sqlBuilder = new StringBuilder("update drv_recruiting set approver_status = ? ,datachange_lasttime = ?,modify_user = ?,remark = ? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "approver_status", Types.INTEGER, approverStatus);
        parameters.setSensitive(i++, "datachange_lasttime", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?"":modifyUser);
        parameters.setSensitive(i++, "remark", Types.VARCHAR, StringUtils.isEmpty(remark)?"":remark);
        if(checkStatus!=null){
            sqlBuilder.append(",check_status = ? ");
            parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
        }
        if(approveTimeFlag){
            sqlBuilder.append(",approve_time = ? ");
            parameters.setSensitive(i++, "approve_time", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
            sqlBuilder.append(",approve_aging = ? ");
            parameters.setSensitive(i++, "approve_aging", Types.INTEGER, TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
        }
        if(Objects.equals(approverStatus, TmsTransportConstant.RecruitingApproverStatusEnum.operating_turnDown.getCode())){
            sqlBuilder.append(",bd_approve_status = ? ");
            parameters.setSensitive(i++, "bd_approve_status", Types.INTEGER, 1);
            sqlBuilder.append(",bd_turn_down_count = ? ");
            parameters.setSensitive(i++, "bd_turn_down_count", Types.INTEGER, bdTurnDownCount + 1);
        }
        if(Objects.equals(approverStatus, TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode())){
            sqlBuilder.append(",approve_aging = ? ");
            parameters.setSensitive(i++, "approve_aging", Types.INTEGER, TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
        }
        sqlBuilder.append(" where drv_recruiting_id in (?) ");
        parameters.setInParameter(i++,"drv_recruiting_id",Types.BIGINT, drvRecruitingIds);
        sqlBuilder.append(" and active = 1  ");
        builder.setTemplate(sqlBuilder.toString());
        return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<Long> getVehicleIdList(List<Long> drvRecruitingIds, int drvFrom) throws SQLException {
        if (org.springframework.util.CollectionUtils.isEmpty(drvRecruitingIds)) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.in("drv_recruiting_id",drvRecruitingIds,Types.BIGINT);
        builder.and();
        builder.equalNullable("drv_from", drvFrom, Types.TINYINT);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        List<DrvRecruitingPO> idList = drvRecruitingRepo.getDao().query(builder,hints);
        if (org.springframework.util.CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        List<Long> result = Lists.newArrayListWithExpectedSize(idList.size());
        for (DrvRecruitingPO recruitingPO : idList) {
            result.add(recruitingPO.getVehicleId());
        }
        return result;
    }

    @Override
    public List<DrvRecruitingPO> getDrvRecruitingList(List<Long> drvRecruitingIds) throws SQLException {
        if (org.springframework.util.CollectionUtils.isEmpty(drvRecruitingIds)) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.in("drv_recruiting_id",drvRecruitingIds,Types.BIGINT);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        return drvRecruitingRepo.getDao().query(builder,hints);
    }

    @Override
    public Boolean checkDrvOnly(String str, Integer type,List<Integer> approverStatus) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
            builder.and();
            builder.equal("drv_phone", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
            builder.and();
            builder.equal("drv_idcard", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
            builder.and();
            builder.equal("login_account", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
            builder.and();
            builder.equal("email", str, Types.VARCHAR, false);
        }
        builder.and();
        builder.notIn("approver_status",approverStatus,Types.INTEGER,false);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        if (drvRecruitingRepo.getDao().count(builder, hints).intValue() > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<DrvRecruitingPO> queryDrvRecruitingBySupplierIdAndId(List<Long> drvRecruitingIds, Long supplierId) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.in("drv_recruiting_id",drvRecruitingIds,Types.BIGINT);
            builder.and().equal("supplier_id", supplierId, Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvRecruitingRepo.getDao().query(builder,hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateCheckStatus(List<Long> drvRecruitingIds, int checkStatus) throws SQLException {
        if(CollectionUtils.isEmpty(drvRecruitingIds)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_recruiting set check_status = ?,approve_time = now()  where drv_recruiting_id in (?)");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
        parameters.setInParameter(i++,"drv_recruiting_id",Types.BIGINT, drvRecruitingIds);
        return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public int unBingVehicleFromDrvRecruiting(List<Long> vehicleids,String modifyUser) {
        try {
            if(CollectionUtils.isEmpty(vehicleids)){
                return 0;
            }
            DalHints hints = DalHints.createIfAbsent(null);
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update drv_recruiting set vehicle_id = 0 ,vehicle_license = '',vehicle_type_id = 0,modify_user = ? where vehicle_id in (?) and drv_from = 2 ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, StringUtils.isEmpty(modifyUser)?"":modifyUser);
            parameters.setInParameter(i++,"vehicle_id",Types.BIGINT, vehicleids);
            return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<TodoListCountPO> todoListCount(Long supplierId) {
        DalHints hints = DalHints.createIfAbsent(null);
        FreeSelectSqlBuilder<List<TodoListCountPO>> builder = new FreeSelectSqlBuilder<>();
        StringBuilder sqlStr = new StringBuilder("select supplier_id,count(1) count from drv_recruiting where approver_status = 0 and active = 1 ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        if (supplierId !=null && supplierId >0) {
            sqlStr.append(" AND supplier_id = ? ");
            parameters.set(i++, "supplier_id", Types.BIGINT, supplierId);
        }
        sqlStr.append(" group by supplier_id");
        builder.setTemplate(sqlStr.toString());
        try {
            builder.mapWith(toduListCountPORowMapper);
            return toduListCountRepo.getQueryDao().query(builder, parameters, hints);
        } catch (SQLException e) {
            return Lists.newArrayList();
        }
    }

    @Override
    public int countApproveIngDrvRecruiting(List<Long> drvIds, Integer approveStatus, Integer checkStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        try {
            builder.equal("approver_status",approveStatus,Types.INTEGER);
            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            if(CollectionUtils.isNotEmpty(drvIds)){
                builder.and().in("drv_recruiting_id",drvIds,Types.BIGINT);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvRecruitingRepo.getDao().count(builder,hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvRecruitingPO> queryApproveIngDrvRecruiting(List<Long> drvIds, Integer approveStatus, Integer checkStatus) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        try {
            builder.equal("approver_status",approveStatus,Types.INTEGER);
            builder.and().equal("check_status",checkStatus,Types.INTEGER);
            if(CollectionUtils.isNotEmpty(drvIds)){
                builder.and().in("drv_recruiting_id",drvIds,Types.BIGINT);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("datachange_lasttime", false);
            return drvRecruitingRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateApproveAging(List<Long> drvRecruitingIds, int approveAging) throws SQLException {
        if (CollectionUtils.isEmpty(drvRecruitingIds)) {
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_recruiting set approve_aging = ?  where drv_recruiting_id in (?)");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "approve_aging", Types.INTEGER, approveAging);
        parameters.setInParameter(i++, "drv_recruiting_id", Types.BIGINT, drvRecruitingIds);
        return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<DrvRecruitingPO> queryvRecruitingByVehicleId(Long vehicleId) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        try {
            builder.equal("vehicle_id",vehicleId,Types.BIGINT);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("drv_recruiting_id", false);
            return drvRecruitingRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateDrvCheckStatus(Long recruitingId, int checkStatus, String modifyUser) throws SQLException {
        if(recruitingId == null || recruitingId <= 0){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        builder.setTemplate("update drv_recruiting set check_status = ?,approve_time = now(),approve_time = now(),approve_aging = 1,modify_user = ?  where drv_recruiting_id =? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "check_status", Types.INTEGER, checkStatus);
        parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
        parameters.setSensitive(i++,"drv_recruiting_id",Types.BIGINT, recruitingId);
        return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public Boolean checkRecruitingDrvOnly(String str, Integer type) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
            builder.and();
            builder.equal("drv_phone", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
            builder.and();
            builder.equal("drv_idcard", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
            builder.and();
            builder.equal("login_account", str, Types.VARCHAR, false);
        }
        if (Objects.equals(type, TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
            builder.and();
            builder.equal("email", str, Types.VARCHAR, false);
        }
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        if (drvRecruitingRepo.getDao().count(builder, hints).intValue() > 0) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<DrvRecruitingPO> queryWaitApproveRecruitingDrvByPage(int pageNo, int pageSize) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        try {
            builder.equal("approver_status", TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("drv_recruiting_id", false);
            builder.atPage(pageNo,pageSize);
            return drvRecruitingRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int countWaitApproveRecruitingDrv() {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        try {
            builder.equal("approver_status", TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode(),Types.INTEGER);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvRecruitingRepo.getDao().count(builder, hints).intValue();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int updateApproveAging(Long recruitingId) throws SQLException {
        if(recruitingId == null){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        StringBuilder sqlBuilder = new StringBuilder("update drv_recruiting set approve_time = ? ,approve_aging = ? where drv_recruiting_id = ? ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        parameters.setSensitive(i++, "approve_time", Types.TIMESTAMP, new Timestamp(System.currentTimeMillis()));
        parameters.setSensitive(i++, "approve_aging", Types.INTEGER, TmsTransportConstant.ApproveAgingEnum.NOTIMEOUT.getCode());
        parameters.setSensitive(i++,"drv_recruiting_id",Types.BIGINT, recruitingId);
        builder.setTemplate(sqlBuilder.toString());
        return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public List<DrvRecruitingPO> queryRecruitingDrvByPhone(String drvPhone, Long recruitingId) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("drv_recruiting_id","approver_status","version_flag");
        if (recruitingId != null && recruitingId > 0) {
            builder.and();
            builder.equal("drv_recruiting_id", recruitingId, Types.BIGINT, false);
        }
        builder.and();
        builder.equal("drv_phone", drvPhone, Types.VARCHAR, false);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        builder.orderBy("drv_recruiting_id",false);
        return drvRecruitingRepo.getDao().query(builder,hints);
    }

    @Override
    public List<DrvRecruitingPO> queryDomesticRecruitingDrvByPhone(String drvPhone) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.and();
        builder.equal("drv_phone", drvPhone, Types.VARCHAR, false);
        builder.and().equal("internal_scope", AreaScopeTypeEnum.DOMESTIC.getCode(),Types.INTEGER,false);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        builder.orderBy("drv_recruiting_id",false);
        return drvRecruitingRepo.getDao().query(builder,hints);
    }

    @Override
    public int updateDrvApproveSchedule(Long recruitingId, Integer accountType, Integer approveSchedule) throws SQLException {
        if(recruitingId == null || recruitingId <= 0){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
        StringBuilder sqlStr = new StringBuilder();
        sqlStr.append("update drv_recruiting set ");
        StatementParameters parameters = new StatementParameters();
        int i = 1;
        if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
            sqlStr.append(" supplier_approve_schedule = ? ");
            parameters.setSensitive(i++, "supplier_approve_schedule", Types.INTEGER, approveSchedule);
        }
        if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
            sqlStr.append(" approve_schedule = ? ");
            parameters.setSensitive(i++, "approve_schedule", Types.INTEGER, approveSchedule);
        }
        sqlStr.append(" where drv_recruiting_id = ? ");
        parameters.setSensitive(i++,"drv_recruiting_id",Types.BIGINT, recruitingId);
        builder.setTemplate(sqlStr.toString());
        return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
    }

    @Override
    public Long findNewDrvRecruitingCount(RecruitingSOARequestDTO recruitingSOARequestDTO, List<Integer> jurisdictionIdList, List<Integer> lineCodeList, Integer accountType) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectCount();
        if (CollectionUtils.isNotEmpty(recruitingSOARequestDTO.getRecruitingIdList())) {
            builder.and();
            builder.in("drv_recruiting_id", recruitingSOARequestDTO.getRecruitingIdList(),Types.BIGINT,false);
        }
        if (recruitingSOARequestDTO.getRecruitingId()!=null && recruitingSOARequestDTO.getRecruitingId() > 0) {
            builder.and();
            builder.equal("drv_recruiting_id", recruitingSOARequestDTO.getRecruitingId(),Types.BIGINT,false);
        }
        if (recruitingSOARequestDTO.getSupplierId() != null) {
            builder.and();
            builder.equal("supplier_id", recruitingSOARequestDTO.getSupplierId(),Types.BIGINT,false);
        }
        if (recruitingSOARequestDTO.getApproveStatus()!=null) {
            builder.and();
            builder.equal("approver_status", recruitingSOARequestDTO.getApproveStatus(),Types.INTEGER,false);
        }
        if (recruitingSOARequestDTO.getVehicleTypeId() != null && recruitingSOARequestDTO.getVehicleTypeId().intValue() != 0) {
            builder.and();
            builder.equal("vehicle_type_id", recruitingSOARequestDTO.getVehicleTypeId(),Types.BIGINT,false);
        }
        if (recruitingSOARequestDTO.getCityId() != null) {
            builder.and();
            builder.equal("city_id", recruitingSOARequestDTO.getCityId(),Types.BIGINT,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getVehicleLicense())) {
            builder.and();
            builder.like("vehicle_license", recruitingSOARequestDTO.getVehicleLicense()+"%",Types.VARCHAR,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvName())) {
            builder.and();
            builder.like("drv_name", recruitingSOARequestDTO.getDrvName()+"%",Types.VARCHAR,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvEnglishName())) {
            builder.and();
            builder.like("drv_english_name", toLikeStr(recruitingSOARequestDTO.getDrvEnglishName()),Types.VARCHAR,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvPhone())) {
            builder.and();
            builder.in("drv_phone", Arrays.asList(recruitingSOARequestDTO.getDrvPhone().split(",|，")),Types.VARCHAR,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvIdcard())) {
            builder.and();
            builder.equal("drv_idcard", recruitingSOARequestDTO.getDrvIdcard(),Types.VARCHAR,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvLanguage())) {
            builder.and();
            builder.like("drv_language", toLikeStr(recruitingSOARequestDTO.getDrvLanguage()),Types.VARCHAR,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDatachangeCreatetimeStart())) {
            builder.and();
            builder.greaterThanEquals("datachange_createtime", DateUtil.string2Timestamp(recruitingSOARequestDTO.getDatachangeCreatetimeStart(),DateUtil.YYYYMMDDHHMMSS),Types.TIMESTAMP,false);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDatachangeCreatetimeEnd())) {
            builder.and();
            builder.lessThanEquals("datachange_createtime", DateUtil.string2Timestamp(recruitingSOARequestDTO.getDatachangeCreatetimeEnd(),DateUtil.YYYYMMDDHHMMSS),Types.TIMESTAMP,false);
        }
        if (recruitingSOARequestDTO.getCheckStatus() != null && recruitingSOARequestDTO.getCheckStatus() > 0) {
            builder.and();
            builder.equal("check_status", recruitingSOARequestDTO.getCheckStatus(),Types.INTEGER,false);
        }
        if (CollectionUtils.isNotEmpty(lineCodeList)) {
            builder.and();
            builder.in("category_synthesize_code", lineCodeList,Types.INTEGER,false);
        }
        if (recruitingSOARequestDTO.getApproveSchedule()!=null) {
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
                builder.and();
                builder.equal("supplier_approve_schedule", recruitingSOARequestDTO.getApproveSchedule(),Types.INTEGER,false);
            }
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                builder.and();
                builder.equal("approve_schedule", recruitingSOARequestDTO.getApproveSchedule(),Types.INTEGER,false);
            }
        }
        if (recruitingSOARequestDTO.getApproveAging()!=null) {
            builder.and();
            builder.equal("approve_aging", recruitingSOARequestDTO.getApproveAging(),Types.INTEGER,false);
        }
        if (recruitingSOARequestDTO.getSupplierId() == null) {
            builder.and();
            builder.greaterThan("supplier_id", 0L,Types.BIGINT,false);
        }
        builder.and();
        builder.equal("active", CtripCommonUtils.queryActiveChoose(recruitingSOARequestDTO.isActive()),Types.BIT);
        return drvRecruitingRepo.getDao().count(builder, hints).longValue();
    }

    @Override
    public List<DrvRecruitingPO> queryDriverRecruitingInfo(String driverPhone, String driverIdCard, String vehicleLicense,List<Long> drvRecruitingIds) {
        try{
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            if (!Strings.isNullOrEmpty(vehicleLicense)) {
                builder.and();
                builder.equal("vehicle_license", vehicleLicense,Types.VARCHAR,false);
            }
            if (!Strings.isNullOrEmpty(driverPhone)) {
                builder.and();
                builder.equal("drv_phone", driverPhone,Types.VARCHAR,false);
            }
            if (!Strings.isNullOrEmpty(driverIdCard)){
                builder.and();
                builder.equal("drv_idcard",driverIdCard,Types.VARCHAR,false);
            }
            if (!CollectionUtils.isEmpty(drvRecruitingIds)){
                builder.and();
                builder.in("drv_recruiting_id",drvRecruitingIds,Types.BIGINT,false);
            }
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvRecruitingRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int syncDiscardvRecruitingDrv(Long drvId, String drvPhone, String drvIdCard, String modifyUser, Boolean active) {
        if(drvId == null || drvId == 0 || StringUtils.isEmpty(drvPhone) || StringUtils.isEmpty(drvIdCard)){
            return 0;
        }
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlBuilder = new StringBuilder("update drv_recruiting set active = ? ,modify_user = ? where drv_id = ? or  drv_phone = ? or drv_idcard = ?  ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "active", Types.BIT, active);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR, modifyUser);
            parameters.setSensitive(i++,"drv_id",Types.BIGINT, drvId);
            parameters.setSensitive(i++,"drv_phone",Types.VARCHAR, drvPhone);
            parameters.setSensitive(i++,"drv_idcard",Types.VARCHAR, drvIdCard);
            builder.setTemplate(sqlBuilder.toString());
            return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int discardRecruitingDrv(Long recruitingId, Boolean active, String modifyUser) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            builder.setTemplate("update drv_recruiting set active = ?,modify_user = ? where drv_recruiting_id = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.setSensitive(i++, "active", Types.BIT,active);
            parameters.setSensitive(i++, "modify_user", Types.VARCHAR,modifyUser);
            parameters.setSensitive(i++, "drv_recruiting_id", Types.BIGINT, recruitingId);
            return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
        } catch (SQLException e) {
            throw new BaijiRuntimeException(e);
        }
    }

    /**
    　* @description: 只限车辆列表使用，其它接口慎用
    　* <AUTHOR>
    　* @date 2022/9/1 11:32
    */
    @Override
    public List<DrvRecruitingPO> queryvRecruitingByVehicleIds(List<Long> vehicleIds) {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        try {
            builder.in("vehicle_id",vehicleIds,Types.BIGINT);
//            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.orderBy("drv_recruiting_id", false);
            return drvRecruitingRepo.getDao().query(builder, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public List<DrvRecruitingPO> queryRecruitingDrvByPhoneORId(String drvPhone, Long recruitingId) throws SQLException {
        if(org.apache.commons.lang3.StringUtils.isEmpty(drvPhone)){
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        if (recruitingId != null && recruitingId > 0) {
            builder.and();
            builder.equal("drv_recruiting_id", recruitingId, Types.BIGINT, false);
        }
        builder.and();
        builder.equal("drv_phone", drvPhone, Types.VARCHAR, false);
        builder.and().equal("active",Boolean.TRUE,Types.BIT);
        builder.orderBy("drv_recruiting_id",false);
        return drvRecruitingRepo.getDao().query(builder,hints);
    }

    @Override
    public List<Long> findDrvRecruitingIdList(RecruitingSOARequestDTO recruitingSOARequestDTO, PaginatorDTO paginator, List<Integer> lineCodeList, Integer accountType) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("drv_recruiting_id");
        if (CollectionUtils.isNotEmpty(recruitingSOARequestDTO.getRecruitingIdList())) {
            builder.and();
            builder.in("drv_recruiting_id", recruitingSOARequestDTO.getRecruitingIdList(),Types.BIGINT);
        }
        if (recruitingSOARequestDTO.getRecruitingId()!=null && recruitingSOARequestDTO.getRecruitingId() > 0) {
            builder.and();
            builder.equal("drv_recruiting_id", recruitingSOARequestDTO.getRecruitingId(),Types.BIGINT);
        }
        if (recruitingSOARequestDTO.getSupplierId() != null) {
            builder.and();
            builder.equal("supplier_id", recruitingSOARequestDTO.getSupplierId(),Types.BIGINT);
        }
        if (recruitingSOARequestDTO.getApproveStatus()!=null) {
            builder.and();
            builder.equal("approver_status", recruitingSOARequestDTO.getApproveStatus(),Types.INTEGER);
        }
        if (recruitingSOARequestDTO.getVehicleTypeId() != null && recruitingSOARequestDTO.getVehicleTypeId().intValue() != 0) {
            builder.and();
            builder.equal("vehicle_type_id", recruitingSOARequestDTO.getVehicleTypeId(),Types.BIGINT);
        }
        if (recruitingSOARequestDTO.getCityId() != null) {
            builder.and();
            builder.equal("city_id", recruitingSOARequestDTO.getCityId(),Types.BIGINT);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getVehicleLicense())) {
            builder.and();
            builder.like("vehicle_license", recruitingSOARequestDTO.getVehicleLicense()+"%",Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvName())) {
            builder.and();
            builder.like("drv_name", recruitingSOARequestDTO.getDrvName()+"%",Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvEnglishName())) {
            builder.and();
            builder.like("drv_english_name", toLikeStr(recruitingSOARequestDTO.getDrvEnglishName()),Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvPhone())) {
            builder.and();
            builder.in("drv_phone", Arrays.asList(recruitingSOARequestDTO.getDrvPhone().split(",|，")),Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvIdcard())) {
            builder.and();
            builder.equal("drv_idcard", recruitingSOARequestDTO.getDrvIdcard(),Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDrvLanguage())) {
            builder.and();
            builder.like("drv_language", toLikeStr(recruitingSOARequestDTO.getDrvLanguage()),Types.VARCHAR);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDatachangeCreatetimeStart())) {
            builder.and();
            builder.greaterThanEquals("datachange_createtime", DateUtil.string2Timestamp(recruitingSOARequestDTO.getDatachangeCreatetimeStart(),DateUtil.YYYYMMDDHHMMSS),Types.TIMESTAMP);
        }
        if (!Strings.isNullOrEmpty(recruitingSOARequestDTO.getDatachangeCreatetimeEnd())) {
            builder.and();
            builder.lessThanEquals("datachange_createtime", DateUtil.string2Timestamp(recruitingSOARequestDTO.getDatachangeCreatetimeEnd(),DateUtil.YYYYMMDDHHMMSS),Types.TIMESTAMP);
        }
        if (recruitingSOARequestDTO.getCheckStatus() != null && recruitingSOARequestDTO.getCheckStatus() > 0) {
            builder.and();
            builder.equal("check_status", recruitingSOARequestDTO.getCheckStatus(),Types.INTEGER);
        }
        if (CollectionUtils.isNotEmpty(lineCodeList)) {
            builder.and();
            builder.in("category_synthesize_code", lineCodeList,Types.INTEGER);
        }
        if (recruitingSOARequestDTO.getApproveSchedule()!=null) {
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue())){
                builder.and();
                builder.equal("supplier_approve_schedule", recruitingSOARequestDTO.getApproveSchedule(),Types.INTEGER);
            }
            if(Objects.equals(accountType, TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue())){
                builder.and();
                builder.equal("approve_schedule", recruitingSOARequestDTO.getApproveSchedule(),Types.INTEGER);
            }
        }
        if (recruitingSOARequestDTO.getApproveAging()!=null) {
            builder.and();
            builder.equal("approve_aging", recruitingSOARequestDTO.getApproveAging(),Types.INTEGER);
        }
        builder.and();
        builder.equal("active",CtripCommonUtils.queryActiveChoose(recruitingSOARequestDTO.isActive()),Types.BIT);
        builder.atPage(paginator.getPageNo(),paginator.getPageSize());
        builder.orderBy("datachange_lasttime",false);

        List<DrvRecruitingPO> recruitingPOList = drvRecruitingRepo.getDao().query(builder, hints);
        if(CollectionUtils.isEmpty(recruitingPOList)){
            return Collections.emptyList();
        }
        return recruitingPOList.stream().map(DrvRecruitingPO::getDrvRecruitingId).collect(Collectors.toList());
    }

    @Override
    public List<DrvRecruitingPO> queryDrvRecruitingListByIds(List<Long> drvRecruitingIds) throws SQLException {
        if (CollectionUtils.isEmpty(drvRecruitingIds)) {
            return Collections.emptyList();
        }
        DalHints hints = DalHints.createIfAbsent(null);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll();
        builder.in("drv_recruiting_id",drvRecruitingIds,Types.BIGINT);
        builder.orderBy("datachange_lasttime",false);
        return drvRecruitingRepo.getDao().query(builder,hints);
    }

    @Override
    public int updateDrvImgQToC(DrvRecruitingPO drvRecruitingPO) {
        DalHints hints = DalHints.createIfAbsent(null);
        try {
            if(StringUtils.isEmpty(drvRecruitingPO.getDrvcardImg()) && StringUtils.isEmpty(drvRecruitingPO.getIdcardImg()) && StringUtils.isEmpty(drvRecruitingPO.getDrvHeadImg()) &&
                    StringUtils.isEmpty(drvRecruitingPO.getPeopleVehicleImg()) && StringUtils.isEmpty(drvRecruitingPO.getNetVehiclePeoImg())&& StringUtils.isEmpty(drvRecruitingPO.getIdcardBackImg())){
                return 0;
            }
            FreeUpdateSqlBuilder builder = new FreeUpdateSqlBuilder();
            StringBuilder sqlStr = new StringBuilder();
            sqlStr.append("update drv_recruiting set modify_user = ? ");
            StatementParameters parameters = new StatementParameters();
            int i = 1;
            parameters.set(i++, "modify_user", Types.VARCHAR,TmsTransportConstant.TMS_DEFAULT_USERNAME);
            if(StringUtils.isNotEmpty(drvRecruitingPO.getDrvcardImg())){
                sqlStr.append(", drvcard_img = ? ");
                parameters.set(i++, "drvcard_img", Types.VARCHAR,drvRecruitingPO.getDrvcardImg());
            }
            if(StringUtils.isNotEmpty(drvRecruitingPO.getIdcardImg())){
                sqlStr.append(", idcard_img = ? ");
                parameters.set(i++, "idcard_img", Types.VARCHAR,drvRecruitingPO.getIdcardImg());
            }
            if(StringUtils.isNotEmpty(drvRecruitingPO.getDrvHeadImg())){
                sqlStr.append(", drv_head_img = ? ");
                parameters.set(i++, "drv_head_img", Types.VARCHAR,drvRecruitingPO.getDrvHeadImg());
            }
            if(StringUtils.isNotEmpty(drvRecruitingPO.getPeopleVehicleImg())){
                sqlStr.append(", people_vehicle_img = ? ");
                parameters.set(i++, "people_vehicle_img", Types.VARCHAR,drvRecruitingPO.getPeopleVehicleImg());
            }
            if(StringUtils.isNotEmpty(drvRecruitingPO.getNetVehiclePeoImg())){
                sqlStr.append(", net_vehicle_peo_img = ? ");
                parameters.set(i++, "net_vehicle_peo_img", Types.VARCHAR,drvRecruitingPO.getNetVehiclePeoImg());
            }
            if(StringUtils.isNotEmpty(drvRecruitingPO.getIdcardBackImg())){
                sqlStr.append(", idcard_back_img = ? ");
                parameters.set(i++, "idcard_back_img", Types.VARCHAR,drvRecruitingPO.getIdcardBackImg());
            }
            sqlStr.append(" where drv_recruiting_id = ? ");
            parameters.set(i++, "drv_recruiting_id", Types.BIGINT, drvRecruitingPO.getDrvRecruitingId());
            builder.setTemplate(sqlStr.toString());
            return drvRecruitingRepo.getQueryDao().update(builder, parameters, hints);
        }catch (Exception e){
            throw new BaijiRuntimeException("updateDrvImgQToC error", e);
        }
    }

    @Override
    public int countDrvDirtyPhone() {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            builder.like("drv_phone", "0%", Types.VARCHAR);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            return drvRecruitingRepo.getDao().count(builder, hints).intValue();
        } catch (Exception e){
            logger.error("countDrvDirtyPhone error", e);
        }
        return 0;
    }

    @Override
    public List<DrvRecruitingPO> queryDrvDirtyPhone(int pageNo, int pageSize) {
        try {
            DalHints hints = DalHints.createIfAbsent(null);
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll();
            builder.like("drv_phone", "0%", Types.VARCHAR);
            builder.and().equal("active",Boolean.TRUE,Types.BIT);
            builder.atPage(pageNo, pageSize);
            return drvRecruitingRepo.getDao().query(builder, hints);
        } catch (Exception e){
            logger.error("queryDrvDirtyPhone error", e);
        }
        return Collections.emptyList();
    }

    private String toLikeStr(String likeStr) {
        if (Strings.isNullOrEmpty(likeStr)) {
            return null;
        }
        return likeStr + "%";
    }

    private Timestamp getTimestamp(String timestamp) {
        if (Strings.isNullOrEmpty(timestamp)) {
            return null;
        }
        return DateUtil.string2Timestamp(timestamp, DateUtil.YYYYMMDDHHMMSS);
    }
    
    @Override
    public List<DrvRecruitingPO> queryFromByPage(long lastDriverId, int pageSize) {
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll().atPage(1, pageSize);
            builder.orderBy("drv_recruiting_id", true);
            builder.where("drv_recruiting_id > " + lastDriverId);
            return drvRecruitingRepo.getDao().query(builder,DalHints.createIfAbsent(null));
        } catch (Exception e) {
            logger.error("queryDrvDirtyPhone error", e);
        }
        return Collections.emptyList();
    }
    
    @Override
    public long countAll() {
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectCount();
            return drvRecruitingRepo.getDao().count(builder,DalHints.createIfAbsent(null)).longValue();
        } catch (Exception e) {
            logger.error("queryDrvDirtyPhone error", e);
        }
        return 0L;
    }
}
