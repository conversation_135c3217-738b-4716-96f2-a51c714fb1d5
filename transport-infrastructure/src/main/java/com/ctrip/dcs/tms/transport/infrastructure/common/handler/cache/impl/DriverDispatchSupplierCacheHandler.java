package com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.impl;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDispatchRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ModRecordConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.handler.cache.AbstraceCacheHandler;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CacheBusinessQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDispatchRelationRepository;
import com.ctrip.frt.xresource.framework.utility.json.JsonUtil;
import com.ctrip.frt.xresource.framework.utility.redis.ResourceFillDataAction;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DriverDispatchSupplierCacheHandler extends AbstraceCacheHandler {

  @Autowired
  private DrvDispatchRelationRepository drvDispatchRelationRepository;

  @Autowired
  CacheBusinessQconfig cacheBusinessQconfig;

  public List<DrvDispatchRelationPO> queryDrvDispatchSupplierIds(ArrayList<Long> drvIds) {
    Cat.logEvent(Constant.EventType.CACHE, Constant.EventName.CACHE_EVENT_DRIVER_DISPATCH_SUPPLIER_TRANSPORT_QUERY);
    List<List<DrvDispatchRelationPO>> resultList =
      mgetHit(drvIds.stream().map(String::valueOf).collect(Collectors.toList()),
        JsonUtil.buildJavaType(new TypeReference<List<DrvDispatchRelationPO>>() {
        }.getType()));
    return Optional.ofNullable(resultList).orElse(Collections.emptyList()).stream().flatMap(List::stream).collect(
      Collectors.toList());
  }
  @Override
  public void refreshCache(DataChange dataChange) {
    doRefresh(getBusinessId(dataChange));
  }

  @Override
  protected String getCacheName() {
    return "drv_dsp";
  }

  @Override
  protected ResourceFillDataAction getFillDataAction() {
    return new ResourceFillDataAction<List<DrvDispatchRelationPO>>() {
      @SneakyThrows
      @Override
      public Map<String, List<DrvDispatchRelationPO>> refreshAction(List<String> keyList) {
        Cat.logEvent(Constant.EventType.CACHE, Constant.EventName.CACHE_EVENT_DRIVER_DISPATCH_SUPPLIER_TRANSPORT_REFRESH);
        Map<String, List<DrvDispatchRelationPO>> resultMap = drvDispatchRelationRepository.queryDrvDispatchSupplierIds(keyList.stream().map(Long::valueOf).collect(
          Collectors.toList())).stream().collect(Collectors.groupingBy(data -> String.valueOf(data.getDrvId())));

        // 对于没有绑定关系的，直接返回空list
        Map<String, List<DrvDispatchRelationPO>> cacheMap = Maps.newHashMap();
        keyList.forEach(key -> cacheMap.put(key, resultMap.getOrDefault(key, Collections.emptyList())));
        return cacheMap;
      }

      @Override
      public String getVersion() {
        return null;
      }

      @Override
      public boolean enableDiff() {
        return cacheBusinessQconfig.getConfig().isDrvDispatchSupplierDiff();
      }

      @Override
      public void diff(Map<String, List<DrvDispatchRelationPO>> oldDataMap, Map<String, List<DrvDispatchRelationPO>> newDataMap) {
        if (Objects.isNull(oldDataMap) || Objects.isNull(newDataMap)) {
          return;
        }
        for (Map.Entry<String, List<DrvDispatchRelationPO>> entry : oldDataMap.entrySet()) {
          String key = entry.getKey();
          List<DrvDispatchRelationPO> newData = newDataMap.get(key);
          if (CollectionUtils.isEmpty(newData)) {
            Cat.logEvent(Constant.EventType.CACHE_DIFF, getCacheName());
            return;
          }
          if (!Objects.equals(entry.getValue(), newData)) {
            Cat.logEvent(Constant.EventType.CACHE_DIFF, getCacheName());
            return;
          }
        }
      }

    };
  }

  @Override
  public boolean support(String tableName) {
    return ModRecordConstant.TableName.tmsDrvDispatchRelation.equalsIgnoreCase(tableName);
  }

}
