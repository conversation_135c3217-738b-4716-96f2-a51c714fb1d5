package com.ctrip.dcs.tms.transport.infrastructure.port.repository;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.OfflineVehicleInformationPO;

import java.time.LocalDate;
import java.util.List;

public interface OfflineVehicleInformationRepository {

    void saveOfflineVehicleInformation(List<OfflineVehicleInformationPO> offlineVehicleInformationPO);

    List<OfflineVehicleInformationPO>  queryofflineVehicleInformation(long id, LocalDate localDate);
}
