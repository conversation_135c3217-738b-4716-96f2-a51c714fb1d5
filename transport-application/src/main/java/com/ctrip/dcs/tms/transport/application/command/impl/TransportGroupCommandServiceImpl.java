package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.price.sku.pricing.facade.dto.SimpleAreaGroupDTO;
import com.ctrip.dcs.price.sku.pricing.facade.dto.SimpleCityAreaInfoDTO;
import com.ctrip.dcs.tms.transport.api.model.DriverRelationBindRequestSOAType;
import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupApplyStatusSOADTO;
import com.ctrip.dcs.tms.transport.api.model.UpdateTransportGroupApplyStatusSOARequestType;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.command.TransportGroupCommandService;
import com.ctrip.dcs.tms.transport.application.helper.ShortTransportGroupHelper;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.TmsPmsproductQueryService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.DriverPointsQueryProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverPoint;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.ApplyDriverRelationDetailPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportTrackRecordPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupDriverRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupSkuAreaRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupWorkShiftPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.GlobalApplyDriverLeaveDurationVO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.TransportGroupDriverApplyProcessDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupDriverApplicationRecordRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportTrackRecordRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuArearRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.WorkShiftRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.InsertBatchRelationParam;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.UpdateRelationStatusParam;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/3/13 17:20
 */
@Service("transportGroupCommandService")
public class TransportGroupCommandServiceImpl implements TransportGroupCommandService {

    private static final Logger logger = LoggerFactory.getLogger(TransportGroupCommandServiceImpl.class);

    @Autowired
    private TmsPmsproductQueryService tmsPmsproductQueryService;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;

    @Autowired
    private TspTransportGroupSkuRelationRepository tspTransportGroupSkuRelationRepository;

    @Autowired
    private TspTransportGroupSkuArearRelationRepository tspTransportGroupSkuArearRelationRepository;

    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private WorkShiftRepository workShiftRepository;

    @Autowired
    private DriverPointsQueryProxy driverPointsQueryProxy;

    @Autowired
    private DriverCommandService driverCommandService;

    @Autowired
    private TmsTransportQconfig qconfig;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Autowired
    private TransportTrackRecordRepository transportTrackRecordRepository;
    @Autowired
    DriverQueryService driverQueryService;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private DriverGuideProxy driverGuideProxy;

    @Autowired
    TransportGroupDriverApplicationRecordRepository transportGroupDriverApplicationRecordRepository;

    @Autowired
    ShortTransportGroupHelper shortTransportgroupHelper;

    @Autowired
    MobileHelper mobileHelper;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<UpdateTransportGroupApplyStatusSOADTO> updateTransportGroupApplyStatus(UpdateTransportGroupApplyStatusSOARequestType soaRequestType) {
        Result.Builder<UpdateTransportGroupApplyStatusSOADTO> builder = Result.Builder.<UpdateTransportGroupApplyStatusSOADTO>newResult();
        TransportGroupDriverApplyProcessDTO applyProcessDTO = TransportGroupDriverApplyProcessDTO.builder().build();

        try {
            //获取当前运力组信息
            TspTransportGroupPO tspTransportGroupPO = transportGroupRepository.queryTransportGroupDetail(soaRequestType.getTransportGroupId());
            if (Objects.isNull(tspTransportGroupPO) || !Objects.equals(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),tspTransportGroupPO.getTransportGroupMode())||
                    !Objects.equals(tspTransportGroupPO.getGroupStatus(), TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode())) {
                return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportGroupIsNotExistsOrNotLine)).build();
            }
            //获取与该运力组同供应商+城市+车型+模式为报名制运力组的所有班次信息
            List<TspTransportGroupWorkShiftPO> workShiftPOS = workShiftRepository.queryWorkShifts(tspTransportGroupPO.getSupplierId(), tspTransportGroupPO.getPointCityId(), tspTransportGroupPO.getVehicleTypeId(), tspTransportGroupPO.getTransportGroupMode(),null);
            if(CollectionUtils.isEmpty(workShiftPOS)){
                return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftNoExist)).build();//未查询到对应的班次信息
            }
            //workShiftPOSMap:<workShiftId:[TspTransportGroupWorkShiftPO]>
            Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap = workShiftPOSMap(workShiftPOS);
            //记录每个班次报名成功的司机 workShiftApplyInfo:<workshiftId:[drvId]> 如果运力缺失补位，初始化的时候还包括了已经报名成功的司机
            Map<Long,List<Long>> workShiftApplyInfo = Maps.newHashMap();

            //待更新赛道信息
            List<ApplyDriverRelationDetailPO> relationDetailPOList = tspTransportGroupDriverRelationRepository.queryRelationDrvList(Lists.newArrayList(workShiftPOSMap.keySet()),Lists.newArrayList(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode(),TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode()));
            if(CollectionUtils.isEmpty(relationDetailPOList)){
                return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftNoAssociatedDrv)).build();//班次无可操作的司机
            }

            //获取可接单司机<drvId:[ApplyDriverRelationDetailPO]> 存储了所有本次参与报名的司机
            Map<Long, List<ApplyDriverRelationDetailPO>> participatingDriversMap = Maps.newHashMap();
            Map<Long, List<ApplyDriverRelationDetailPO>> originParticipatingDriversMap = Maps.newHashMap();
            //请假冻结数没有超过阈值的司机
            Set<Long> drvIds = Sets.newHashSet();
            if (soaRequestType.getUpdateType() == 2) {
                List<ApplyDriverRelationDetailPO> fillList = relationDetailPOList;
                workShiftPOS = workShiftPOS.stream().filter(shiftPO -> Objects.equals(shiftPO.getId(),soaRequestType.getWorkShiftId())).collect(Collectors.toList());
                workShiftPOSMap = workShiftPOSMap(workShiftPOS);
                relationDetailPOList = relationDetailPOList.stream().filter(applyDriver ->  Objects.equals(applyDriver.getWorkShiftId(),soaRequestType.getWorkShiftId())).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(relationDetailPOList)){
                    return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftNoAssociatedDrv)).build();//班次无可操作的司机
                }
                //2为运力缺失补位(只处理“已报名”的记录)
                for (ApplyDriverRelationDetailPO applyDriverRelationDetailPO : relationDetailPOList) {
                    if (Objects.equals(TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode(),applyDriverRelationDetailPO.getApplyStatus())) {
                        ////筛选出“报名成功”的记录
                        if (!workShiftApplyInfo.containsKey(applyDriverRelationDetailPO.getWorkShiftId())) {
                            //初始化
                            workShiftApplyInfo.put(applyDriverRelationDetailPO.getWorkShiftId(),new ArrayList<>());
                        }
                        workShiftApplyInfo.get(applyDriverRelationDetailPO.getWorkShiftId()).add(applyDriverRelationDetailPO.getDrvId());
                    }else if (Objects.equals(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode(),applyDriverRelationDetailPO.getApplyStatus())){
                        //筛选出“已报名”的可接单司机
                        if (Objects.nonNull(applyDriverRelationDetailPO.getVehicleId()) && applyDriverRelationDetailPO.getVehicleId() > 0
                                && Objects.equals(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), applyDriverRelationDetailPO.getDrvStatus())
                                && Objects.equals(tspTransportGroupPO.getPointCityId(), applyDriverRelationDetailPO.getCityId())) {
                            if (!participatingDriversMap.containsKey(applyDriverRelationDetailPO.getDrvId())) {
                                //初始化
                                participatingDriversMap.put(applyDriverRelationDetailPO.getDrvId(),Lists.newArrayList());
                            }
                            participatingDriversMap.get(applyDriverRelationDetailPO.getDrvId()).add(applyDriverRelationDetailPO);
                        }
                    }
                }
                //从当前赛道-已报名司机中选择补位司机，但可补位司机在其他赛道为报名成功，则不要动
                Iterator<Map.Entry<Long, List<ApplyDriverRelationDetailPO>>> it = participatingDriversMap.entrySet().iterator();
                while (it.hasNext()){
                    Map.Entry<Long, List<ApplyDriverRelationDetailPO>> entry = it.next();
                    for(ApplyDriverRelationDetailPO detailPO : fillList){
                        if(Objects.equals(entry.getKey(),detailPO.getDrvId()) && !Objects.equals(detailPO.getWorkShiftId(),soaRequestType.getWorkShiftId())
                                &&Objects.equals(detailPO.getApplyStatus(), TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode())){
                            it.remove();
                            break;
                        }
                    }
                }

                //运力缺失补位提示
                //司机上限数=报名成功数 ->当前赛道名额已满,无需补位
                //司机上限数<报名成功数 ->当前赛道名额已超,无需补位
                String errorStr = judgeMissingFill(workShiftPOSMap,soaRequestType.getWorkShiftId(),workShiftApplyInfo);
                if(org.apache.commons.lang3.StringUtils.isNotEmpty(errorStr)){
                    return builder.fail().withMsg(errorStr).build();
                }
            }else {
                //新增剔除报名状态，更新全局赛道时，已剔除司机也算上
                relationDetailPOList = tspTransportGroupDriverRelationRepository.queryRelationDrvList(Lists.newArrayList(workShiftPOSMap.keySet()),Lists.newArrayList(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode(),
                            TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode(),TmsTransportConstant.ApplyStatusEnum.ELIMINATE.getCode()));
                //1、为全局赛道更新（处理“已报名”+“报名成功”的记录）
                participatingDriversMap = relationDetailPOList.stream().filter(relationDetailPO -> Objects.nonNull(relationDetailPO.getVehicleId()) && relationDetailPO.getVehicleId() > 0
                        && Objects.equals(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), relationDetailPO.getDrvStatus())
                        && Objects.equals(tspTransportGroupPO.getPointCityId(), relationDetailPO.getCityId())).collect(Collectors.groupingBy(ApplyDriverRelationDetailPO::getDrvId));
                tspTransportGroupDriverRelationRepository.updateApplyStatus(Lists.newArrayList(workShiftPOSMap.keySet()), TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode(), soaRequestType.getModifyUser());
            }
            drvIds = participatingDriversMap.keySet();
            if (CollectionUtils.isEmpty(drvIds)) {
                //没有可接单司机，则不用执行任何更新操作，直接返回
                return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftNoAssociatedDrv)).build();//没有可接单司机
            }

            //克隆一个参与司司机列表
            originParticipatingDriversMap = participatingDriversMap.entrySet()
              .stream()
              .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                (e1, e2) -> e1, HashMap::new
              ));

            logger.info("GlobalApplyTransportNewBusinessStart","drvIds:{}",JsonUtil.toJson(drvIds));
            //全局赛道-新增逻辑，判断司机请假+冻结时长，当前赛道-30天的请假时长 和 当前赛道+31天的请假时长,新增配置开关
            if(Objects.equals(TmsTransportConstant.ApplyTransPortOperationTypeEnum.GLOBAL_TRACK.getCode(),soaRequestType.getUpdateType()) ){
                //当前操作赛道前多少天的时间
                Date nowTrackBeforeDate = DateUtil.dayDisplacement(new Date(),qconfig.getApplyTransportADayThreshold());
                //当前操作赛道后多少天的时间
                Date nowTrackAlterDate = DateUtil.dayDisplacement(new Date(),qconfig.getApplyTransportBDayThreshold());
                GlobalApplyDriverLeaveDurationVO durationVO = new GlobalApplyDriverLeaveDurationVO(new ArrayList<>(drvIds),DateUtil.dateToTimestamp(nowTrackBeforeDate),DateUtil.getNow(),
                  DateUtil.getNow(),DateUtil.dateToTimestamp(nowTrackAlterDate),qconfig.getApplyTransportDurationAThreshold(),qconfig.getApplyTransportDurationBThreshold());
                Map<Long,Boolean> durationMap =  driverQueryService.checkGlobalApplyDriverLeaveDuration(durationVO, applyProcessDTO);
                logger.info("GlobalApplyTransportNewBusinessDurationResult","durationMap:{}",JsonUtil.toJson(durationMap));
                for(Iterator<Long> iterator =  drvIds.iterator(); iterator.hasNext();){
                    Long drvId = iterator.next();
                    if(MapUtils.isNotEmpty(durationMap) && durationMap.get(drvId)!=null && durationMap.get(drvId)){
                        iterator.remove();
                    }
                }
            }
            //运力缺失补位计算司机请假+冻结时长
            if(Objects.equals(TmsTransportConstant.ApplyTransPortOperationTypeEnum.CAPACITY_REPLACEMENT.getCode(),soaRequestType.getUpdateType())){
                logger.info("TrackApplyTransportNewBusinessStart","drvIds:{}",JsonUtil.toJson(drvIds));
                Integer  applyTransportDurationDThreshold = qconfig.getApplyTransportDurationDThreshold();
                //获取当前运力组赛道更新时间
                List<TransportTrackRecordPO> recordPOList = transportTrackRecordRepository.queryTrackByTransportGroupId(soaRequestType.getTransportGroupId(), TmsTransportConstant.ApplyTransPortOperationTypeEnum.GLOBAL_TRACK.getCode());
                if(CollectionUtils.isNotEmpty(recordPOList)){
                    TransportTrackRecordPO recordPO = recordPOList.get(0);
                    Timestamp datachangeCreatetime = recordPO.getDatachangeCreatetime();
                    Date trackAlterDate = DateUtil.dayDisplacement(datachangeCreatetime,qconfig.getApplyTransportDDayThreshold());
                    //计算时长
                    applyProcessDTO.setRule(TmsTransportConstant.TransportDriverApplyRuleEnum.D);
                    Map<Long,Long> calculateMap =  driverQueryService.checkApplyDriverLeaveDuration(new ArrayList<>(drvIds),DateUtil.getNow(),DateUtil.dateToTimestamp(trackAlterDate),null, applyProcessDTO);
                    logger.info("TrackApplyTransportNewBusinessDurationResult","calculateMap:{}",JsonUtil.toJson(calculateMap));
                    for(Iterator<Long> iterator =  drvIds.iterator(); iterator.hasNext();){
                        Long drvId = iterator.next();
                        if(MapUtils.isNotEmpty(calculateMap) && calculateMap.get(drvId) != null){
                            long leaveCount = calculateMap.get(drvId);
                            if(leaveCount > applyTransportDurationDThreshold.longValue()){
                                iterator.remove();
                                //记录失败信息
                                applyProcessDTO.setApplyResult(drvId, TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED, TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT,
                                        TmsTransportConstant.TransportDriverApplyRuleEnum.D.getText(), applyTransportDurationDThreshold);
                            }
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(drvIds)) {
                //没有可接单司机，则不用执行任何更新操作，直接返回
                return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftNoAssociatedDrv)).build();//没有可接单司机
            }
            //获取可接单司机司机分排名
            Result<Map<Long, List<DriverPoint>>> drvCityRankingResult = driverPointsQueryProxy.queryDrvCityRanking(Joiner.on(",").join(drvIds), tspTransportGroupPO.getPointCityId());
            if (!drvCityRankingResult.isSuccess() || MapUtils.isEmpty(drvCityRankingResult.getData())) {
                return builder.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDriverPointFail)).build();
            }
            //开始刷选"报名成功"的司机
            Map<Long, List<DriverPoint>> data = drvCityRankingResult.getData();
            //1、对排名进行从低到高排序 cityRankingDriverPointMap:<point:[DriverPoint]>
            List<Map.Entry<Long, List<DriverPoint>>> cityRankingDriverPointMap = data.entrySet().stream().sorted(Comparator.comparing(Map.Entry::getKey)).collect(Collectors.toList());
            if(applyProcessDTO.getRule() == null) {
                applyProcessDTO.setRule(Objects.equals(soaRequestType.getUpdateType(), TmsTransportConstant.ApplyTransPortOperationTypeEnum.GLOBAL_TRACK.getCode()) ? TmsTransportConstant.TransportDriverApplyRuleEnum.A : TmsTransportConstant.TransportDriverApplyRuleEnum.D);
            }

            if (qconfig.getLogTransportgroupDriverApplyLog()) {
                // 剔除请假冻结时长没有触发阈值的记录
                applyProcessDTO.removeDriverLeaveAndFreezeDurationNotTriggerThreshold();

                // 初始化司机排名和司机工作时段关联关系到applyProcess对象
                applyProcessDTO.setDriverPointAndWorkShiftIntoApplyProcess(originParticipatingDriversMap, cityRankingDriverPointMap);
                // 设置赛道司机最大报名数量
                applyProcessDTO.setDriverApplicationMaximumLimit(workShiftPOSMap, workShiftApplyInfo);
            }

            //2、开始对司机进行报名
            beginDrvApply(cityRankingDriverPointMap, participatingDriversMap,workShiftPOSMap, workShiftApplyInfo, applyProcessDTO);

            if (qconfig.getLogTransportgroupDriverApplyLog()) {
                //2.1 设置司机因为司机分排名过低报名失败情况
                applyProcessDTO.setDriverApplyStatusCaseDriverPoint();

                // 剔除请假冻结时长没有触发阈值的记录
                applyProcessDTO.removeDriverLeaveAndFreezeDurationNotTriggerThreshold();
            }

            //3、更新满足条件的数据为报名成功
            List<Long> workShiftIds = Lists.newArrayList();
            //操作报名成功的司机ID
            List<Long> drvLists = Lists.newArrayList();
            for (Map.Entry<Long, List<Long>> entry : workShiftApplyInfo.entrySet()) {
                tspTransportGroupDriverRelationRepository.updateApplyStatus(entry.getKey(),entry.getValue(),TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode(),soaRequestType.getModifyUser());
                workShiftIds.add(entry.getKey());
                drvLists = (List<Long>) CollectionUtils.union(new ArrayList<>(entry.getValue()),drvLists);
            }
            //赛道数
            int trackCount = workShiftApplyInfo.size();
            if(trackCount == 0){
                 return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportUpdateError)).build();
            }
            //查询班次列表
            List<TspTransportGroupWorkShiftPO> workShiftPOList =  workShiftRepository.queryWorkShiftList(workShiftIds);
            if(CollectionUtils.isEmpty(workShiftIds)){
                return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftNoExist)).build();
            }
            Map<Long,TspTransportGroupWorkShiftPO> workShiftPOMap = workShiftPOList.stream().collect(Collectors.toMap(TspTransportGroupWorkShiftPO::getId,a->a,(k1,k2)->k1));
            //更新司机工作时段
            this.updateDrvModeAndWorkPeriod(workShiftApplyInfo,workShiftPOMap,soaRequestType.getModifyUser(), applyProcessDTO);
            //提示赛道司机上限数code
            Map<String,Integer> resultCode =  this.equalDrvUpperLimitCode(workShiftPOSMap,soaRequestType.getWorkShiftId());
            UpdateTransportGroupApplyStatusSOADTO soadto = new UpdateTransportGroupApplyStatusSOADTO();
            soadto.setCode(resultCode.get("resultCode"));
            soadto.setTrackCount(resultCode.get("trackCount"));
            soadto.setTrackbeyondCount(resultCode.get("trackbeyondCount"));
            //同步司机合作模式
            this.syncDrvCoopMode(relationDetailPOList,soaRequestType.getModifyUser());
            //保存全局赛道记录
            this.saveGlobalTrackRecord(soaRequestType.getUpdateType(),workShiftPOMap,soaRequestType.getModifyUser());

            if (qconfig.getLogTransportgroupDriverApplyLog()) {
                applyProcessDTO.setApplyTransPortOperationTypeEnum(TmsTransportConstant.ApplyTransPortOperationTypeEnum.getEnum(
                  soaRequestType.getUpdateType()));

                // 处理报名失败失败原因
                transportGroupDriverApplicationRecordRepository.updateHistoryData2Inactive(soaRequestType.getTransportGroupId(), soaRequestType.getWorkShiftId());

                // 保存失败原因
                transportGroupDriverApplicationRecordRepository.batchInsert(applyProcessDTO.getTransportGroupDriverApplicationRecordPOList(soaRequestType, originParticipatingDriversMap));
                //发报名成功对push消息
                tmsQmqProducerCommandService.sendDriverApplySuccessMessage(applyProcessDTO.getApplySuccessDriverWorkPeriodMap());

                //发报名失败对push消息
                tmsQmqProducerCommandService.sendDriverApplyFailedMessage(applyProcessDTO.getFailedDriverList());
            }

            return builder.success().withData(soadto).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }finally {
            logger.info(String.valueOf(soaRequestType.getTransportGroupId()), "{}", applyProcessDTO.log());
        }
    }


    /**
     * //运力缺失补位提示
     * 司机上限数=报名成功数 ->当前赛道名额已满,无需补位
     * 司机上限数<报名成功数 ->当前赛道名额已超,无需补位
     * @param workShiftPOSMap
     * @param workShiftId
     * @param workShiftApplyInfo
     * @return
     */
    public String judgeMissingFill(Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap,Long workShiftId,Map<Long,List<Long>> workShiftApplyInfo){
        int successApplySize = 0;//已报名成功数
        int driverUpperLimit = 0;//司机上限数
        String errorStr = "";
        Long driverUpperLimitL  = workShiftPOSMap.get(workShiftId).getDriverUpperLimit();
        if(driverUpperLimitL!=null){
            driverUpperLimit = driverUpperLimitL.intValue();
        }
        List<Long> successApplyDrvList =  workShiftApplyInfo.get(workShiftId);
        if(CollectionUtils.isNotEmpty(successApplyDrvList)){
            Set<Long> successSet = new HashSet<>(successApplyDrvList);
            successApplySize = successSet.size();
        }
        if(driverUpperLimit == successApplySize){
            errorStr = SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftIsFull);
        }else if(driverUpperLimit < successApplySize){
            errorStr = SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftIsBeyond);
        }
        return errorStr;
    }

    private Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap(List<TspTransportGroupWorkShiftPO> workShiftPOS){
        Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap = Maps.newHashMap();
        for (TspTransportGroupWorkShiftPO workShiftPO : workShiftPOS) {
            workShiftPOSMap.put(workShiftPO.getId(),workShiftPO);
        }
        return workShiftPOSMap;
    }

    /**
     * 开始对司机进行报名
     * cityRankingDriverPointMap:<cityRanking:[DriverPoint]> 司机分城市排名，从前到后
     * participatingDriversMap:<drvId:[ApplyDriverRelationDetailPO]>
     * workShiftPOSMap:<workShiftId:[TspTransportGroupWorkShiftPO]> 赛道从低到高
     *  workShiftApplyInfo:<workshiftId:[drvId]> 记录每个班次报名成功的司机
     */
    private void beginDrvApply(List<Map.Entry<Long, List<DriverPoint>>> cityRankingDriverPointMap,Map<Long, List<ApplyDriverRelationDetailPO>> participatingDriversMap,Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap,Map<Long,List<Long>> workShiftApplyInfo, TransportGroupDriverApplyProcessDTO applyProcessDTO){
        logger.info("beginDrvApply {}, {}， {}, {}, {}, {}", JsonUtil.toJson(cityRankingDriverPointMap), JsonUtil.toJson(participatingDriversMap), JsonUtil.toJson(workShiftPOSMap), JsonUtil.toJson(workShiftApplyInfo), JsonUtil.toJson(applyProcessDTO));
        Map<Long,Double> pointMap = Maps.newHashMap();
        Map<Long, List<Long>> workShiftapplySuccessDrvListMap = Maps.newHashMap();

        for(Map.Entry<Long, List<DriverPoint>> entry : cityRankingDriverPointMap){
            List<DriverPoint> pointList = entry.getValue();
            pointList.forEach(driverPoint -> {
                pointMap.put(driverPoint.getDriverId(),driverPoint.getPoints());
              }
            );
        }

        for (Map.Entry<Long, List<DriverPoint>> entry : cityRankingDriverPointMap) {
            if (Objects.equals(0L,entry.getKey())) {
                //排名为0的被过滤掉
                continue;
            }

            //记录当前排名的司机是否还可以报名该班次
            Map<Long,Boolean> isAllowWorkShiftApply = Maps.newHashMap();
            for (DriverPoint driverPoint : entry.getValue()) {
                if (!participatingDriversMap.containsKey(driverPoint.getDriverId())) {
                    continue;
                }
                //获取司机已关联的有效班次
                List<ApplyDriverRelationDetailPO> relationDetailPOS = participatingDriversMap.get(driverPoint.getDriverId());
                LinkedHashMap<Long, List<ApplyDriverRelationDetailPO>> relationDetailMap = relationDetailPOS.stream().collect(Collectors.groupingBy(ApplyDriverRelationDetailPO::getWorkShiftId, LinkedHashMap::new, Collectors.toList()));
                Map<Long,Boolean> successFlagMap = Maps.newHashMap();

                for(Map.Entry<Long,List<ApplyDriverRelationDetailPO>> entry1: relationDetailMap.entrySet()){
                    for (ApplyDriverRelationDetailPO relationDetailPO : entry1.getValue()) {
                        //判断当前司机是否在其它班次中报名成功
                        if(MapUtils.isNotEmpty(successFlagMap) && successFlagMap.get(relationDetailPO.getDrvId())){
                            break;
                        }
                        if (!isAllowWorkShiftApply.containsKey(relationDetailPO.getWorkShiftId())) {
                            List<Long> drvset = workShiftApplyInfo.get(relationDetailPO.getWorkShiftId());
                            int successDrvCount = 0;
                            if(!CollectionUtils.isEmpty(drvset)){
                                successDrvCount = drvset.size();
                            }
                            long lastDrv = 0L;
                            if(CollectionUtils.isNotEmpty(drvset)){
                                lastDrv =  drvset.stream().reduce((first, second) -> second).orElse(0L);
                            }
                            Double lastPoint = pointMap.get(lastDrv);
                            Double nowPoint = pointMap.get(relationDetailPO.getDrvId());
                            if (workShiftPOSMap.get(relationDetailPO.getWorkShiftId()).getDriverUpperLimit().compareTo((long) successDrvCount) > 0) {
                                isAllowWorkShiftApply.put(relationDetailPO.getWorkShiftId(),true);
                            }else{
                                if(Objects.equals(lastPoint,nowPoint)){//达到司机上限数，如果最后一个司机分和下面的司机分相同,也可报名成功
                                    isAllowWorkShiftApply.put(relationDetailPO.getWorkShiftId(),true);
                                }else{
                                    isAllowWorkShiftApply.put(relationDetailPO.getWorkShiftId(),false);
                                }
                            }
                        }
                        if (!isAllowWorkShiftApply.get(relationDetailPO.getWorkShiftId())) {
                            //达到司机上限数，停止向该班次报名
                            continue;
                        }
                        //报名成功该班次
                        if (!workShiftApplyInfo.containsKey(relationDetailPO.getWorkShiftId())) {
                            //初始化
                            workShiftApplyInfo.put(relationDetailPO.getWorkShiftId(),new ArrayList<>());
                        }
                        workShiftApplyInfo.get(relationDetailPO.getWorkShiftId()).add(relationDetailPO.getDrvId());
                        successFlagMap.put(relationDetailPO.getDrvId(),Boolean.TRUE);
                        workShiftapplySuccessDrvListMap.computeIfAbsent(relationDetailPO.getWorkShiftId(), k -> Lists.newArrayList()).add(relationDetailPO.getDrvId());
                        break;
                    }
                }
            }
            //判断是否所有班次均已达到报名成功司机上限
            if (isAllowWorkShiftApply.size() == workShiftPOSMap.size()) {
                boolean anyMatch = isAllowWorkShiftApply.entrySet().stream().anyMatch(Map.Entry::getValue);
                if (!anyMatch) {
                    break;
                }
            }
        }

        // 设置报名成功的司机
        applyProcessDTO.getWorkshiftApplySuccessDriverListMap().putAll(workShiftapplySuccessDrvListMap);
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> bindDriverRelation(DriverRelationBindRequestSOAType requestSOAType) {
        Result.Builder<Boolean> builder = Result.Builder.<Boolean>newResult();
        int status = -1;
        //0:绑定 1:解绑
        if (requestSOAType.getBindStatus() == 1) {
            UpdateRelationStatusParam param = new UpdateRelationStatusParam(requestSOAType.getTransportGroupId(),requestSOAType.getDrvIdList()
                    ,requestSOAType.getOperator(),requestSOAType.getWorkShiftId());
            status = tspTransportGroupDriverRelationRepository.updateRelationStatus(param);
            if (status > 0) {
                tmsQmqProducerCommandService.sendTransportGroupUnbondDrvQmq(requestSOAType.getTransportGroupId(), requestSOAType.getDrvIdList());
            }
            //重新计算司机合作模式
            driverCommandService.calculateUpdateDrvCoopMode(requestSOAType.getDrvIdList(),requestSOAType.getOperator());
        } else {

            // 查询运力组信息
            TspTransportGroupPO transportGroupPO = transportGroupRepository.queryTransportGroupDetail(requestSOAType.getTransportGroupId());
            if (transportGroupPO == null) {
                return builder.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgTransportDataIsEmpty)).build();
            }

            // 短公里运力组和司机的匹配校验
            Result<String>  shortTransportGroupCheck = shortTransportgroupHelper.checkShortTransportGroupDriver(requestSOAType.getDrvIdList(),
              requestSOAType.getTransportGroupId(), transportGroupPO.getShortTransportGroup());
            if (!shortTransportGroupCheck.isSuccess()) {
                return ResponseResultUtil.failed(shortTransportGroupCheck.getCode(), shortTransportGroupCheck.getMsg());
            }

            // 如果运力组模式是1006-全职报名 且班次为null话，返回错误信息
            if(Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode()) && requestSOAType.getWorkShiftId() == null){
                return builder.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportWorkShiftNoExist)).build();
            }
            List<Long> drvIdList = Lists.newArrayListWithCapacity(requestSOAType.getDrvIdList().size());

            // 查询已经在绑定关系中的司机id，然后将入参中的司机id剔除
            eliminateIsBinding(requestSOAType.getDrvIdList(), tspTransportGroupDriverRelationRepository.queryRelationDrvListByTransportGroupIdAndDrvIdList(transportGroupPO.getTransportGroupId(),requestSOAType.getDrvIdList(),requestSOAType.getWorkShiftId()));

            if (CollectionUtils.isEmpty(requestSOAType.getDrvIdList())) {
                return builder.success().withData(true).build();
            }

            //TODO 保持绑定关系一个运力组与一个司机只有一条
            List<DrvDriverPO> drvList = drvDrvierRepository.queryDrvList(requestSOAType.getDrvIdList());

            if (CollectionUtils.isEmpty(drvList)) {
                return builder.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgNoOneDriverCanBind)).build();
            }

            List<Long> vehicleIds = drvList.stream().map(DrvDriverPO::getVehicleId).filter(Objects::nonNull).filter(item -> item != 0).collect(Collectors.toList());
            List<VehVehiclePO> vehVehiclePOS = vehicleRepository.queryVehicleByIds(vehicleIds);

            Map<Long, Integer> vehicleCategoryMap = Optional.ofNullable(vehVehiclePOS).orElseGet(Lists::newArrayList).stream().collect(Collectors.toMap(VehVehiclePO::getVehicleId, VehVehiclePO::getCategorySynthesizeCode, (a, b) -> a));
            // 判断 运力组产线 + 司机产线 + 车辆产线
            Integer transportCategorySynthesizeCode = transportGroupPO.getCategorySynthesizeCode();

            StringBuilder message = new StringBuilder();
            for (DrvDriverPO driverPO : drvList) {
                // 司机产线
                Integer driverCategorySynthesizeCode = driverPO.getCategorySynthesizeCode();
                // 司机绑定了车辆
                if (Objects.nonNull(driverPO.getVehicleId()) && MapUtils.isNotEmpty(vehicleCategoryMap) && vehicleCategoryMap.containsKey(driverPO.getVehicleId())){
                    // 车辆产线
                    Integer vehiCategorySynthesizeCode = vehicleCategoryMap.get(driverPO.getVehicleId());
                    if (!productionLineUtil.checkBind(transportCategorySynthesizeCode, driverCategorySynthesizeCode, vehiCategorySynthesizeCode)){
                        message.append(driverPO.getDrvName()).append(",");
                        continue;
                    }
                }else {
                    // 司机没有绑定车辆
                    if (!productionLineUtil.checkBind(transportCategorySynthesizeCode, driverCategorySynthesizeCode)) {
                        message.append(driverPO.getDrvName()).append(",");
                        continue;
                    }
                }

                if (transportGroupQueryService.canBind(driverPO, transportGroupPO.getTransportGroupMode()).getData()) {
                    drvIdList.add(driverPO.getDrvId());
                } else {
                    message.append(driverPO.getDrvName()).append(",");
                }
            }
            if (CollectionUtils.isNotEmpty(drvIdList)) {
                InsertBatchRelationParam param = new InsertBatchRelationParam(requestSOAType.getTransportGroupId(),drvIdList,requestSOAType.getOperator(),requestSOAType.getWorkShiftId());
                status = tspTransportGroupDriverRelationRepository.insetBatch(param);
                if (status > 0) {
                    tmsQmqProducerCommandService.sendTransportGroupBoundDrvQmq(requestSOAType.getTransportGroupId(), drvIdList);
                }
                //重新计算司机合作模式
                driverCommandService.calculateUpdateDrvCoopMode(requestSOAType.getDrvIdList(),requestSOAType.getOperator());
            }
            if (!Strings.isNullOrEmpty(message.toString())) {
                return builder.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(String.format(SharkUtils.getSharkValue(SharkKeyConstant.CAN_NOT_BIND_DRIVER_RELATION), message.toString())).build();
            }
        }
        if (status < 0) {
            return builder.fail().withData(false).build();
        }
        return builder.success().withData(true).build();
    }

    private void eliminateIsBinding(List<Long> drvIdList, List<Long> isBindingIdList) {
        if (CollectionUtils.isEmpty(isBindingIdList)) {
            return;
        }
        for (int i = drvIdList.size() - 1; i >= 0; i--) {
            for (Long id : isBindingIdList) {
                if (drvIdList.get(i).longValue() == id.longValue()) {
                    drvIdList.remove(i);
                    break;
                }
            }
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Long> addTransportGroup(TspTransportGroupPO transportGroupPO) {
        if (!tmsPmsproductQueryService.checkSkuIsExist(transportGroupPO.getSupplierId())) {
            return Result.Builder.<Long>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportPleaseFinishSkuCreate))
                    .withData(null)
                    .build();
        }
        if (transportGroupRepository.queryTransportNameCount(transportGroupPO.getTransportGroupName(),null) > 0) {
            return Result.Builder.<Long>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.capacityGroupNameExist))
                    .withData(null)
                    .build();
        }
        transportGroupPO.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(Lists.newArrayList(transportGroupPO.getCategorySynthesizeCode())));
        long transportGroupId = transportGroupRepository.addTransportGroup(transportGroupPO);
        return Result.Builder.<Long>newResult()
                .success()
                .withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE)
                .withData(transportGroupId)
                .build();
    }


    @Override
    public Result<Boolean> updateTransportGroupSkuRelationStatus(Long transportGroupId, List<Long> skuInfoList, String operator) {
        int count = tspTransportGroupSkuRelationRepository.updateRelationStatus(transportGroupId, skuInfoList, operator);
        if (count > 0) {
            //解绑SKU，将SKU城市从运力组中移除
//            this.updateBingCityToTransportGroup(transportGroupId,skuInfoList);
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        } else {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUnBindingError))
                    .withData(false)
                    .build();
        }
    }

    /**
     * <AUTHOR>
     * @Description 点击【批量关联】时，所选资源的“点位”理应都属于该运力组进单配置所选“点位”之一
     *              属于：可以绑定
     *              不属于：toast“绑定资源的枢纽点位不属于该运力组”
     * @Date 10:48 2020/8/7
     * @Param [transportGroupId, skuInfoList, operator]
     * @return com.ctrip.igt.framework.common.result.Result<java.lang.Boolean>
     **/
    @Override
    public Result<Boolean> insertTransportGroupSkuRelationStatus(Long transportGroupId, List<Long> skuInfoList, String operator) {

        TspTransportGroupPO transportGroupPO = transportGroupRepository.queryTransportGroupDetail(transportGroupId);
        if (Objects.isNull(transportGroupPO)) {
            return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgTransportGroupNotFound)).build();
        }
        //同点位同城市同车型，创建多个运力组，关联的sku不可重复
        if(Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())
                && this.judgeSkuRepeat(transportGroupPO.getTransportGroupId(),transportGroupPO.getPointCityId(),transportGroupPO.getVehicleTypeId(),skuInfoList)){
            return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportSameLocalSkuIsExist)).build();
        }

        List<Long> skuIds = tspTransportGroupSkuRelationRepository.queryBindSkuId(transportGroupId,true);

        if (CollectionUtils.isNotEmpty(skuIds)) {
             for (int i = skuInfoList.size() - 1; i >= 0;i--) {
                for (Long id : skuIds) {
                    if (id.longValue() == skuInfoList.get(i).longValue()) {
                        skuInfoList.remove(i);
                        break;
                    }
                }
             }
        }

        if (skuInfoList.size() <= 0) {
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        }

        int count = tspTransportGroupSkuRelationRepository.insetBatch(transportGroupId, skuInfoList, operator);
        if (count > 0) {
            //绑定SKU，将SKU关联城市 关联到运力组
//            this.bingCityToTransportGroup(transportGroupId, skuInfoList);
            return Result.Builder.<Boolean>newResult()
                    .success()
                    .withData(true)
                    .build();
        } else {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgUnBindingError))
                    .withData(false)
                    .build();
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateTransportGroup(TspTransportGroupPO transportGroupPO) {
        if (transportGroupRepository.queryTransportNameCount(transportGroupPO.getTransportGroupName(),transportGroupPO.getTransportGroupId()) > 0) {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.capacityGroupNameExist))
                    .withData(false)
                    .build();
        }
        TspTransportGroupPO original = transportGroupRepository.queryTransportGroupDetail(transportGroupPO.getTransportGroupId());
        //运力组模式变更需要校验旗下已绑定司机
        if (!original.getTransportGroupMode().equals(transportGroupPO.getTransportGroupMode())) {
            List<DrvDriverPO> driverPOList = drvDrvierRepository.queryDrvListByTgId(transportGroupPO.getTransportGroupId());
            Set<Long> collect = driverPOList.stream().filter(drvDriverPO -> {
                Result<Boolean> result = transportGroupQueryService.canBind(drvDriverPO, transportGroupPO.getTransportGroupMode());
                return !result.getData();
            }).map(DrvDriverPO::getDrvId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(collect)) {
                return Result.Builder.<Boolean>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(String.format(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgDriverNotMatchGroupMode), StringUtils.collectionToDelimitedString(collect, ",")))
                        .withData(false)
                        .build();
            }
        }
        transportGroupRepository.updateTransportGroup(transportGroupPO);
        //发送运力组信息变更消息
        tmsQmqProducerCommandService.sendTransportGroupModifyQmq(transportGroupPO.getTransportGroupId());
        return Result.Builder.<Boolean>newResult()
                .success()
                .withData(true)
                .build();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateTransportGroupStatus(TspTransportGroupPO transportGroupPO) {
        Result.Builder<Boolean> result = Result.Builder.<Boolean>newResult();
        if (Strings.isNullOrEmpty(TmsTransportConstant.TransportGroupStatusEnum.getText(transportGroupPO.getGroupStatus()))) {
            return result.fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportGroupStatusError))
                    .withData(false)
                    .build();
        }
        TspTransportGroupPO transportGroupDetail = transportGroupRepository.queryTransportGroupDetail(transportGroupPO.getTransportGroupId());
        if (transportGroupPO.getGroupStatus().equals(transportGroupDetail.getGroupStatus())) {
            return result.success()
                    .withData(true)
                    .build();
        }

        if(qconfig.getProhibitOperationTransportGroupCodeList().contains(transportGroupDetail.getTransportGroupMode())){
            return result.fail().withMsg("1006 transportGroup Prohibit operation").withData(false).build();
        }

        List<TspTransportGroupDriverRelationPO> driverRelationPOS = tspTransportGroupDriverRelationRepository.queryRelationListByTransportGroupId(transportGroupPO.getTransportGroupId());
        if(TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode().equals(transportGroupPO.getGroupStatus())){

            Result<Boolean> checkResult = commonCheck(transportGroupDetail, transportGroupPO);
            if (!checkResult.isSuccess()) {
                return checkResult;
            }

            //运力组上线需要校验是否已绑定司机和商品
            //1、校验是否已绑定司机
            // 司导项目,不再校验是否绑定司机
            Map<String,Object> drvDrvMap = isBingDrv(driverRelationPOS,transportGroupDetail.getTransportGroupMode(),transportGroupPO.getTransportGroupId());
            // 判断如果供应商处于包车迁移中，且运力组产线为包车，则hasDrv为true（不校验是否绑定司机）
            boolean isSupplierInDayProductLineMigration = Objects.equals(transportGroupDetail.getCategorySynthesizeCode(), CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue()) && driverGuideProxy.getGrayControl(transportGroupDetail.getSupplierId());
            boolean hasDrv = isSupplierInDayProductLineMigration || (boolean) drvDrvMap.get("resultFlag");
            //2、校验是否已绑定商品
            boolean hasSku = CollectionUtils.isNotEmpty(tspTransportGroupSkuArearRelationRepository.queryTransportGroupSkuInfoList(transportGroupPO.getTransportGroupId()));
            if (!hasDrv && !hasSku){
                return result.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverCarMgtCapacityBindDriverAndSku))
                        .withData(false)
                        .build();
            }else if (hasDrv && !hasSku){
                return result.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverCarMgtCapacityBindSku))
                        .withData(false)
                        .build();
            }else if (!hasDrv){
                if(!Objects.equals(transportGroupDetail.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
                    return result.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                            .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverCarMgtCapacityBindDriver))
                            .withData(false)
                            .build();
                }else{
                    return result.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                            .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportNotApplySuccessDrvFail,drvDrvMap.get("resultCount")))
                            .withData(false)
                            .build();
                }
            }

        }
        transportGroupRepository.updateTransportGroup(transportGroupPO);
        //发送运力组状态变更消息
        if (TmsTransportConstant.TransportGroupStatusEnum.OFFLINE.getCode().equals(transportGroupPO.getGroupStatus())) {
            //运力组下线：将该组关联司机的全职报名状态全部刷为“已报名”，并重新更新所有上线报名制运力组司机的合作模式
            if(Objects.equals(transportGroupDetail.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
                tspTransportGroupDriverRelationRepository.updateDriverRelationApplyStatus(transportGroupPO.getTransportGroupId(),
                        transportGroupPO.getModifyUser(), TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode());
            }
            sendCalculateDrvCoopModeMethod(driverRelationPOS,transportGroupPO.getModifyUser());
            tmsQmqProducerCommandService.sendTransportGroupOfflineQmq(transportGroupPO.getTransportGroupId());
        } else if (TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode().equals(transportGroupPO.getGroupStatus())) {
            //运力组上线:该组关联司机的全职报名状态全部保持“已报名”，并重新更新所有上线报名制运力组司机的合作模式
            sendCalculateDrvCoopModeMethod(driverRelationPOS,transportGroupPO.getModifyUser());
            tmsQmqProducerCommandService.sendTransportGroupOnlineQmq(transportGroupPO.getTransportGroupId());
        }
        return result.success()
                .withData(true)
                .build();
    }

    protected Result<Boolean> commonCheck(TspTransportGroupPO transportGroupDetail, TspTransportGroupPO transportGroupPO) {
        Integer internalScope = enumRepository.getAreaScope(transportGroupDetail.getPointCityId());
        // 校验手机号是否经过验证
        Result<Boolean> mobileVerified =
            mobileHelper.isMobileVerified(transportGroupDetail.getIgtCode(),
                transportGroupDetail.getDispatcherPhone(),
                internalScope, ErrorCodeEnum.TRANSPORT_GROUP_DISPATCHER_MOBILE_NOT_VERIFIED);
        if (!mobileVerified.isSuccess()) {
            return mobileVerified;
        }
        // 运力组备用手机号是否经过验证
//        mobileVerified = mobileHelper.isMobileVerified(transportGroupDetail.getStandbyIgtCode(), transportGroupDetail.getStandbyPhone(), internalScope);
//
//        if (!mobileVerified.isSuccess()) {
//            return mobileVerified;
//        }

        return ResponseResultUtil.success();
    }

    /**
     * 运力组上下线,重新计算所关联的司机合作模式
     * @param driverRelationPOS
     * @param modifyUser
     */
    private void sendCalculateDrvCoopModeMethod(List<TspTransportGroupDriverRelationPO> driverRelationPOS,String modifyUser){
        if(CollectionUtils.isEmpty(driverRelationPOS)){
            return;
        }
        List<Long> drvList = driverRelationPOS.stream().map(TspTransportGroupDriverRelationPO::getDrvId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(drvList)){
            tmsQmqProducerCommandService.sendCalculateDrvCoopModeQmq(drvList,modifyUser);
        }
    }

    @Override
    public void unBoundTransport(List<Long> drvid, String modifyUser,boolean allIn) {
        try{
            List<TspTransportGroupDriverRelationPO> relationPOList =  tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(drvid);
            if(CollectionUtils.isEmpty(relationPOList)){
                return;
            }
            List<Long> transportGroupIds = Lists.newArrayList();
            relationPOList.forEach(relationPO -> {
                transportGroupIds.add(relationPO.getTransportGroupId());
            });
            if(CollectionUtils.isEmpty(transportGroupIds)){
                return;
            }
            List<Long> unfreezeGroupIds = Lists.newArrayList();
            List<TspTransportGroupPO> transportGroupPOList =  transportGroupRepository.queryTspTransportByIds(transportGroupIds);
            if(CollectionUtils.isEmpty(transportGroupPOList)){
                return;
            }
            //新增，如果是报名制运力组,将报名状态变更为未报名
            if (!allIn) {
                transportGroupPOList.forEach(portGroupPO -> {
                    if(Objects.equals(portGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode())||
                            Objects.equals(portGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())||
                            Objects.equals(portGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
                        unfreezeGroupIds.add(portGroupPO.getTransportGroupId());
                    }
                });
                if(CollectionUtils.isEmpty(unfreezeGroupIds)){
                    return;
                }
            }
            int count =tspTransportGroupDriverRelationRepository.unFreezeTransportGroup(drvid,!allIn ? unfreezeGroupIds:transportGroupIds,modifyUser);
            if(count > 0){
                //司机解绑运力组,重新计算司机合作模式
                tmsQmqProducerCommandService.sendCalculateDrvCoopModeQmq(drvid,modifyUser);
            }
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Boolean unBingTransportAssociated(Long transportGroupId,Long pointCityId,Integer originAreaType,Integer targetAreaType,Long originAreaGroupId,Long targetAreaGroupId,String modifyUser) {
        if(Objects.equals(originAreaType,targetAreaType) && Objects.equals(originAreaGroupId,targetAreaGroupId)){
            return Boolean.FALSE;
        }
        Map<Long,List<SimpleAreaGroupDTO>> araeMap =  transportGroupQueryService.getAreaCityIdByGroupIds(Arrays.asList(originAreaGroupId,targetAreaGroupId));
        Set<Long> compareCityIds = Sets.newHashSet();
        if(MapUtils.isEmpty(araeMap)){
            return Boolean.FALSE;
        }
        Set<Long> originCityIds = Sets.newHashSet();
        Set<Long> targetCityIds = Sets.newHashSet();
        //行政区域类型相同，判断相应城市
        if(Objects.equals(originAreaType,targetAreaType)){
            if(Objects.equals(targetAreaType,TmsTransportConstant.AreaTypeTypeEnum.DIY_REGION.getCode().intValue())){
                 originCityIds = getDiyRegionCity(araeMap.get(originAreaGroupId));
               targetCityIds = getDiyRegionCity(araeMap.get(targetAreaGroupId));
            }else if(Objects.equals(targetAreaType,TmsTransportConstant.AreaTypeTypeEnum.ADMINISTRATIVE_REGION.getCode().intValue())){
                originCityIds = queryAreaCityIds(araeMap.get(originAreaGroupId));
                targetCityIds = queryAreaCityIds(araeMap.get(targetAreaGroupId));
            }
        }
        //行政区域类型不同，判断相应城市
        if(!Objects.equals(originAreaType,targetAreaType)){
            if(Objects.equals(originAreaType,TmsTransportConstant.AreaTypeTypeEnum.ADMINISTRATIVE_REGION.getCode().intValue())&&
                    Objects.equals(targetAreaType,TmsTransportConstant.AreaTypeTypeEnum.DIY_REGION.getCode().intValue())){
                originCityIds = queryAreaCityIds(araeMap.get(originAreaGroupId));
                targetCityIds = getDiyRegionCity(araeMap.get(targetAreaGroupId));
            }else if(Objects.equals(originAreaType,TmsTransportConstant.AreaTypeTypeEnum.DIY_REGION.getCode().intValue())&&
                    Objects.equals(targetAreaType,TmsTransportConstant.AreaTypeTypeEnum.ADMINISTRATIVE_REGION.getCode().intValue())){
                originCityIds = getDiyRegionCity(araeMap.get(originAreaGroupId));
                targetCityIds = queryAreaCityIds(araeMap.get(targetAreaGroupId));
            }
        }
        if(CollectionUtils.isEmpty(originCityIds) || CollectionUtils.isEmpty(targetCityIds)){
            return Boolean.FALSE;
        }
        compareCityIds = compareCityIds(originCityIds, targetCityIds,pointCityId);
        if(CollectionUtils.isEmpty(compareCityIds)){
            return Boolean.FALSE;
        }
        List<Long> drvIds = tspTransportGroupDriverRelationRepository.queryRelationDrvIdListByTransportGroupId(transportGroupId);
        if(CollectionUtils.isEmpty(drvIds)){
            return Boolean.FALSE;
        }
        List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.queryDrvList(drvIds);
        if (CollectionUtils.isEmpty(drvDriverPOList)) {
            return Boolean.FALSE;
        }
        //找出待解绑的司机ID
        List<Long> unBingDrvIdList = Lists.newArrayList();
        for (DrvDriverPO driverPO : drvDriverPOList) {
            if(compareCityIds.contains(driverPO.getCityId())){
                unBingDrvIdList.add(driverPO.getDrvId());
            }
        }
        if(CollectionUtils.isEmpty(unBingDrvIdList)){
            return Boolean.FALSE;
        }
        //解绑司机
        DriverRelationBindRequestSOAType relationBindRequestSOAType = new DriverRelationBindRequestSOAType();
        relationBindRequestSOAType.setDrvIdList(unBingDrvIdList);
        relationBindRequestSOAType.setTransportGroupId(transportGroupId);
        relationBindRequestSOAType.setBindStatus(1);
        relationBindRequestSOAType.setOperator(modifyUser);
        bindDriverRelation(relationBindRequestSOAType);
        return Boolean.FALSE;
    }

    @Override
    public Boolean drvDispatchunBoundTransport(Long drvId, String modifyUser, Long supplierId) {
        try {
            //解绑司机+供应商对应运力组
            List<TspTransportGroupDriverRelationPO> relationVO = tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Arrays.asList(drvId));
            if(CollectionUtils.isNotEmpty(relationVO)){
                List<Long> transportGroupIds = relationVO.stream().map(TspTransportGroupDriverRelationPO::getTransportGroupId).collect(Collectors.toList());
                List<TspTransportGroupPO> transportGroupPOS = transportGroupRepository.queryTspTransportByIds(transportGroupIds);
                List<Long> unBingTransprtIds = Lists.newArrayList();
                if(CollectionUtils.isNotEmpty(transportGroupPOS)){
                    for(TspTransportGroupPO transportGroupPO : transportGroupPOS){
                        //筛选出操作的供应商所属运力组
                        if(Objects.equals(supplierId,transportGroupPO.getSupplierId())){
                            unBingTransprtIds.add(transportGroupPO.getTransportGroupId());
                        }
                    }
                }
                //解绑运力组
                if(CollectionUtils.isNotEmpty(unBingTransprtIds)){
                    tspTransportGroupDriverRelationRepository.unFreezeTransportGroup(Arrays.asList(drvId),unBingTransprtIds, SessionHolder.getRestSessionAccountName());
                    //司机解绑运力组,重新计算司机合作模式
                    tmsQmqProducerCommandService.sendCalculateDrvCoopModeQmq(Arrays.asList(drvId),SessionHolder.getRestSessionAccountName());
                }
            }
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public static Set<Long> queryAreaCityIds(List<SimpleAreaGroupDTO> areaGroupDTOList){
        Set<Long> cityIdSets = Sets.newHashSet();
        if(CollectionUtils.isEmpty(areaGroupDTOList)){
            return cityIdSets;
        }
        SimpleAreaGroupDTO simpleAreaGroupDTO = areaGroupDTOList.get(0);
        if(Objects.isNull(simpleAreaGroupDTO)){
            return cityIdSets;
        }
        Map<String, String> dataMap = simpleAreaGroupDTO.getRegionInfo();
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            String entryKey = entry.getKey();
            if(org.apache.commons.lang3.StringUtils.isEmpty(entryKey)){
                continue;
            }
            String[] entryKeys = org.apache.commons.lang3.StringUtils.split(entryKey,"/");
            cityIdSets.add(Long.parseLong(entryKeys[entryKeys.length - 1]));
        }
        return cityIdSets;
    }

    public Set<Long> getDiyRegionCity(List<SimpleAreaGroupDTO> areaGroupDTOList){
        Set<Long> cityIdSets = Sets.newHashSet();
        if(CollectionUtils.isEmpty(areaGroupDTOList)){
            return cityIdSets;
        }
        SimpleAreaGroupDTO simpleAreaGroupDTO = areaGroupDTOList.get(0);
        if(Objects.isNull(simpleAreaGroupDTO)){
            return cityIdSets;
        }
        List<SimpleCityAreaInfoDTO> areaInfoDTOList = simpleAreaGroupDTO.getCityAreaInfoList();
        for (SimpleCityAreaInfoDTO simpleCityAreaInfoDTO : areaInfoDTOList) {
            cityIdSets.add(simpleCityAreaInfoDTO.getCityId());
        }
        return cityIdSets;
    }

    /**
     * 从新行政区域城市中找到不存在于原行政区域的城市ID
     * @param originCityIds
     * @param targetCityIds
     * @return
     */
    public Set<Long> compareCityIds(Set<Long> originCityIds,Set<Long> targetCityIds,Long pointCityId){
        Set<Long> notExistCityIds = Sets.newHashSet();
        for (Long originCityId : originCityIds) {
            if(!targetCityIds.contains(originCityId)){
                notExistCityIds.add(originCityId);
            }
        }
        //去除运力组点位城市
        for(Iterator<Long> iterator = notExistCityIds.iterator();iterator.hasNext();){
            Long it = iterator.next();
            if(Objects.equals(pointCityId,it)){
                iterator.remove();
            }
        }
        return notExistCityIds;
    }

    //比较每个班次设置的上限数和实际报名成功的司机数
    private Map<String,Integer> equalDrvUpperLimitCode(Map<Long, TspTransportGroupWorkShiftPO> workShiftPOMap,Long workShiftId){
        Map<String,Integer> codeMap = Maps.newHashMap();
        int trackCount = 0;//运力不足count
        int trackbeyondCount = 0;//运力超出count
        Set<Long> transportGroupId = Sets.newHashSet();
        List<Long> workShiftIdList = Lists.newArrayList();
        Map<Long,List<TspTransportGroupDriverRelationPO>> workshirtMaps = Maps.newHashMap();//初始化待判断的班次
        for (Map.Entry<Long, TspTransportGroupWorkShiftPO> entry : workShiftPOMap.entrySet()) {
            TspTransportGroupWorkShiftPO shiftPO = entry.getValue();
            if(Objects.isNull(shiftPO)){
                continue;
            }
            workshirtMaps.put(entry.getKey(),Lists.newArrayList());
            workShiftIdList.add(entry.getKey());
            transportGroupId.add(shiftPO.getTransportGroupId());
        }

        //查询已关联运力组的司机列表
        List<TspTransportGroupDriverRelationPO> relationPOList = tspTransportGroupDriverRelationRepository.queryRelationListByTransportGroupIds(new ArrayList<>(transportGroupId),workShiftId);
        //查询班次信息
        List<TspTransportGroupWorkShiftPO> shiftPOList = workShiftRepository.queryWorkShiftList(workShiftIdList);
        Map<Long,Long> workShiftMap = Maps.newHashMap();
        for(TspTransportGroupWorkShiftPO shiftPO : shiftPOList){
            workShiftMap.put(shiftPO.getId(),shiftPO.getDriverUpperLimit());
        }
        Map<Long,List<TspTransportGroupDriverRelationPO>> workshirtMap = relationPOList.stream().collect(Collectors.groupingBy(TspTransportGroupDriverRelationPO::getWorkShiftId));
        //覆盖班次中已报名的司机
        for(Map.Entry<Long, List<TspTransportGroupDriverRelationPO>> entry : workshirtMaps.entrySet()){
            List<TspTransportGroupDriverRelationPO> entryV = workshirtMap.get(entry.getKey());
            if(CollectionUtils.isEmpty(entryV)){
                continue;
            }
            workshirtMaps.put(entry.getKey(),entryV);
        }
        for (Map.Entry<Long, List<TspTransportGroupDriverRelationPO>> entry : workshirtMaps.entrySet()) {
            List<TspTransportGroupDriverRelationPO> relationPOList1 = entry.getValue();
            for(Iterator<TspTransportGroupDriverRelationPO> iterator = relationPOList1.iterator();iterator.hasNext();){
                TspTransportGroupDriverRelationPO relationPO = iterator.next();
                if(!Objects.equals(relationPO.getApplyStatus(), TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode())){
                    iterator.remove();
                }
            }
            Set<Long> drvIds = relationPOList1.stream().map(TspTransportGroupDriverRelationPO::getDrvId).collect(Collectors.toSet());
            Long driverUpperLimit = workShiftMap.get(entry.getKey()) == null?0L:workShiftMap.get(entry.getKey());
            //比较班次上眼数和报名成功的司机数
            if(driverUpperLimit > drvIds.size()){
                codeMap.put("drvCountLess",1);//司机数不足标识
                trackCount +=1;
            }else if(driverUpperLimit < drvIds.size()){
                codeMap.put("drvCountbeyond",1);//司机数超出标识
                trackbeyondCount +=1;
            }
        }

        int resultCode = 0;//1.司机数不足,2.司机数超出标识,3.同是都存在
        //如果司机数既包含超出和不足
        if(codeMap.get("drvCountLess")!=null && codeMap.get("drvCountbeyond")!=null){
            resultCode = 3;
        }
        if(codeMap.get("drvCountLess")!=null && codeMap.get("drvCountbeyond")==null){
            resultCode = 1;
        }
        if(codeMap.get("drvCountbeyond")!=null && codeMap.get("drvCountLess")==null){
            resultCode = 2;
        }
        return buildApplyMap(resultCode,trackCount,trackbeyondCount);
    }

    private Map<String,Integer> buildApplyMap(int resultCode,int trackCount,int trackbeyondCount){
        Map<String,Integer> resultMap = Maps.newHashMap();
        resultMap.put("resultCode",resultCode);
        resultMap.put("trackCount",trackCount);
        resultMap.put("trackbeyondCount",trackbeyondCount);
        return resultMap;
    }

    //批量更新司机合作模式和工作时段，
    // 报名成功的司机合作模式变是全职指派，工作时段更新为班次时段
    private void updateDrvModeAndWorkPeriod(Map<Long,List<Long>> workShiftApplyInfo,Map<Long,TspTransportGroupWorkShiftPO> workShiftPOMap,String modifyUser, TransportGroupDriverApplyProcessDTO applyProcessDTO){
        for (Map.Entry<Long, List<Long>> entry : workShiftApplyInfo.entrySet()) {
            TspTransportGroupWorkShiftPO workShiftPO = workShiftPOMap.get(entry.getKey());
            if(Objects.isNull(workShiftPO)){
                continue;
            }
            TspTransportGroupPO transportGroupPO = transportGroupRepository.queryTransportGroupDetail(workShiftPO.getTransportGroupId());
            if(Objects.isNull(transportGroupPO) || transportGroupPO.getGroupStatus() == 1){
                continue;
            }
            List<Long> drvIds = entry.getValue();
            List<DrvDriverPO> batchUpdateList = Lists.newArrayList();
            for(Long drvId : drvIds){
                DrvDriverPO drvDriverPO = new DrvDriverPO();
                drvDriverPO.setDrvId(drvId);
                drvDriverPO.setCoopMode(TmsTransportConstant.DrvCoopModeEnum.DRV_QJJP.getCode());
                if(!StringUtils.isEmpty(workShiftPO.getStarTime()) && !StringUtils.isEmpty(workShiftPO.getEndTime())){
                    drvDriverPO.setWorkPeriod(workShiftPO.getStarTime()+"~"+workShiftPO.getEndTime());
                    drvDriverPO.setModifyUser(modifyUser);
                    batchUpdateList.add(drvDriverPO);
                    applyProcessDTO.getDriverWorkPeriodMap().put(drvId, drvDriverPO.getWorkPeriod());
                }
            }
            if(CollectionUtils.isEmpty(batchUpdateList)){
                continue;
            }
            try {
                int count = drvDrvierRepository.batchUpdateDrv(batchUpdateList);
                if(count > 0){
                    //发送司机变更QMQ
                    tmsQmqProducerCommandService.sendDrvBatchChangeQmq(new ArrayList<>(drvIds),2,1);
                }
            }catch (Exception e){
                throw new BaijiRuntimeException(e);
            }
        }
    }

    //同点位同城市同车型，创建多个运力组，关联的sku不可重复
    private Boolean judgeSkuRepeat(Long transportGroupId,Long cityId,Long vehicleTypeId, List<Long> skuInfoList){
        try {
            //去掉去重逻辑
            if(true){
                return Boolean.FALSE;
            }
            List<TspTransportGroupPO> transportGroupPOList = transportGroupRepository.queryTransportGroupList(cityId,vehicleTypeId,0);
            if(CollectionUtils.isEmpty(transportGroupPOList)){
                return Boolean.FALSE;
            }
            List<Long> transportGroupIdSet = Lists.newArrayList();
            transportGroupPOList.forEach(tspTransportGroupPO -> {
                if(!Objects.equals(transportGroupId,tspTransportGroupPO.getTransportGroupId())){
                    transportGroupIdSet.add(tspTransportGroupPO.getTransportGroupId());
                }
            });
            if(CollectionUtils.isEmpty(transportGroupIdSet)){
                return Boolean.FALSE;
            }
            transportGroupIdSet.add(transportGroupId);
            List<TspTransportGroupSkuAreaRelationPO> relationPOList = tspTransportGroupSkuRelationRepository.querySkuRelationList(transportGroupIdSet,Boolean.TRUE);
            if(CollectionUtils.isEmpty(relationPOList)){
                return Boolean.FALSE;
            }
            List<Long> skuIdList = Lists.newArrayList();
            relationPOList.forEach(relationPO -> skuIdList.add(relationPO.getSkuId()));
            if(CollectionUtils.intersection(skuInfoList,skuIdList).size() >0){
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }catch (Exception e){
           throw new BaijiRuntimeException(e);
        }
    }

    //上线“全职司机指派-报名制”运力组的逻辑：该运力组每个赛道（班次时段）都至少有一个“报名成功”状态的司机
    //若至少有一个赛道没有“报名成功”状态的司机，则toast提示“该运力组有X个赛道没有报名成功司机”，X = 没报名成功司机的赛道数
    private Map<String,Object> isBingDrv(List<TspTransportGroupDriverRelationPO> driverRelationPOS,Integer transportGroupMode,Long transportGroupId){
        if(CollectionUtils.isEmpty(driverRelationPOS) && !Objects.equals(transportGroupMode, TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
            return getResultMap(0,Boolean.FALSE);
        }
        if(Objects.equals(transportGroupMode, TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
            List<TspTransportGroupWorkShiftPO> tspIntoOrderConfigPOS = workShiftRepository.queryWorkShifts(transportGroupId, TmsTransportConstant.WorkShiftActiveEnum.VALID.getCode());
            if(CollectionUtils.isEmpty(tspIntoOrderConfigPOS)){
                return getResultMap(0,Boolean.FALSE);
            }
            if(CollectionUtils.isEmpty(driverRelationPOS)){
                return getResultMap(tspIntoOrderConfigPOS.size(),Boolean.FALSE);
            }

            List<Long> workShiftIdList = tspIntoOrderConfigPOS.stream().map(TspTransportGroupWorkShiftPO::getId).collect(Collectors.toList());
            Boolean resultFlag = Boolean.FALSE;
            //该运力组下多个赛道，如果有一个赛道中没有报名成功的司机，则不能上线
            Map<Long,List<TspTransportGroupDriverRelationPO>> workShiftIdMap =  driverRelationPOS.stream().collect(Collectors.groupingBy(TspTransportGroupDriverRelationPO::getWorkShiftId));
            int resultCount = tspIntoOrderConfigPOS.size();
            for(Map.Entry<Long,List<TspTransportGroupDriverRelationPO>> entry : workShiftIdMap.entrySet()){
                Boolean workFlag = Boolean.FALSE;
                if(workShiftIdList.contains(entry.getKey())){
                    for(TspTransportGroupDriverRelationPO relationPO : entry.getValue()){
                        if(Objects.equals(relationPO.getApplyStatus(), TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode())){
                            workFlag = Boolean.TRUE;
                            break;
                        }
                    }
                }
                if(workFlag){
                    --resultCount;
                }
            }
            if(resultCount == 0){
                resultFlag = Boolean.TRUE;
            }
            return getResultMap(resultCount,resultFlag);
        }
        return getResultMap(0,Boolean.TRUE);
    }

    private Map<String,Object> getResultMap(Integer resultCount,Boolean resultFlag){
        Map<String,Object> isBingDrvMap = Maps.newHashMap();
        isBingDrvMap.put("resultFlag",resultFlag);
        isBingDrvMap.put("resultCount",resultCount);
        return isBingDrvMap;
    }

    /***
    　* @description: 报名制赛道更新/缺少补位 同步刷新司机合作模式
    　* <AUTHOR>
    　* @date 2021/9/15 15:32
    */
    public void syncDrvCoopMode(List<ApplyDriverRelationDetailPO> relationDetailPOList,String modifyUser){
        if(CollectionUtils.isEmpty(relationDetailPOList)){
            return;
        }
        Set<Long> drvList = relationDetailPOList.stream().map(ApplyDriverRelationDetailPO::getDrvId).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(drvList)){
            tmsQmqProducerCommandService.sendCalculateDrvCoopModeQmq(new ArrayList<>(drvList),modifyUser);
        }
    }

    //保存全局赛道更新记录
    public Boolean saveGlobalTrackRecord(Integer updateType,Map<Long, TspTransportGroupWorkShiftPO> workShiftPOMap,String modifyUser){
        if(Objects.equals(TmsTransportConstant.ApplyTransPortOperationTypeEnum.CAPACITY_REPLACEMENT.getCode(),updateType)){
            return Boolean.FALSE;
        }
        Set<Long> transportGroupIds = Sets.newHashSet();
        for (Map.Entry<Long, TspTransportGroupWorkShiftPO> entry : workShiftPOMap.entrySet()) {
            TspTransportGroupWorkShiftPO shiftPO = entry.getValue();
            if(Objects.isNull(shiftPO)){
                continue;
            }
            transportGroupIds.add(shiftPO.getTransportGroupId());
        }
        if(CollectionUtils.isEmpty(transportGroupIds)){
            return Boolean.FALSE;
        }
        try {
            for(Long transportGroupId : transportGroupIds){
                TransportTrackRecordPO recordPO = new TransportTrackRecordPO();
                recordPO.setTransportGroupId(transportGroupId);
                recordPO.setOperationType(TmsTransportConstant.ApplyTransPortOperationTypeEnum.GLOBAL_TRACK.getCode());
                recordPO.setCreateUser(modifyUser);
                recordPO.setModifyUser(modifyUser);
                transportTrackRecordRepository.insert(recordPO);
            }
            return Boolean.TRUE;
        }catch (Exception e){
            logger.error("saveGlobalTrackRecordError","workShiftPOMap:{},e",JsonUtil.toJson(workShiftPOMap),e);
            return Boolean.FALSE;
        }
    }
}
