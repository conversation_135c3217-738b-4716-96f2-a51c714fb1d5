package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.dto.DrvAddContext;
import com.ctrip.dcs.tms.transport.application.helper.DrvStatusTransitionPermissionHelper;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.AddApproveDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.*;
import java.sql.Date;
import java.sql.*;
import java.util.Objects;
import java.util.*;
import java.util.stream.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum.TRANSPORT_DRIVER_NAME_NOT_MATCH_LICENSE_NAME;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum.TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.TRANSPORT_DRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.SharkKeyConstant.TRANSPORT_DRIVER__CREATE_DRIVER_ACCOUNT_FAILED;

/**
 * 司机命令类接口
 *
 * <AUTHOR>
 * @Date 2020/3/17 15:00
 */
@Service
public class DriverCommandServiceImpl implements DriverCommandService {

    private static final Logger logger = LoggerFactory.getLogger(DriverCommandServiceImpl.class);

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private ModRecordRespository modRecordRespository;
    @Autowired
    private TmsPmsproductQueryService pmsproductQueryService;
    @Autowired
    private DrvRecruitingRepository drvRecruitingRepository;
    @Autowired
    private DriverQueryService driverQueryService;
    @Autowired
    private RecruitingCommandService recruitingCommandService;
    @Autowired
    private TransportGroupCommandService transportGroupCommandService;
    @Autowired
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;
    @Autowired
    private ApprovalProcessAuthQconfig authQconfig;

    @Autowired
    private CommonCommandService commonCommandService;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    TmsDrvFreezeRepository tmsDrvFreezeRepository;
    @Autowired
    private DriverPasswordService driverPasswordService;
    @Autowired
    TmsCertificateCheckRepository checkRepository;
    @Autowired
    DriverGroupRelationRepository driverGroupRelationRepository;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;
    @Autowired
    private TransportGroupRepository transportGroupRepository;
    @Autowired
    TmsTransportApproveRepository approveRepository;
    @Autowired
    TmsTransportApproveCommandService approveCommandService;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    TmsDrvLoginInformationRepository informationRepository;
    @Autowired
    private DrvVehRecruitingCommandService drvVehRecruitingCommandService;
    @Autowired
    private DrvHealthPunchRepository drvHealthPunchRepository;
    @Autowired
    DrvVehRecruitingQueryService drvVehRecruitingQueryService;
    @Autowired
    AuthorizationCheckService authorizationCheckService;
    @Resource
    private TransportGroupQueryService transportGroupQueryService;
    @Autowired
    DrvDispatchRelationRepository drvDispatchRelationRepository;
    @Autowired
    private DrvFreezeRecordRepository drvFreezeRecordRepository;
    @Autowired
    OverseasQconfig overseasQconfig;
    @Autowired
    DriverCommandService driverCommandService;
    @Autowired
    private TmsVerifyEventCommandService eventCommandService;

    @Autowired
    DriverAccountManagementHelper driverAccountManagementHelper;

    @Autowired
    TransportCommonQconfig commonQconfig;

    @Autowired
    MobileHelper mobileHelper;

    @Autowired
    DriverGuideProxy driverGuidProxy;

    @Autowired
    SupplierDayProductLineMigrationHelper supplierDayProductLineMigrationHelper;

    @Autowired
    QueryCategoryService queryCategoryService;

    @Autowired
    DrvStatusTransitionPermissionHelper drvStatusTransitionPermissionHelper;

    @Autowired
    DriverCommandManager driverCommandManager;


    @Override
    public Result<Boolean> updateDrvIntendVehicleType(UpdateDrvIntendVehicleTypeRequestType requestType) {
        Result.Builder<Boolean> newResult = Result.Builder.<Boolean>newResult();
        try {
            DrvDriverPO drvDriverPO = drvDrvierRepository.getDrvDriverRepo().queryByPk(requestType.getDrvId());
            if (Objects.isNull(drvDriverPO)) {
                return newResult.fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverNotExist)).build();
            }
            String modifyUser = String.format(TmsTransportConstant.TMS_DEFAULT_USERNAME_DRIVER_FORMAT,drvDriverPO.getDrvName(),drvDriverPO.getDrvId());
            int count = drvDrvierRepository.updateDrvIntendVehicleType(drvDriverPO.getDrvId(), requestType.getIntendVehicleTypeId(), modifyUser);
            if (count > 0) {
                //发送司机变更QMQ
                tmsQmqProducerCommandService.sendDrvChangeQmq(requestType.getDrvId(), 2,1);
                return newResult.success().withData(true).build();
            }
        }catch (Exception e){
            logger.error(e);
            return newResult.fail().withException(e).build();
        }
        return newResult.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg("update error").build();
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateDrvStatus(List<Long> drvIds, Integer status, Integer opFrom, String modifyUser) {
        if (CollectionUtils.isEmpty(drvIds)) {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvIdIsZero))
                    .withData(false)
                    .build();
        }
        if (qconfig.getPenaltySwitch() && TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().intValue() == status.intValue()) {
            List<Long> penaltyFreezeDrvIdList = driverQueryService.queryPenaltyFreezeDrvIdList(drvIds).getData();
            List<Long> penaltyOfflineDrvIdList = driverQueryService.queryPenaltyOfflineDrvIdList(drvIds).getData();
            drvIds = filtrationPenaltyDrvId(drvIds, penaltyFreezeDrvIdList, penaltyOfflineDrvIdList);
            if (CollectionUtils.isEmpty(drvIds)) {
                return Result.Builder.<Boolean>newResult().success().withData(true).build();
            }
        }
        MetricsUtils.drvUpdateStatusConnectTimes(ApiTypeEnum.UPDATE_STATUS_DRV,drvIds);
        //查询司机状态
        List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.queryDrvList(drvIds);
        List<Long> ids = Lists.newArrayList();
        List<Long> cityIdList = Lists.newArrayList();
        List<Long> vehicleList = Lists.newArrayList();
        Map<Long, DrvDriverPO> driverPOMap = Maps.newHashMap();
        for (DrvDriverPO drvDriverPO : drvDriverPOList) {
            Result<Boolean> mobileValid =
              mobileHelper.isMobileVerified(drvDriverPO.getIgtCode(), drvDriverPO.getDrvPhone(), drvDriverPO.getInternalScope());
            if(Objects.equals(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), status) && !mobileValid.isSuccess()) {
                return ResponseResultUtil.failed(mobileValid.getCode(), "[" + drvDriverPO.getDrvId() +"]" + mobileValid.getMsg());
            }

            //检查操作人是否有权限
            Result<Boolean> unfreezePermissionCheck = drvStatusTransitionPermissionHelper.checkPermission(
                String.valueOf(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode()), drvDriverPO.getDrvId(),
                StringUtils.isEmpty(SessionHolder.getRestSessionAccountType()) ? null : Integer.valueOf(SessionHolder.getRestSessionAccountType()));
            if (!unfreezePermissionCheck.isSuccess()) {
                return unfreezePermissionCheck;
            }

            cityIdList.add(drvDriverPO.getCityId());
            if(drvDriverPO.getVehicleId()!=null && drvDriverPO.getVehicleId() >0){
                vehicleList.add(drvDriverPO.getVehicleId());
            }
            if (drvDriverPO.getDrvStatus().intValue() == TmsTransportConstant.DrvStatusEnum.UNACT.getCode().intValue()) {
                driverPOMap.put(drvDriverPO.getDrvId(),drvDriverPO);
            }
            if (checkDrvStatus(status,drvDriverPO.getDrvStatus())) {
                continue;
            }
            ids.add(drvDriverPO.getDrvId());
        }
        //给判断用
        if(opFrom != null && ids.size() > 0){
            return Result.Builder.<Boolean>newResult().success().withData(true).build();
        }
        if (ids.size() > 0) {
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgDriverStatusChangeError))
                    .withData(false)
                    .build();
        }

        List<Long> failedRegisteAccountDriverIdList = Lists.newArrayList();
        List<DrvDriverPO> successededDriverList = Lists.newArrayList();
        StringBuilder errorMessage = new StringBuilder();

        //新增按城市限制操作
        Result<Boolean> disableFlagResult = driverQueryService.judgeDrvOnlinePermission(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(status),cityIdList,vehicleList);
        if(!disableFlagResult.isSuccess()){
            return disableFlagResult;
        }
        int count = drvDrvierRepository.updateDrvStatus(drvIds,opFrom, status, modifyUser);
        if(count <= 0){
            MetricsUtils.drvUpdateStatusExceptionTimes(ApiTypeEnum.UPDATE_STATUS_DRV,drvIds);
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportStatusChangeError))
                    .withData(false)
                    .build();
        }

        if (TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode().equals(status)) {
            //司机下线解绑车辆
            drvDrvierRepository.unbindCarforDrv(drvIds,modifyUser);
            //司机下线解绑运力组
            //若司机已关联“全职司机指派-报名制”运力组
            //直接从已报名的原车型运力组解绑掉（上线&下线运力组），全职报名状态变更为“未报名”
            transportGroupCommandService.unBoundTransport(drvIds,modifyUser,false);
            //发送司机下线消息
            tmsQmqProducerCommandService.sendDrvOfflineQmq(drvIds,opFrom == null?0:1);

        }else if (TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(status)){

            Map<Long, DrvDriverPO> drvPOMap = Maps.newHashMap();
            drvDriverPOList.forEach(drvDriverPO -> {
                drvPOMap.put(drvDriverPO.getDrvId(),drvDriverPO);
            });

            // 到账号中心注册司机账号
            List<DriverAccountRegisterResultDTO> driverAccountRegisterResultDTOList =
              driverAccountManagementHelper.batchRegisterDriverAccountWhenNotExist(drvDriverPOList);
            for (DriverAccountRegisterResultDTO driverAccountRegisterResultDTO : driverAccountRegisterResultDTOList) {
                if(driverAccountRegisterResultDTO.isNeedRegisterAccount()) { // 如果需要注册
                    DrvDriverPO drvDriverPO = drvPOMap.get(driverAccountRegisterResultDTO.getDrvId());
                    if (driverAccountRegisterResultDTO.isRegisterSuccess()) { // 如果注册成功, 更新司机账号
                        drvDrvierRepository.updateDrvUid(drvDriverPO.getDrvId(), driverAccountRegisterResultDTO.getUid(), modifyUser, driverAccountRegisterResultDTO.getPpmAccount(), driverAccountRegisterResultDTO.getQunarAccount());
                    }else { // 注册失败, 还原司机状态
                        drvDrvierRepository.updateDrvStatus(Lists.newArrayList(drvDriverPO.getDrvId()), drvDriverPO.getDrvStatus(), modifyUser);
                        failedRegisteAccountDriverIdList.add(drvDriverPO.getDrvId());
                        errorMessage.append("" + drvDriverPO.getDrvId() + ":" + driverAccountRegisterResultDTO.getErrorMsg()).append(";");
                    }
                }
            }

            logger.info("failedRegisteAccountDriverIdList", "{}", failedRegisteAccountDriverIdList);

            // 如果全都失败，直接返回全部失败错误
            if(failedRegisteAccountDriverIdList.size() > 0 && failedRegisteAccountDriverIdList.containsAll(drvIds)){
                Cat.logEvent(ErrorCodeEnum.TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_FAILED.name().toLowerCase(), failedRegisteAccountDriverIdList.toString());
                MetricsUtils.drvUpdateStatusExceptionTimes(ApiTypeEnum.UPDATE_STATUS_DRV,failedRegisteAccountDriverIdList);
                return Result.Builder.<Boolean>newResult()
                  .fail()
                  .withCode(ErrorCodeEnum.TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_FAILED.getCode())
                  .withMsg(SharkUtils.getSharkValue(TRANSPORT_DRIVER__CREATE_DRIVER_ACCOUNT_FAILED) + "[" + errorMessage.substring(0, errorMessage.length() - 1) + "]")
                  .withData(false)
                  .build();
            }


            // 移除失败的司机id
            driverPOMap.entrySet().removeIf(entry -> failedRegisteAccountDriverIdList.contains(entry.getKey()));
            drvIds = drvIds.stream().filter(driverId -> !failedRegisteAccountDriverIdList.contains(driverId)).collect(
              Collectors.toList());
            successededDriverList = drvDriverPOList.stream().filter(drvDriverPO -> !failedRegisteAccountDriverIdList.contains(drvDriverPO.getDrvId())).collect(
              Collectors.toList());

            tmsQmqProducerCommandService.sendDrvOnlineQmq(drvIds);
            if (driverPOMap.size() > 0) {
                for (DrvDriverPO driverPO : driverPOMap.values()) {
                    commonCommandService.sendJoinMessageByPhone(driverPO.getDrvId(),driverPO.getIgtCode(),driverPO.getDrvPhone());
                }
            }
        }

        //批量操作司机上线时，将未激活的司机上线时间刷新
        if (TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(status)) {
            updateDrvOnline(successededDriverList);
        }
        //记录状态变更历史
        for (DrvDriverPO drvDriverPO : drvDriverPOList) {
            //司机入驻携程大学
            tmsQmqProducerCommandService.sendPushDataToCtripUniversityQmq(drvDriverPO.getDrvId());

        }

        //todo 记录解冻记录
        saveDrvUnFreezeRecord(successededDriverList,modifyUser);
        //清除司机冻结记录
        tmsDrvFreezeRepository.batchUnFreezeByDrvIds(drvIds,modifyUser);

        // 如果注册账号部分失败，直接返回部分失败错误
        if(failedRegisteAccountDriverIdList.size() > 0){
            MetricsUtils.drvUpdateStatusExceptionTimes(ApiTypeEnum.UPDATE_STATUS_DRV,failedRegisteAccountDriverIdList);
            Cat.logEvent(ErrorCodeEnum.TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED.name().toLowerCase(), failedRegisteAccountDriverIdList.toString());
            return Result.Builder.<Boolean>newResult()
              .fail()
              .withCode(ErrorCodeEnum.TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED.getCode())
              .withMsg(SharkUtils.getSharkValue(TRANSPORT_DRIVER_CREATE_DRIVER_ACCOUNT_PARTIAL_FAILED)  + "[" + errorMessage.substring(0, errorMessage.length() - 1) + "]")
              .withData(false)
              .build();
        }

        return Result.Builder.<Boolean>newResult()
                .success()
                .withData(true)
                .build();
    }

    @Override
    public Result<Boolean> updateDrvStatus(List<Long> drvIds, Integer status, String modifyUser) {
        drvDrvierRepository.updateDrvStatus(drvIds,status,modifyUser);
        return Result.Builder.<Boolean>newResult()
                .success()
                .withData(true)
                .build();
    }

    @Override
    public Result<Boolean> updateDrvUid(Long drvId,String uid,String modifyUser, String ppmAccount, String qunarAccount) {
        drvDrvierRepository.updateDrvUid(drvId,uid,modifyUser, ppmAccount, qunarAccount);
        return Result.Builder.<Boolean>newResult()
                .success()
                .withData(true)
                .build();
    }

    /**
     * 状态校验
     * @param updateStatus
     * @param originalStatus
     * @return
     */
    private boolean checkDrvStatus(Integer updateStatus,Integer originalStatus){
        if (TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(updateStatus)){
            return !TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(originalStatus);
        }else if (TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode().equals(updateStatus)){
            return TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(originalStatus)
                    || TmsTransportConstant.DrvStatusEnum.FREEZE.getCode().equals(originalStatus);
        }else {
            return false;
        }
    }

    public Result<Boolean> addDrvCommonCheckNew(DrvDriverPO drvDriverPO,Boolean audit) throws Exception {
        final String template = "[%s]%s";
        try {
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), drvDriverPO.getInternalScope())) {
                //判断手机号唯一 (境内、外)
                if (!Strings.isNullOrEmpty(drvDriverPO.getDrvPhone()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvPhone(), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), SharkUtils.getSharkValue(SharkKeyConstant.mobilePhoneUsedChangeForRegistration)));
                }
                //判断邮箱唯一 (境内、外)
                if (!Strings.isNullOrEmpty(drvDriverPO.getEmail()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getEmail(), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getEmail(),KeyType.Mail), SharkUtils.getSharkValue(SharkKeyConstant.mailboxUsedChangeForRegistration)));
                }
                //身份证唯一
                if (drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvIdcard(), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getDrvIdcard(),KeyType.Identity_Card), SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists)));
                }
            } else {
                //境外派遣司机，判断邮件或手机号已存在
                DrvDriverPO drvDispatchPO = null;
                if (!Strings.isNullOrEmpty(drvDriverPO.getDrvPhone()) && !Objects.isNull(drvDispatchPO =  drvDrvierRepository.drvDispatchcheckDrvOnly(drvDriverPO.getDrvPhone(), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode()))) {
                    return overseasDrvDispatchCheck(SharkKeyConstant.transportPhoneAlreadyExists,drvDriverPO.getSupplierId(),drvDriverPO.getCityId(),drvDispatchPO);
                }
                if (!Objects.isNull(drvDispatchPO = drvDrvierRepository.drvDispatchcheckDrvPhoneOnly(drvDriverPO.getDrvPhone()))) {
                    Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "addDrv");
                    return overseasDrvDispatchCheck(SharkKeyConstant.transportPhoneAlreadyExists, drvDriverPO.getSupplierId(), drvDriverPO.getCityId(), drvDispatchPO);
                }
                //境外派遣司机，判断邮件或手机号已存在
                if (!Strings.isNullOrEmpty(drvDriverPO.getEmail()) && !Objects.isNull(drvDispatchPO = drvDrvierRepository.drvDispatchcheckDrvOnly(drvDriverPO.getEmail(), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode()))) {
                    return overseasDrvDispatchCheck(SharkKeyConstant.mailboxUsedChangeForRegistration,drvDriverPO.getSupplierId(),drvDriverPO.getCityId(),drvDispatchPO);
                }
                //账号唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(drvDriverPO.getLoginAccount()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getLoginAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, drvDriverPO.getLoginAccount(), SharkUtils.getSharkValue(SharkKeyConstant.transportAccountAlreadyExists)));
                }
                //派安盈账户唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(drvDriverPO.getPaiayAccount()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getPaiayAccount(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_ACCOUNT.getCode())) {
                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST)).build();
                }
                //派安盈邮箱唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(drvDriverPO.getPaiayEmail()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getPaiayEmail(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_EMAIL.getCode())) {
                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST)).build();
                }
            }
            if (audit) {
                //判断车辆信息
                if (vehicleRepository.checkVehAvailAndHasNoDrv(drvDriverPO.getVehicleId(), drvDriverPO.getSupplierId())) {
                    return this.resultErrorNewInfo(String.format(template, drvDriverPO.getVehicleLicense(), SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleIsEmptyOrOccupied)));
                }
            }
            //同步车型
            if (drvDriverPO.getVehicleId() != null && drvDriverPO.getVehicleId() > 0) {
                VehVehiclePO vehVehiclePO =  vehicleRepository.getVehVehicleRepo().queryByPk(drvDriverPO.getVehicleId());
                if (vehVehiclePO != null) {
                    drvDriverPO.setVehicleTypeId(vehVehiclePO.getVehicleTypeId());
                }
            }
            return Result.Builder.<Boolean>newResult().success().build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public Result<Boolean> overseasDrvDispatchCheck(String sharkValue,Long nowSupplierId,Long cityId,DrvDriverPO drvDriverPO){
        //灰度城市
        Result<Boolean> cityGray = driverQueryService.drvDispatchCityGray(cityId);
        if(cityGray.isSuccess() && !cityGray.getData()){
            return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(sharkValue)).withData(false).build();
        }
        //如果当前供应商和已存在正式司机所属供应商相同，则返回原有提示
        if(Objects.equals(nowSupplierId,drvDriverPO.getSupplierId()) ||
                Objects.equals(drvDriverPO.getTemporaryDispatchMark(), TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode())){
            return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(sharkValue)).withData(false).build();
        }

        return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100023).withMsg(String.valueOf(drvDriverPO.getDrvId())).build();

    }

    public Result<Long> commonCheckNew(DrvDriverPO drvDriverPO,Boolean audit){
        final String template = "[%s]%s";
        try {
            //判断手机号唯一 (境内、外)
            if (!Strings.isNullOrEmpty(drvDriverPO.getDrvPhone()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvPhone(), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
                return this.resultErrorNewInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), SharkUtils.getSharkValue(SharkKeyConstant.mobilePhoneUsedChangeForRegistration)),0L);
            }
            //判断邮箱唯一 (境内、外)
            if (!Strings.isNullOrEmpty(drvDriverPO.getEmail()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getEmail(), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
                return this.resultErrorNewInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getEmail(), KeyType.Mail), SharkUtils.getSharkValue(SharkKeyConstant.mailboxUsedChangeForRegistration)),0L);
            }
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), drvDriverPO.getInternalScope())) {
                //身份证唯一
                if (drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvIdcard(), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getDrvIdcard(), KeyType.Identity_Card), SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists)),0L);
                }
            } else {
                //账号唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(drvDriverPO.getLoginAccount()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getLoginAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, drvDriverPO.getLoginAccount(), SharkUtils.getSharkValue(SharkKeyConstant.transportAccountAlreadyExists)),0L);
                }
                //派安盈账户唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(drvDriverPO.getPaiayAccount()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getPaiayAccount(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_ACCOUNT.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, drvDriverPO.getPaiayAccount(), SharkUtils.getSharkValue(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST)),0L);
                }
                //派安盈邮箱唯一校验 (境外 可以不填写)
                if (!Strings.isNullOrEmpty(drvDriverPO.getPaiayEmail()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getPaiayEmail(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_EMAIL.getCode())) {
                    return this.resultErrorNewInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getPaiayEmail(), KeyType.Mail), SharkUtils.getSharkValue(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST)),0L);
                }
            }
            if (audit) {
                //判断车辆信息
                if (vehicleRepository.checkVehAvailAndHasNoDrv(drvDriverPO.getVehicleId(), drvDriverPO.getSupplierId())) {
                    return this.resultErrorNewInfo(String.format(template, drvDriverPO.getVehicleLicense(), SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleIsEmptyOrOccupied)),0L);
                }
            }
            //同步车型
            if (drvDriverPO.getVehicleId() != null && drvDriverPO.getVehicleId() > 0) {
                VehVehiclePO vehVehiclePO =  vehicleRepository.getVehVehicleRepo().queryByPk(drvDriverPO.getVehicleId());
                if (vehVehiclePO != null) {
                    drvDriverPO.setVehicleTypeId(vehVehiclePO.getVehicleTypeId());
                }
            }
            return Result.Builder.<Long>newResult().success().build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

  @Override
  @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
  public Result<Boolean> addDrv(DrvAddSOARequestType requestType) {
      try {
          // check mobile
          Result<Boolean> siMobileValid = mobileHelper.isMobileValid(requestType.getIgtCode(), requestType.getDrvPhone(),
            requestType.getCityId());
          if(!siMobileValid.isSuccess()){
              return Result.Builder.<Boolean>newResult().fail()
                .withCode(siMobileValid.getCode())
                .withMsg(siMobileValid.getMsg())
                .withData(siMobileValid.getData())
                .build();
          }

      DrvDriverPO drvDriverPO = toDrvPOBean(requestType);
      //校验工作时段是否合法
      final String template = "【%s】%s";
      if (!driverQueryService.checkWorkPeriod(drvDriverPO.getWorkPeriod())){
          return Result.Builder.<Boolean>newResult().fail().withMsg(String.format(template, drvDriverPO.getWorkPeriod(), SharkUtils.getSharkValue(SharkKeyConstant.transportDriverWorkPeriodOverlap))).build();
      }

//      if (StringUtils.isBlank(drvDriverPO.getDrvIdcard()) && (Objects.isNull(drvDriverPO.getTaxi()) || BooleanUtils.isNotTrue(BooleanUtils.toBoolean(drvDriverPO.getTaxi(), Integer.valueOf(1), Integer.valueOf(0))))) {
//          return Result.Builder.<Boolean>newResult().fail().withMsg(String.format(SharkUtils.getSharkValue(SharkKeyConstant.transportDriverCardImgRequired))).build();
//      }
//      commonCheck(drvDriverPO, true);
      // 校验司机唯一属性
      Result<Boolean> checkResult = addDrvCommonCheckNew(drvDriverPO,true);
      if(!checkResult.isSuccess()){
          return Result.Builder.<Boolean>newResult().fail().withCode(checkResult.getCode()).withMsg(checkResult.getMsg()).build();
      }

      //新重构版本并且境内司机校验
      if(requestType.getVersionFlag()!=null && Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), drvDriverPO.getInternalScope())){
          //校验境内招募司机是否否存在手机号、身份证号
          Result<Boolean> checkRecruitingResult  = drvVehRecruitingQueryService.checkRecruitingDrvOnly(requestType.getDrvPhone(),requestType.getDrvIdcard());
          if(!checkRecruitingResult.isSuccess()){
              return Result.Builder.<Boolean>newResult().fail().withCode(checkRecruitingResult.getCode()).withMsg(checkRecruitingResult.getMsg()).withData(checkRecruitingResult.getData()).build();
          }
      }

      // 生成密码 境内外司机均可设置密码
      if(StringUtils.isNotEmpty(drvDriverPO.getLoginPwd())){
          //判断登录密码是否合规
          if(!driverPasswordService.isPasswordValid(drvDriverPO.getLoginPwd())){
              return Result.Builder.<Boolean>newResult().fail().withCode("405").withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.driverLoginPassworderror)).build();
          }
          String salt = driverPasswordService.genPwdSalt();
          String encPwd = driverPasswordService.encryptPwd(drvDriverPO.getLoginPwd(), salt);
          drvDriverPO.setLoginPwd(encPwd);
          drvDriverPO.setSalt(salt);
      }
      // 工作台创建司机，进入司机审批表
      DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
      BeanUtils.copyProperties(drvDriverPO, drvRecruitingPO);
      drvRecruitingPO.setDrvFrom(TmsTransportConstant.DrvFromEnum.DRV_MANUAL.getCode());
      drvRecruitingPO.setCoopMode(TmsTransportConstant.DrvCoopModeEnum.NO.getCode());
      drvRecruitingPO.setTaxi(drvDriverPO.getTaxi());
//      drvRecruitingPO.setVersionFlag(requestType.getVersionFlag() == null?qconfig.getSystemVersionFlag():requestType.getVersionFlag());
      if (CollectionUtils.isNotEmpty(requestType.getVaccinationTimeList())) {
          drvRecruitingPO.setVaccinationTimeList(Joiner.on(",").join(requestType.getVaccinationTimeList()));
      }
      if (!Strings.isNullOrEmpty(requestType.getNucleicAcidTestingTime())) {
          java.util.Date date = DateUtil.stringToDate(requestType.getNucleicAcidTestingTime(), DateUtil.YYYYMMDD);
          if (date != null) {
              drvRecruitingPO.setNucleicAcidTestingTime(new Date(date.getTime()));
          }
      }
      drvRecruitingPO.setOcrNucleicAcidData(requestType.getOcrNucleicAcidTestingData());
      drvRecruitingPO.setOcrVaccineData(requestType.getOcrVaccineData());
      drvRecruitingPO.setApproveTime(DateUtil.getNow());
      drvRecruitingPO.setApproveSchedule(0);
      // int count = drvDrvierRepository.addDrv(drvDriverPO);
      Map<String,String> modSnapshotMap = Maps.newHashMap();
      modSnapshotMap.put("drvIdCard",drvDriverPO.getDrvIdcard());
      modSnapshotMap.put("drvName",drvDriverPO.getDrvName());
      drvRecruitingPO.setModSnapshotValues(JsonUtil.toJson(modSnapshotMap));
      //存储境外OCR校验结果
      if(CollectionUtils.isNotEmpty(requestType.getOcrPassStatusList())){
          drvRecruitingPO.setOcrPassStatusJson(JsonUtil.toJson(requestType.getOcrPassStatusList()));
      }
      Long count = drvRecruitingRepository.addDrvRecruiting(drvRecruitingPO);
      if (count > 0) {
          // 进入招募时做核酸校验
          Map<String,Object> nucleicAcidMap =  drvVehRecruitingCommandService.toDoNucleicAcidLabelMap(count, drvRecruitingPO.getCityId(), drvRecruitingPO.getDrvName(), drvRecruitingPO.getDrvIdcard(), requestType.getNucleicAcidTestingTime(), requestType.getOcrNucleicAcidTestingData());
          drvVehRecruitingCommandService.toDoVaccineLabelMap(count, drvRecruitingPO.getDrvName(), drvRecruitingPO.getDrvIdcard(), requestType.getVaccinationTimeList(), requestType.getOcrVaccineData(),nucleicAcidMap);
          //初始化审批步骤数据
          if(requestType.getVersionFlag()!=null && requestType.getVersionFlag()>=3 && Objects.equals(requestType.getAreaScope(), AreaScopeTypeEnum.DOMESTIC.getCode())){
              recruitingCommandService.initSingleApprovalData(count,TmsTransportConstant.SingleApproveTypeEnum.DRV.getCode(),requestType.getModifyUser(),null,requestType,requestType.getChildCheckStatusList(),nucleicAcidMap);
          }

        if (Objects.equals(requestType.getAreaScope(), AreaScopeTypeEnum.DOMESTIC.getCode()) && requestType.isOcrheadPortraitResult() != null) {
            drvVehRecruitingCommandService.toDoOcrHeadPortraitLabel(count, requestType.ocrheadPortraitResult);
            drvVehRecruitingCommandService.toDoComplianceHeadPortraitLabel(count);
        }

        if (drvDriverPO.getVehicleId() != null && drvDriverPO.getVehicleId() > 0) {
          vehicleRepository.updateVehicleHasDrv(drvDriverPO.getVehicleId(),
              TmsTransportConstant.VehicleHasDrvEnum.IS_HASDRV.getCode(), drvDriverPO.getModifyUser());
        }
        //TODO 招募变更记录需要切换到新流程
        modRecordRespository.insetModRecord(count, null, CommonEnum.RecordTypeEnum.RECRUIT,
            changeRecordAttributeNameQconfig.getDriverRecordMap(), drvDriverPO.getModifyUser());
        // qmq 招募司机不需要qmq
        // tmsQmqProducerCommandService.sendDrvChangeQmq(count,1);
        // 添加招募司机，直接通过
        if (requestType.getAction() != null) {
          recruitingCommandService.approveRoute(buildRequest(requestType, count),TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
        }
          if(requestType.getVersionFlag() == null || requestType.getVersionFlag() < 5){
              // 运营角色，自动通过审核
              recruitingCommandService.recruitingApproveAdd(requestType.getRoleCode(), requestType.getAreaScope(),
                      TmsTransportConstant.DrvFromEnum.DRV_MANUAL.getCode(), count, requestType.getCreateUser(), requestType.getRemark(),TmsTransportConstant.RecruitingTypeEnum.drv.getCode());
          }
          //境外新逻辑
          Result<Boolean> overseasResult =  recruitingCommandService.overseasNewBusinessApprove(requestType.getSupplierId(),requestType.getVersionFlag(),requestType.getAreaScope(),count,requestType.getOcrPassStatusList(), TmsTransportConstant.RecruitingTypeEnum.drv, TmsTransportConstant.SingleApproveTypeEnum.DRV,requestType.getModifyUser());
          if(!overseasResult.isSuccess()){
              return Result.Builder.<Boolean>newResult().fail().withCode(overseasResult.getCode()).withMsg(overseasResult.getMsg()).build();
          }
          //发送审批时效qmq
          tmsQmqProducerCommandService.sendRecruitingApproveAgingQMQ(count,TmsTransportConstant.RecruitingTypeEnum.drv.getCode(),TmsTransportConstant.RecruitingApproverStatusEnum.supplier_Approve_finish.getCode());

          //司机处理器，以后境内外司机都分开不同的处理器处理
          DrvAddContext addDrvContext = new DrvAddContext();
          addDrvContext.setDrvDriverPO(drvDriverPO);
          driverCommandManager.getHandler(requestType.getAreaScope()).afterAdd(addDrvContext);

        return Result.Builder.<Boolean>newResult().success().withData(true).build();
      }
    } catch (Exception e) {
          logger.warn("addDrv error", e);
          return Result.Builder.<Boolean>newResult().fail().withMsg(e.getLocalizedMessage()).build();
    }
    return Result.Builder.<Boolean>newResult().fail().build();
  }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Long> updateDrv(DrvDriverPO drvDriverPO, Boolean ocrHeadPortraitResult,List<OcrPassStatusModelSOA> ocrPassStatusList) {
        try {
            // check mobile
            Result<Boolean> siMobileValid = mobileHelper.isMobileValid(drvDriverPO.getIgtCode(), drvDriverPO.getDrvPhone(),
              drvDriverPO.getCityId());
            if(!siMobileValid.isSuccess()){
                return Result.Builder.<Long>newResult().fail()
                  .withCode(siMobileValid.getCode())
                  .withMsg(siMobileValid.getMsg())
                  .withData(null)
                  .build();
            }

            // 到司机表中查询司机的老数据
            DrvDriverPO originDrv = drvDrvierRepository.queryByPk(drvDriverPO.getDrvId());
            if (originDrv == null) {
                return this.resultErrorInfo(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty),String.valueOf(drvDriverPO.getDrvId()),0L);
            }
            //网约车驾驶证号 更新值为空 且 数据库已存在该值  则不允许清空该值
            if(org.springframework.util.StringUtils.isEmpty(drvDriverPO.getDriverNetCertNo()) && !org.springframework.util.StringUtils.isEmpty(originDrv.getDriverNetCertNo())){
                return this.resultErrorInfo(SharkUtils.getSharkValue(SharkKeyConstant.driverNetCertNoNotEmpty),String.valueOf(drvDriverPO.getDrvId()),0L);
            }
            if(!originDrv.getActive()){
                return this.resultErrorInfo(SharkUtils.getSharkValue(SharkKeyConstant.driverlistpageDiscardtipWrongAlreadydiscard),String.valueOf(drvDriverPO.getDrvId()),0L);
            }

            //修改产线，司机必须下线
            if(Objects.equals(originDrv.getTemporaryDispatchMark(), TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode()) && drvDriverPO.getCategorySynthesizeCode() != null && !Objects.equals(originDrv.getCategorySynthesizeCode(),drvDriverPO.getCategorySynthesizeCode()) && !Objects.equals(TmsTransportConstant.DrvStatusEnum.OFFLINE.getCode(),originDrv.getDrvStatus())){
                return this.resultErrorInfo(SharkUtils.getSharkValue(SharkKeyConstant.drvWarning1),String.valueOf(drvDriverPO.getDrvId()),0L);
            }

            // 判断是否有灰度中的供应商是否新增了包车产线
            if( (productionLineUtil.isProductLineCodeNewAddDayCheck(originDrv.getCategorySynthesizeCode(),drvDriverPO.getCategorySynthesizeCode()) || productionLineUtil.isOnlyDayProductLine(drvDriverPO.getCategorySynthesizeCode()))
              && driverGuidProxy.getGrayControl(originDrv.getSupplierId())) {
                return this.resultErrorInfo(ErrorCodeEnum.TRANSPORT_DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID.getCode(), SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_DAY_PRODUCT_LINE_MOVED_TO_DRIVER_GUID.getMessage(), "Can not new add day"),String.valueOf(drvDriverPO.getDrvId()),0L);
            }

            // 判断供应商，城市两个字段是否有变更,如果有变更，看一下供应商能否在这个城市服务，且产线也满足需求
            boolean supplierChange = !checkEquality(drvDriverPO.getSupplierId(), originDrv.getSupplierId());
            boolean cityChange = !checkEquality(drvDriverPO.getCityId(), originDrv.getCityId());
            boolean productLineChange = !Objects.equals(drvDriverPO.getCategorySynthesizeCode(), originDrv.getCategorySynthesizeCode());
            if (supplierChange || cityChange || productLineChange) {
                List<CategorySOADTO> contractList = queryCategoryService.getContractList(drvDriverPO.getSupplierId(),
                  Lists.newArrayList(drvDriverPO.getCityId()));
                List<Integer> showProductionLineList =
                  productionLineUtil.getShowProductionLineList(drvDriverPO.getCategorySynthesizeCode());
                if (CollectionUtils.isEmpty(contractList) || !new HashSet<>(
                  contractList.stream().map(CategorySOADTO::getId).collect(Collectors.toList())).containsAll(showProductionLineList)) {
                    logger.info(TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH.getMessage(), "{},{}", showProductionLineList, contractList);
                    return this.resultErrorInfo(ErrorCodeEnum.TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH.getCode(), SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_SUPPLIER_CITY_CATEGORY_NOT_MATCH.getMessage(), "Supplier city, category not match"),String.valueOf(drvDriverPO.getDrvId()),0L);
                }
            }

            //临派司机产线单独计算-获取产线-供应商+城市下所有已上线的运力组的产线并集
//            if(Objects.equals(originDrv.getTemporaryDispatchMark(), TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode())){
//               Result<Integer> proLineList = driverQueryService.queryTemporaryProLine(originDrv.getSupplierId(),originDrv.getCityId());
//               if(!proLineList.isSuccess()){
//                   return this.resultErrorInfo(proLineList.getMsg(),String.valueOf(drvDriverPO.getDrvId()),0L);
//               }
//                drvDriverPO.setCategorySynthesizeCode(proLineList.getData());
//            }
            boolean changeHeadPortrait = Objects.equals(drvDriverPO.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode()) && !Objects.equals(drvDriverPO.getDrvHeadImg(), originDrv.getDrvHeadImg());
            boolean domesticFlag =  Objects.equals(drvDriverPO.getInternalScope(), AreaScopeTypeEnum.DOMESTIC.getCode());
            //境外数据是否进审批
            boolean overseasExecuteEditApprove = overseasExecuteEditApprove(drvDriverPO.getSupplierId(),drvDriverPO.getInternalScope(),ocrPassStatusList);
            //临派司机
            Boolean temporaryDispatchMarkFlag = Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(),originDrv.getTemporaryDispatchMark());
            //只有境内司机进编辑审核
            if(domesticFlag || overseasExecuteEditApprove || temporaryDispatchMarkFlag){
                //当某字段在审核过程中时，即该字段修改事件审核状态为【待审核】时，该字段不可进行编辑。（即字段在审批流程中时，不可进行编辑修改操作）
                if (approveCommandService.checkColumnApproveIng(AddApproveDTO.buildcheckColumnApproveIngDTO(drvDriverPO.getDrvId(), drvDriverPO, TmsTransportConstant.ApproveSourceTypeEnum.DRV, domesticFlag,
                    TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate.getCode(), ocrPassStatusList, temporaryDispatchMarkFlag, false, 0, drvDriverPO.getCityId()))) {
                    return this.resultErrorInfo(SharkUtils.getSharkValue(SharkKeyConstant.transportApproveIng), String.valueOf(drvDriverPO.getDrvId()), 0L);
                }
                if (changeHeadPortrait) {
                    if (approveRepository.queryApproveCountByParams(drvDriverPO.getDrvId(), TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(), TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode(), TmsTransportConstant.EnentTypeEnum.headPortraitCompliance.getCode()) > 0) {
                        return this.resultErrorInfo(SharkUtils.getSharkValue(SharkKeyConstant.transportApproveIng),String.valueOf(drvDriverPO.getDrvId()),0L);
                    }
                }
            }
            Result<Long> compareAttributeResult = compareAttribute(drvDriverPO,originDrv);
            if(!compareAttributeResult.isSuccess()){
                return Result.Builder.<Long>newResult().fail().withCode(compareAttributeResult.getCode()).withMsg(compareAttributeResult.getMsg()).build();
            }
            Long newVehicleId = drvDriverPO.getVehicleId();
            Long originVehicleId = originDrv.getVehicleId();
            boolean vehcileTypeChange = Boolean.FALSE;
            Result<String> bindLimitResult = null;
            if (!checkEquality(newVehicleId, originVehicleId)) {
                boolean newVehicleIdValid = checkValid(newVehicleId);
                if (newVehicleIdValid) {
                    VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(newVehicleId);
                    if (checkVehicleValid(vehVehiclePO, drvDriverPO)) {
                        return this.resultErrorInfo(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleIsEmptyOrOccupied),drvDriverPO.getVehicleLicense(),0L);
                    }
                    Result<String> changeLimitResult = changeLimitCheck(drvDriverPO, originDrv.getCategorySynthesizeCode(), vehVehiclePO);
                    if (!changeLimitResult.isSuccess()) {
                        return this.resultErrorInfo(changeLimitResult.getMsg(), null, 0L);
                    }

                    if (BooleanUtils.isTrue(qconfig.getCheckBindFlag())){
                        bindLimitResult = productionLineUtil.checkBind(drvDriverPO.getDrvId(),drvDriverPO.getCategorySynthesizeCode(), vehVehiclePO.getCategorySynthesizeCode());
                    }else {
                        bindLimitResult = productionLineUtil.bindTransportCheck(originDrv.getCategorySynthesizeCode(), vehVehiclePO.getCategorySynthesizeCode());
                    }
                    drvDriverPO.setVehicleTypeId(vehVehiclePO.getVehicleTypeId());
                    drvDriverPO.setVehicleLicense(vehVehiclePO.getVehicleLicense());
                } else {
                    Result<String> unbindLimitResult = unbindVehicleCheck(drvDriverPO.getDrvId());
                    if (!unbindLimitResult.isSuccess()) {
                        return this.resultErrorInfo(unbindLimitResult.getMsg(), null,0L);
                    }
                    // 从有车变到没车
                    drvDriverPO.setVehicleLicense("");
                    drvDriverPO.setVehicleTypeId(0L);
                    drvDriverPO.setVehicleId(0L);
                }
                //若车型变更,则将已关联“全职司机指派-报名制”运力组解绑,司机添加车辆不解绑运力组
                if(originVehicleId!=null && originVehicleId > 0){
                    vehcileTypeChange = !checkEquality(drvDriverPO.getVehicleTypeId(), originDrv.getVehicleTypeId());
                }
            }
            if(domesticFlag || overseasExecuteEditApprove || temporaryDispatchMarkFlag ){
                updateApproveMethod(drvDriverPO,originDrv,domesticFlag,changeHeadPortrait,ocrHeadPortraitResult,ocrPassStatusList,overseasExecuteEditApprove,temporaryDispatchMarkFlag);
            }
            boolean isChangeNewCar = !checkEquality(originVehicleId, newVehicleId);
            if (isChangeNewCar) {
                drvDriverPO.setVehBindTime(new Timestamp(System.currentTimeMillis()));
            }
            if(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),drvDriverPO.getInternalScope())){
                drvDriverPO.setDrvHeadImg(StringUtils.isEmpty(drvDriverPO.getDrvHeadImg())?qconfig.getOverseasDefaultDrvHead():drvDriverPO.getDrvHeadImg());
            }
            if(CollectionUtils.isNotEmpty(ocrPassStatusList)){
                drvDriverPO.setOcrPassStatusJson(JsonUtil.toJson(ocrPassStatusList));
            }
            //临派司机转为正式司机
            //如果是临派司机并且OCR通过，并且驾驶证不为空，并且驾驶证未进编辑审核
//            if(temporaryDispatchMarkFlag && overseasOCRCheck(ocrPassStatusList,TmsTransportConstant.OverseasOCRPassStatusEnum.pass) && StringUtils.isNotEmpty(originDrv.getDrvcardImg()) && StringUtils.equals(drvDriverPO.getDrvcardImg(),originDrv.getDrvcardImg())){
//                drvDriverPO.setTemporaryDispatchMark(TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode());
//            }

            int count = drvDrvierRepository.updateDrv(drvDriverPO);
            if (count > 0) {
                drvDriverPO.setUid(originDrv.getUid());
                updateDrvSuccessFollow(drvDriverPO,isChangeNewCar,newVehicleId,originVehicleId,originDrv,vehcileTypeChange);
            }
            if (bindLimitResult != null && !bindLimitResult.isSuccess()) {
                return Result.Builder.<Long>newResult().success().withData(originDrv.getDrvId()).withMsg(bindLimitResult.getMsg()).build();
            }
            return Result.Builder.<Long>newResult().success().withData(originDrv.getDrvId()).build();
        } catch (Exception e) {
            MetricsUtils.drvUpdateExceptionTimes(ApiTypeEnum.UPDATE_DRV,drvDriverPO.getDrvId());
            if (e instanceof BizException) {
                throw (BizException)e;
            }
            throw new BaijiRuntimeException(e);
        }finally {
            MetricsUtils.drvUpdateConnectTimes(ApiTypeEnum.UPDATE_DRV,drvDriverPO.getDrvId());
        }
    }

    //唯一值校验
    public Result<Long> compareAttribute(DrvDriverPO drvDriverPO,DrvDriverPO originDrv){
        final String template = "【%s】%s";
        try {
            //判断手机号唯一
            if (!Strings.isNullOrEmpty(drvDriverPO.getDrvPhone()) && !Objects.equals(drvDriverPO.getDrvPhone(),originDrv.getDrvPhone()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvPhone(), TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode())) {
                return this.resultErrorInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), SharkUtils.getSharkValue(SharkKeyConstant.mobilePhoneUsedChangeForRegistration)),String.valueOf(drvDriverPO.getDrvId()),0L);
            }
            //判断手机号带0/00唯一
            if (drvDrvierRepository.checkDrvPhoneOnly(drvDriverPO.getDrvPhone(), originDrv.getDrvId())) {
                Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "updateDrv");
                return this.resultErrorInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getDrvPhone(), KeyType.Phone), SharkUtils.getSharkValue(SharkKeyConstant.mobilePhoneUsedChangeForRegistration)), String.valueOf(drvDriverPO.getDrvId()), 0L);
            }
            //判断邮箱唯一
            if (!Strings.isNullOrEmpty(drvDriverPO.getEmail()) && !Objects.equals(drvDriverPO.getEmail(),originDrv.getEmail()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getEmail(), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
                return this.resultErrorInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getEmail(),KeyType.Mail), SharkUtils.getSharkValue(SharkKeyConstant.mailboxUsedChangeForRegistration)),String.valueOf(drvDriverPO.getDrvId()),0L);
            }
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), drvDriverPO.getInternalScope())) {
                //身份证唯一
                if (!Strings.isNullOrEmpty(drvDriverPO.getDrvIdcard()) && !Objects.equals(drvDriverPO.getDrvIdcard(),originDrv.getDrvIdcard()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getDrvIdcard(), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
                    return this.resultErrorInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getDrvIdcard(),KeyType.Identity_Card), SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists)),String.valueOf(drvDriverPO.getDrvId()),0L);
                }
                // 姓名和驾驶证姓名不一致
                if (StringUtils.isNotBlank(drvDriverPO.getDrvLicenseName()) && !Objects.equals(drvDriverPO.getDrvName(),drvDriverPO.getDrvLicenseName())) {
                    return this.resultErrorInfo(TRANSPORT_DRIVER_NAME_NOT_MATCH_LICENSE_NAME.getCode(),SharkUtils.getSharkValue(TRANSPORT_DRIVER_NAME_NOT_MATCH_LICENSE_NAME.getMessage(),"Driver name not match license name"), String.valueOf(drvDriverPO.getDrvId()), 0L);
                }
            } else {
                //账号唯一校验
                if (!Strings.isNullOrEmpty(drvDriverPO.getLoginAccount()) && !Objects.equals(drvDriverPO.getLoginAccount(),originDrv.getLoginAccount()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getLoginAccount(), TmsTransportConstant.DrvOnlyTypeEnum.LOGIN_ACCOUNT.getCode())) {
                    return this.resultErrorInfo(String.format(template, drvDriverPO.getLoginAccount(), SharkUtils.getSharkValue(SharkKeyConstant.transportAccountAlreadyExists)),String.valueOf(drvDriverPO.getDrvId()),0L);
                }
                //派安盈账户唯一校验
                if (!Strings.isNullOrEmpty(drvDriverPO.getPaiayAccount()) && !Objects.equals(drvDriverPO.getPaiayAccount(),originDrv.getPaiayAccount()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getPaiayAccount(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_ACCOUNT.getCode())) {
                    return this.resultErrorInfo(String.format(template, drvDriverPO.getPaiayAccount(), SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST)),String.valueOf(drvDriverPO.getDrvId()),0L);
                }
                //派安盈邮箱唯一校验
                if (!Strings.isNullOrEmpty(drvDriverPO.getPaiayEmail()) && !Objects.equals(drvDriverPO.getPaiayEmail(),originDrv.getPaiayEmail()) && drvDrvierRepository.checkDrvOnly(drvDriverPO.getPaiayEmail(), TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_EMAIL.getCode())) {
                    return this.resultErrorInfo(String.format(template, TmsTransUtil.decrypt(drvDriverPO.getPaiayEmail(),KeyType.Mail), SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST)),String.valueOf(drvDriverPO.getDrvId()),0L);
                }
            }
            if (!driverQueryService.checkWorkPeriod(drvDriverPO.getWorkPeriod())){
                return Result.Builder.<Long>newResult().fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(String.format(template, drvDriverPO.getWorkPeriod(), SharkUtils.getSharkValue(SharkKeyConstant.transportDriverWorkPeriodOverlap)))
                        .build();
            }
            return Result.Builder.<Long>newResult().success().withData(0L).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //插入编辑审批数据
    public Boolean updateApproveMethod(DrvDriverPO drvDriverPO, DrvDriverPO originDrv, Boolean domesticFlag, Boolean changeHeadPortrait, Boolean ocrHeadPortraitResult, List<OcrPassStatusModelSOA> ocrPassStatusList,Boolean overseasExecuteEditApprove,Boolean temporaryDispatchMarkFlag) {
        try {
            //部分字段进审批流
            Result<Long> result = approveCommandService.insertApprove(
                AddApproveDTO.buildAddDTO(drvDriverPO.getDrvId(), drvDriverPO.getSupplierId(), drvDriverPO, String.valueOf(drvDriverPO.getDrvId()), TmsTransportConstant.ApproveSourceTypeEnum.DRV,
                    changeRecordAttributeNameQconfig.getDriverRecordMap(), drvDriverPO.getModifyUser(), null, domesticFlag, ocrPassStatusList, temporaryDispatchMarkFlag, false, 0, drvDriverPO.getCityId()),
                TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate, drvDriverPO.getCityId());
            if (result.isSuccess() && result.getData() > 0) {
                //异步调用审批流中证件核验信息
                tmsQmqProducerCommandService.sendApproveCertificateCheckQmq(result.getData(), TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(), JsonUtil.toJson(originDrv), JsonUtil.toJson(drvDriverPO), null, null);
                drvDriverPO.setDrvcardImg(originDrv.getDrvcardImg());
                drvDriverPO.setNetVehiclePeoImg(originDrv.getNetVehiclePeoImg());
                //在原逻辑中兼容新逻辑，如果是境外临派司机并且OCR识别不通过，则姓名进编辑审核
                if ((!domesticFlag && overseasExecuteEditApprove) || (!domesticFlag && temporaryDispatchMarkFlag && overseasOCRCheck(ocrPassStatusList,TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass))) {
                    drvDriverPO.setDrvName(originDrv.getDrvName());
                }
            }
            if (changeHeadPortrait) {
                insertHeadPortraitApprove(drvDriverPO, originDrv, ocrHeadPortraitResult);
                drvDriverPO.setDrvHeadImg(originDrv.getDrvHeadImg());
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    //编辑司机成功后续操作
    public Boolean updateDrvSuccessFollow(DrvDriverPO drvDriverPO,boolean isChangeNewCar,Long newVehicleId,Long originVehicleId,DrvDriverPO originDrv,boolean vehcileTypeChange){
        try {
            //修改证件状态
            if(CollectionUtils.isNotEmpty(drvDriverPO.getCheckStatusList())){
                for(UpdateCertificateStatusSOADTO statusSOADTO : drvDriverPO.getCheckStatusList()){
                    checkRepository.updateCheckStatus(statusSOADTO.getId(),statusSOADTO.getCheckStatus());
                }
            }
            boolean newVehicleChange = false;
            if (isChangeNewCar) {
                tmsQmqProducerCommandService.sendDrvVehicleChangeQmq(drvDriverPO.getDrvId(), newVehicleId);
                //司机修改车辆,将新车辆致为绑定
                if (checkValid(newVehicleId)) {
                    vehicleRepository.updateVehicleHasDrv(newVehicleId, TmsTransportConstant.VehicleHasDrvEnum.IS_HASDRV.getCode(), drvDriverPO.getModifyUser());
                }
                //司机修改车辆,将原车辆致为解绑
                if (checkValid(originVehicleId)) {
                    vehicleRepository.updateVehicleHasDrv(originVehicleId, TmsTransportConstant.VehicleHasDrvEnum.NO_HASDRV.getCode(), drvDriverPO.getModifyUser());
                }
                //编辑司机,去掉车牌号,需要解绑运力组,更改供应商信息
                if (newVehicleId == null || newVehicleId == 0) {
                    newVehicleChange = true;
                }
            }
            boolean supplierChange = !checkEquality(drvDriverPO.getSupplierId(), originDrv.getSupplierId());
            boolean cityChange = !checkEquality(drvDriverPO.getCityId(), originDrv.getCityId());
            boolean accountChange = false;
            if (!Strings.isNullOrEmpty(originDrv.getLoginAccount()) &&
                    !Strings.isNullOrEmpty(drvDriverPO.getLoginAccount()) &&
                    !originDrv.getLoginAccount().equals(drvDriverPO.getLoginAccount())) {
                accountChange = true;
            }
            if (supplierChange) {
                tmsQmqProducerCommandService.sendDrvSupplierIdChangeQmq(drvDriverPO.getDrvId(), drvDriverPO.getSupplierId());
            }
            if (cityChange) {
                tmsQmqProducerCommandService.sendDrvCityIdChangeQmq(drvDriverPO.getDrvId(), drvDriverPO.getCityId(), drvDriverPO.getSupplierId());
            }
            if (accountChange) {
                tmsQmqProducerCommandService.sendDrvLoginAccountChangeQmq(drvDriverPO.getDrvId(), drvDriverPO.getLoginAccount());
            }
            //车型变更、司机解绑车辆，会解绑合作模式为“全职司机指派、兼职司机播报、全职报名制“ 运力组
            //同时更换供应商或城市，不管有没有换车，运力组都全部解绑
            boolean flag = supplierChange || cityChange || vehcileTypeChange || newVehicleChange;
            if (flag) {
                Boolean allIn = Boolean.FALSE;
                if(supplierChange || cityChange){
                    allIn = Boolean.TRUE;
                }
                //境外派遣逻辑
                if(supplierChange && Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),originDrv.getInternalScope())){
                    //解绑该司机所属供应商的运力组
                    transportGroupCommandService.drvDispatchunBoundTransport(drvDriverPO.getDrvId(),drvDriverPO.getModifyUser(),originDrv.getSupplierId());
                    //解绑派遣关系
                    drvDispatchRelationRepository.operationDrvDispatchUnBing(drvDriverPO.getDrvId(),drvDriverPO.getSupplierId(),SessionHolder.getRestSessionAccountName());
                }else{
                    //司机下线解绑运力组
                    transportGroupCommandService.unBoundTransport(Arrays.asList(drvDriverPO.getDrvId()), drvDriverPO.getModifyUser(),allIn);
                }
            }

            // 更新用户公共中心的账号信息
            driverAccountManagementHelper.updateAccount(drvDriverPO);
            //qmq
            tmsQmqProducerCommandService.sendDrvChangeQmq(originDrv.getDrvId(), 2,1);
            ///司机入驻携程大学
            tmsQmqProducerCommandService.sendPushDataToCtripUniversityQmq(drvDriverPO.getDrvId());
            //司机从临派到正式 发qmq
//            if(Objects.equals(originDrv.getTemporaryDispatchMark(), TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode()) &&
//                    Objects.equals(drvDriverPO.getTemporaryDispatchMark(), TmsTransportConstant.TemporaryDispatchMarkEnum.OFFICIAL.getCode())){
//                //临派转正式的逻辑
//                temToOfficialSendQmq(drvDriverPO.getDrvId(),drvDriverPO.getDrvPhone(),drvDriverPO.getModifyUser());
//
//            }
           sendRealNameAuth(drvDriverPO, originDrv);

            return Boolean.TRUE;
        }catch (Exception e){
            if (e instanceof BizException) {
                throw (BizException)e;
            }
            throw new BaijiRuntimeException(e);
        }
    }

    protected void sendRealNameAuth(DrvDriverPO drvDriverPO, DrvDriverPO originDrv) {
        try {
            boolean phoneChange = !StringUtils.equals(drvDriverPO.getDrvPhone(), originDrv.getDrvPhone());
            boolean nameChange = !StringUtils.equals(drvDriverPO.getDrvName(), originDrv.getDrvName());
            boolean idCardChange = !StringUtils.equals(drvDriverPO.getIdcardImg(), originDrv.getIdcardImg());
            boolean idBackCardChange = !StringUtils.equals(drvDriverPO.getIdcardBackImg(), originDrv.getIdcardBackImg());
            boolean scenePhotoChange = !StringUtils.equals(drvDriverPO.getScenePhoto(), originDrv.getScenePhoto());
            boolean drvIdCardChange = !StringUtils.equals(drvDriverPO.getDrvIdcard(), originDrv.getDrvIdcard());
            boolean change = phoneChange || nameChange || idCardChange || idBackCardChange || scenePhotoChange || drvIdCardChange;
            if (change) {
                tmsQmqProducerCommandService.sendRealNameAuth(Collections.singletonList(drvDriverPO.getDrvId()));
            }
        } catch (Exception e) {
            logger.warn("sendRealNameAuth error", e);
        }
    }

    public void insertHeadPortraitApprove(DrvDriverPO drvDriverPO, DrvDriverPO originDrv, Boolean ocrHeadPortraitResult) throws SQLException {
      TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
      approvePO.setEventType(TmsTransportConstant.EnentTypeEnum.headPortraitCompliance.getCode());
      approvePO.setApproveName(drvDriverPO.getDrvId() + "_" + SharkKeyConstant.keyDrvHeadPortraitChange);
      approvePO.setSupplierId(drvDriverPO.getSupplierId());
      approvePO.setApproveSourceId(drvDriverPO.getDrvId());
      approvePO.setApproveSourceType(TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode());
      approvePO.setApproveFrom(StringUtils.isEmpty(SessionHolder.getSessionSource().get("accountType")) ? Integer.valueOf(1) : Integer.valueOf(SessionHolder.getSessionSource().get("accountType")));
      approvePO.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode());
      List<ApproveContentSOADTO> contentSOADTOList = Lists.newArrayListWithExpectedSize(2);
      ApproveContentSOADTO urlDTO = new ApproveContentSOADTO();
      urlDTO.setChangeItem("drvHeadPortraitImg");
      urlDTO.setOriginalValue(originDrv.getDrvHeadImg());
      urlDTO.setChangeValue(drvDriverPO.getDrvHeadImg());
      contentSOADTOList.add(urlDTO);
      if (ocrHeadPortraitResult != null) {
          ApproveContentSOADTO ocrDTO = new ApproveContentSOADTO();
          ocrDTO.setChangeValue(String.valueOf(ocrHeadPortraitResult));
          approvePO.setCertificateCheckResult(ocrDTO.getChangeValue());
          contentSOADTOList.add(ocrDTO);
      } else {
          approvePO.setCertificateCheckResult(String.valueOf(Boolean.FALSE));
      }
      approvePO.setApproveContent(JsonUtil.toJson(contentSOADTOList));
      approvePO.setCreateUser(SessionHolder.getSessionSource().getOrDefault("accountName",Constant.SYSTEM));
      approvePO.setModifyUser(approvePO.getCreateUser());
      approvePO.setAccountId(MapUtils.isEmpty(SessionHolder.getSessionSource()) ? "0" : SessionHolder.getSessionSource().get("accountId"));
      approveRepository.insertTmsTransportApprovePO(approvePO);
  }

  private Result<Long> resultErrorInfo(String errorMsg, String infoBean, Long data) {
    return resultErrorInfo(ServiceResponseConstants.ResStatus.EXCEPTION_CODE, errorMsg, infoBean, data);
  }

    private Result<Long> resultErrorInfo(String code, String errorMsg, String infoBean, Long data) {
        return Result.Builder.<Long>newResult()
          .fail()
          .withCode(code)
          .withMsg(Strings.isNullOrEmpty(infoBean) ? errorMsg :"【" + infoBean + "】" + errorMsg)
          .withData(data)
          .build();
    }

  private Result<Long> resultErrorNewInfo(String errorMsg, Long data) {
    return Result.Builder.<Long>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(errorMsg)
        .withData(data).build();
  }

   private Result<Boolean> resultErrorNewInfo(String errorMsg) {
        return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(errorMsg).build();
   }

  private boolean checkEquality(Long param1, Long param2) {
    if (param1 == null) {
      param1 = 0L;
    }
    if (param2 == null) {
      param2 = 0L;
    }
    return param1.longValue() == param2.longValue();
  }

  private boolean checkValid(Long param) {
    return param != null && param > 0;
  }

  @Override
  @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
  public Result<Boolean> freezeDrv(List<Long> drvIds, Integer freezeHour, String freezeReason, String modifyUser) {
    if (drvIds.size() <= 0) {
      return Result.Builder.<Boolean>newResult()
          .fail()
          .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
          .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvIdIsZero))
          .withData(Boolean.FALSE)
          .build();
    }
    // 查询司机状态
    List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.queryDrvList(drvIds);
    List<Long> ids = Lists.newArrayList();
    for (DrvDriverPO drvDriverPO : drvDriverPOList) {
      if (TmsTransportConstant.DrvStatusEnum.ONLINE.getCode().equals(drvDriverPO.getDrvStatus())) {
        continue;
      }
      ids.add(drvDriverPO.getDrvId());
    }
    if (ids.size() > 0) {
      return Result.Builder.<Boolean>newResult()
          .fail()
          .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
          .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgDriverFreezeNoExamsAllowed))
          .withData(false)
          .build();
    }
    // 执行冻结
    int count = drvDrvierRepository.freezeDrv(drvIds, TmsTransportConstant.DrvStatusEnum.FREEZE.getCode(),
        freezeHour, freezeReason, modifyUser);
    if (count <= 0) {
      return Result.Builder.<Boolean>newResult()
          .fail()
          .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
          .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgFreezeError))
          .withData(false)
          .build();
    }
    // tmsQmqProducerCommandService.sendDrvFreezeQmq(drvIds,freezeHour);
    // 记录冻结变更历史（切换到新版变更记录流程）
    return Result.Builder.<Boolean>newResult()
        .success()
        .withData(true)
        .build();
  }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Long> addDrvOfficial(DrvDriverPO drvDriverPO) {
        try {
            //基础通用检查
            Result<Long> checkResult = commonCheckNew(drvDriverPO,false);
            if(!checkResult.isSuccess()){
               return Result.Builder.<Long>newResult().fail().withMsg(checkResult.getMsg()).build();
            }
            //工作台创建司机，进入司机审批表
            Long drvId = drvDrvierRepository.addDrv(drvDriverPO);
            tmsQmqProducerCommandService.sendDrvChangeQmq(drvId,1,1);
            //Vbk审核通过的司机，将历史临派废弃车辆置为正式废弃
            if(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),drvDriverPO.getInternalScope())){
                driverCommandService.temToOfficialSendQmq(null,drvDriverPO.getDrvPhone(),drvDriverPO.getModifyUser());
            }
            return Result.Builder.<Long>newResult().success().withData(drvId).build();
        } catch (Exception e) {
            logger.error("add driver error",e);
            if (e instanceof SQLException) {
                return Result.Builder.<Long>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists)).withData(0L).build();
            }
            return Result.Builder.<Long>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(e.getLocalizedMessage()).withData(0L).build();
        }
    }

    public DrvDriverPO toDrvPOBean(DrvAddSOARequestType addDrvRequestType){
        DrvDriverPO po = new DrvDriverPO();
        BeanUtils.copyProperties(addDrvRequestType,po);
        po.setDrvPhone(TmsTransUtil.encrypt(addDrvRequestType.getDrvPhone(),KeyType.Phone));
        po.setEmail(TmsTransUtil.encrypt(addDrvRequestType.getEmail(),KeyType.Mail));
        if(Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(),addDrvRequestType.getAreaScope())){
            po.setDrvIdcard(TmsTransUtil.encrypt(addDrvRequestType.getDrvIdcard(),KeyType.Identity_Card));
            po.setDrvLicenseNumber(TmsTransUtil.encrypt(addDrvRequestType.getDrvLicenseNumber(),KeyType.Identity_Card));
            if(org.apache.commons.lang3.StringUtils.isNotEmpty(addDrvRequestType.getCertiDate())){
                po.setCertiDate(Date.valueOf(addDrvRequestType.getCertiDate()));
            }
        }
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(addDrvRequestType.getExpiryBeginDate())){
            po.setExpiryBeginDate(Date.valueOf(addDrvRequestType.getExpiryBeginDate()));
        }
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(addDrvRequestType.getExpiryEndDate())){
            po.setExpiryEndDate(Date.valueOf(addDrvRequestType.getExpiryEndDate()));
        }

        po.setInternalScope(addDrvRequestType.getAreaScope());
        po.setDrvStatus(TmsTransportConstant.DrvStatusEnum.UNACT.getCode());
        po.setDrvFrom(TmsTransportConstant.DrvFromEnum.DRV_MANUAL.getCode());
        po.setCategorySynthesizeCode(productionLineUtil.getIntegratedLine(addDrvRequestType.getProLineList()));
        po.setCertificateConfig(addDrvRequestType.getCertificateConfigStr());
        if(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),addDrvRequestType.getAreaScope())){
            po.setDrvHeadImg(StringUtils.isEmpty(addDrvRequestType.getDrvHeadImg())?qconfig.getOverseasDefaultDrvHead():addDrvRequestType.getDrvHeadImg());
        }
        po.setPaiayEmail(TmsTransUtil.encrypt(addDrvRequestType.getPaiayEmail(),KeyType.Mail));
        po.setTaxi(addDrvRequestType.getIsTaxi());
        return po;
    }

    private RecruitingApproveSOARequestType buildRequest(DrvAddSOARequestType requestType,Long drvId){
        RecruitingApproveSOARequestType approveSOARequestType = new RecruitingApproveSOARequestType();
        RecruitingApproveSOARequestDTO requestDTO = new RecruitingApproveSOARequestDTO();
        requestDTO.setAction(requestType.getAction());
        requestDTO.setMediumIdList(Arrays.asList(drvId));
        approveSOARequestType.setData(requestDTO);
        return approveSOARequestType;
    }

    private void updateDrvOnline(List<DrvDriverPO> drvDriverPOList){
        Set<Long>  drvIdSet = Sets.newHashSet();
        try{
            drvDriverPOList.stream().forEach(drvDriverPO -> {
                if (TmsTransportConstant.DrvStatusEnum.UNACT.getCode().equals(drvDriverPO.getDrvStatus())) {
                    drvIdSet.add(drvDriverPO.getDrvId());
                }
            });
            if(CollectionUtils.isEmpty(drvIdSet)){
                return;
            }
            drvDrvierRepository.updateDrvOnlineTime(new ArrayList<>(drvIdSet));
        }catch (Exception e){
            logger.error("update date driver online time error,e= ",e);
        }
    }

    @Override
    public Boolean calculateUpdateDrvCoopMode(List<Long> drvList, String modifyUser) {
        try {
            if(CollectionUtils.isEmpty(drvList)){
                return Boolean.FALSE;
            }
            drvList = drvList.stream().distinct().collect(Collectors.toList());
            List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.queryDrvList(drvList);
            if(CollectionUtils.isEmpty(drvDriverPOList)){
                return Boolean.FALSE;
            }
            modifyUser = StringUtils.isEmpty(modifyUser)? TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser;
            //如果查询关联运力组为空,说明司机没有关联运力组，合作模式记为空
            List<TransportGroupBasePO> transportGroupBasePOList = transportGroupQueryService.queryDriverGroupRelationPO(drvList, Lists.newArrayList());
            List<DrvDriverPO> updateDrvList = Lists.newArrayList();
            if(CollectionUtils.isEmpty(transportGroupBasePOList)){
                String finalModifyUser = modifyUser;
                drvList.forEach(drvId ->{
                    updateDrvList.add(buildUpdteDrvDriverPO(drvId,TmsTransportConstant.DrvCoopModeEnum.NO.getCode(), finalModifyUser));
                });
            }else{
                List<Long> drvLists = transportGroupBasePOList.stream().map(TransportGroupBasePO::getDrvId).collect(Collectors.toList());
                List<Long> remainingDrvList = (List<Long>) CollectionUtils.subtract(drvList,drvLists);
                if(CollectionUtils.isNotEmpty(remainingDrvList)){
                    String finalModifyUser = modifyUser;
                    remainingDrvList.forEach(drvId ->{
                        updateDrvList.add(buildUpdteDrvDriverPO(drvId,TmsTransportConstant.DrvCoopModeEnum.NO.getCode(), finalModifyUser));
                    });
                }
            }
            Map<Long,List<TransportGroupBasePO>> basePOList = transportGroupBasePOList.stream().collect(Collectors.groupingBy(TransportGroupBasePO::getDrvId));
            for(Map.Entry<Long,List<TransportGroupBasePO>> entry :basePOList.entrySet()){
                DrvInfoCacheDto drvInfoCacheDto = driverQueryService.calculateDrvCoopMode(entry.getKey(),entry.getValue());
                updateDrvList.add(buildUpdteDrvDriverPO(entry.getKey(),drvInfoCacheDto.getCoopMode(), modifyUser));
            }
            if(CollectionUtils.isEmpty(updateDrvList)){
                return Boolean.FALSE;
            }
            int count = drvDrvierRepository.batchUpdateDrv(updateDrvList);
            if(count > 0){
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }catch (Exception e){
           throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public int insertDrvLoginInfo(TmsDrvLoginInformationPO informationPO) {
        return informationRepository.insert(informationPO);
    }

    @Override
    public Result<Boolean> drvHealthPunchAdd(DrvHealthPunchAddRequestType requestType) {
        DrvHealthPunchPO drvHealthPunchPO = new DrvHealthPunchPO();
        try {
            BeanUtils.copyProperties(requestType, drvHealthPunchPO);
            drvHealthPunchPO.setShowPlaceQRCode(requestType.isIsShowPlaceQRCode());
            drvHealthPunchRepository.insert(drvHealthPunchPO);
            return Result.Builder.<Boolean>newResult().success().build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }


    @Override
    public Result<Boolean> drvDispatchRelationUpdate(DrvDispatchRelationUpdateSOARequestType requestType) {
        try {

            //只限于运营操作
            if(checkOperationRule(TmsTransportConstant.AccountTypeEnum.B_SYSTEM)){
                return Result.Builder.<Boolean>newResult().fail().withCode("403").build();
            }

            Long drvId = requestType.getDrvId();
            Long supplierId = requestType.getSupplierId();
            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
            if (Objects.isNull(drvDriverPO)) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty)).build();
            }

            //临时派遣司机不能绑定派遣关系
            if(Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(),drvDriverPO.getTemporaryDispatchMark())){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.dispatchdriverrelationship)).build();
            }
            //判断当前司机是否灰度
            Result<Boolean>  dispatchCityGray =  driverQueryService.drvDispatchCityGray(drvDriverPO.getCityId());
            if(dispatchCityGray.isSuccess() && !dispatchCityGray.getData()){
                return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100024).build();
            }
            //绑定派遣
            if(Objects.equals(TmsTransportConstant.DrvDispatchOperationTypeEnum.binding.getCode(),requestType.getOperationType())){
                //判断当前绑定的供应商是否 关联关系
                if(Objects.equals(supplierId,drvDriverPO.getSupplierId())){
                    return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100025).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.drvDispatchAlreadyBelongTo)).build();
                }
                //判断之前是否绑定过
                List<DrvDispatchRelationPO> dispatchRelationPOList = drvDispatchRelationRepository.queryDrvDisPatchRecord(drvId, supplierId,Boolean.TRUE);
                if(CollectionUtils.isNotEmpty(dispatchRelationPOList)){
                    return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100021).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.drvDispatchHaveBeenBound)).build();
                }
                //添加派遣
                insertDrvDispatch(drvId,supplierId);
            }
            //解绑派遣
            if(Objects.equals(TmsTransportConstant.DrvDispatchOperationTypeEnum.unbind.getCode(),requestType.getOperationType())){
                //判断之前是否绑定过
                List<DrvDispatchRelationPO> dispatchRelationPOList = drvDispatchRelationRepository.queryDrvDisPatchRecord(requestType.getDrvId(), requestType.getSupplierId(),Boolean.TRUE);
                if(CollectionUtils.isEmpty(dispatchRelationPOList)){
                    return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100022).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.drvDispatchUnbind)).build();
                }
                //操作解绑派遣
                int count = drvDispatchRelationRepository.operationDrvDispatchUnBing(drvId,supplierId,SessionHolder.getRestSessionAccountName());
                if(count > 0){
                    //司机派遣-解绑运力力组
                    transportGroupCommandService.drvDispatchunBoundTransport(drvId,SessionHolder.getRestSessionAccountName(),supplierId);
                }
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Boolean> vbkDrvDispatchBinding(Long drvId, Long supplierId) {
        try {
            //只限于VBK操作
            if(checkOperationRule(TmsTransportConstant.AccountTypeEnum.OFFLINE)){
                return Result.Builder.<Boolean>newResult().fail().withCode("403").build();
            }
            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
            if (Objects.isNull(drvDriverPO)) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryDataIsEmpty)).build();
            }
            //此功能只限于境外司机
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), drvDriverPO.getInternalScope())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.drvDispatchRestrictionOverseas)).build();
            }
            //临时派遣司机不能绑定派遣关系
            if(Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(),drvDriverPO.getTemporaryDispatchMark())){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.dispatchdriverrelationship)).build();
            }
            //供应商关联司机时，需要判断之前是否与该司机有过派遣关系，则不能绑定，弹窗提示供应商：该司机与您有过派遣关系绑定，如需重新绑定，请联系平台操作，谢谢。
            List<DrvDispatchRelationPO> dispatchRelationPOList = drvDispatchRelationRepository.queryDrvDisPatchRecord(drvId, supplierId,null);
            if(CollectionUtils.isNotEmpty(dispatchRelationPOList)){
                if (dispatchRelationPOList.get(0).getActive()) {
                    return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100026).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.drvDispatchHaveBeenBound)).build();
                }else {
                    return Result.Builder.<Boolean>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100027).build();
                }
            }

            insertDrvDispatch(drvId,supplierId);
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Long> temporaryDispatchDrvAdd(TemporaryDispatchDrvAddSOARequestType requestType) {
        try {
            String drvEncryptPhone = TmsTransUtil.encrypt(requestType.getDrvPhone(),KeyType.Phone);

            // check mobile
            Result<Boolean> siMobileValid = mobileHelper.isMobileValid(requestType.getIgtCode(), requestType.getDrvPhone(),
              requestType.getCityId());
            if(!siMobileValid.isSuccess()){
                return Result.Builder.<Long>newResult().fail()
                  .withCode(siMobileValid.getCode())
                  .withMsg(siMobileValid.getMsg())
                  .withData(null)
                  .build();
            }

            //判断手机号是否唯一
            Result<Boolean> phoneUniqueness = checkPhoneUniqueness(drvEncryptPhone);
            if(!phoneUniqueness.isSuccess()){
                return Result.Builder.<Long>newResult().fail().withCode(phoneUniqueness.getCode()).build();
            }
            //获取产线-供应商+城市下所有已上线的运力组的产线并集
            List<TspTransportGroupPO> transportGroupPOList = transportGroupRepository.queryGroupBySupplierAndCity(Arrays.asList(requestType.getSupplierId()),Arrays.asList(requestType.getCityId()));
            if(CollectionUtils.isEmpty(transportGroupPOList)){
                return Result.Builder.<Long>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.capacitygroupTemporary)).build();
            }
            transportGroupPOList = supplierDayProductLineMigrationHelper.filterDayProductLineIfSupplier(requestType.getSupplierId(),transportGroupPOList);
            List<Long> transportIds = transportGroupPOList.stream().map(TspTransportGroupPO::getTransportGroupId).collect(Collectors.toList());
            //运力组中的产线
            Set<Integer> proLineList = transportGroupPOList.stream().map(TspTransportGroupPO::getCategorySynthesizeCode).collect(Collectors.toSet());
            Timestamp dispatchTime = DateUtil.nowDatePlusHours(overseasQconfig.getTemporaryDispatchEndDatetimeConfig());
            //插入临派司机信息
            Long drvId = insertDispatchDrv(requestType.getSupplierId(),requestType.getCityId(),requestType.getDrvName(),drvEncryptPhone,requestType.getIgtCode(),proLineList,dispatchTime,transportIds);
            if(drvId !=null && drvId > 0){
                tmsQmqProducerCommandService.sendTemporaryReplenishInfo(drvId,TmsTransportConstant.AutoDiscardTemporaryTypeEnum.DRV.getCode(),null);
            }
            return Result.Builder.<Long>newResult().success().withData(drvId).build();
        }catch (Exception e){
            if (e instanceof BizException) {
                throw e;
            }
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<TemporaryDrvVehAddSOAResponseType> temporaryDrvVehAdd(TemporaryDrvVehAddSOARequestType requestType) {
        TemporaryDrvVehAddSOAResponseType responseType = new TemporaryDrvVehAddSOAResponseType();
        try {
            String drvEncryptPhone = TmsTransUtil.encrypt(requestType.getDrvPhone(),KeyType.Phone);

            // check mobile
            Result<Boolean> siMobileValid = mobileHelper.isMobileValid(requestType.getIgtCode(), requestType.getDrvPhone(),
              requestType.getCityId());
            if(!siMobileValid.isSuccess()){
                return Result.Builder.<TemporaryDrvVehAddSOAResponseType>newResult().fail()
                  .withCode(siMobileValid.getCode())
                  .withMsg(siMobileValid.getMsg())
                  .withData(null)
                  .build();
            }

            //判断手机号和车牌号是否唯一
            Result<Boolean> uniqueness = checkUniqueness(drvEncryptPhone,requestType.getVehicleLicense());
            if(!uniqueness.isSuccess()){
                return Result.Builder.<TemporaryDrvVehAddSOAResponseType>newResult().fail().withCode(uniqueness.getCode()).build();
            }
            //获取产线-供应商+城市下所有已上线的运力组的产线并集
            List<TspTransportGroupPO> transportGroupPOList = transportGroupRepository.queryGroupBySupplierAndCity(Arrays.asList(requestType.getSupplierId()),Arrays.asList(requestType.getCityId()));
            if(CollectionUtils.isEmpty(transportGroupPOList)){
                return Result.Builder.<TemporaryDrvVehAddSOAResponseType>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.capacitygroupTemporary)).build();
            }
            transportGroupPOList = supplierDayProductLineMigrationHelper.filterDayProductLineIfSupplier(requestType.getSupplierId(),transportGroupPOList);
            List<Long> transportIds = transportGroupPOList.stream().map(TspTransportGroupPO::getTransportGroupId).collect(Collectors.toList());
            //运力组中的产线
            Set<Integer> proLineList = transportGroupPOList.stream().map(TspTransportGroupPO::getCategorySynthesizeCode).collect(Collectors.toSet());

            Timestamp dispatchTime = DateUtil.nowDatePlusHours(overseasQconfig.getTemporaryDispatchEndDatetimeConfig());
            VehVehiclePO vehVehiclePO = new VehVehiclePO();
            BeanUtils.copyProperties(requestType,vehVehiclePO);
            vehVehiclePO.setCountryId(enumRepository.getCountryId(requestType.getCityId()));
            vehVehiclePO.setCountryName(enumRepository.getCountryName(enumRepository.getCountryId(requestType.getCityId())));
            vehVehiclePO.setCategorySynthesizeCode(productionLineUtil.insideProLineMerge(proLineList));
            vehVehiclePO.setVehicleStatus(TmsTransportConstant.VehStatusEnum.ONLINE.getCode());
            vehVehiclePO.setCreateUser(SessionHolder.getRestSessionAccountName());
            vehVehiclePO.setModifyUser(SessionHolder.getRestSessionAccountName());
            vehVehiclePO.setTemporaryDispatchMark(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode());
            vehVehiclePO.setTemporaryDispatchEndDatetime(dispatchTime);
            Long vehicleId = vehicleRepository.addVehicle(vehVehiclePO);
            if(vehicleId > 0){
                //插入临派司机信息
                Long drvId = insertDispatchDrv(requestType.getSupplierId(),requestType.getCityId(),requestType.getDrvName(),drvEncryptPhone,requestType.getIgtCode(),proLineList,dispatchTime,transportIds);
                responseType.setDrvId(drvId);
                responseType.setVehicleId(vehicleId);
                if(drvId !=null && drvId > 0){
                    tmsQmqProducerCommandService.sendTemporaryReplenishInfo(drvId,TmsTransportConstant.AutoDiscardTemporaryTypeEnum.DRV_VEH.getCode(),vehicleId);
                }
            }

            return Result.Builder.<TemporaryDrvVehAddSOAResponseType>newResult().success().withData(responseType).build();
        }catch (Exception e){
            if (e instanceof BizException) {
                throw e;
            }
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Boolean temToOfficialSendQmq(Long drvId, String drvPHone,String modifyUser) {
        try {
            //查询手机号对应的已被废弃的临派司机
            List<Long> discardDrvIds = drvDrvierRepository.queryDiscardTemDrv(drvPHone);
            if(drvId == null && CollectionUtils.isEmpty(discardDrvIds)){
                return Boolean.FALSE;
            }
            //将已废弃临派司机置为正式废弃司机
            drvDrvierRepository.updateTemporaryDispatchMark(discardDrvIds,Boolean.FALSE,modifyUser);
            List<Long> sendDrvIds = Lists.newArrayList();
            if(drvId !=null ){
                sendDrvIds.add(drvId);
            }
            if(CollectionUtils.isNotEmpty(discardDrvIds)){
                sendDrvIds.addAll(discardDrvIds);
            }
            //发送临派转正式qmq给采购
            tmsQmqProducerCommandService.sendTemporaryToOfficialQmq(sendDrvIds,TmsTransportConstant.AutoDiscardTemporaryTypeEnum.DRV.getCode());
            return Boolean.TRUE;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Boolean> drvAddPaiayAccount(Long drvId,String paiayAccount,String paiayEmail) {
        try {
            if(drvId == null || StringUtils.isEmpty(paiayAccount) || StringUtils.isEmpty(paiayEmail)){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.paramsFail)).build();
            }
            DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
            if(Objects.isNull(drvDriverPO)){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.queryNoData)).build();
            }
            if(StringUtils.isNotEmpty(drvDriverPO.getPaiayAccount())){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST)).build();
            }
            if(StringUtils.isNotEmpty(drvDriverPO.getPaiayEmail())){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST)).build();
            }
            paiayEmail = TmsTransUtil.encrypt(paiayEmail,KeyType.Mail);
            //todo 账户和邮箱唯一性校验
            if (drvDrvierRepository.checkDrvOnly(paiayAccount, TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_ACCOUNT.getCode())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_ACCOUNT_EXIST)).build();
            }
            if (drvDrvierRepository.checkDrvOnly(paiayEmail, TmsTransportConstant.DrvOnlyTypeEnum.PAIAY_EMAIL.getCode())) {
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.TRANSPORT_DRVDRIVER_PAIAY_EMAIL_EXIST)).build();
            }
            drvDrvierRepository.updatedDvAddPaiayAccount(drvId,paiayAccount,paiayEmail,String.format(TmsTransportConstant.TMS_DEFAULT_USERNAME_DRIVER_FORMAT,drvDriverPO.getDrvName(),drvDriverPO.getDrvId()));
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<Boolean> createDrvChangeEquipmentEvent(CreateDrvChangeEquipmentEventRequestType requestType) {

        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(requestType.getDrvId());
        if(Objects.isNull(drvDriverPO)){
            return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.VALIDATION_ERROR_CODE).withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.queryNoData)).build();
        }
        CheckDrvLoginRequestType checkDrvLoginRequestType = new CheckDrvLoginRequestType();
        checkDrvLoginRequestType.setDriverImei(requestType.getDriverImei());
        checkDrvLoginRequestType.setDriverLocCsys(requestType.getDriverLocCsys());
        checkDrvLoginRequestType.setDriverLocLat(requestType.getDriverLocLat());
        checkDrvLoginRequestType.setDriverLocLong(requestType.getDriverLocLong());
        //司机变更设置,新增未验证事件
        eventCommandService.insertConventionalEvent(checkDrvLoginRequestType,drvDriverPO);
        //同步司机登录信息
        insertDrvLoginInfo(buildLoginInfoPO(checkDrvLoginRequestType,requestType.getDrvId(),drvDriverPO.getDrvName()));
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
    }

    public TmsDrvLoginInformationPO buildLoginInfoPO(CheckDrvLoginRequestType request,Long drvId,String drvName){
        TmsDrvLoginInformationPO informationPO = new TmsDrvLoginInformationPO();
        informationPO.setDrvId(drvId);
        informationPO.setDrvLoginTime(DateUtil.getNow());
        informationPO.setDrvLocLat(request.getDriverLocLat() == null?0D:request.getDriverLocLat());
        informationPO.setDrvLocLong(request.getDriverLocLong() == null?0D:request.getDriverLocLong());
        informationPO.setDrvLocCsys(request.getDriverLocCsys());
        informationPO.setCreateUser(drvName);
        informationPO.setModifyUser(drvName);
        informationPO.setDrvImei(request.getDriverImei());
        return informationPO;
    }

    public Long insertDispatchDrv(Long supplierId,Long cityId,String drvName,String drvPhone,String igtCode,Set<Integer> proLineList,Timestamp dispatchTime,List<Long> transportIds){
        try {
            DrvDriverPO insertDrvPO = new DrvDriverPO();
            insertDrvPO.setSupplierId(supplierId);
            insertDrvPO.setDrvName(drvName);
            insertDrvPO.setDrvPhone(drvPhone);
            insertDrvPO.setIgtCode(igtCode);
            insertDrvPO.setCityId(cityId);
            insertDrvPO.setCategorySynthesizeCode(productionLineUtil.insideProLineMerge(proLineList));
            insertDrvPO.setInternalScope(AreaScopeTypeEnum.OVERSEAS.getCode());
            insertDrvPO.setCountryId(enumRepository.getCountryId(cityId));
            insertDrvPO.setCountryName(enumRepository.getCountryName(enumRepository.getCountryId(cityId)));
            insertDrvPO.setCreateUser(SessionHolder.getRestSessionAccountName());
            insertDrvPO.setModifyUser(SessionHolder.getRestSessionAccountName());
            insertDrvPO.setDrvStatus(TmsTransportConstant.DrvStatusEnum.ONLINE.getCode());
            insertDrvPO.setTemporaryDispatchMark(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode());
            insertDrvPO.setTemporaryDispatchEndDatetime(dispatchTime);
            insertDrvPO.setOnlineTime(DateUtil.nowDatePlusMinus(1));
            insertDrvPO.setRaisingPickUp(Boolean.TRUE);
            //设置初始密码(8位随机数)
            String initpwd = StringUtil.createRandomData();
            String salt = driverPasswordService.genPwdSalt();
            String encPwd = driverPasswordService.encryptPwd(initpwd, salt);
            insertDrvPO.setLoginPwd(encPwd);
            insertDrvPO.setSalt(salt);
            //账户类型 默认 境内-ppm 境外-派安盈
            insertDrvPO.setAccountType(TmsTransportConstant.PayAccountTypeEnum.PAIAY.getCode());
            Long drvId = drvDrvierRepository.addDrv(insertDrvPO);
            insertDrvPO.setDrvId(drvId);
            if(drvId > 0){
                //自动关联当前供应商+城市下的所有已上线运力组
                tspTransportGroupDriverRelationRepository.batchRelationGroup(transportIds,drvId);
                // 发送密码到供应商邮箱, 如果是灰度的城市，则不发邮件
                if (!driverAccountManagementHelper.isInCityGrayProcess(insertDrvPO)) {
                    String supplierEmail = enumRepository.getSupplierEmail(supplierId);
                    String content = buildResetPwdEmailContent(drvName,drvId,TmsTransUtil.decrypt(drvPhone,KeyType.Phone),initpwd);
                    commonCommandService.sendEmail(SharkUtils.getSharkValue(SharkKeyConstant.TemporaryMailSubject), supplierEmail, content);
                }
                //计算司机合作模式
                calculateUpdateDrvCoopMode(Arrays.asList(drvId),SessionHolder.getRestSessionAccountName());

                //注册司机账号, 如果注册司机账号异常，抛异常
                DriverAccountRegisterResultDTO driverAccountRegisterResultDTO =
                  driverAccountManagementHelper.registerDriverUserAccount(insertDrvPO);
                if(driverAccountRegisterResultDTO.isNeedRegisterAccount()) { // 如果需要注册
                    if (driverAccountRegisterResultDTO.isRegisterSuccess()) { // 如果注册成功, 更新司机账号
                        drvDrvierRepository.updateDrvUid(insertDrvPO.getDrvId(), driverAccountRegisterResultDTO.getUid(), insertDrvPO.getModifyUser(), driverAccountRegisterResultDTO.getPpmAccount(), driverAccountRegisterResultDTO.getQunarAccount());
                    }else { // 注册失败, 抛出异常，回滚
                        throw new BizException(ErrorCodeEnum.TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_FAILED.getCode(), SharkUtils.getSharkValue(ErrorCodeEnum.TRANSPORT_DRVDRIVER_CREATE_DRIVER_ACCOUNT_FAILED.getMessage()) + ":" + driverAccountRegisterResultDTO.getErrorMsg());
                    }
                }

                tmsQmqProducerCommandService.sendDrvChangeQmq(drvId,1,1);

            }
            return drvId;
        }catch (Exception e){
            if (e instanceof BizException) {
                throw (BizException)e;
            }
            throw new BaijiRuntimeException(e);
        }
    }
    private String buildResetPwdEmailContent(String name, Long drvId,String drvPhone,String initpwd) {
        return String.format(SharkUtils.getSharkValueDefault(SharkKeyConstant.TemporaryMailContent), name,drvId,drvPhone, initpwd);
    }

    //手机号是否唯一
    public Result<Boolean> checkPhoneUniqueness(String drvPhone){
        try {
            //判断手机号是否唯一
            DrvDriverPO drvDriverPO = drvDrvierRepository.drvDispatchcheckDrvOnly(drvPhone,TmsTransportConstant.DrvOnlyTypeEnum.PHONE.getCode());
            if (!Objects.isNull(drvDriverPO)) {
                return CtripCommonUtils.resultDrvUniquenessCode(drvDriverPO.getTemporaryDispatchMark());
            }
            drvDriverPO = drvDrvierRepository.drvDispatchcheckDrvPhoneOnly(drvPhone);
            if (!Objects.isNull(drvDriverPO)) {
                Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "temporaryDispatch");
                return CtripCommonUtils.resultDrvUniquenessCode(drvDriverPO.getTemporaryDispatchMark());
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //手机号车牌号是否唯一
    public Result<Boolean> checkUniqueness(String drvPhone,String vehicleLicense){
        try {
            //判断手机号是否唯一
            Result<Boolean> phoneUniqueness = checkPhoneUniqueness(drvPhone);
            if(!phoneUniqueness.isSuccess()){
                return Result.Builder.<Boolean>newResult().fail().withCode(phoneUniqueness.getCode()).build();
            }
            //判断车牌号唯一
            List<VehVehiclePO> vehVehiclePOList = vehicleRepository.queryVehByVehicleLicense(vehicleLicense);
            if(CollectionUtils.isNotEmpty(vehVehiclePOList)){
                VehVehiclePO vehVehiclePO = vehVehiclePOList.get(0);
                if(!Objects.isNull(vehVehiclePO)){
                    //查询当前重复数据是正式数据还是临派数据
                    return CtripCommonUtils.resultVehUniquenessCode(vehVehiclePO.getTemporaryDispatchMark());
                }
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public Long insertDrvDispatch(Long drvId,Long supplierId){
        try {
            DrvDispatchRelationPO relationPO = new DrvDispatchRelationPO();
            relationPO.setDrvId(drvId);
            relationPO.setSupplierId(supplierId);
            relationPO.setCreateUser(SessionHolder.getRestSessionAccountName());
            relationPO.setModifyUser(SessionHolder.getRestSessionAccountName());
            relationPO.setRelationType(TmsTransportConstant.DrvDispatchRelationTypeEnum.DISPATCH.getCode());
            return drvDispatchRelationRepository.insert(relationPO);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private DrvDriverPO buildUpdteDrvDriverPO(Long drvId,Integer coopMode,String modifyUser){
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(drvId);
        drvDriverPO.setCoopMode(coopMode);
        drvDriverPO.setModifyUser(StringUtils.isEmpty(modifyUser)? TmsTransportConstant.TMS_DEFAULT_USERNAME:modifyUser);
        return drvDriverPO;
    }

    /**
     * 司机绑定车辆时对车辆的检查措施
     */
    private boolean checkVehicleValid(VehVehiclePO vehVehiclePO, DrvDriverPO drvDriverPO) {
        if (vehVehiclePO == null || vehVehiclePO.getHasDrv()) {
            return true;
        }
        if (!checkEquality(vehVehiclePO.getSupplierId(), drvDriverPO.getSupplierId())) {
            return true;
        }
        return false;
    }

    /**
     * 换车检查
     */
    private Result<String> changeLimitCheck(DrvDriverPO drvDriverPO, Integer drvLineCode, VehVehiclePO vehVehiclePO) throws SQLException {
        List<TspTransportGroupPO> groupPOList = getTransportGroupByDriverAndMode(drvDriverPO.getDrvId(), null);
        if (CollectionUtils.isEmpty(groupPOList)) {
            return Result.Builder.<String>newResult().success().build();
        }
        groupPOList.removeIf(groupPO -> productionLineUtil.isProductLineCodeCheck(productionLineUtil.getShowProductionLineList(groupPO.getCategorySynthesizeCode()), productionLineUtil.getDAYProductLineCode()));
        if (CollectionUtils.isEmpty(groupPOList)) {
            return Result.Builder.<String>newResult().success().build();
        }
        if (BooleanUtils.isTrue(qconfig.getCheckBindFlag())){
           if (productionLineUtil.checkBind(drvDriverPO.getDrvId(),drvDriverPO.getCategorySynthesizeCode(), vehVehiclePO.getCategorySynthesizeCode()).isSuccess()){
               return Result.Builder.<String>newResult().success().build();
           }
        }else if (productionLineUtil.bindTransportCheck(drvLineCode, vehVehiclePO.getCategorySynthesizeCode()).isSuccess()) {
            return Result.Builder.<String>newResult().success().build();
        }
        String names = Joiner.on(", ").join(groupPOList.stream().map(TspTransportGroupPO::getTransportGroupName).collect(Collectors.toList()));
        return Result.Builder.<String>newResult().fail().withMsg(String.format(SharkUtils.getSharkValue(SharkKeyConstant.replaceVehicleCapacityGroup), names)).build();
    }

    /**
     * 解绑车辆检查
     */
    private Result<String> unbindVehicleCheck(Long drvId) {
        if (CollectionUtils.isEmpty(getTransportGroupByDriverAndMode(drvId, Lists.newArrayList(TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())))) {
            return Result.Builder.<String>newResult().success().build();
        }
        return Result.Builder.<String>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.untieVehicleCapacityGroup)).build();
    }

    private List<TspTransportGroupPO> getTransportGroupByDriverAndMode(Long drvId, List<Integer> modeList) {
        List<TspTransportGroupDriverRelationPO> relationPOList = tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Lists.newArrayList(drvId));
        if (CollectionUtils.isEmpty(relationPOList)) {
            return Collections.emptyList();
        }
        List<Long> transportIdList = relationPOList.stream().map(TspTransportGroupDriverRelationPO::getTransportGroupId).collect(Collectors.toList());
        return transportGroupRepository.queryDriverRelatedTransportGroupByModeList(transportIdList, modeList);
    }

    private List<Long> filtrationPenaltyDrvId(List<Long> srcDrvIdList, List<Long> penaltyFreezeDrvIdList, List<Long> penaltyOfflineDrvIdList) {
        if (CollectionUtils.isEmpty(penaltyFreezeDrvIdList) && CollectionUtils.isEmpty(penaltyOfflineDrvIdList)) {
            return srcDrvIdList;
        }
        Set<Long> srcSet = Sets.newHashSet(srcDrvIdList);
        Set<Long> freezeDrvIdSet = CollectionUtils.isEmpty(penaltyFreezeDrvIdList) ? Sets.newHashSet() : Sets.newHashSet(penaltyFreezeDrvIdList);
        Set<Long> offlineDrvIdSet = CollectionUtils.isEmpty(penaltyOfflineDrvIdList) ? Sets.newHashSet() : Sets.newHashSet(penaltyOfflineDrvIdList);
        return Lists.newArrayList(Sets.difference(Sets.difference(srcSet, offlineDrvIdSet), freezeDrvIdSet));
    }

    public Boolean checkOperationRule(TmsTransportConstant.AccountTypeEnum accountTypeEnum){
        if(StringUtils.isEmpty(SessionHolder.getRestSessionAccountType()) || Objects.equals(SessionHolder.getRestSessionAccountType(), accountTypeEnum.getValue().toString())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    //司机上线或下线时，如果当前司机是冻结状态，则示为提前解冻，记录解冻记录
    public Boolean saveDrvUnFreezeRecord(List<DrvDriverPO> drvDriverPOList,String modifyUser){
        try {
            if(CollectionUtils.isEmpty(drvDriverPOList)){
                return Boolean.TRUE;
            }
            Set<Long> drvIds = Sets.newHashSet();
            //筛选出冻结状态司机
            for(DrvDriverPO drvDriverPO : drvDriverPOList){
                if(!Objects.isNull(drvDriverPO) && Objects.equals(TmsTransportConstant.DrvStatusEnum.FREEZE.getCode(),drvDriverPO.getDrvStatus())){
                    drvIds.add(drvDriverPO.getDrvId());
                }
            }
            if(CollectionUtils.isEmpty(drvIds)){
                return Boolean.TRUE;
            }
            List<TmsDrvFreezePO> drvFreezePOList =  tmsDrvFreezeRepository.queryDrvFreezeByDrvIds(drvIds);
            if(CollectionUtils.isEmpty(drvFreezePOList)){
                return Boolean.TRUE;
            }
            for(TmsDrvFreezePO drvFreezePO : drvFreezePOList){
                if(Objects.isNull(drvFreezePO) || Objects.equals(TmsTransportConstant.FreezeStatusEnum.UNFRREZE.getValue(),drvFreezePO.getFreezeStatus())){
                    continue;
                }
                DrvFreezeRecordPO record = new DrvFreezeRecordPO();
                record.setDrvId(drvFreezePO.getDrvId());
                record.setFirstFreezeTime(drvFreezePO.getFirstFreezeTime());
                record.setFreezeStatus(TmsTransportConstant.FreezeStatusEnum.UNFRREZE.getValue());
                record.setFreezeFrom(drvFreezePO.getTotalFreezeFrom());
                record.setFreezeHour(drvFreezePO.getFreezeHour());
                record.setFreezeReason("driver online or offline");
                record.setUnfreezeReason("driver online or offline");
                record.setUnfreezeAction(drvFreezePO.getUnfreezeAction());
                record.setFreezeOrderSet(drvFreezePO.getFreezeOrderSet());
                record.setUnfreezeTime(DateUtil.getNow());
                record.setCreateUser(modifyUser);
                record.setModifyUser(modifyUser);
                drvFreezeRecordRepository.insert(record);
            }
            return Boolean.TRUE;
        }catch (Exception e){
            logger.error("saveDrvUnFreezeRecord_ERROR","e:{}",e);
            return Boolean.FALSE;
        }
    }

    public Boolean overseasExecuteEditApprove(Long supplierId,Integer areaScope, List<OcrPassStatusModelSOA> ocrPassStatusList) {
        Boolean supplierGray = driverQueryService.overseasSupplierIsGray(supplierId, areaScope);
        if (!supplierGray) {
            return Boolean.FALSE;
        }
        //如果传过来的OCR校验值为空，则不进编辑审核
        if (CollectionUtils.isEmpty(ocrPassStatusList)) {
            return Boolean.FALSE;
        }
        Boolean checkFlag = Boolean.FALSE;
        //遍历校验列表，如果有不通过的标识，则直接进入编辑审批
        for (OcrPassStatusModelSOA modelSOA : ocrPassStatusList) {
            if (Objects.isNull(modelSOA)) {
                continue;
            }
            if (modelSOA.getPassStatus() != null && Objects.equals(TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass.getCode(), modelSOA.getPassStatus())) {
                checkFlag = Boolean.TRUE;
                break;
            }
        }
        return checkFlag;
    }

    //判断OCR结果是否为通过
    public Boolean overseasOCRCheck(List<OcrPassStatusModelSOA> ocrPassStatusList,TmsTransportConstant.OverseasOCRPassStatusEnum statusEnum){
        //如果传过来的OCR校验值为空，则不进编辑审核
        if (CollectionUtils.isEmpty(ocrPassStatusList)) {
            return Boolean.FALSE;
        }
        //遍历校验列表，如果有不通过的标识，则直接进入编辑审批
        for (OcrPassStatusModelSOA modelSOA : ocrPassStatusList) {
            if (Objects.isNull(modelSOA)) {
                continue;
            }
            if (modelSOA.getPassStatus() != null && Objects.equals(statusEnum.getCode(), modelSOA.getPassStatus())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
