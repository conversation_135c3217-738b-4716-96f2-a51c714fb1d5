package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.igt.framework.common.result.*;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.*;

/**
 * 司机命令类接口
 * <AUTHOR>
 * @Date 2020/3/17 15:00
 */
public interface DriverCommandService {

 /**
  * 更新司机住址
  * @param requestType
  * @return
  */
   Result<Boolean> updateDrvIntendVehicleType(UpdateDrvIntendVehicleTypeRequestType requestType);

    /**
     * 司机状态变更（上线/下线）
     * @param drvIds
     * @param status
     * @param opFrom
     * @param modifyUser
     * @return
     */
   Result<Boolean> updateDrvStatus(List<Long> drvIds, Integer status, Integer opFrom, String modifyUser);

  /**
   * 司机状态变更
   * @param drvIds
   * @param status
   * @param modifyUser
   * @return
   */
   Result<Boolean> updateDrvStatus(List<Long> drvIds, Integer status, String modifyUser);

  /**
   * 司机UID变更
   *
   * @param drvId
   * @param uid
   * @param modifyUser
   * @param ppmAccount
   * @param qunarAccount
   * @return
   */
  Result<Boolean> updateDrvUid(Long drvId,String uid,String modifyUser, String ppmAccount, String qunarAccount);


  /**
   * 添加司机
   *
   * @param addDrvRequestType
   * @return
   */
  Result<Boolean> addDrv(DrvAddSOARequestType addDrvRequestType);

  /**
   * 编辑司机
   *
   * @param drvDriverPO
   * @return
   */
  Result<Long> updateDrv(DrvDriverPO drvDriverPO, Boolean ocrheadPortraitResult,List<OcrPassStatusModelSOA> ocrPassStatusList);

    /**
     * 司机冻结
     * @param drvIds
     * @param freezeHour
     * @param freezeReason
     * @param modifyUser
     * @return
     */
    Result<Boolean> freezeDrv(List<Long> drvIds, Integer freezeHour, String freezeReason, String modifyUser);


    /**
     * 添加司机正式
     *
     * @param drvDriverPO
     * @return
     */
    Result<Long> addDrvOfficial(DrvDriverPO drvDriverPO);

    /**
     * 根据司机绑定的运力组关系，计算司机合作模式
     * @param drvList
     * @param modifyUser
     * @return
     */
    Boolean calculateUpdateDrvCoopMode(List<Long> drvList,String modifyUser);

    int insertDrvLoginInfo(TmsDrvLoginInformationPO informationPO);

    /**
     * 司机健康打卡信息存储
     *
     * @param requestType
     * @return
     */
    Result<Boolean> drvHealthPunchAdd(DrvHealthPunchAddRequestType requestType);

    /**
     　* @description: 绑定司机派遣
     　* <AUTHOR>
     　* @date 2023/2/13 14:06
     */
    Result<Boolean> drvDispatchRelationUpdate(DrvDispatchRelationUpdateSOARequestType requestType);

    /**
     　* @description: 供应商绑定司机派遣
     　* <AUTHOR>
     　* @date 2023/2/13 14:06
     */
    Result<Boolean> vbkDrvDispatchBinding(Long drvId,Long supplierId);

    /**
    　* @description: 新增临派司机
    　* <AUTHOR>
    　* @date 2023/10/9 11:04
    */
    Result<Long> temporaryDispatchDrvAdd(TemporaryDispatchDrvAddSOARequestType requestType);

    /**
    　* @description: 新增临派司机
    　* <AUTHOR>
    　* @date 2023/10/9 11:04
    */
    Result<TemporaryDrvVehAddSOAResponseType> temporaryDrvVehAdd(TemporaryDrvVehAddSOARequestType requestType);


    Boolean temToOfficialSendQmq(Long drvId,String drvPHone,String modifyUser);

    /**
    　* @description: 司机新增派安盈账户
    　* <AUTHOR>
    　* @date 2024/3/7 15:09
    */
  Result<Boolean> drvAddPaiayAccount(Long drvId,String paiayAccount,String paiayEmail);

    /**
     * 司机生成换设备事件
     * @return
     */
  Result<Boolean> createDrvChangeEquipmentEvent(CreateDrvChangeEquipmentEventRequestType requestType);

}
