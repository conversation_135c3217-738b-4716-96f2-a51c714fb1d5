package com.ctrip.dcs.tms.transport.application.dto;

public class RefreshNetVehLicenseCheckIngParamDTO {
    private Long id;
    private String vehicleLicense;
    private Integer checkType;
    private Long supplierId;
    private Integer versionFlag;
    private Integer checkStatus;
    private Long checkId;
    private Long recruitingId;
    private Integer recruitingType;
    private Long cityId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVehicleLicense() {
        return vehicleLicense;
    }

    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public Integer getVersionFlag() {
        return versionFlag;
    }

    public void setVersionFlag(Integer versionFlag) {
        this.versionFlag = versionFlag;
    }

    public Integer getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    public Long getCheckId() {
        return checkId;
    }

    public void setCheckId(Long checkId) {
        this.checkId = checkId;
    }

    public Long getRecruitingId() {
        return recruitingId;
    }

    public void setRecruitingId(Long recruitingId) {
        this.recruitingId = recruitingId;
    }

    public Integer getRecruitingType() {
        return recruitingType;
    }

    public void setRecruitingType(Integer recruitingType) {
        this.recruitingType = recruitingType;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }
}
