package com.ctrip.dcs.tms.transport.application.query.impl;

import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import com.ctrip.dcs.driver.domain.account.*;
import com.ctrip.dcs.tms.transport.application.dto.PreCheckResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainService;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.arch.distlock.DLock;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.api.regulation.OldDriverInfo;
import com.ctrip.dcs.tms.transport.api.resource.driver.DrvBase;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.convert.DrvResourceConverter;
import com.ctrip.dcs.tms.transport.application.convert.HistoryDrvResourceConverter;
import com.ctrip.dcs.tms.transport.application.convert.SOAReqConverter;
import com.ctrip.dcs.tms.transport.application.dto.SimpleDriverInfoDTO;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverAccountDetail;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.InfrastructureServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.config.DistributedLockConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.config.TempDispatchDateConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.TransportMetric;
import com.dianping.cat.Cat;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.DrvDispatchSupplierRelationgateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.igt.infrastructureservice.executor.contract.CarInfoDTO;
import com.ctrip.igt.infrastructureservice.executor.contract.CarOCRResponseType;
import com.ctrip.igt.infrastructureservice.executor.contract.DrivingLicenseDTO;
import com.ctrip.igt.infrastructureservice.executor.contract.DrivingLicenseResponseType;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import lombok.SneakyThrows;

/**
 * 司机查询类接口
 * <AUTHOR>
 * @Date 2020/3/17 14:59
 */
@Service
public class DriverQueryServiceImpl implements DriverQueryService {

    private static final Logger logger = LoggerFactory.getLogger(DriverQueryServiceImpl.class);

    private  static final String DRVKEY = "drv_key_";//司机key,为了司机端查询code
    private  static final String CODEKEY = "code_key_";//code key,携挰大学第三方(智行力返查drvId是否有效)

    private static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");

    @Autowired
    private DrvDrvierRepository repository;
    @Autowired
    private EnumRepository enumRepository;
    @Autowired
    private TspTransportGroupDriverRelationRepository relationRepository;
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private TransportGroupRepository groupRepository;
    @Autowired
    private ApprovalProcessAuthQconfig authQconfig;
    @Autowired
    private CertificateCheckQueryService checkQueryService;

    @Autowired
    private TmsTransportQconfig transportQconfig;
    @Autowired
    private ProductionLineUtil productionLineUtil;
    @Autowired
    private TmsDrvFreezeRepository tmsDrvFreezeRepository;

    @Autowired
    DrvDriverLeaveRepository leaveRepository;
    @Autowired
    CommonCommandService commonCommandService;
    @Autowired
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;
    public static final Map<Integer, String> periodTimeMap = Maps.newHashMap();

    private static Map<String, String> dict = Maps.newHashMap();

    @Autowired
    private DrvResourceConverter drvResourceConverter;

    @Autowired
    private SensitiveDataControl control;

    @Autowired
    private HistoryDrvRepository historyDrvRepository;

    @Autowired
    private TransportGroupQueryService transportGroupQueryService;
    @Autowired
    DrvDispatchRelationRepository drvDispatchRelationRepository;

    @Autowired
    private DrvFreezeRecordRepository drvFreezeRecordRepository;

    @Autowired
    private OverseasOcrRecordRepository overseasOcrRecordRepository;

    @Autowired
    private OverseasQconfig overseasQconfig;

    @Autowired
    private TempDispatchDateConfig tempDispatchDateConfig;

    @Autowired
    private DrvVehLegendRepository drvVehLegendRepository;

    @Autowired
    DrvDispatchSupplierRelationgateway drvDispatchSupplierRelationgateway;

    @Autowired
    TmsDrvInactiveReasonRepository drvInactiveReasonRepository;

    @Autowired
    MobileHelper mobileHelper;

    @Autowired
    private DriverDomainService driverDomainService;

    @Autowired
    private DriverRegestConfig driverRegestConfig;

    static {
        periodTimeMap.put(1, "00:00~01:59");
        periodTimeMap.put(2, "02:00~03:59");
        periodTimeMap.put(3, "04:00~05:59");
        periodTimeMap.put(4, "06:00~07:59");
        periodTimeMap.put(5, "08:00~09:59");
        periodTimeMap.put(6, "10:00~11:59");
        periodTimeMap.put(7, "12:00~13:59");
        periodTimeMap.put(8, "14:00~15:59");
        periodTimeMap.put(9, "16:00~17:59");
        periodTimeMap.put(10, "18:00~19:59");
        periodTimeMap.put(11, "20:00~21:59");
        periodTimeMap.put(12, "22:00~23:59");
    }

    @Resource
    private InfrastructureServiceClientProxy infrastructureServiceClientProxy;

    @Autowired
    DistributedLockConfig lockConfig;

    @Autowired
    DriverDomainServiceProxy driverDomainServiceProxy;

    @Autowired
    BusinessQConfig businessQConfig;

    @Autowired
    IVRCallService ivrCallService;

    @Override
    public boolean checkWorkPeriod(String workPeriod) {
        if (Strings.isNullOrEmpty(workPeriod)) {
            return true;
        }
        //检查工作时段是否重复
        Set<TimeFrame> timeSet = Sets.newHashSet();
        String[] array = workPeriod.split(",");
        boolean anyMatch = Stream.of(array).anyMatch(period -> {
            String[] split = period.split("~");
            TimeFrame timeFrame = new TimeFrame(split[0], split[1], dateTimeFormatter);
            boolean flag = timeSet.contains(timeFrame);
            if (!flag) {
                timeSet.add(timeFrame);
            }
            return flag;
        });
        return !anyMatch;
    }

    @Override
    public Result<List<QueryIntendVehicleTypeSOAResponseDTO>> queryOptionalIntendVehicleType(QueryOptionalIntendVehicleTypeSOARequestType requestType) {
        Result.Builder<List<QueryIntendVehicleTypeSOAResponseDTO>> newResult = Result.Builder.<List<QueryIntendVehicleTypeSOAResponseDTO>>newResult();
        List<QueryIntendVehicleTypeSOAResponseDTO> data = Lists.newArrayList();
        Long cityId;
        Long vehicleTypeId;
        boolean anyMatch = false;
        if (Objects.nonNull(requestType.getDrvId())) {
            DrvDriverPO drvDriverPO = repository.queryByPk(requestType.getDrvId());
            if (Objects.isNull(drvDriverPO) || Strings.isNullOrEmpty(drvDriverPO.getVehicleLicense())) {
                return newResult.success().withData(data).build();
            }
            VehVehiclePO vehiclePO = vehicleRepository.getVehVehicleRepo().queryByPk(drvDriverPO.getVehicleId());
            if (Objects.isNull(vehiclePO) || Objects.isNull(vehiclePO.getVehicleTypeId()) || vehiclePO.getVehicleTypeId() == 0) {
                return newResult.success().withData(data).build();
            }
            cityId = drvDriverPO.getCityId();
            vehicleTypeId = vehiclePO.getVehicleTypeId();
            if (!Strings.isNullOrEmpty(drvDriverPO.getIntendVehicleTypeId())) {
                anyMatch = Arrays.stream(drvDriverPO.getIntendVehicleTypeId().split(TmsTransportConstant.SPLIT))
                        .map(id -> Long.parseLong(id.trim())).anyMatch(id -> id.equals(vehicleTypeId));
            }
        }else {
            cityId = requestType.getCityId();
            vehicleTypeId = requestType.getVehicleTypeId();
        }
        if (Objects.nonNull(cityId) && Objects.nonNull(vehicleTypeId)) {
            //查询可选意愿车型
            Map<Long, String> optionalIntendVehicleType = enumRepository.getOptionalIntendVehicleType(cityId, vehicleTypeId,anyMatch);
            data = optionalIntendVehicleType.entrySet().stream().map(entry -> {
                return new QueryIntendVehicleTypeSOAResponseDTO(entry.getKey(),entry.getValue());
            }).collect(Collectors.toList());
        }
        return newResult.success().withData(data).build();
    }

    @Override
    public Result<PageHolder<DrvDriverSOAResponseDTO>> queryDrvList(QueryDrvDO drvDO) {
        PageHolder pageHolder = null;
        try {
            int count = 0;
            if(drvDO.getRelationType() == null || drvDO.getSupplierId() == null){
                drvDO.setRelationType(TmsTransportConstant.DrvDispatchRelationTypeEnum.CORRELATION.getCode());
            }
            if(Objects.equals(TmsTransportConstant.DrvDispatchRelationTypeEnum.CORRELATION.getCode(),drvDO.getRelationType())){
                count = repository.countDrvList(drvDO);
            }
            //查询司机派遣列表-只限供应商角色查询，供应商ID必传
            if(Objects.equals(TmsTransportConstant.DrvDispatchRelationTypeEnum.DISPATCH.getCode(),drvDO.getRelationType())){
                //派遣关系没有派遣司机，返回0
                if(Objects.equals(TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode(),drvDO.getTemporaryDispatchMark())){
                    count = 0;
                }else {
                    count = repository.countDrvDispatchList(drvDO);
                }
            }
            if (count == 0) {
                if(CollectionUtils.isNotEmpty(drvDO.getDrvPhoneList())){
                    MetricsUtils.drvPhoneQueryfail(ApiTypeEnum.QUERY_DRV_LIST, Joiner.on(",").join(drvDO.getDrvPhoneList()));
                }
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(drvDO.getPage()).pageSize(drvDO.getSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }
            List<DrvDriverPO> driverPOList = Lists.newArrayList();
            if(Objects.equals(TmsTransportConstant.DrvDispatchRelationTypeEnum.CORRELATION.getCode(),drvDO.getRelationType())){
                driverPOList = repository.queryDrvList(drvDO);
            }
            //查询司机派遣列表-只限供应商角色查询，供应商ID必传
            if(Objects.equals(TmsTransportConstant.DrvDispatchRelationTypeEnum.DISPATCH.getCode(),drvDO.getRelationType())){
                driverPOList = repository.queryDrvDispatchList(drvDO);
            }
            if (CollectionUtils.isEmpty(driverPOList)) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(drvDO.getPage()).pageSize(drvDO.getSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }

            Map<Integer, String> drvStatusMap = enumRepository.getDrvStatusName();
            Map<Integer, String> drvFromMap = enumRepository.getDrvFrom();
            Map<String, String> languageMap = enumRepository.getDrvLanguageMap();
            Map<Integer,String> coopModeMap = enumRepository.getDrvCoopMode();
            List<DrvDriverSOAResponseDTO> drvList = Lists.newArrayListWithCapacity(driverPOList.size());
            for (DrvDriverPO po : driverPOList) {
                DrvDriverSOAResponseDTO soaResponseDTO = new DrvDriverSOAResponseDTO();
                BeanUtils.copyProperties(po, soaResponseDTO);
                soaResponseDTO.setDatachangeCreatetime(DateUtil.timestampToString(po.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
                soaResponseDTO.setDatachangeLasttime(DateUtil.timestampToString(po.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
                soaResponseDTO.setCityName(enumRepository.getCityName(po.getCityId()));
                soaResponseDTO.setDrvLanguageName(enumRepository.getDrvLanguageName(po.getDrvLanguage(), languageMap));
                soaResponseDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(po.getVehicleTypeId()));
                soaResponseDTO.setDrvStatusName(drvStatusMap.get(po.getDrvStatus()));
                soaResponseDTO.setDrvFromName(drvFromMap.get(soaResponseDTO.getDrvFrom()));
                soaResponseDTO.setIntendVehicleTypeName(enumRepository.getIntendVehicleTypeName(soaResponseDTO.getIntendVehicleTypeId()));
                soaResponseDTO.setDrvPhone(control.getSensitiveData(po.getDrvPhone(), KeyType.Phone));
                soaResponseDTO.setDrvIdcard(control.getSensitiveData(po.getDrvIdcard(), KeyType.Identity_Card));
                soaResponseDTO.setSupplierName(enumRepository.getSupplierName(po.getSupplierId()));
                soaResponseDTO.setCoopModeName(coopModeMap.get(po.getCoopMode()));
                soaResponseDTO.setProLineName(productionLineUtil.getProductionLineNames(po.getCategorySynthesizeCode()));
                soaResponseDTO.setAreaScope(po.getInternalScope());
                soaResponseDTO.setRaisingPickUp(po.getRaisingPickUp());
                soaResponseDTO.setChildSeat(po.getChildSeat());
                drvList.add(soaResponseDTO);
            }
            pageHolder = PageHolder.of(drvList).pageIndex(drvDO.getPage()).pageSize(drvDO.getSize()).totalSize(count).build();

            // 增加未激活原因
            getInActiveRemark(drvList);
            // 构建ivr验证信息
            buildIvrVerifyInfo(drvList);
            return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
        } catch (Exception e) {
            logger.error("select driver list error,",e);
        }finally {
            MetricsUtils.drvQueryListConnectTimes(ApiTypeEnum.QUERY_DRV_LIST);
        }
        return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().fail().withData(pageHolder).build();
    }

    protected void getInActiveRemark(List<DrvDriverSOAResponseDTO> dtoList) {
        //查询未激活原因
        List<TmsDrvInactiveReasonPO> inActiveList = drvInactiveReasonRepository.query(dtoList.stream().filter(
            dto -> Objects.equals(dto.getDrvStatus(), TmsTransportConstant.DrvStatusEnum.UNACT.getCode()) && Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(), dto.getAreaScope()))
          .map(DrvDriverSOAResponseDTO::getDrvId).collect(Collectors.toList()));

        // 组装未激活原因
        for (TmsDrvInactiveReasonPO inActive : inActiveList) {
            for (DrvDriverSOAResponseDTO driver : dtoList) {
                if(Objects.equals(driver.getDrvId(), inActive.getDrvId())){
                    driver.setInactiveRemark(Objects.isNull(driver.getInactiveRemark()) ? DrvInActiveEnum.getReasonDesc(inActive.getReasonCode()) : driver.getInactiveRemark() + ";" + DrvInActiveEnum.getReasonDesc(inActive.getReasonCode()));
                }
            }
        }
    }

    @Override
    public Result<Integer> queryBundleStatusDrvCount(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType) {
        return Result.Builder.<Integer>newResult()
                .success()
                .withData(repository.queryBundleStatusDrvCount(drvIds, supplierId, requestSOAType))
                .build();
    }

    @Override
    public Result<List<DrvDriverPO>> queryBundleStatusDrvList(List<Long> drvIds, Long supplierId, DriverRelationListRequestSOAType requestSOAType) {
        return Result.Builder.<List<DrvDriverPO>>newResult()
                .success()
                .withData(repository.queryBundleStatusDrvList(drvIds, supplierId, requestSOAType))
                .build();
    }

    @Override
    public Result<QueryDrvDetailSOAResponseType> queryDrvDetail(Long drvId) {
        QueryDrvDetailSOAResponseType responseType = new QueryDrvDetailSOAResponseType();
        DrvDriverPO drvDriverPO = repository.queryByPk(drvId);
        if(drvDriverPO == null){
            return Result.Builder.<QueryDrvDetailSOAResponseType>newResult().success() .withData(responseType) .build();
        }

        Map<String,String> languageMap = enumRepository.getDrvLanguageMap();
        Map<Integer,String> coopModeMap = enumRepository.getDrvCoopMode();
        //司机对应证件核验记录
        Map<Integer, TmsCertificateCheckPO> checkPOMap = checkQueryService.queryCertificateCheckToMap(drvId, TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode());
        QueryDrvDetailDTOSOA drvDetailDTOSOA = new QueryDrvDetailDTOSOA();
        BeanUtils.copyProperties(drvDriverPO,drvDetailDTOSOA);
        drvDetailDTOSOA.setCertiDate(DateUtil.dateToString(drvDriverPO.getCertiDate(), DateUtil.YYYYMMDD));
        drvDetailDTOSOA.setDatachangeCreatetime(DateUtil.timestampToString(drvDriverPO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
        drvDetailDTOSOA.setDatachangeLasttime(DateUtil.timestampToString(drvDriverPO.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
        drvDetailDTOSOA.setExpiryBeginDate(DateUtil.dateToString(drvDriverPO.getExpiryBeginDate(), DateUtil.YYYYMMDD));
        drvDetailDTOSOA.setExpiryEndDate(DateUtil.dateToString(drvDriverPO.getExpiryEndDate(), DateUtil.YYYYMMDD));
        drvDetailDTOSOA.setSupplierName(enumRepository.getSupplierName(drvDriverPO.getSupplierId()));
        drvDetailDTOSOA.setCityName(enumRepository.getCityName(drvDriverPO.getCityId()));
        drvDetailDTOSOA.setVehicleTypeName(enumRepository.getVehicleTypeName(drvDriverPO.getVehicleTypeId()));
        drvDetailDTOSOA.setDrvLanguageName(enumRepository.getDrvLanguageName(drvDriverPO.getDrvLanguage(),languageMap));
        drvDetailDTOSOA.setDrvFromName(enumRepository.getDrvFrom().get(drvDriverPO.getDrvFrom()));
        drvDetailDTOSOA.setDrvStatusName(enumRepository.getDrvStatusName().get(drvDriverPO.getDrvStatus()));
        drvDetailDTOSOA.setAreaScope(drvDriverPO.getInternalScope());
        drvDetailDTOSOA.setIntendVehicleTypeName(enumRepository.getIntendVehicleTypeName(drvDetailDTOSOA.getIntendVehicleTypeId()));
        drvDetailDTOSOA.setDrvPhone(control.getSensitiveData(drvDriverPO.getDrvPhone(), KeyType.Phone));
        drvDetailDTOSOA.setEmail(control.getSensitiveData(drvDriverPO.getEmail(), KeyType.Mail));
        drvDetailDTOSOA.setDrvIdcard(control.getSensitiveData(drvDriverPO.getDrvIdcard(), KeyType.Identity_Card));
        drvDetailDTOSOA.setDrvLicenseNumber(control.getSensitiveData(drvDriverPO.getDrvLicenseNumber(), KeyType.Identity_Card));
        drvDetailDTOSOA.setCoopModeName(coopModeMap.get(drvDetailDTOSOA.getCoopMode()));
        drvDetailDTOSOA.setCoopMode(drvDetailDTOSOA.getCoopMode());
        drvDetailDTOSOA.setIsTaxi(drvDriverPO.getTaxi());
        List<TspTransportGroupDriverRelationPO> relationPOS =  relationRepository.queryTransportGroupIdByDrvIds(Arrays.asList(drvId));
        if(CollectionUtils.isEmpty(relationPOS)){
            drvDetailDTOSOA.setTransportGroupList(new ArrayList<>());
        }
        Set<Long> transGroupIds = Sets.newHashSet();
        relationPOS.forEach(r->transGroupIds.add(r.getTransportGroupId()));
        drvDetailDTOSOA.setTransportGroupList(this.getTransportGroupData(transGroupIds));
        drvDetailDTOSOA.setDrvLicenseData(CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.DRIVERLICENSE.getCode())));
        CertificateResultSOADTO soadto = CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode()));
        drvDetailDTOSOA.setNetDrvLicenseData(soadto);
        drvDetailDTOSOA.setIdCardData(CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.IDCARD.getCode())));
        drvDetailDTOSOA.setNucleicAcidReportCertiData(CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.NUCLEIC_ACID.getCode())));
        drvDetailDTOSOA.setVaccinationReportCtfctData(CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.VACCINE.getCode())));
        drvDetailDTOSOA.setHeadPortraitCertiData(CtripCommonUtils.resultSOADTO(checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode())));
        TmsCertificateCheckPO ocrCheckPO = checkPOMap.get(TmsTransportConstant.CertificateTypeEnum.OCR_HEAD_PORTRAIT.getCode());
        if (ocrCheckPO != null) {
            drvDetailDTOSOA.setOcrheadPortraitResult(Objects.equals(ocrCheckPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.THROUGH.getCode()));
        }
        drvDetailDTOSOA.setVaccinationReportUrl(drvDriverPO.getVaccineReportImg());
        drvDetailDTOSOA.setNucleicAcidReportUrl(drvDriverPO.getNucleicAcidReportImg());
        if (soadto != null) {
            drvDetailDTOSOA.setNetDrvCheckStatus(soadto.getCheckStatus());
        }
        //司机网约车证为空，默认核验不通过标识
        if(StringUtils.isEmpty(drvDriverPO.getNetVehiclePeoImg())){
            drvDetailDTOSOA.setNetDrvCheckStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
        }
        if(drvDetailDTOSOA.getVehicleId()!=null && drvDetailDTOSOA.getVehicleId() > 0){
            Map<Integer, TmsCertificateCheckPO> vehCheckPOMap = checkQueryService.queryCertificateCheckToMap(drvDetailDTOSOA.getVehicleId(), TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode());
            drvDetailDTOSOA.setVehicleCertiData(CtripCommonUtils.resultSOADTO(vehCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.CARCERTILICENSE.getCode())));
            drvDetailDTOSOA.setNetTansCtfctData(CtripCommonUtils.resultSOADTO(vehCheckPOMap.get(TmsTransportConstant.CertificateTypeEnum.NETTANSCTFCT.getCode())));
        }

        //设置司机的报名状态
        int drvApplyStatus = repository.queryDrvApplyStatus(drvId, TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode());
        drvDetailDTOSOA.setApplyStatus(drvApplyStatus);
        drvDetailDTOSOA.setProLineList(productionLineUtil.getShowProductionLineList(drvDriverPO.getCategorySynthesizeCode()));

        DrvEpidemicPreventionControlInfoPO controlInfoPO = drvEpidemicPreventionControlInfoRepository.queryByDrvId(drvId);
        if (controlInfoPO != null) {
            List<String> timeList = Lists.newArrayListWithExpectedSize(4);
            if (controlInfoPO.getFirstVaccinationTime() != null) {
                String val = BaseUtil.dealWithDateToStr(controlInfoPO.getFirstVaccinationTime());
                if (!Strings.isNullOrEmpty(val)) {
                    timeList.add(BaseUtil.dealWithDateToStr(controlInfoPO.getFirstVaccinationTime()));
                }
            }
            if (controlInfoPO.getSecondVaccinationTime() != null) {
                String val = BaseUtil.dealWithDateToStr(controlInfoPO.getSecondVaccinationTime());
                if (!Strings.isNullOrEmpty(val)) {
                    timeList.add(val);
                }
            }
            if (controlInfoPO.getThirdVaccinationTime() != null) {
                String val = BaseUtil.dealWithDateToStr(controlInfoPO.getThirdVaccinationTime());
                if (!Strings.isNullOrEmpty(val)) {
                    timeList.add(val);
                }
            }
            drvDetailDTOSOA.setVaccinationTimeList(timeList);
            drvDetailDTOSOA.setNucleicAcidTestingTime(BaseUtil.dealWithDateToStr(controlInfoPO.getNucleicAcidTestingTime()));
        }

        //删除证件时，对应的标签需要一并隐藏，结果为页面不展示、下游（司机端）识别人证车证的标识为不再加分
        if(StringUtils.isEmpty(drvDriverPO.getIdcardImg())){
            drvDetailDTOSOA.setIdCardData(null);
        }
        if(StringUtils.isEmpty(drvDriverPO.getDrvcardImg())){
            drvDetailDTOSOA.setDrvLicenseData(null);
        }
        if(StringUtils.isEmpty(drvDriverPO.getNetVehiclePeoImg()) && StringUtils.isEmpty(drvDriverPO.getNetAppealMaterials())){
            drvDetailDTOSOA.setNetDrvLicenseData(null);
        }
        if(StringUtils.isEmpty(drvDriverPO.getNucleicAcidReportImg())){
            drvDetailDTOSOA.setNucleicAcidReportCertiData(null);
        }
        if(StringUtils.isEmpty(drvDriverPO.getVaccineReportImg())){
            drvDetailDTOSOA.setVaccinationReportCtfctData(null);
        }
        drvDetailDTOSOA.setCertificateConfigStr(drvDriverPO.getCertificateConfig());
        drvDetailDTOSOA.setRaisingPickUp(drvDriverPO.getRaisingPickUp());
        drvDetailDTOSOA.setChildSeat(drvDriverPO.getChildSeat());
        //0=国内 1=国外
        drvDetailDTOSOA.setInternalScope(drvDriverPO.getInternalScope());
        drvDetailDTOSOA.setOnlineTime(DateUtil.timestampToString(drvDriverPO.getOnlineTime(), DateUtil.YYYYMMDDHHMMSS));
        //只显示境外的标签
        if(Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(),drvDriverPO.getInternalScope())){
            drvDetailDTOSOA.setCheckDataList(CtripCommonUtils.assembleCheckData(checkPOMap));
        }
        drvDetailDTOSOA.setTemporaryDispatchEndDatetime(DateUtil.timestampToString(drvDriverPO.getTemporaryDispatchEndDatetime(), DateUtil.YYYYMMDDHHMMSS));
        drvDetailDTOSOA.setPaiayEmail(control.getSensitiveData(drvDriverPO.getPaiayEmail(), KeyType.Mail));
        responseType.setData(drvDetailDTOSOA);
        return Result.Builder.<QueryDrvDetailSOAResponseType>newResult().success() .withData(responseType) .build();
    }

    @Override
    public Result<QueryDrvInfoDTOSOA> queryDrvInfo(QueryDrvInfoSOARequestType requestType) {
        try{
            if (Strings.isNullOrEmpty(requestType.getEmail()) && Objects.isNull(requestType.getDrvId())
                    && !(Objects.nonNull(requestType.getDrvPhone()) && Objects.nonNull(requestType.getIgtCode()))
                    && Objects.isNull(requestType.getLoginAccount())
                    && Objects.isNull(requestType.getQunarAccout())) {
                return Result.Builder.<QueryDrvInfoDTOSOA>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing))
                        .build();
            }
            DrvInfoParam drvInfoParam = new DrvInfoParam();
            drvInfoParam.setDrvId(requestType.getDrvId());
            drvInfoParam.setDrvPhone(TmsTransUtil.encrypt(requestType.getDrvPhone(),KeyType.Phone));
            drvInfoParam.setIgtCode(requestType.getIgtCode());
            drvInfoParam.setLoginAccount(requestType.getLoginAccount());
            drvInfoParam.setQunarAccout(requestType.getQunarAccout());
            drvInfoParam.setEmail(TmsTransUtil.encrypt(requestType.getEmail(),KeyType.Mail));
            DrvInfoPO drvInfoPO = repository.queryDrvInfo(drvInfoParam);
            QueryDrvInfoDTOSOA drvInfoDTOSOA = null;
            if (drvInfoPO != null){
                drvInfoDTOSOA = new QueryDrvInfoDTOSOA();
                BeanUtils.copyProperties(drvInfoPO, drvInfoDTOSOA);
                drvInfoDTOSOA.setDrvIdcard(control.getSensitiveData(drvInfoDTOSOA.getDrvIdcard(), KeyType.Identity_Card));
                drvInfoDTOSOA.setDrvPhone(control.getSensitiveData(drvInfoDTOSOA.getDrvPhone(), KeyType.Phone));
                drvInfoDTOSOA.setEmail(control.getSensitiveData(drvInfoDTOSOA.getEmail(), KeyType.Mail));
            }
            return Result.Builder.<QueryDrvInfoDTOSOA>newResult()
                    .success()
                    .withData(drvInfoDTOSOA)
                    .build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result<List<QueryDrvInfoDTOSOA>> queryDrvInfoList(QueryDrvInfoListSOARequestType requestType) {
        try{
            List<DrvInfoPO> drvInfoPOS = repository.queryDrvInfoList(requestType.getDrvId());
            List<QueryDrvInfoDTOSOA> collect = drvInfoPOS.stream().map(po -> {
                QueryDrvInfoDTOSOA drvInfoDTOSOA = new QueryDrvInfoDTOSOA();
                BeanUtils.copyProperties(po, drvInfoDTOSOA);
                return drvInfoDTOSOA;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                for (QueryDrvInfoDTOSOA drvInfoDTOSOA : collect) {
                    drvInfoDTOSOA.setDrvIdcard(control.getSensitiveData(drvInfoDTOSOA.getDrvIdcard(), KeyType.Identity_Card));
                    drvInfoDTOSOA.setDrvPhone(control.getSensitiveData(drvInfoDTOSOA.getDrvPhone(), KeyType.Phone));
                    drvInfoDTOSOA.setEmail(control.getSensitiveData(drvInfoDTOSOA.getEmail(), KeyType.Mail));
                }
            }
            return Result.Builder.<List<QueryDrvInfoDTOSOA>>newResult()
                    .success()
                    .withData(collect)
                    .build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    protected void buildIvrVerifyInfo(List<DrvDriverSOAResponseDTO> collect) {
        //获取手机号验证情况
        List<PhoneDTO> phoneDtoList =
            collect.stream().filter(drv -> businessQConfig.isVerifyPhone(drv.getAreaScope())).map(
                drv -> PhoneDTO.builder().mobilePhone(drv.getDrvPhone())
                    .countryCode(drv.getIgtCode()).build()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(phoneDtoList)) {
            return;
        }

        Map<String, Boolean> phoneVerifiedMap =
            ivrCallService.isPhoneVerified(phoneDtoList);
        //设置手机号验证情况
        collect.forEach(drv -> drv.setPhoneNumberVerified( !(businessQConfig.isVerifyPhone(drv.getAreaScope()) || StringUtil.isEmpty(drv.getDrvPhone())) ?  null : phoneVerifiedMap.getOrDefault(drv.getIgtCode() + "-" + TmsTransUtil.encrypt(drv.getDrvPhone(), KeyType.Phone), false)));
    }

    @Override
    public Result<QueryDrvListByAppResponseType> queryDrvListByApp(QueryDrvListByAppDO appDO) {
        QueryDrvListByAppResponseType responseType = new QueryDrvListByAppResponseType();
        try{
            List<DrvDriverPO> drvDriverPOS = repository.queryDrvListByApp(appDO);
            if (CollectionUtils.isEmpty(drvDriverPOS)) {
                responseType.setData(new ArrayList<>());
                responseType.setTransportGroupData(new ArrayList<>());
                return Result.Builder.<QueryDrvListByAppResponseType>newResult().success().withData(responseType).build();
            }
            Set<Long> drvIdSet = Sets.newHashSet();
            Set<Long> vehicleIdSet = Sets.newHashSet();
            for (DrvDriverPO driverPO : drvDriverPOS) {
                drvIdSet.add(driverPO.getDrvId());
                if (driverPO.getVehicleId() != null && driverPO.getVehicleId() > 0) {
                    vehicleIdSet.add(driverPO.getVehicleId());
                }
            }
            Map<Long, List<TransportGroupBasePO>> transportIdsMap = this.getTransportIdsMap(drvIdSet);
            Map<Long, VehVehiclePO> vehMap = this.getVehVehicleMap(vehicleIdSet);
            Set<Long> transportIdSet = Sets.newHashSet();
            if (MapUtils.isNotEmpty(transportIdsMap)) {
                for (Map.Entry<Long, List<TransportGroupBasePO>> entry : transportIdsMap.entrySet()) {
                    entry.getValue().stream().forEach(l -> transportIdSet.add(l.getTransportGroupId()));
                }
            }
            responseType.setTransportGroupData(this.getTransportGroupData(transportIdSet));

            responseType.setData(this.getData(drvDriverPOS, transportIdsMap, vehMap));
            return  Result.Builder.<QueryDrvListByAppResponseType>newResult().success().withData(responseType).build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result<List<QueryDrvBySupplierIdSOADTO>> queryDrvBySupplierIdList(Long supplierId) {
        try{
            List<DrvDriverPO> drvDriverPOS = repository.queryDrvBySupplierIdList(supplierId);
            if(CollectionUtils.isEmpty(drvDriverPOS)){
                return Result.Builder.<List<QueryDrvBySupplierIdSOADTO>>newResult().success().withData(Lists.newArrayList()).build();
            }
            List<QueryDrvBySupplierIdSOADTO> resultList = Lists.newArrayList();
            drvDriverPOS.forEach(drvDriverPO -> {
                QueryDrvBySupplierIdSOADTO soadto = new QueryDrvBySupplierIdSOADTO();
                BeanUtils.copyProperties(drvDriverPO,soadto);
                resultList.add(soadto);
            });
            return Result.Builder.<List<QueryDrvBySupplierIdSOADTO>>newResult().success().withData(resultList).build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result<String> checkDrvPwd(String loginAccount, String loginPwd) {
        if(StringUtils.isEmpty(loginAccount)){
            return  Result.Builder.<String>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing))
                    .build();
        }
        List<DrvDriverPO> drvDriverPOList = repository.queryDrvByLoginAccount(loginAccount);
        int drvSize = drvDriverPOList.size();
        if(drvSize >= 2 || drvSize  == 0 ){
            return  Result.Builder.<String>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgAccountNotFoundOrMoreDriverData))
                    .build();
        }
        DrvDriverPO drvDriverPO = drvDriverPOList.get(0);
        loginPwd = MD5Util.digestByMD5(MD5Util.digestByMD5(loginPwd, "", null, "UTF-8"), authQconfig.getPwdKey(), drvDriverPO.getSalt(), "UTF-8");
        return Result.Builder.<String>newResult()
                .success()
                .withData(loginPwd)
                .build();
    }

    @Override
    public Result<Boolean> checkDrvTransportMode(List<Long> drvIdList) {
        if(CollectionUtils.isEmpty(drvIdList)){
            return  Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        }
        try{
            //查询司机关联的运力组
            List<TspTransportGroupDriverRelationPO> driverRelationPOS =  relationRepository.queryTransportGroupIdByDrvIds(drvIdList);
            if(CollectionUtils.isEmpty(driverRelationPOS)){
                return  Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
            }

            List<Long> transPortIds = Lists.newArrayList();
            driverRelationPOS.forEach(tspPO -> transPortIds.add(tspPO.getTransportGroupId()));
            //查询运力组信息
            List<TspTransportGroupPO> transportGroupList = groupRepository.queryTspTransportByIds(transPortIds);
            if(CollectionUtils.isEmpty(transportGroupList)){
                return  Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
            }
            Boolean flag = Boolean.FALSE;
            //如果司机关联的运力组中有 全职司机指派 或 兼职司机播报 模式，返回标识，说明要二次确定是否要解绑司机中的车辆
            //update 如果是司机关联的是报名制运力组
            for(TspTransportGroupPO transportGroupPO : transportGroupList){
                if(Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode())
                        ||Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())
                        ||Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
                    flag = Boolean.TRUE;
                    break;
                }
            }
            return  Result.Builder.<Boolean>newResult().success().withData(flag).build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result<QueryDrvByMuSelConditionsSOAResponseType> queryDrvByMuSelConditions(QueryDrvByMuSelConditionsSOARequestType requestType) {
        logger.info("queryDrvByMuSelConditions ","params:{}",JsonUtil.toJson(requestType));
        List<QueryDrvByMuSelConditionsDTO> resultList = Lists.newArrayList();
        QueryDrvByMuSelConditionsSOAResponseType responseType = new QueryDrvByMuSelConditionsSOAResponseType();
        responseType.setData(Lists.newArrayList());
        responseType.setDataCount(0);
//        if(StringUtils.isEmpty(requestType.getDrvLanguage())){
//            return Result.Builder.<QueryDrvByMuSelConditionsSOAResponseType>newResult().success().withData(responseType).build();
//        }
        int count = repository.countDrvByMuSelConditions(buildQueryDrvByMuSelDO(requestType));
        if(count == 0){
            return Result.Builder.<QueryDrvByMuSelConditionsSOAResponseType>newResult().success().withData(responseType).build();
        }
        List<DrvDriverPO> drvDriverPOList =  repository.queryDrvByMuSelConditions(buildQueryDrvByMuSelDO(requestType));
        if(CollectionUtils.isEmpty(drvDriverPOList)){
            return Result.Builder.<QueryDrvByMuSelConditionsSOAResponseType>newResult().success().withData(responseType).build();
        }

        //新增 司机 播报 查询条件
        Map<Long,Integer> broadcastMap = getBroadcastMap(drvDriverPOList);
        Map<Integer, String> drvStatusMap = enumRepository.getDrvStatusName();
        Map<Integer,String> coopModeMap = enumRepository.getDrvCoopMode();
        for(DrvDriverPO drvDriverPO : drvDriverPOList){
            QueryDrvByMuSelConditionsDTO conditionsDTO = new QueryDrvByMuSelConditionsDTO();
            BeanUtils.copyProperties(drvDriverPO,conditionsDTO);
            if(!Objects.isNull(requestType.getBroadcast())){
                if(MapUtils.isEmpty(broadcastMap) || broadcastMap.get(drvDriverPO.getDrvId()) == null){
                    continue;
                }
                Integer broadcast = broadcastMap.get(drvDriverPO.getDrvId());
                if(!Objects.equals(requestType.getBroadcast(),broadcast)){
                    continue;
                }
            }
            conditionsDTO.setCityName(enumRepository.getCityName(drvDriverPO.getCityId()));
            conditionsDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(drvDriverPO.getVehicleTypeId()));
            conditionsDTO.setDrvStatusName(drvStatusMap.get(drvDriverPO.getDrvStatus()));
            conditionsDTO.setDrvPhone(control.getSensitiveData(drvDriverPO.getDrvPhone(), KeyType.Phone));
            conditionsDTO.setSupplierName(enumRepository.getSupplierName(drvDriverPO.getSupplierId()));
            conditionsDTO.setCoopModeName(coopModeMap.get(drvDriverPO.getCoopMode()));
            if(MapUtils.isEmpty(broadcastMap) || broadcastMap.get(drvDriverPO.getDrvId()) == null){
                conditionsDTO.setBroadcast(0);
            }else{
                conditionsDTO.setBroadcast(broadcastMap.get(drvDriverPO.getDrvId()));
            }
            conditionsDTO.setProLineList(productionLineUtil.getShowProductionLineList(drvDriverPO.getCategorySynthesizeCode()));
            conditionsDTO.setProLineName(productionLineUtil.getProductionLineNames(drvDriverPO.getCategorySynthesizeCode()));
            resultList.add(conditionsDTO);
        }
        responseType.setData(resultList);
        responseType.setDataCount(count);
        return Result.Builder.<QueryDrvByMuSelConditionsSOAResponseType>newResult().success().withData(responseType).build();
    }

    public Map<Long,Integer> getBroadcastMap(List<DrvDriverPO> drvDriverPOList){
        Map<Long,Integer> broadcastMap = Maps.newHashMap();
        List<Long> drvIdList = drvDriverPOList.stream().map(DrvDriverPO::getDrvId).collect(Collectors.toList());
        drvIdList.forEach(drvId->{
            broadcastMap.put(drvId,0);
        });
        List<TransportGroupBasePO> basePOList = transportGroupQueryService.queryDriverGroupRelationPO(drvIdList, Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(basePOList)){
            Map<Long,List<TransportGroupBasePO>> drvMap = basePOList.stream().collect(Collectors.groupingBy(TransportGroupBasePO::getDrvId));
            for(Map.Entry<Long,List<TransportGroupBasePO>> map : drvMap.entrySet()){
                DrvInfoCacheDto cacheDto =  calculateDrvCoopMode(map.getKey(),map.getValue());
                if(!Objects.isNull(cacheDto)){
                    broadcastMap.put(map.getKey(),cacheDto.getBroadcast());
                }
            }
        }
        return broadcastMap;
    }

    @Override
    public Result<String> queryDrvCodeByDrvId(Long drvId) {
        String randomCode = RandomStringUtils.randomAlphabetic(20);
        String drvKey = DRVKEY + drvId;//司机key,为了司机端查询code
        String codeKey = CODEKEY + randomCode;//code key,携挰大学第三方(智行力返查drvId是否有效)
        String drvCode =  RedisUtils.get(drvKey);
        if(StringUtils.isEmpty(drvCode)){
            drvCode = randomCode;
            RedisUtils.set(drvKey,RedisUtils.TEN_MINIUTES,randomCode);
            RedisUtils.set(codeKey,RedisUtils.TEN_MINIUTES,TmsTransportConstant.CTRIP_UNIVERSITY_USERID + drvId);
        }
        return Result.Builder.<String>newResult().success().withData(drvCode).build();
    }

    @Override
    public Result<String> queryDrvIdByCode(String drvCode) {
        String codeKey = CODEKEY + drvCode;
        String redisVal = RedisUtils.get(codeKey);
        if (StringUtils.isEmpty(redisVal)) {
            return Result.Builder.<String>newResult().success().withData("").build();
        }
        return Result.Builder.<String>newResult().success().withData(redisVal).build();
    }

    @Override
    public Result<Boolean> checkVehicleTypeMatching(CheckVehicleTypeMatchingSOARequestType requestType) {
        try {
            //变更车型触发场景（司机换了车牌，该车牌对应车辆的车型不同/司机没换车牌，但该车牌换了品牌车系，导致车辆的车型不同）
            //几种情况影响结果为：车型变更（变更为另一个值或空值）
            //若司机已关联“全职司机指派-报名制”运力组，则给弹框提示，提示文案：当前操作涉及司机车型变更，变更后该司机将从所关联的所有原城市“全职司机指派-报名制”运力组中自动解绑，是否确认继续？
            Integer operationType = requestType.getOperationType();
            if(operationType == 1){
                DrvDriverPO drvDriverPO = repository.queryByPk(requestType.getDrvId());
                if(drvDriverPO == null){
                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverNotExist)).build();
                }
                //司机换车 vehicleId = 0 说明是司机下线,vehicleId > 0 说明是换车
                if(requestType.getVehicleId()!=null && requestType.getVehicleId() >0){
                    List<VehVehiclePO> vehVehiclePOList = vehicleRepository.queryVehVehicleByIds(Arrays.asList(requestType.getVehicleId(),drvDriverPO.getVehicleId()));
                    if(CollectionUtils.isEmpty(vehVehiclePOList) || vehVehiclePOList.size()!=2){
                        return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportVehicleIsEmpty)).build();
                    }
                    Long vehicleTypeId1 = vehVehiclePOList.get(0).getVehicleTypeId();
                    Long vehicleTypeId2 = vehVehiclePOList.get(1).getVehicleTypeId();
                    //判断新车和旧车的车型是否相同，如果不同，则判断该司机是否绑定在报名制-运力组下
                    if(Objects.equals(vehicleTypeId1,vehicleTypeId2)){
                        return Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
                    }
                    Boolean checkDrvFlag = this.getIsQZSJATmsTransport(drvDriverPO.getDrvId());
                    if(checkDrvFlag){
                        return Result.Builder.<Boolean>newResult().success().withData(checkDrvFlag).build();
                    }
                }else{
                    return Result.Builder.<Boolean>newResult().success().withData(this.getIsQZSJATmsTransport(drvDriverPO.getDrvId())).build();
                }
                //判断车辆绑定的司机是否是运力组
            }else if (operationType == 2){
                VehVehiclePO orginVehicle = vehicleRepository.queryByPk(requestType.getVehicleId());
                if(Objects.equals(requestType.getVehicleTypeId(),orginVehicle.getVehicleTypeId())){
                    return Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
                }
                List<DrvDriverPO> drvDriverList = repository.queryDrvByVehicleIds(Arrays.asList(orginVehicle.getVehicleId()));
                if(CollectionUtils.isNotEmpty(drvDriverList)){
                    return Result.Builder.<Boolean>newResult().success().withData(this.getIsQZSJATmsTransport(drvDriverList.get(0).getDrvId())).build();
                }
            }
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public DrvInfoCacheDto calculateQJBMDrvCoopMode(Long drvId, List<TransportGroupBasePO> relationVOS) {
        try {
            if(CollectionUtils.isEmpty(relationVOS)){
                return DrvInfoCacheDto.defaultDTO();
            }
            Set<Integer> transportModeSet = Sets.newHashSet();
            Set<Integer> appStatusSet = Sets.newHashSet();
            for(TransportGroupBasePO relationVO : relationVOS){
                transportModeSet.add(relationVO.getTransportGroupMode());
                //如果关联关系是已剔除，则和已报名同等计算逻辑
                appStatusSet.add(Objects.equals(relationVO.getApplyStatus(),TmsTransportConstant.ApplyStatusEnum.ELIMINATE.getCode())?
                        TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode():relationVO.getApplyStatus());
            }
            //报名至少一个上线“全职司机指派-报名制”运力组，且有报名成功状态（报名成功）
            if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode()) &&
                    appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode())){
                int coopMode = TmsTransportConstant.DrvCoopModeEnum.DRV_QJJP.getCode(),
                        broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode(),
                        sendWorkPeriod = TmsTransportConstant.SendWorkPeriodEnum.YES.getCode(),
                        compatibleCoopMode = TmsTransportConstant.CompatibleCoopModeEnum.QZ.getCode();
                //报名制全职（报名成功）
                //2、报名制全职（报名成功）+报名制全职（已报名）
                //3、报名制全职（报名成功）+调度
                //4、报名制全职（报名成功）+报名制全职（已报名）+调度
                if(appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()) ||
                        judgeScheduling(transportModeSet)||(judgeScheduling(transportModeSet)
                        && appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()))){
                    broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode();
                }
                //1、报名制全职（报名成功）+兼职播报
                //2、报名制全职（报名成功）+报名制全职（已报名）+兼职播报
                //3、报名制全职（报名成功）+兼职播报+调度
                //4、报名制全职（报名成功）+报名制全职（已报名）+兼职播报+调度
                if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())||
                        (transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())
                                && appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()))||
                        (transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())
                                && judgeScheduling(transportModeSet))||
                        (appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode())
                                && transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())
                                && judgeScheduling(transportModeSet))){
                    broadcast = TmsTransportConstant.BroadcastEnum.YES.getCode();
                }
                return  new DrvInfoCacheDto(drvId,coopMode, broadcast,sendWorkPeriod,compatibleCoopMode);
            }

            //报名至少一个上线“全职司机指派-报名制”运力组，但没有报名成功状态（已报名）
            if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode()) && !appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode())){
                int coopMode = TmsTransportConstant.DrvCoopModeEnum.DRV_JJJP.getCode(),
                        broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode(),
                        sendWorkPeriod = TmsTransportConstant.SendWorkPeriodEnum.YES.getCode(),
                        compatibleCoopMode = TmsTransportConstant.CompatibleCoopModeEnum.JZ.getCode();
                //报名制全职（已报名）
                ///2、报名制全职（已报名）+调度
                if(appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()) ||
                        (appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()) && judgeScheduling(transportModeSet))){
                    broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode();
                }
                //1、报名制全职（已报名）+兼职播报
                //2、报名制全职（已报名）+兼职播报+调度
                if((appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()) && transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode()))||
                        (appStatusSet.contains(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()) && transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())
                                && judgeScheduling(transportModeSet))){
                    broadcast = TmsTransportConstant.BroadcastEnum.YES.getCode();
                }
                return  new DrvInfoCacheDto(drvId,coopMode, broadcast,sendWorkPeriod,compatibleCoopMode);
            }

            //未报名任何上线“全职司机指派-报名制”，但至少关联一个上线的“兼职播报”
            if(!transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())
                    && transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())){
                int coopMode = TmsTransportConstant.DrvCoopModeEnum.DRV_JJBB.getCode(),
                        broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode(),
                        sendWorkPeriod = TmsTransportConstant.SendWorkPeriodEnum.YES.getCode(),
                        compatibleCoopMode = TmsTransportConstant.CompatibleCoopModeEnum.JZ.getCode();
                if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode()) && judgeScheduling(transportModeSet)){
                    broadcast = TmsTransportConstant.BroadcastEnum.YES.getCode();
                }
                return  new DrvInfoCacheDto(drvId,coopMode, broadcast,sendWorkPeriod,compatibleCoopMode);
            }

            //仅关联至少一个上线的“调度”类
            if(judgeScheduling(transportModeSet)
                    && !transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode())
                    && !transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())
                    && !transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
                int coopMode = TmsTransportConstant.DrvCoopModeEnum.DRV_RGDD.getCode(),
                        broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode(),
                        sendWorkPeriod = TmsTransportConstant.SendWorkPeriodEnum.NO.getCode(),
                        compatibleCoopMode = TmsTransportConstant.CompatibleCoopModeEnum.JZ.getCode();
                return  new DrvInfoCacheDto(drvId,coopMode, broadcast,sendWorkPeriod,compatibleCoopMode);
            }
            return getSchedulingResult(drvId,transportModeSet);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public DrvInfoCacheDto calculatQJZPDrvCoopMode(Long drvId, List<TransportGroupBasePO> relationVOS) {
        try {
            if(CollectionUtils.isEmpty(relationVOS)){
                return DrvInfoCacheDto.defaultDTO();
            }
            Set<Integer> transportModeSet = Sets.newHashSet();
            Set<Integer> appStatusSet = Sets.newHashSet();
            for(TransportGroupBasePO relationVO : relationVOS){
                transportModeSet.add(relationVO.getTransportGroupMode());
                appStatusSet.add(relationVO.getApplyStatus());
            }
            //关联至少一个上线“全职司机指派”运力组
            if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode())){
                int coopMode = TmsTransportConstant.DrvCoopModeEnum.DRV_QJJP.getCode(),
                        broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode(),
                        sendWorkPeriod = TmsTransportConstant.SendWorkPeriodEnum.YES.getCode(),
                        compatibleCoopMode = TmsTransportConstant.CompatibleCoopModeEnum.QZ.getCode();
                //老全职+调度
                if(judgeScheduling(transportModeSet)){
                    broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode();
                }
                //老全职+兼职播报
                //老全职+兼职播报+调度
                if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())||
                        (transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())&&
                                judgeScheduling(transportModeSet))){
                    broadcast = TmsTransportConstant.BroadcastEnum.YES.getCode();
                }
                return  new DrvInfoCacheDto(drvId,coopMode, broadcast,sendWorkPeriod,compatibleCoopMode);
            }

            //未关联任何上线“全职司机指派”运力组，但至少关联一个上线的“兼职播报”
            //1、兼职播报
            //2、兼职播报+调度
            if(!transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode()) &&
                    transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())){
                int coopMode = TmsTransportConstant.DrvCoopModeEnum.DRV_JJBB.getCode(),
                        broadcast = TmsTransportConstant.BroadcastEnum.YES.getCode(),
                        sendWorkPeriod = TmsTransportConstant.SendWorkPeriodEnum.NO.getCode(),
                        compatibleCoopMode = TmsTransportConstant.CompatibleCoopModeEnum.JZ.getCode();
                //兼职播报
                if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())||
                        (transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode()) &&
                                judgeScheduling(transportModeSet))){
                    broadcast = TmsTransportConstant.BroadcastEnum.YES.getCode();
                }
                return  new DrvInfoCacheDto(drvId,coopMode, broadcast,sendWorkPeriod,compatibleCoopMode);
            }
            return getSchedulingResult(drvId,transportModeSet);
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    //仅关联至少一个上线的“调度”类
    private DrvInfoCacheDto getSchedulingResult(Long drvId,Set<Integer> transportModeSet){
        //仅关联至少一个上线的“调度”类
        if(judgeScheduling(transportModeSet)
                && !transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode())
                && !transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.JZSJ.getCode())
                && !transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
            int coopMode = TmsTransportConstant.DrvCoopModeEnum.DRV_RGDD.getCode(),
                    broadcast = TmsTransportConstant.BroadcastEnum.NO.getCode(),
                    sendWorkPeriod = TmsTransportConstant.SendWorkPeriodEnum.NO.getCode(),
                    compatibleCoopMode = TmsTransportConstant.CompatibleCoopModeEnum.JZ.getCode();
            return  new DrvInfoCacheDto(drvId,coopMode, broadcast,sendWorkPeriod,compatibleCoopMode);
        }
        return DrvInfoCacheDto.defaultDTO();
    }

    //运力组是否包含调度
    private Boolean judgeScheduling(Set<Integer> transportModeSet){
        if(transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.RGDD.getCode())||
                transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.YXRGDD.getCode())||
                transportModeSet.contains(TmsTransportConstant.TransportGroupModeEnum.DDRGDD.getCode())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    //判断司机是否是报名制运力组
    private Boolean getIsQZSJATmsTransport(Long drvId){
        try {
            //查询司机关联的运力组中是否有报名制运力组
            List<TspTransportGroupDriverRelationPO> relationPOList = relationRepository.queryTransportGroupDriverRelation(Arrays.asList(drvId));
            if(CollectionUtils.isEmpty(relationPOList)){
                return Boolean.FALSE;
            }
            List<Long>  tspTransportGroupIds = relationPOList.stream().map(relationPO->relationPO.getTransportGroupId()).collect(Collectors.toList());

            List<TspTransportGroupPO> tspTransportGroupPOList =  groupRepository.queryTspTransportByIds(tspTransportGroupIds);
            if(CollectionUtils.isEmpty(tspTransportGroupPOList)){
                return Boolean.FALSE;
            }
            //如果有全职报名制运力组 返回true标识
            for(TspTransportGroupPO transportGroupPO : tspTransportGroupPOList){
                if(Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
                    return Boolean.TRUE;
                }
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Boolean.FALSE;
    }

    @Override
    public DrvInfoCacheDto calculateDrvCoopMode(Long id, List<TransportGroupBasePO> list) {
        if(CollectionUtils.isEmpty(list)){
            return DrvInfoCacheDto.defaultDTO();
        }
        Boolean flag = Boolean.FALSE;
        for(TransportGroupBasePO transportGroupBasePO : list){
            if(Objects.equals(transportGroupBasePO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode())){
                flag = Boolean.TRUE;
                break;
            }
        }
        if(flag){
            return calculateQJBMDrvCoopMode(id,list);
        }
        return calculatQJZPDrvCoopMode(id,list);
    }

    @Override
    public Boolean judgeDisableDrvOnlineCity(List<Long> drvCityList) {
        List<Long> configCityList = transportQconfig.getDisableDrvOnlineCityConfigList();
        if(CollectionUtils.isEmpty(configCityList)){
            return Boolean.FALSE;
        }
        List<Long> resultList = (List<Long>) CollectionUtils.intersection(configCityList,drvCityList);
        if(CollectionUtils.isEmpty(resultList)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean judgeOperatorPermission() {
        List<String> operatorList =  transportQconfig.getOperatorPermissionConfigList();
        if(CollectionUtils.isEmpty(operatorList)){
            return Boolean.FALSE;
        }
        Map<String, String> operatorMap =  SessionHolder.getSessionSource();
        if(MapUtils.isEmpty(operatorMap)){
            return Boolean.TRUE;
        }
        String accountId = operatorMap.get("accountId");
        if(StringUtils.isEmpty(accountId)){
            return Boolean.TRUE;
        }
        if(operatorList.contains(accountId)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public Result<Boolean> judgeDrvOnlinePermission(Boolean firstFlag,List<Long> drvCityList,List<Long> vehicleList) {
        if(firstFlag && (judgeDisableDrvOnlineCity((List<Long>) CollectionUtils.union(drvCityList,queryVehicleCity(vehicleList))) && judgeOperatorPermission())){
            return Result.Builder.<Boolean>newResult()
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportcapacitygroupmgt))
                    .withData(false)
                    .build();
        }
        return Result.Builder.<Boolean>newResult().success().build();
    }

    @Override
    public Result<List<Long>> queryPenaltyOfflineDrvIdList(List<Long> drvIdList) {
        List<Long> penaltyOfflineDrvIdList = repository.queryPenaltyOfflineDrvIdList(drvIdList);
        return Result.Builder.<List<Long>>newResult().success().withData(penaltyOfflineDrvIdList).build();
    }

    @Override
    public Result<List<Long>> queryPenaltyFreezeDrvIdList(List<Long> drvIdList) {
        List<TmsDrvFreezePO> tmsDrvFreezePOS = tmsDrvFreezeRepository.queryPenaltyFreezeDrvIds(drvIdList);
        if (CollectionUtils.isEmpty(tmsDrvFreezePOS)) {
            return Result.Builder.<List<Long>>newResult().success().withData(Collections.emptyList()).build();
        }
        List<Long> resDrvIdList = Lists.newArrayListWithExpectedSize(tmsDrvFreezePOS.size());
        for (TmsDrvFreezePO freezePO : tmsDrvFreezePOS) {
            if (freezePO.getPenalizeFreezeHour() != null && freezePO.getPenalizeFreezeHour() > 0 && System.currentTimeMillis() < DateUtils.addHours(freezePO.getFirstFreezeTime(), freezePO.getPenalizeFreezeHour()).getTime()) {
                resDrvIdList.add(freezePO.getDrvId());
            }
        }
        return Result.Builder.<List<Long>>newResult().success().withData(resDrvIdList).build();
    }

    @Override
    public Result<List<String>> queryDrvNameByIdList(List<Long> drvIdList) {
        List<String> nameList = repository.queryDrvNameByIdList(drvIdList);
        return Result.Builder.<List<String>>newResult().success().withData(nameList).build();
    }

    @Override
    public Result<DrvVerifyGraySOADTO> queryDrvVerifyGray(DrvVerifyGraySOARequestType requestType) {
        if(requestType.getDriverId() == null || requestType.getDriverId() <=0){
            return Result.Builder.<DrvVerifyGraySOADTO>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.paramsFail)).build();
        }
        DrvDriverPO drvDriverPO = repository.queryByPk(requestType.getDriverId());
        if(drvDriverPO == null){
            return Result.Builder.<DrvVerifyGraySOADTO>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty)).build();
        }
        DrvVerifyGraySOADTO drvVerifyGraySOADTO = new DrvVerifyGraySOADTO();
        drvVerifyGraySOADTO.setDriverId(requestType.getDriverId());
        drvVerifyGraySOADTO.setVerifyFlag(Boolean.FALSE);
        List<Long> verifyCityIds = transportQconfig.getDrvVerifyCitySwitchList();
        //-1 代表开全量
        if(verifyCityIds.contains(drvDriverPO.getCityId()) || verifyCityIds.contains(-1L)){
            drvVerifyGraySOADTO.setVerifyFlag(Boolean.TRUE);
        }
        return Result.Builder.<DrvVerifyGraySOADTO>newResult().success().withData(drvVerifyGraySOADTO).build();
    }

    @Override
    public Result<QueryDrvAddrModCountSOAResponseType> queryDrvAddrModCount(QueryDrvAddrModCountSOARequestType requestType) {
        DrvDriverPO drvDriverPO = repository.queryByPk(requestType.getDrvId());
        if(drvDriverPO == null){
            return Result.Builder.<QueryDrvAddrModCountSOAResponseType>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.driverNotExist)).build();
        }
        //如果入参月份为空，默认取当前月份
        Integer nowMonth = requestType.getMonth();
        if(nowMonth == null || nowMonth == 0){
            nowMonth = DateUtil.getMonth();
        }
        QueryDrvAddrModCountSOAResponseType responseType = new QueryDrvAddrModCountSOAResponseType();
        try {
            Map<Integer,Integer> drvAddrModCountThresholdMap =  transportQconfig.getDrvAddrModCountThresholdMap();
            responseType.setModCountThreshold(drvAddrModCountThresholdMap.get(nowMonth));
            if(StringUtils.isEmpty(drvDriverPO.getAddrModCount())){
                responseType.setModCount(0);
                responseType.setIsModify(Boolean.TRUE);
                return Result.Builder.<QueryDrvAddrModCountSOAResponseType>newResult().success().withData(responseType).build();
            }
            Map<Integer,Integer> addrModMap = JsonUtil.fromJson(drvDriverPO.getAddrModCount(), new TypeReference<Map<Integer, Integer>>() {
            });
            responseType.setModCount(addrModMap.get(nowMonth));
            responseType.setIsModify(!(responseType.getModCount().intValue() >= responseType.getModCountThreshold().intValue()));
            return Result.Builder.<QueryDrvAddrModCountSOAResponseType>newResult().success().withData(responseType).build();
        }catch (Exception e){
            responseType.setModCount(0);
            return Result.Builder.<QueryDrvAddrModCountSOAResponseType>newResult().fail().withData(responseType).build();
        }
    }

    @Override
    public Result<Boolean> queryDrvHealthPunchPermissions(Long drvId) {
        if(drvId == null || drvId <= 0){
            return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.paramsFail)).build();
        }
        DrvDriverPO drvDriverPO = repository.queryByPk(drvId);
        if(drvDriverPO == null){
            return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportDrvisEmpty)).build();
        }
        List<Long> verifyCityIds = transportQconfig.getDrvHealthPunchCitySwitchList();
        Boolean cityFlag = Boolean.FALSE;
        //-1 代表开全量
        if(verifyCityIds.contains(drvDriverPO.getCityId()) || verifyCityIds.contains(-1L)){
            cityFlag = Boolean.TRUE;
        }
        return Result.Builder.<Boolean>newResult().success().withData(cityFlag).build();
    }

    @Override
    public SimpleDriverInfoDTO querySimpleDriver(Long driverId) {
        DrvDriverPO drvDriverPO = repository.queryByPk(driverId);
        if(drvDriverPO == null){
            return null;
        }
        SimpleDriverInfoDTO simpleDriverInfoDTO = new SimpleDriverInfoDTO();
        String drvName = org.apache.commons.lang3.StringUtils.isEmpty(drvDriverPO.getDrvLicenseName())?drvDriverPO.getDrvName():drvDriverPO.getDrvLicenseName();
        String drvIdcard = org.apache.commons.lang3.StringUtils.isEmpty(drvDriverPO.getDrvLicenseNumber())?drvDriverPO.getDrvIdcard():drvDriverPO.getDrvLicenseNumber();
        simpleDriverInfoDTO.setCityId(drvDriverPO.getCityId());
        simpleDriverInfoDTO.setDriverId(drvDriverPO.getDrvId());
        simpleDriverInfoDTO.setDriverIdCard(TmsTransUtil.decrypt(drvIdcard,KeyType.Identity_Card));
        simpleDriverInfoDTO.setDriverName(drvName);
        simpleDriverInfoDTO.setSupplierId(drvDriverPO.getSupplierId());
        return simpleDriverInfoDTO;
    }

    @Override
    public List<DrvBase> queryDrvBaseResource(QueryDrvResourceConditionDTO condition) {
        List<DrvDriverPO> driverPOS = repository.queryDrvResourceByCondition(condition);
        return drvResourceConverter.convertDrvBase(driverPOS);
    }

    @Override
    public List<OldDriverInfo> queryHistoryDrvResource(QueryHistoryDrvConditionDTO condition) {
        List<DrvHistoryDriverPO> driverPOS = historyDrvRepository.queryHistoryDrvResourceByCondition(condition);
        return HistoryDrvResourceConverter.convertOldDriver(driverPOS);
    }

    @Override
    public Map<Long,Long> checkApplyDriverLeaveDuration(List<Long> drvList,Timestamp beginCheckDateTime,Timestamp endCheckDateTime,Map<Long,Timestamp> drvCalculateBeginTimeMap, TransportGroupDriverApplyProcessDTO applyProcessDTO) {
        Map<Long,Long> drvDurationMap = initDrvDurationMap(drvList);
        if(CollectionUtils.isEmpty(drvList)){
            return drvDurationMap;
        }
        //查询司机请假记录，查询2个月前的记录
        List<DrvDriverLeavePO> drvDriverLeavePOS = leaveRepository.queryDrvLeaveRecord(drvList);
        if(CollectionUtils.isEmpty(drvDriverLeavePOS)){
            return calculationDrvFrezzeHour(drvList,beginCheckDateTime,endCheckDateTime,drvDurationMap,drvCalculateBeginTimeMap, applyProcessDTO);
        }
        return calculationDrvLeave(drvList,drvDriverLeavePOS,beginCheckDateTime,endCheckDateTime,drvCalculateBeginTimeMap, applyProcessDTO);
    }

    @Override
    public Map<Long, Boolean> checkGlobalApplyDriverLeaveDuration(GlobalApplyDriverLeaveDurationVO driverLeaveDurationVO, TransportGroupDriverApplyProcessDTO applyProcessDTO) {
        Map<Long,Boolean> drvResultDurationMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(driverLeaveDurationVO.getDrvList())){
            return drvResultDurationMap;
        }
        //初始化司机为请假时长未超时
        for(Long drvId : driverLeaveDurationVO.getDrvList()){
            drvResultDurationMap.put(drvId,Boolean.FALSE);
        }
        //查询司机请假记录，查询2个月前的记录
        List<DrvDriverLeavePO> drvDriverLeavePOS = leaveRepository.queryDrvLeaveRecord(driverLeaveDurationVO.getDrvList());
        if(CollectionUtils.isEmpty(drvDriverLeavePOS)){
            return calculationGlobalFreezeDuration(driverLeaveDurationVO, applyProcessDTO);
        }

        //计算赛道前30天的逻辑
        applyProcessDTO.setRule(TmsTransportConstant.TransportDriverApplyRuleEnum.A);
        Map<Long, Long> calculationAMap = calculationDrvLeave(driverLeaveDurationVO.getDrvList(),drvDriverLeavePOS, driverLeaveDurationVO.getBeginBeforeCheckDateTime(), driverLeaveDurationVO.getEndBeforeCheckDateTime(), null, applyProcessDTO);
        logger.info("checkGlobalApplyDriverLeaveDurationBeforeA","result:{}",JsonUtil.toJson(calculationAMap));
        for(Long drvId : driverLeaveDurationVO.getDrvList()){
            drvResultDurationMap.put(drvId,calculationAMap.get(drvId) >driverLeaveDurationVO.getDurationA().longValue()?Boolean.TRUE:Boolean.FALSE);
            if (drvResultDurationMap.get(drvId)) {// 如果超过阈值
                //记录失败信息
                applyProcessDTO.setApplyResult(drvId, TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED, TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT,
                  TmsTransportConstant.TransportDriverApplyRuleEnum.A.getText(), driverLeaveDurationVO.getDurationA());
            }
        }

        // 剔除请假冻结时长没有触发阈值的记录
        applyProcessDTO.removeDriverLeaveAndFreezeDurationNotTriggerThreshold();

        //计算赛道后30天的逻辑
        applyProcessDTO.setRule(TmsTransportConstant.TransportDriverApplyRuleEnum.B);
        Map<Long, Long> calculationBMap = calculationDrvLeave(driverLeaveDurationVO.getDrvList(),drvDriverLeavePOS, driverLeaveDurationVO.getBeginAfterCheckDateTime(), driverLeaveDurationVO.getEndAfterCheckDateTime(), null, applyProcessDTO);
        logger.info("checkGlobalApplyDriverLeaveDurationAfterB","result:{}",JsonUtil.toJson(calculationBMap));
        for(Long drvId : driverLeaveDurationVO.getDrvList()){
            if(!drvResultDurationMap.get(drvId)){
                drvResultDurationMap.put(drvId,calculationBMap.get(drvId) > driverLeaveDurationVO.getDurationB().longValue()?Boolean.TRUE:Boolean.FALSE);
                if (drvResultDurationMap.get(drvId)) { // 如果超过阈值
                    //记录失败信息
                    applyProcessDTO.setApplyResult(drvId, TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED, TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT,
                      TmsTransportConstant.TransportDriverApplyRuleEnum.B.getText(), driverLeaveDurationVO.getDurationB());
                }
            }
        }
        return drvResultDurationMap;
    }

    @Override
    public int queryDrvBaseResourceCount(QueryDrvResourceConditionDTO condition) {
        return repository.queryDrvResourceCountByCondition(condition);
    }

    public  Map<Long, Boolean> calculationGlobalFreezeDuration(GlobalApplyDriverLeaveDurationVO driverLeaveDurationVO, TransportGroupDriverApplyProcessDTO applyProcessDTO){
        Map<Long,Boolean> drvResultDurationMap = Maps.newHashMap();
        //初始化司机为请假时长未超时
        for(Long drvId : driverLeaveDurationVO.getDrvList()){
            drvResultDurationMap.put(drvId,Boolean.FALSE);
        }
        List<DrvFreezeRecordPO> freezeRecordList = drvFreezeRecordRepository.queryDrvFreezeRecordList(driverLeaveDurationVO.getDrvList());
        if(CollectionUtils.isEmpty(freezeRecordList)){
            return drvResultDurationMap;
        }

        Map<Long,Long> drvDurationMap = initDrvDurationMap(driverLeaveDurationVO.getDrvList());
        //计算赛道前30天的逻辑
        applyProcessDTO.setRule(TmsTransportConstant.TransportDriverApplyRuleEnum.A);
        Map<Long, Long> calculationAMap = calculationDrvFreeze(freezeRecordList, driverLeaveDurationVO.getBeginBeforeCheckDateTime(), driverLeaveDurationVO.getEndBeforeCheckDateTime(),null,drvDurationMap, applyProcessDTO);
        logger.info("calculationGlobalFreezeDurationBeforeA","result:{}",JsonUtil.toJson(calculationAMap));
        for(Long drvId : driverLeaveDurationVO.getDrvList()){
            drvResultDurationMap.put(drvId,calculationAMap.get(drvId) >driverLeaveDurationVO.getDurationA().longValue()?Boolean.TRUE:Boolean.FALSE);
            if(drvResultDurationMap.get(drvId)) {
                //记录失败信息
                applyProcessDTO.setApplyResult(drvId, TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED, TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT,
                  TmsTransportConstant.TransportDriverApplyRuleEnum.A.getText(), driverLeaveDurationVO.getDurationA());
            }
        }

        drvDurationMap =  initDrvDurationMap(driverLeaveDurationVO.getDrvList());
        //计算赛道后30天的逻辑
        applyProcessDTO.setRule(TmsTransportConstant.TransportDriverApplyRuleEnum.B);
        Map<Long, Long> calculationBMap = calculationDrvFreeze(freezeRecordList, driverLeaveDurationVO.getBeginAfterCheckDateTime(), driverLeaveDurationVO.getEndAfterCheckDateTime(),null,drvDurationMap, applyProcessDTO);
        logger.info("calculationGlobalFreezeDurationAfterB","result:{}",JsonUtil.toJson(calculationBMap));
        for(Long drvId : driverLeaveDurationVO.getDrvList()){
            if(!drvResultDurationMap.get(drvId)){
                drvResultDurationMap.put(drvId,calculationBMap.get(drvId) > driverLeaveDurationVO.getDurationB().longValue()?Boolean.TRUE:Boolean.FALSE);

                if(drvResultDurationMap.get(drvId)) {
                    //记录失败信息
                    applyProcessDTO.setApplyResult(drvId, TmsTransportConstant.ApplyStatusEnum.APPLY_FAILED, TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT,
                      TmsTransportConstant.TransportDriverApplyRuleEnum.B.getText(), driverLeaveDurationVO.getDurationB());
                }
            }
        }
        return drvResultDurationMap;

    }

    //请假或者冻结是按天的，司机真实工作时长是每天12小时，所以如果司机请假或者冻结1天（这里的冻结指有供应商参与的冻结），每个自然日超过12小时，则按照12小时计算：
    //举例：请假周期为 3.12 23：00 - 3.14 10：00，拆分为
    //1、3.12 23：00 - 3.13 0：00，共1个自然日，小于12小时，计1小时
    //2、3.13 0：00 - 3.14 0：00，共1个自然日，大于12小时，计12小时
    //3、3.14 0：00 - 3.14 10：00，共1个自然日，小于12小时，计10小时

    public Map<Long, Long> calculationDrvLeave(List<Long> drvIds, List<DrvDriverLeavePO> drvDriverLeavePOS, Timestamp beginCheckDateTime, Timestamp endCheckDateTime, Map<Long, Timestamp> drvCalculateBeginTimeMap, TransportGroupDriverApplyProcessDTO applyProcessDTO) {
        Map<Long,Long> drvDurationMap = initDrvDurationMap(drvIds);
        if (CollectionUtils.isEmpty(drvDriverLeavePOS)) {
            return drvDurationMap;
        }
        Map<Long, List<DrvDriverLeavePO>> longListMap = drvDriverLeavePOS.stream().collect(Collectors.groupingBy(DrvDriverLeavePO::getDrvId));
        for (Map.Entry<Long, List<DrvDriverLeavePO>> entry : longListMap.entrySet()) {
            long totalDuration = 0;
            List<DrvDriverLeavePO> entryValue = entry.getValue();
            for (DrvDriverLeavePO drvDriverLeave : entryValue) {
                if (MapUtils.isNotEmpty(drvCalculateBeginTimeMap) && drvCalculateBeginTimeMap.get(entry.getKey()) != null) {
                    beginCheckDateTime = drvCalculateBeginTimeMap.get(entry.getKey());
                }
                Timestamp leaveBeginTime = drvDriverLeave.getLeaveBeginTime();
                Timestamp leaveEndTime = drvDriverLeave.getLeaveEndTime();
                if (drvDriverLeave.getDatachangeDeltime() != null && drvDriverLeave.getDatachangeDeltime().before(leaveEndTime)) {
                    leaveEndTime = drvDriverLeave.getDatachangeDeltime();
                }
                //过滤请假结束时间小于计算开始时间的数据,过滤掉检查结束时间小于请假开始时间的记录
                if(leaveEndTime.before(beginCheckDateTime) || endCheckDateTime.before(leaveBeginTime)){
                    continue;
                }
                //过滤提前销假的记录
                if(drvDriverLeave.getDatachangeDeltime() != null && drvDriverLeave.getDatachangeDeltime().before(leaveBeginTime)){
                    continue;
                }
                //如果请假开始时间小于计算的开始时间，则取计算的开始时间
                if (drvDriverLeave.getLeaveBeginTime().before(beginCheckDateTime)) {
                    leaveBeginTime = beginCheckDateTime;
                }
                //如果有销假时间,结果时间以销假时间为准，如果销假时间大于计算的结果时间，为取计算的结果时间
                if (drvDriverLeave.getDatachangeDeltime() != null && drvDriverLeave.getDatachangeDeltime().after(endCheckDateTime)) {
                    leaveEndTime = endCheckDateTime;
                }
                if (drvDriverLeave.getDatachangeDeltime() == null && drvDriverLeave.getLeaveEndTime().after(endCheckDateTime)) {
                    leaveEndTime = endCheckDateTime;
                }
                //计算每条请假记录时长
                long duration = calculateLeaveHour(leaveBeginTime, leaveEndTime);
                totalDuration += duration;
                logger.info("leave-count-" + entry.getKey(), "totalLeveHour:{}, duration{}, time:{}--{}", totalDuration, duration, leaveBeginTime, leaveEndTime);

                applyProcessDTO.setLeaveHour(drvDriverLeave.getDrvId(), leaveBeginTime, leaveEndTime, duration);
            }
            drvDurationMap.put(entry.getKey(), totalDuration);
            applyProcessDTO.setCalculateTimeRange(entry.getKey(), beginCheckDateTime, endCheckDateTime);
        }
        return calculationDrvFrezzeHour(drvIds, beginCheckDateTime, endCheckDateTime, drvDurationMap, drvCalculateBeginTimeMap, applyProcessDTO);
    }
    public long calculateLeaveHour(Timestamp leaveBeginTime, Timestamp leaveEndTime) {
        //判断是否跨天
        String leaveBeginDate = DateUtil.timestampToString(leaveBeginTime, DateUtil.YYYYMMDD);
        String leaveEndDate = DateUtil.timestampToString(leaveEndTime, DateUtil.YYYYMMDD);
        //判断两个日期是否跨天
        Boolean isSameDay = org.apache.commons.lang.time.DateUtils.isSameDay(leaveBeginTime, leaveEndTime);
        //计算请假时长
        long durationHour = DateUtil.compareHourRoundUp(leaveBeginTime, leaveEndTime);
        long onlyDurationHour = 0;
        if (isSameDay) {
            //默认一天工作时长是12天
            onlyDurationHour = 12;
            if (durationHour <= 12) {
                onlyDurationHour = durationHour;
            }
        } else {
            if (durationHour <= 12) {
                onlyDurationHour = durationHour;
            }
            //超过12小时，则要单独算，开始时间或结束时间哪个超个12小时
            if (durationHour > 12) {
                //判断是否跨多天
                long intervalDays = DateUtil.compareDay(DateUtil.string2Timestamp(leaveBeginDate, DateUtil.YYYYMMDD), DateUtil.string2Timestamp(leaveEndDate, DateUtil.YYYYMMDD));
                //中间没有跨多天
                if (intervalDays == 1) {
                    onlyDurationHour = calculateBeginEndHour(leaveBeginTime, leaveBeginDate, leaveEndDate, leaveEndTime);
                } else {
                    //计算两个时间相差的天数,关得到一半的小时数
                    Date beginAfterOneday = DateUtil.dayDisplacement(DateUtil.stringToDate(leaveBeginDate, DateUtil.YYYYMMDD), 1);
                    long days = DateUtil.compareDay(DateUtil.dateToTimestamp(beginAfterOneday), DateUtil.string2Timestamp(leaveEndDate, DateUtil.YYYYMMDD));
                    long leaveHours = days * 12;
                    //三个小时数相加
                    onlyDurationHour = calculateBeginEndHour(leaveBeginTime, leaveBeginDate, leaveEndDate, leaveEndTime) + leaveHours;
                }
            }
        }
        return onlyDurationHour;
    }

    public long calculateBeginEndHour(Timestamp leaveBeginTime,String leaveBeginDate,String leaveEndDate,Timestamp leaveEndTime){
        long totalBeginDuration = 0;
        long totalEndDuration = 0;
        //开始时间 在 当前的请假数，如果请假数大于12小时，则取12
        long beginDurationHour = DateUtil.compareHourRoundUp(leaveBeginTime, DateUtil.string2Timestamp(leaveBeginDate + " 23:59:59",DateUtil.YYYYMMDDHHMMSS));
        totalBeginDuration = beginDurationHour;
        if(beginDurationHour > 12){
            totalBeginDuration = 12;
        }
        //结束时间 在 当前的请假数，如果请假数大于12小时，则取12
        long endDurationHour = DateUtil.compareHourRoundUp(DateUtil.string2Timestamp(leaveEndDate + " 00:00:00",DateUtil.YYYYMMDDHHMMSS), leaveEndTime);
        totalEndDuration = endDurationHour;
        if(endDurationHour > 12){
            totalEndDuration = 12;
        }
        return totalBeginDuration + totalEndDuration;
    }

    public  Map<Long,Long> calculationDrvFrezzeHour(List<Long> drvList,Timestamp beginCheckDateTime,Timestamp endCheckDateTime,Map<Long,Long> drvDurationMap,Map<Long,Timestamp> drvCalculateBeginTimeMap, TransportGroupDriverApplyProcessDTO applyProcessDTO){
        //查询两个月前的冻结记录
        List<DrvFreezeRecordPO> freezeRecordList = drvFreezeRecordRepository.queryDrvFreezeRecordList(drvList);
        if(CollectionUtils.isEmpty(freezeRecordList)){
            return drvDurationMap;
        }
        return calculationDrvFreeze(freezeRecordList, beginCheckDateTime, endCheckDateTime, drvCalculateBeginTimeMap,drvDurationMap, applyProcessDTO);
    }

    public Map<Long,Long> calculationDrvFreeze(List<DrvFreezeRecordPO> freezeRecordList,Timestamp beginCheckDateTime,Timestamp endCheckDateTime,Map<Long,Timestamp> drvCalculateBeginTimeMap,Map<Long,Long> drvDurationMap, TransportGroupDriverApplyProcessDTO applyProcessDTO){
        if(CollectionUtils.isEmpty(freezeRecordList)){
            return drvDurationMap;
        }
        Map<Long,List<DrvFreezeRecordPO>> longListMap = freezeRecordList.stream().collect(Collectors.groupingBy(DrvFreezeRecordPO::getDrvId));
        for(Map.Entry<Long,List<DrvFreezeRecordPO>> entry : longListMap.entrySet()){
            long totalDuration = 0;
            if(MapUtils.isNotEmpty(drvCalculateBeginTimeMap) && drvCalculateBeginTimeMap.get(entry.getKey())!=null){
                beginCheckDateTime = drvCalculateBeginTimeMap.get(entry.getKey());
            }
            List<DrvFreezeRecordPO> entryValue = entry.getValue();
            //按冻结开始时间为分组，计算每组的冻结时长
            Map<Timestamp,List<DrvFreezeRecordPO>> entryValueMap = entryValue.stream().collect(Collectors.groupingBy(DrvFreezeRecordPO::getFirstFreezeTime));
            for(Map.Entry<Timestamp,List<DrvFreezeRecordPO>> entry1 : entryValueMap.entrySet()){
                List<DrvFreezeRecordPO> entryValue1 = entry1.getValue();
                List<Integer> freezeFromList = Lists.newArrayList();
                int totalFreezeHour = 0;
                long unFreezeHour = 0;
                for(DrvFreezeRecordPO freezeRecordPO : entryValue1){
                    //筛选出冻结来源
                    if(Objects.equals(TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(),freezeRecordPO.getFreezeStatus()) && freezeRecordPO.getFreezeFrom().length() == 1){
                        freezeFromList.add(Integer.valueOf(freezeRecordPO.getFreezeFrom()));
                    }
                    if(Objects.equals(TmsTransportConstant.FreezeStatusEnum.FREEZE.getValue(),freezeRecordPO.getFreezeStatus())){
                        totalFreezeHour += freezeRecordPO.getFreezeHour();
                    }
                }
                //如果该组数据没有供应商参与冻结，则不计算
                if(!freezeFromList.contains(CommonEnum.FreezeOPFromEnum.SUPPLIER.getValue())){
                    continue;
                }

                Timestamp firstFreezeTime = entry1.getKey();
                //计算冻结的结束时间
                Date freezeEndDateTime = DateUtil.getDayShift(firstFreezeTime,totalFreezeHour);
                //如果冻结结束时间小于计算的开始时间，则过滤掉
                if(beginCheckDateTime.after(freezeEndDateTime)){
                    continue;
                }
                //如果冻结开始时间小于计算的开始时间，则取计算的开始时间为冻结的起始时间
                if(firstFreezeTime.before(beginCheckDateTime)){

                    firstFreezeTime = beginCheckDateTime;
                }
                if(freezeEndDateTime.after(endCheckDateTime)){
                    freezeEndDateTime = endCheckDateTime;
                }
                totalFreezeHour = (int)DateUtil.compareHour(firstFreezeTime,DateUtil.dateToTimestamp(freezeEndDateTime));
                for(DrvFreezeRecordPO freezeRecordPO : entryValue1){
                    //如果有手动解冻，则要去掉解冻时间到-到最后的小时数
                    if(Objects.equals(TmsTransportConstant.FreezeStatusEnum.UNFRREZE.getValue(),freezeRecordPO.getFreezeStatus()) && freezeRecordPO.getUnfreezeTime()!=null){
                        //人工解冻时间小于检查开始时间，则表示该冻结不算
                        if(freezeRecordPO.getUnfreezeTime().before(beginCheckDateTime)){
                            totalFreezeHour = 0;
                            break;
                        }
                        //只计算解冻时间小于冻结结束时间
                        if(freezeRecordPO.getUnfreezeTime().before(freezeEndDateTime)){
                            unFreezeHour = DateUtil.compareHour(freezeRecordPO.getUnfreezeTime(),DateUtil.dateToTimestamp(freezeEndDateTime));
                            logger.info("unfreeze-" + entry.getKey(), "totalFreezeHour:{}, unFreezeHour{}, unfreeze-time:{}--{}", totalFreezeHour, unFreezeHour, freezeRecordPO.getUnfreezeTime(), freezeEndDateTime);
                        }
                    }
                }
                long freezeHour = totalFreezeHour - unFreezeHour;
                logger.info("freeze-count-" + entry.getKey(), "totalFreezeHour:{}, unFreezeHour:{}, time:{}--{}", totalFreezeHour, unFreezeHour, firstFreezeTime, freezeEndDateTime);
                totalDuration += freezeHour;
                applyProcessDTO.setFreezeHour(entry.getKey(), firstFreezeTime, new Timestamp(freezeEndDateTime.getTime()), freezeHour);
            }
            drvDurationMap.put(entry.getKey(),drvDurationMap.get(entry.getKey()) + totalDuration);
            applyProcessDTO.setCalculateTimeRange(entry.getKey(), beginCheckDateTime, endCheckDateTime);
        }
        return drvDurationMap;
    }

    public Map<Long,Long> initDrvDurationMap(List<Long> drvIds){
        Map<Long,Long> drvDurationMap = Maps.newHashMap();
        for(Long drvid : drvIds){
            drvDurationMap.put(drvid,0L);
        }
        return drvDurationMap;
    }
    @Override
    public Result<Boolean> drvDispatchCityGray(Long cityId) {
        String grayCityStr = transportQconfig.getDrvDispatchCityGrayConfig();
        if(StringUtils.isEmpty(grayCityStr)){
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        }
        Set<Long> cityIdList = BaseUtil.getLongSet(grayCityStr);
        //-1 代表开全量
        if(cityIdList.contains(cityId) || cityIdList.contains(-1L)){
            return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
    }

    @Override
    public Result<List<QueryDrvDispatchListSOAVO>> queryDrvDispatchList(QueryDrvDispatchListSOARequestType requestType) {
        //未登录用户不可查询接口
        if(StringUtils.isEmpty(SessionHolder.getRestSessionAccountId())){
            return Result.Builder.<List<QueryDrvDispatchListSOAVO>>newResult().fail().withCode("403").build();
        }
        List<DrvDispatchRelationPO> drvDispatchRelationPOS = drvDispatchRelationRepository.queryDrvDispatchSupplierIds(requestType.getDrvIds());
        if (CollectionUtils.isEmpty(drvDispatchRelationPOS)) {
            return Result.Builder.<List<QueryDrvDispatchListSOAVO>>newResult().success().withData(Lists.newArrayList()).build();
        }
        List<QueryDrvDispatchListSOAVO> dispatchList = Lists.newArrayList();
        for (DrvDispatchRelationPO relationPo :drvDispatchRelationPOS
        ) {
            QueryDrvDispatchListSOAVO dispatchVo = new QueryDrvDispatchListSOAVO();
            dispatchVo.setId(relationPo.getId());
            dispatchVo.setDrvId(relationPo.getDrvId());
            dispatchVo.setSupplierId(relationPo.getSupplierId());
            dispatchVo.setSupplierName(enumRepository.getSupplierName(relationPo.getSupplierId()));
            dispatchVo.setCorrelationType(relationPo.getRelationType());
            dispatchList.add(dispatchVo);
        }
        return Result.Builder.<List<QueryDrvDispatchListSOAVO>>newResult().success().withData(dispatchList).build();
    }

    @Override
    public Map<Long, List<Long>> queryDrvDispatchSupplierIds(Set<Long> drvIdSet) {
        if(CollectionUtils.isEmpty(drvIdSet)){
            return Collections.emptyMap();
        }
        try {
            List<DrvDispatchRelationPO> dispatchRelationPOList = drvDispatchSupplierRelationgateway.queryDrvDispatchSupplierIds(new ArrayList<>(drvIdSet));
            if(CollectionUtils.isEmpty(dispatchRelationPOList)){
                return Collections.emptyMap();
            }
            return  dispatchRelationPOList.stream().collect(Collectors.groupingBy(DrvDispatchRelationPO::getDrvId,Collectors.mapping(DrvDispatchRelationPO::getSupplierId,Collectors.toList())));
        }catch (Exception e){
            logger.error("queryDrvDispatchSupplierIdsCacheError","drvIdSet:{}",e);
            return Collections.emptyMap();
        }
    }


    public Boolean gayMethod(String qconfigValue,Long supplierId){
        if(StringUtils.isEmpty(qconfigValue) || supplierId == null || supplierId <= 0){
            return Boolean.FALSE;
        }
        Set<Long> supplierIdList = BaseUtil.getLongSet(qconfigValue);
        //-1 代表开全量
        if(supplierIdList.contains(supplierId) || supplierIdList.contains(-1L)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private List<Long> queryVehicleCity(List<Long> vehicleList){
        try {
            List<VehVehiclePO> vehVehiclePOList = vehicleRepository.queryVehVehicleByIds(vehicleList);
            if(CollectionUtils.isEmpty(vehVehiclePOList)){
                return Collections.emptyList();
            }
            List<Long> vehicleCity = Lists.newArrayList();
            for(VehVehiclePO vehiclePO : vehVehiclePOList){
                vehicleCity.add(vehiclePO.getCityId());
            }
            return vehicleCity;
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    private QueryDrvByMuSelDO buildQueryDrvByMuSelDO(QueryDrvByMuSelConditionsSOARequestType requestType){
        QueryDrvByMuSelDO muSelDO = new QueryDrvByMuSelDO();
        BeanUtils.copyProperties(requestType,muSelDO);
        PaginatorDTO paginationDTO  = requestType.getPaginator();
        if(paginationDTO == null || paginationDTO.getPageNo() == null || paginationDTO.getPageSize() == null){
            muSelDO.setPage(1);
            muSelDO.setPageSize(20000000);
        }else{
            muSelDO.setPage(paginationDTO.getPageNo());
            muSelDO.setPageSize(paginationDTO.getPageSize());
        }
        muSelDO.setRegistStartDate(DateUtil.string2Timestamp(requestType.getRegistStartDate(),DateUtil.YYYYMMDDHHMMSS));
        muSelDO.setRegistEndDate(DateUtil.string2Timestamp(requestType.getRegistEndDate(),DateUtil.YYYYMMDDHHMMSS));
        muSelDO.setProLineIdList(productionLineUtil.getIncludeProductionLineList(requestType.getProductLines()));
        return muSelDO;

    }

    /**
     * 查询司机运力组关联表,获取司机对应的运力组
     *
     * @param drvIdSet
     * @return
     */
    private Map<Long, List<TransportGroupBasePO>> getTransportIdsMap(Set<Long> drvIdSet) {
        List<TransportGroupBasePO> relationPOS = transportGroupQueryService.queryDriverGroupRelationPO(new ArrayList<>(drvIdSet),Lists.newArrayList());
        Map<Long, List<TransportGroupBasePO>> transportIdsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(relationPOS)) {
            transportIdsMap = relationPOS.stream().collect(Collectors.groupingBy(TransportGroupBasePO::getDrvId, Collectors.toList()));
        }
        return transportIdsMap;
    }

    /**
     * 获取车辆信息
     * @param vehicleIdSet
     * @return
     */
    private Map<Long, VehVehiclePO> getVehVehicleMap(Set<Long> vehicleIdSet) {
        try {
            List<VehVehiclePO> vehVehiclePOS = vehicleRepository.queryVehVehicleByIds(new ArrayList<>(vehicleIdSet));
            Map<Long, VehVehiclePO> vehMap = Maps.newHashMap();
            for (VehVehiclePO vehVehiclePO : vehVehiclePOS) {
                vehMap.put(vehVehiclePO.getVehicleId(), vehVehiclePO);
            }
            return vehMap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Collections.emptyMap();
    }

    /**
     * 司机信息封装
     * @param drvDriverPOS
     * @param transportIdsMap
     * @param vehMap
     * @return
     */
    private List<QueryDrvListByAppResponseDTO> getData(List<DrvDriverPO> drvDriverPOS, Map<Long, List<TransportGroupBasePO>> transportIdsMap, Map<Long, VehVehiclePO> vehMap) {
        List<QueryDrvListByAppResponseDTO> data = Lists.newArrayList();
        for (DrvDriverPO driverPO : drvDriverPOS) {
            VehVehiclePO vehVehiclePO = vehMap.get(driverPO.getVehicleId());
            QueryDrvListByAppResponseDTO responseDTO = new QueryDrvListByAppResponseDTO();
            BeanUtils.copyProperties(driverPO, responseDTO);
            responseDTO.setVehicleFullImg(vehVehiclePO == null ? "" : vehVehiclePO.getVehicleFullImg());
            responseDTO.setIsEnergy(0);
            if (vehVehiclePO != null && vehVehiclePO.getVehicleEnergyType() == 64) {
                responseDTO.setIsEnergy(1);
            }
            List<Long> transportIds = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(transportIdsMap.get(driverPO.getDrvId()))){
                transportIdsMap.get(driverPO.getDrvId()).forEach(t -> transportIds.add(t.getTransportGroupId()));
            }
            responseDTO.setVehicleColorId(vehVehiclePO == null ? 0L : vehVehiclePO.getVehicleColorId());
            responseDTO.setVehicleBrandId(vehVehiclePO == null ? 0L : vehVehiclePO.getVehicleBrandId());
            responseDTO.setVehicleSeries(vehVehiclePO == null ? 0L : vehVehiclePO.getVehicleSeries());
            responseDTO.setVehicleTypeId(vehVehiclePO == null ? 0L : vehVehiclePO.getVehicleTypeId());
            responseDTO.setTransportGroupIds(transportIds);
            responseDTO.setDatachangeCreatetime(DateUtil.dateToString(driverPO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            responseDTO.setDatachangeLasttime(DateUtil.dateToString(driverPO.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
            responseDTO.setDrvPhone(control.getSensitiveData(driverPO.getDrvPhone(),KeyType.Phone));
            responseDTO.setEmail(control.getSensitiveData(driverPO.getEmail(),KeyType.Mail));
            responseDTO.setDrvIdcard(control.getSensitiveData(driverPO.getDrvIdcard(),KeyType.Identity_Card));
            responseDTO.setVendor(enumRepository.getSupplierName(driverPO.getSupplierId()));
            DrvInfoCacheDto infoCacheDto = calculateDrvCoopMode(driverPO.getDrvId(),transportIdsMap.get(driverPO.getDrvId()));
            if (infoCacheDto != null) {
                if (infoCacheDto.getCoopMode() != null) {
                    responseDTO.setCoopMode(infoCacheDto.getCoopMode());
                }
                if (infoCacheDto.getBroadcast() != null) {
                    responseDTO.setBroadcast(infoCacheDto.getBroadcast());
                }
                if (infoCacheDto.getIsSendWorkPeriod() != null) {
                    responseDTO.setIsSendWorkPeriod(infoCacheDto.getIsSendWorkPeriod());
                }
                if (infoCacheDto.getCompatibleCoopMode() != null) {
                    responseDTO.setCompatibleCoopMode(infoCacheDto.getCompatibleCoopMode());
                }
            }
            if (!Strings.isNullOrEmpty(driverPO.getDrvAddr()) && driverPO.getDrvAddr().startsWith("{") && driverPO.getDrvAddr().endsWith("}")) {
                Map<String,Object> params = JsonUtil.fromJson(driverPO.getDrvAddr(),new TypeReference<Map<String, Object>>() {});
                if (params != null) {
                    Object temp;
                    if ((temp = params.get("name")) != null) {
                        responseDTO.setHomeName(String.valueOf(temp));
                    }
                    if ((temp = params.get("address")) != null) {
                        responseDTO.setAddress(String.valueOf(temp));
                    }
                    if ((temp = params.get("longitude")) != null) {
                        responseDTO.setLongitude(Double.valueOf(temp.toString()));
                    }
                    if ((temp = params.get("latitude")) != null) {
                        responseDTO.setLatitude(Double.valueOf(temp.toString()));
                    }
                }
            }
            responseDTO.setDrvProductionLineCodeList(getCategoryList(driverPO.getCategorySynthesizeCode()));
            responseDTO.setOnlineTime(DateUtil.dateToString(driverPO.getOnlineTime(), DateUtil.YYYYMMDDHHMMSS));
            responseDTO.setRaisingPickUp(driverPO.getRaisingPickUp());
            data.add(responseDTO);
        }
        return data;
    }

    /**
     * 运力组数据封装
     * @param transportIdSet
     * @return
     */
    private List<TransportGroupResponseDTO> getTransportGroupData(Set<Long> transportIdSet) {
        if (CollectionUtils.isEmpty(transportIdSet)) {
            return Collections.emptyList();
        }
        try {

            Map<Integer,String> transModeMap = enumRepository.getTransportGroupMode();
            List<TspTransportGroupPO> transportGroupPOS = groupRepository.queryTspTransportByIds(new ArrayList<>(transportIdSet));
            if (CollectionUtils.isEmpty(transportGroupPOS)) {
                return Collections.emptyList();
            }
            transportGroupPOS.sort(Comparator.comparing(TspTransportGroupPO::getGroupStatus,Comparator.naturalOrder()).thenComparing(TspTransportGroupPO::getDatachangeLasttime,Comparator.reverseOrder()));
            List<TransportGroupResponseDTO> tgrList = Lists.newArrayList();
            for (TspTransportGroupPO groupPO : transportGroupPOS) {
                TransportGroupResponseDTO responseDTO = new TransportGroupResponseDTO();
                BeanUtils.copyProperties(groupPO, responseDTO);
                if (Objects.isNull(transModeMap)) {
                    responseDTO.setTransportGroupModeName("");
                }else {
                    String transMode = transModeMap.get(groupPO.getTransportGroupMode());
                    responseDTO.setTransportGroupModeName(StringUtils.isEmpty(transMode)?"":transMode);
                }
                responseDTO.setGroupStatusName(enumRepository.getTransportGroupStatusMap().get(groupPO.getGroupStatus()));
                responseDTO.setPointCityName(enumRepository.getCityName(groupPO.getPointCityId()));
                List<Integer> lineList = productionLineUtil.getShowProductionLineList(groupPO.getCategorySynthesizeCode());
                if (CollectionUtils.isNotEmpty(lineList)) {
                    responseDTO.setProLineId(lineList.get(0));
                    responseDTO.setProLineName(productionLineUtil.getProductionLineNames(groupPO.getCategorySynthesizeCode()));
                }
                responseDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(groupPO.getVehicleTypeId()));
                tgrList.add(responseDTO);
            }
            return tgrList;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }

    public List<CategorySOADTO> getCategoryList(Integer lineCode) {
        List<Integer> showCodeList = productionLineUtil.getShowProductionLineList(lineCode);
        if (CollectionUtils.isEmpty(showCodeList)) {
            return Collections.emptyList();
        }
        List<CategorySOADTO> categorySOADTOList = Lists.newArrayListWithExpectedSize(showCodeList.size());
        for (Integer code : showCodeList) {
            if (code == null) {
                continue;
            }
            CategorySOADTO categorySOADTO = new CategorySOADTO();
            categorySOADTO.setId(code);
            categorySOADTO.setName(productionLineUtil.getCategoryName(code.longValue()));
            categorySOADTOList.add(categorySOADTO);
        }
        return categorySOADTOList;
    }

    @Override
    public List<DrvCacheDTO> queryDrvCacheList(Set<Long> drvIdSet) {
        List<DrvCacheDTO> driverCacheEntityList;
        drvIdSet.remove(0L);
        if (CollectionUtils.isEmpty(drvIdSet)) {
            return Collections.emptyList();
        }
        driverCacheEntityList = RedisUtils.mGet(BaseUtil.toCacheKeyList(DriverCacheServiceV2.DRIVER_V2_CACHE_PREFIX, drvIdSet));

        if (driverCacheEntityList == null) {
            driverCacheEntityList = Lists.newArrayListWithExpectedSize(drvIdSet.size());
        }
        if (drvIdSet.size() == driverCacheEntityList.size()) {
            TransportMetric.cacheTimeHitCounter.inc();
            TransportMetric.drvCacheDriverHitCounter.inc();
            logger.info("CacheRecordTitle","drvIdSet params:{} cacheValue:{}", JsonUtil.toJson(drvIdSet), JsonUtil.toJson(driverCacheEntityList));
            return driverCacheEntityList;
        }
        TransportMetric.cacheTimeMissCounter.inc();
        TransportMetric.drvCacheDriverMissCounter.inc();
        Set<Long> drvIdCacheList = driverCacheEntityList.stream().map(DrvCacheDTO::getDriverId).collect(Collectors.toSet());
        Set<Long> diffDrvIdSet = Sets.filter(drvIdSet, drvIdElement -> !drvIdCacheList.contains(drvIdElement));
        List<DrvDriverPO> driverDBEntityList = repository.queryCacheDrvList(diffDrvIdSet);
        Map<Long, Map<Integer, Boolean>> certificateCheckMap = checkQueryService.getCertificateCheckMap(diffDrvIdSet, TmsTransportConstant.CertificateCheckTypeEnum.DRV, Lists.newArrayList(TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode(), TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode()));
        List<DrvCacheDTO> fromDBList = Lists.newArrayListWithExpectedSize(driverDBEntityList.size());
        for (DrvDriverPO infoPO : driverDBEntityList) {
            DrvCacheDTO info = new DrvCacheDTO();
            info.setDriverId(infoPO.getDrvId());
            info.setDriverName(infoPO.getDrvName());
            info.setCityId(infoPO.getCityId());
            City city = enumRepository.getCityById(infoPO.getCityId());
            if (city != null) {
                info.setCityName(city.getTranslationName());
                info.setQunarCityCode(enumRepository.getQCityIdByCCityId(infoPO.getCityId()));
            }
            info.setInternalScope(infoPO.getInternalScope());
            info.setCountryId(infoPO.getCountryId());
            info.setCountryName(infoPO.getCountryName());
            info.setDriverLanguage(infoPO.getDrvLanguage());
            info.setCarId(infoPO.getVehicleId());
            info.setEmail(infoPO.getEmail());
            info.setWechat(infoPO.getWechat());
            info.setStatus(infoPO.getDrvStatus());
            info.setCoopMode(infoPO.getCoopMode());
            info.setPhoneAreaCode(infoPO.getIgtCode());
            info.setDriverPhone(control.getSensitiveData(infoPO.getDrvPhone(), KeyType.Phone));
            info.setSupplierId(infoPO.getSupplierId());
            info.setSupplierName(enumRepository.getSupplierName(info.getSupplierId()));
            try {
                info.setWorkTimes(!Strings.isNullOrEmpty(infoPO.getWorkPeriod()) ? Lists.newArrayList(BaseUtil.getStrSet(infoPO.getWorkPeriod())) : Collections.emptyList());
                if (!Strings.isNullOrEmpty(infoPO.getDrvAddr()) && BaseUtil.isJson(infoPO.getDrvAddr())) {
                    Map<String, Object> drvAddress = JacksonSerializer.INSTANCE().deserialize(infoPO.getDrvAddr(), Map.class);
                    if (drvAddress != null) {
                        info.setAddressLatitude(drvAddress.containsKey(DriverCacheServiceV2.ADDRESS_MAP_KEY[0]) ? Double.valueOf(drvAddress.get(DriverCacheServiceV2.ADDRESS_MAP_KEY[0]).toString()) : null);
                        info.setAddressLongitude(drvAddress.containsKey(DriverCacheServiceV2.ADDRESS_MAP_KEY[1]) ? Double.valueOf(drvAddress.get(DriverCacheServiceV2.ADDRESS_MAP_KEY[1]).toString()) : null);
                    }
                }
            } catch (Exception e) {
                logger.warn(DriverCacheAgent.DRV_CACHE_INFO_TITLE, "STRING TO JSON ERROR PARAMS:{}", JacksonUtil.serialize(infoPO));
            }
            info.setIntendVehicleTypeId(infoPO.getIntendVehicleTypeId());
            info.setCreateTime(DateUtil.timestampToString(infoPO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            info.setDrvProductionLineCodeList(productionLineUtil.getShowProductionLineList(infoPO.getCategorySynthesizeCode()));
            info.setOnlineTime(DateUtil.dateToString(infoPO.getOnlineTime(), DateUtil.YYYYMMDDHHMMSS));
            if (infoPO.getVehBindTime() == null) {
                info.setVehBindTime(info.getOnlineTime());
            } else {
                info.setVehBindTime(DateUtil.dateToString(infoPO.getVehBindTime(), DateUtil.YYYYMMDDHHMMSS));
            }
            info.setDrvIdcard(infoPO.getDrvIdcard());
            info.setDrvConnectAddress(infoPO.getDrvAddr());
            info.setDrvLicenseNumber(infoPO.getDrvLicenseNumber());
            info.setCertiDate(infoPO.getCertiDate() == null ? null : DateUtil.dateToString(infoPO.getCertiDate(), DateUtil.YYYYMMDDHHMMSS));
            info.setExpiryBeginDate(infoPO.getExpiryBeginDate() == null ? null : DateUtil.dateToString(infoPO.getExpiryBeginDate(), DateUtil.YYYYMMDDHHMMSS));
            info.setExpiryEndDate(infoPO.getExpiryBeginDate() == null ? null : DateUtil.dateToString(infoPO.getExpiryEndDate(), DateUtil.YYYYMMDDHHMMSS));
            info.setRideHailingDrvCertValid(certificateCheckMap.get(info.getDriverId()).getOrDefault(TmsTransportConstant.CertificateTypeEnum.NETDRVCTFCT.getCode(), false));
            info.setPicUrl(transportQconfig.getDefaultDriverPic());
            if (certificateCheckMap.get(info.getDriverId()).getOrDefault(TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode(), false)) {
                info.setPicUrl(infoPO.getDrvHeadImg());
            }
            info.setDrvcardImg(infoPO.getDrvcardImg());
            //司机民族
            info.setNation(infoPO.getNation());
            info.setRaisingPickUp(infoPO.getRaisingPickUp());
            info.setChildSeat(infoPO.getChildSeat());
            info.setRealPicUrl(infoPO.getDrvHeadImg());
            //网约车驾驶证号
            info.setDriverNetCertNo(infoPO.getDriverNetCertNo());
            info.setTemporaryDispatchMark(infoPO.getTemporaryDispatchMark());
            info.setTemporaryDispatchEndDatetime(DateUtil.timestampToString(infoPO.getTemporaryDispatchEndDatetime(), DateUtil.YYYYMMDDHHMMSS));
            info.setPaiayAccount(infoPO.getPaiayAccount());
            info.setPaiayEmail(control.getSensitiveData(infoPO.getPaiayEmail(), KeyType.Mail));
            info.setAccountType(infoPO.getAccountType());
            info.setPpmAccount(infoPO.getPpmAccount());
            fromDBList.add(info);
        }
        logger.info("CacheRecordTitle","drvIdSet params:{} cacheValue:{} dbValue:{}", JsonUtil.toJson(drvIdSet), JsonUtil.toJson(driverCacheEntityList), JsonUtil.toJson(fromDBList));
        driverCacheEntityList.addAll(fromDBList);
        CThreadPool.pool(TransportThreadGroupConstant.minorFutureThreadPoolName).execute(() -> {supplementCache(fromDBList);});
        return driverCacheEntityList;
    }

    private void supplementCache(List<DrvCacheDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        Random random = new Random(1);
        // fixme 3 彻底切换Redis 开关 默认为关
        for (DrvCacheDTO cacheDTO : dtoList) {
            String key = DriverCacheServiceV2.DRIVER_V2_CACHE_PREFIX + cacheDTO.getDriverId();
            //redis存储
            DLock lock  = lockConfig.getDistributedLockDrvCache(cacheDTO.getDriverId());
            boolean locked = false;
            try {
                if (lock != null && (locked = lock.tryLock(DistributedLockConfig.PROCESSING_TIME, TimeUnit.SECONDS))) {
                    RedisUtils.set(key,RedisUtils.SIX_HOUR + random.nextInt(100),cacheDTO);
                }
            }catch (Exception e){

            }finally {
                if (locked) {
                    lock.unlock();
                }
            }


        }
    }

    @Override
    public List<DrvDriverPO> queryDrvDriverListByAccount(String hybridAccount) {
        if (!transportQconfig.getSlowSqlFuse3Switch()) {
            return repository.queryByNewHybridAccount(hybridAccount);
        }
        List<DrvDriverPO> drivers = repository.queryByHybridAccount(hybridAccount);
        CThreadPool.pool(TransportThreadGroupConstant.minorFutureThreadPoolName).execute(() -> compareQueryDrvDriverListByAccountRes(hybridAccount, drivers, repository.queryByNewHybridAccount(hybridAccount)));
        return drivers;
    }

    public void compareQueryDrvDriverListByAccountRes(String hybridAccount, List<DrvDriverPO> oldRes, List<DrvDriverPO> newRes) {
        if (CollectionUtils.isEmpty(oldRes) && CollectionUtils.isEmpty(newRes)) {
            return;
        }
        boolean diffRes;
        if (CollectionUtils.isNotEmpty(oldRes) && CollectionUtils.isNotEmpty(newRes)) {
            Set<Long> diffSet = Sets.newHashSet();
            for (DrvDriverPO oldDrvPo : oldRes) {
                if (oldDrvPo != null && oldDrvPo.getDrvId() != null) {
                    diffSet.add(oldDrvPo.getDrvId());
                }
            }
            for (DrvDriverPO newDrvPo : newRes) {
                if (newDrvPo != null && newDrvPo.getDrvId() != null) {
                    boolean addRes = diffSet.add(newDrvPo.getDrvId());
                    if (!addRes) {
                        diffSet.remove(newDrvPo.getDrvId());
                    }
                }
            }
            diffRes = CollectionUtils.isEmpty(diffSet);
        } else {
            diffRes = Boolean.FALSE;
        }
        if (diffRes) {
            return;
        }
        logger.warn("compareQueryDrvDriverListByAccountResDiff", "hybridAccount:{} oldRes:{} newRes:{}",
                hybridAccount, JsonUtil.toJson(oldRes), JsonUtil.toJson(newRes));
        TransportMetric.queryDrvDriverListByAccountResDiffRecordInc();
    }

    @Override
    public Result<List<QueryDrvVehLegendListSOAVO>> queryDrvVehLegendList() {
        List<DrvVehLegendPO> dbList = drvVehLegendRepository.queryDrvVehLegendList();
        List<QueryDrvVehLegendListSOAVO> resultList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(dbList)){
            return Result.Builder.<List<QueryDrvVehLegendListSOAVO>>newResult().success().withData(resultList).build();
        }
        for(DrvVehLegendPO drvVehLegendPO : dbList){
            QueryDrvVehLegendListSOAVO soavo = new QueryDrvVehLegendListSOAVO();
            soavo.setId(drvVehLegendPO.getId());
            soavo.setCityId(drvVehLegendPO.getCityId());
            soavo.setCountryId(drvVehLegendPO.getCountryId());
            soavo.setLegendType(drvVehLegendPO.getLegendType());
            soavo.setSharkKey(drvVehLegendPO.getSharkKey());
            soavo.setImgUrl(drvVehLegendPO.getImgUrl());
            resultList.add(soavo);
        }
        return Result.Builder.<List<QueryDrvVehLegendListSOAVO>>newResult().success().withData(resultList).build();
    }

    public Result<QuerySupplierConfigInfoSOAResponseType> querySupplierConfigInfo(Long supplierId) {
        return querySupplierConfigInfo(supplierId, null, null);
    }

    @Override
    public Result<QuerySupplierConfigInfoSOAResponseType> querySupplierConfigInfo(Long supplierId, Long cityId, String useCarTime) {
        String supplierConf = overseasQconfig.getOverseasGrayScaleSupplierConf();
        Boolean supplierGray = gayMethod(supplierConf, supplierId);
        String overseasOcrVerifyConf = overseasQconfig.getOverseasOcrVerifyConf();
        Boolean ocrVerifyGray = gayMethod(overseasOcrVerifyConf, supplierId);
        String temporarySupplier = overseasQconfig.getOverseasTemporarySupplierConf();
        QuerySupplierConfigInfoSOAResponseType soaResponseType = new QuerySupplierConfigInfoSOAResponseType();
        soaResponseType.setGrayScaleSupplier(supplierGray);
        soaResponseType.setGrayScaleOcrInterceptSwitch(ocrVerifyGray);
        soaResponseType.setGrayScaleOcrTotalSwitch(overseasQconfig.getOverseasOcrTotalSwitch());
        soaResponseType.setOverseasOcrTimeoutThreshold(overseasQconfig.getOverseasOcrTimeoutThreshold());
        soaResponseType.setSupplierName(enumRepository.getSupplierName(supplierId));
        soaResponseType.setSupplierId(supplierId);
        soaResponseType.setGrayTemporaryDispatchSwitch(enableTempDispatchBaseOnDate(gayMethod(temporarySupplier, supplierId), cityId, useCarTime));
        soaResponseType.setTemporaryDispatchEndDatetimeHour(overseasQconfig.getTemporaryDispatchEndDatetimeConfig());
        return Result.Builder.<QuerySupplierConfigInfoSOAResponseType>newResult().success().withData(soaResponseType).build();
    }

    /**
     * 根据日期配置判断临时派遣是否开启 https://idev.ctripcorp.com/share/prod-requirement/10000660/5445249
     * @param graySupplier
     * @param cityId
     * @param useCarTime
     * @return
     */
    protected Boolean enableTempDispatchBaseOnDate(Boolean graySupplier, Long cityId, String useCarTime) {
        return graySupplier && (useCarTime == null || DateUtil.checkDateInRange(tempDispatchDateConfig.getTempDispatchDateConfig(getCountryId(cityId)), useCarTime));
    }

    protected String getCountryId(Long cityId) {
        return String.valueOf(enumRepository.getCountryId(cityId));
    }

    @Override
    public Result<OverseasOCRRecognitionSOAResponseType> overseasOCRRecognition(OverseasOCRRecognitionSOARequestType req) {
        try {
            OverseasOCRRecognitionSOAResponseType soaResponseType = new OverseasOCRRecognitionSOAResponseType();
            //调OCR识别前，先校验是否已经识别过，如果识别过，则直接返回已识别过的数据
            OverseasOcrRecordPO res = overseasOcrRecordRepository.queryOverseasOcrRecordList(req.getCityId(), req.getOcrType(), req.getOcrImgUrl());
            if (res != null && res.getRequestType() != null && !Strings.isNullOrEmpty(res.getResponseResult())) {
                soaResponseType.setOcrId(res.getId());
                if (res.getRequestType().intValue() == CommonEnum.OcrTypeEnum.Driver.getValue().intValue()) {
                    DrivingLicenseDTO licenseDTO = JsonUtil.fromJson(res.getResponseResult(), new TypeReference<DrivingLicenseDTO>() { });
                    if (licenseDTO != null) {
                        soaResponseType.setDrvName(licenseDTO.getName());
                    }
                } else {
                    CarInfoDTO carInfoDTO = JsonUtil.fromJson(res.getResponseResult(), new TypeReference<CarInfoDTO>() { });
                    if (carInfoDTO != null) {
                        soaResponseType.setVehicleColor(carInfoDTO.getColor());
                        soaResponseType.setVehicleLicense(carInfoDTO.getNumber());
                    }
                }
                return Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(soaResponseType).build();
            }
            OverseasOcrRecordPO record = new OverseasOcrRecordPO();
            record.setRequestType(req.getOcrType());
            record.setRequestImg(req.getOcrImgUrl());
            record.setRequestCityId(req.getCityId());
            record.setCreateUser(StringUtils.isEmpty(SessionHolder.getRestSessionAccountName()) ? Constant.SYSTEM : SessionHolder.getRestSessionAccountName());
            record.setModifyUser(record.getCreateUser());
            // 重新OCR
            if (req.getOcrType().intValue() == CommonEnum.OcrTypeEnum.Driver.getValue().intValue()) {
                DrivingLicenseResponseType ocrResp = infrastructureServiceClientProxy.drivingLicense(SOAReqConverter.buildDrvOCRReq(req));
                if (ocrResp == null || !CommonEnum.OcrResEnum.Success.getValue().equals(ocrResp.getCode()) || ocrResp.getDrivingLicense() == null) {
                    return Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.ocrError)).build();
                }
                soaResponseType.setDrvName(ocrResp.getDrivingLicense().getName());
                record.setResponseResult(JsonUtil.toJson(ocrResp.getDrivingLicense()));
            } else {
                CarOCRResponseType ocrResp = infrastructureServiceClientProxy.carOCR(SOAReqConverter.buildCarOCRReq(req));
                if (ocrResp == null || !CommonEnum.OcrResEnum.Success.getValue().equals(ocrResp.getCode()) || ocrResp.getCarInfo() == null) {
                    return Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.ocrError)).build();
                }
                soaResponseType.setVehicleLicense(ocrResp.getCarInfo().getNumber());
                soaResponseType.setVehicleColor(ocrResp.getCarInfo().getColor());
                record.setResponseResult(JsonUtil.toJson(ocrResp.getCarInfo()));
            }
            soaResponseType.setOcrId(overseasOcrRecordRepository.insert(record));
            return Result.Builder.<OverseasOCRRecognitionSOAResponseType>newResult().success().withData(soaResponseType).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Boolean overseasIsBusiness(Long supplierId, Integer versionFlag, Integer areaScope,TmsTransportConstant.BusinessTypeEnum businessTypeEnum) {
        //境外新逻辑，versionFlag = 5
        if (versionFlag == null || versionFlag < 5 || Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), areaScope)) {
            return Boolean.FALSE;
        }
        //判断当前供应商是否在太度中
        Result<QuerySupplierConfigInfoSOAResponseType> soaResponseTypeResult = querySupplierConfigInfo(supplierId);
        if (!soaResponseTypeResult.isSuccess()) {
            return Boolean.FALSE;
        }
        QuerySupplierConfigInfoSOAResponseType soaResponseType = soaResponseTypeResult.getData();
        if(soaResponseType == null){
            return Boolean.FALSE;
        }

        switch (businessTypeEnum){
            case H5:
                //如果供应商在灰度列表中并且开通了OCR识别校验逻辑，所返回true，进入新逻辑
                if (soaResponseType.isGrayScaleSupplier() && soaResponseType.isGrayScaleOcrInterceptSwitch()) {
                    return Boolean.TRUE;
                }
            case OTHER:
                //如果供应商在灰度列表中,所返回true
                if (soaResponseType.isGrayScaleSupplier()) {
                    return Boolean.TRUE;
                }
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean overseasSupplierIsGray(Long supplierId, Integer areaScope) {
        //境外新逻辑，versionFlag = 5
        if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), areaScope)) {
            return Boolean.FALSE;
        }
        //判断当前供应商是否在太度中
        Result<QuerySupplierConfigInfoSOAResponseType> soaResponseTypeResult = querySupplierConfigInfo(supplierId);
        if (!soaResponseTypeResult.isSuccess()) {
            return Boolean.FALSE;
        }
        QuerySupplierConfigInfoSOAResponseType soaResponseType = soaResponseTypeResult.getData();
        //如果供应商在灰度列表中,所返回true
        if (soaResponseType.isGrayScaleSupplier()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Result<PageHolder<DrvDriverSOAResponseDTO>> queryDiscardDrvList(QueryDrvDO drvDO) {
        PageHolder pageHolder = null;
        try {
            int count = repository.countDiscardDrvList(drvDO);
            if (count == 0) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(drvDO.getPage()).pageSize(drvDO.getSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }
            List<DrvDriverPO> driverPOList = repository.queryDiscardDrvList(drvDO);
            if (CollectionUtils.isEmpty(driverPOList)) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(drvDO.getPage()).pageSize(drvDO.getSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }

            Map<Integer, String> drvStatusMap = enumRepository.getDrvStatusName();
            Map<Integer, String> drvFromMap = enumRepository.getDrvFrom();
            Map<String, String> languageMap = enumRepository.getDrvLanguageMap();
            Map<Integer,String> coopModeMap = enumRepository.getDrvCoopMode();
            List<DrvDriverSOAResponseDTO> drvList = Lists.newArrayListWithCapacity(driverPOList.size());
            for (DrvDriverPO po : driverPOList) {
                DrvDriverSOAResponseDTO soaResponseDTO = new DrvDriverSOAResponseDTO();
                BeanUtils.copyProperties(po, soaResponseDTO);
                soaResponseDTO.setDatachangeCreatetime(DateUtil.timestampToString(po.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
                soaResponseDTO.setDatachangeLasttime(DateUtil.timestampToString(po.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
                soaResponseDTO.setCityName(enumRepository.getCityName(po.getCityId()));
                soaResponseDTO.setDrvLanguageName(enumRepository.getDrvLanguageName(po.getDrvLanguage(), languageMap));
                soaResponseDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(po.getVehicleTypeId()));
                soaResponseDTO.setDrvStatusName(drvStatusMap.get(po.getDrvStatus()));
                soaResponseDTO.setDrvFromName(drvFromMap.get(soaResponseDTO.getDrvFrom()));
                soaResponseDTO.setIntendVehicleTypeName(enumRepository.getIntendVehicleTypeName(soaResponseDTO.getIntendVehicleTypeId()));
                soaResponseDTO.setDrvPhone(control.getSensitiveData(po.getDrvPhone(), KeyType.Phone));
                soaResponseDTO.setDrvIdcard(control.getSensitiveData(po.getDrvIdcard(), KeyType.Identity_Card));
                soaResponseDTO.setSupplierName(enumRepository.getSupplierName(po.getSupplierId()));
                soaResponseDTO.setCoopModeName(coopModeMap.get(po.getCoopMode()));
                soaResponseDTO.setProLineName(productionLineUtil.getProductionLineNames(po.getCategorySynthesizeCode()));
                soaResponseDTO.setAreaScope(po.getInternalScope());
                soaResponseDTO.setRaisingPickUp(po.getRaisingPickUp());
                soaResponseDTO.setChildSeat(po.getChildSeat());
                drvList.add(soaResponseDTO);
            }
            pageHolder = PageHolder.of(drvList).pageIndex(drvDO.getPage()).pageSize(drvDO.getSize()).totalSize(count).build();
            return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
        } catch (Exception e) {
            logger.error("queryDiscardDrvListERROR","params:{},e:{}", JsonUtil.toJson(drvDO),e);
        }
        return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().fail().withData(pageHolder).build();
    }
    @Override
    public Result<Integer> queryTemporaryProLine(Long supplierId, Long cityId) {
        //获取产线-供应商+城市下所有已上线的运力组的产线并集
        List<TspTransportGroupPO> transportGroupPOList = groupRepository.queryGroupBySupplierAndCity(Arrays.asList(supplierId),Arrays.asList(cityId));
        if(CollectionUtils.isEmpty(transportGroupPOList)){
            return Result.Builder.<Integer>newResult().fail().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.capacitygroupTemporary)).build();
        }
        //运力组中的产线
        Set<Integer> proLineList = transportGroupPOList.stream().map(TspTransportGroupPO::getCategorySynthesizeCode).collect(Collectors.toSet());
        return Result.Builder.<Integer>newResult().success().withData(productionLineUtil.insideProLineMerge(proLineList)).build();
    }

    @Override
    public Result<PreCheckResultDTO> checkPhoneNumber(CheckPhoneNumberRequestType requestType) {
        DrvDriverPO drvDispatchPO;
        if (!Objects.isNull(drvDispatchPO = repository.drvDispatchcheckDrvPhoneOnly(requestType.getDrvPhone()))) {
            Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "addDrv");
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), requestType.getAreaScope())) {
                return Result.Builder.<PreCheckResultDTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(String.format(requestType.getDrvPhone(), TmsTransUtil.decrypt(requestType.getDrvPhone(), KeyType.Phone), SharkUtils.getSharkValue(SharkKeyConstant.mobilePhoneUsedChangeForRegistration))).build();
            }
            return overseasDrvDispatchCheck(SharkKeyConstant.transportPhoneAlreadyExists, requestType.getSupplierId(), drvDispatchPO);
        }
        return Result.Builder.<PreCheckResultDTO>newResult().success().withData(null).build();
    }

    public Result<PreCheckResultDTO> overseasDrvDispatchCheck(String sharkValue,Long nowSupplierId,DrvDriverPO drvDriverPO){
        //如果当前供应商和已存在正式司机所属供应商相同，则返回原有提示
        if(Objects.equals(nowSupplierId,drvDriverPO.getSupplierId()) ||
          Objects.equals(drvDriverPO.getTemporaryDispatchMark(), TmsTransportConstant.TemporaryDispatchMarkEnum.TEMPORARY.getCode())){
            return Result.Builder.<PreCheckResultDTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
              .withMsg(SharkUtils.getSharkValue(sharkValue)).withData(PreCheckResultDTO.builder().driverId(drvDriverPO.getDrvId()).build()).build();
        }
        return Result.Builder.<PreCheckResultDTO>newResult().fail().withCode(TmsTransportConstant.ResultCode.code100023).withMsg(SharkUtils.getSharkValue(sharkValue)).withData(PreCheckResultDTO.builder().driverId(drvDriverPO.getDrvId()).build()).build();
    }


    /**
     * 司机入驻/编辑前置校验
     *
     */
    @SneakyThrows
    @Override
    public Result<PreCheckResultDTO> drvPreCheck(DrvPreCheckRequestType requestType) {
        if (org.apache.commons.lang3.StringUtils.equals(CheckTypeEnum.IDCARD.getCode(), requestType.getType())) {
            if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), requestType.getAreaScope())) {
                //身份证唯一
                if (repository.checkDrvOnly(requestType.getIdCard(), TmsTransportConstant.DrvOnlyTypeEnum.IDCARD.getCode())) {
                    Cat.logEvent(CatEventType.DRV_PRE_CHECK, "idcard_exists");
                    return Result.Builder.<PreCheckResultDTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportIdcardAlreadyExists)).build();
                }
                QueryAccountByIdCardRequestType queryAccountByIdCardRequestType = new QueryAccountByIdCardRequestType();
                queryAccountByIdCardRequestType.setIdCard(TmsTransUtil.encrypt(requestType.getIdCard(),KeyType.Identity_Card));
                QueryAccountByIdCardResponseType response = driverDomainService.queryAccountByIdCard(queryAccountByIdCardRequestType);
                if (ResponseResultUtil.checkResponseResult(response)) {
                    AccountDetailDTO accountDetailDTO = Optional.ofNullable(response.getAccountDetailList())
                            .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                            .flatMap(b -> b.stream().filter(this::isDscSource).findFirst())
                            .orElse(null);
                    if (Objects.nonNull(accountDetailDTO)) {
                        Cat.logEvent(CatEventType.DRV_PRE_CHECK, "idcard_account_found");
                        String phoneNumber = accountDetailDTO.getPhoneNumber();
                        String decryptPhone = TmsTransUtil.decrypt(phoneNumber, KeyType.Phone);
                        return Result.Builder.<PreCheckResultDTO>newResult().success().withData(PreCheckResultDTO.builder().phoneNumber(decryptPhone).build()).build();
                    }
                }
            }
        }

        if (StringUtil.isEmpty(requestType.getDrvPhone())) {
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "phone_empty");
            return Result.Builder.<PreCheckResultDTO>newResult().success().build();
        }

        if (businessQConfig.isDrvPreCheckOff()) {
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "check_disabled");
            return Result.Builder.<PreCheckResultDTO>newResult().success().build();
        }

        //手机号格式校验
        Result<Boolean> mobileValid = mobileHelper.isMobileValid(requestType.getIgtCode(), requestType.getDrvPhone(), null);
        if(!mobileValid.isSuccess()){
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "phone_format_invalid");
            return ResponseResultUtil.failed(mobileValid.getCode(), mobileValid.getMsg());
        }

        //校验手机号邮箱是否已经存在于供应链库中
        DrvDriverPO driver = repository.drvDispatchcheckDrvPhoneOnly(requestType.getDrvPhone());
        if (!Objects.isNull(driver)) { //手机号已经存在
                Cat.logEvent(CatEventType.CHECK_DRIVER_PHONE_ONLY, "addDrv");
                Cat.logEvent(CatEventType.DRV_PRE_CHECK, "phone_exists");
                if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), requestType.getAreaScope())) {
                    return Result.Builder.<PreCheckResultDTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportPhoneAlreadyExists)).build();
                }
                return overseasDrvDispatchCheck(SharkKeyConstant.transportPhoneAlreadyExists, requestType.getSupplierId(), driver);
        }

        if (StringUtil.isEmpty(requestType.getEmail())) {
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "email_empty");
            return Result.Builder.<PreCheckResultDTO>newResult().success().build();
        }

        //判断邮箱唯一 (境内、外)
        if (repository.checkDrvOnly(TmsTransUtil.encrypt(requestType.getEmail(), KeyType.Mail), TmsTransportConstant.DrvOnlyTypeEnum.EMIAL.getCode())) {
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "email_exists");
            return Result.Builder.<PreCheckResultDTO>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.mailboxUsedChangeForRegistration)).build();
        }

        //判断账号中心邮箱和手机号是否分属两个账号
        QueryAccountByEmailRequestType queryAccountByEmailRequestType = new QueryAccountByEmailRequestType();
        queryAccountByEmailRequestType.setEmail(requestType.getEmail());
        DriverAccountDetail emailDetail =
          driverDomainServiceProxy.queryAccountByEmail(queryAccountByEmailRequestType);
        if (emailDetail == null) {
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "email_account_not_found");
            return Result.Builder.<PreCheckResultDTO>newResult().success().withData(null).build();
        }
        QueryAccountByMobilePhoneRequestType queryAccountByMobilePhoneRequestType = new QueryAccountByMobilePhoneRequestType();
        queryAccountByMobilePhoneRequestType.setCountryCode(requestType.getIgtCode());
        queryAccountByMobilePhoneRequestType.setPhoneNumber(requestType.getDrvPhone());
        DriverAccountDetail phoneDetail =
          driverDomainServiceProxy.queryAccountByMobilePhone(queryAccountByMobilePhoneRequestType);

        if (phoneDetail == null) {
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "phone_account_not_found");
            return Result.Builder.<PreCheckResultDTO>newResult().success().build();
        }

        if (!Objects.equals(emailDetail.getUid(), phoneDetail.getUid())) {
            Cat.logEvent(CatEventType.DRV_PRE_CHECK, "email_phone_different_accounts");
            return Result.Builder.<PreCheckResultDTO>newResult().fail().withCode(ErrorCodeEnum.EMAIL_HAS_ALREADY_BEEN_USED_IN_THE_ACCOUNT_CENTER.getCode()).withMsg(SharkUtils.getSharkValue(ErrorCodeEnum.EMAIL_HAS_ALREADY_BEEN_USED_IN_THE_ACCOUNT_CENTER.getMessage(), new Object[]{emailDetail.getName()})).build();
        }

        return Result.Builder.<PreCheckResultDTO>newResult().success().build();

    }

    @Override
    public Result<Boolean> checkTaxiLabel(Long supplierId, Long cityId) {
        DriverTaxiDTO driverTaxiDTO = driverRegestConfig.getRequiredFieldList(cityId, "city", supplierId);
        if (Objects.isNull(driverTaxiDTO)) {

            City cityById = enumRepository.getCityById(cityId);
            if (Objects.isNull(cityById)) {
                return Result.Builder.<Boolean>newResult().fail().withData(false).build();
            }
            driverTaxiDTO = driverRegestConfig.getRequiredFieldList(cityById.getCountryId(), "country", supplierId);
            if (Objects.isNull(driverTaxiDTO)) {
                return Result.Builder.<Boolean>newResult().fail().withData(false).build();
            }
            return Result.Builder.<Boolean>newResult().success().withData(true).build();
        }
        return Result.Builder.<Boolean>newResult().success().withData(true).build();
    }

    private boolean isDscSource(AccountDetailDTO accountDetailDTO) {
        return Objects.nonNull(accountDetailDTO)
                && this.isH5EnterIgtCodeMatched(accountDetailDTO.getCountryCode())
                && org.apache.commons.collections4.CollectionUtils.isNotEmpty(accountDetailDTO.getIdentityList())
                && accountDetailDTO.getIdentityList().stream()
                .anyMatch(b -> Objects.equals(AccountSourceEnum.DRIVER_GUIDE.getCode(), b.getSource()));
    }

    private boolean isH5EnterIgtCodeMatched(String igtCode) {
        return org.apache.commons.lang3.StringUtils.equals(igtCode, "86");
    }

}
