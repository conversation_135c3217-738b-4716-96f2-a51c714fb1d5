package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.dto.SaasVehicleDTO;
import com.ctrip.dcs.tms.transport.application.query.IQueryVehicleForSaasService;
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.VehCacheDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalCollectionUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository;
import com.ctrip.dcs.tms.transport.infrastructure.service.IQueryVehicleAttributeService;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleBrandDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleColorDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleSeriesDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.VehicleTypeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueryVehicleForSaasService implements IQueryVehicleForSaasService {
    @Autowired
    private VehicleRepository vehicleRepository;
    @Autowired
    private VehicleQueryService vehicleQueryService;
    @Autowired
    private IQueryVehicleAttributeService queryVehicleAttributeService;

    @Override
    public List<Long> queryVehicleId(List<String> vehicleLicense, Long supplierId) {
        return vehicleRepository.queryVehicleId(vehicleLicense,supplierId);
    }

    @Override
    public List<SaasVehicleDTO> query(List<Long> vehicleIds) {
        //从缓存查询
        List<VehCacheDTO> vehCacheDTOS = vehicleQueryService.queryVehCacheList(LocalCollectionUtils.listToSet(vehicleIds));
        List<SaasVehicleDTO> resultList = new ArrayList<>();
        resultList.addAll(getSaasVehicleDTO(vehCacheDTOS));
        //从db查询
        resultList.addAll(queryVehicleFromDb(queryFromDBIds(resultList,vehicleIds)));
        return resultList;
    }

    /**
     * 查询车俩信息从db查询
     * @param vehicleIds
     * @return
     */
    public List<SaasVehicleDTO> queryVehicleFromDb(List<Long> vehicleIds){
        if(LocalCollectionUtils.isEmpty(vehicleIds)){
            return new ArrayList<>();
        }
        List<VehVehiclePO> vehVehiclePOS = vehicleRepository.queryVehicleByIds(vehicleIds);
        if(LocalCollectionUtils.isEmpty(vehVehiclePOS)){
            return new ArrayList<>();
        }
        List<SaasVehicleDTO> resultList = new ArrayList<>();
        for (VehVehiclePO vehVehiclePO : vehVehiclePOS) {
            resultList.add(querySaasVehicleDTO(vehVehiclePO));
        }
        return resultList;
    }

    /**
     * 封装结果
     * @param vehVehiclePO
     * @return
     */
    public SaasVehicleDTO querySaasVehicleDTO(VehVehiclePO vehVehiclePO){
        SaasVehicleDTO saasVehicleSoaDTO  = new SaasVehicleDTO();
        saasVehicleSoaDTO.setVehicleId(vehVehiclePO.getVehicleId());
        saasVehicleSoaDTO.setVehicleStatus(vehVehiclePO.getVehicleStatus());
        saasVehicleSoaDTO.setVehicleBrandId(vehVehiclePO.getVehicleBrandId());

        VehicleBrandDTO vehicleBrandDTO = queryVehicleAttributeService.queryVehicleBrand(vehVehiclePO.getVehicleBrandId());
        saasVehicleSoaDTO.setVehicleBrandName(vehicleBrandDTO.getVehicleBrandName());

        saasVehicleSoaDTO.setVehicleCategory(vehVehiclePO.getTemporaryDispatchMark());
        saasVehicleSoaDTO.setSupplerId(vehVehiclePO.getSupplierId());
        saasVehicleSoaDTO.setVehicleLicense(vehVehiclePO.getVehicleLicense());
        saasVehicleSoaDTO.setVehicleSeriesId(vehVehiclePO.getVehicleSeries());

        VehicleSeriesDTO vehicleSeriesDTO = queryVehicleAttributeService.queryVehicleSeries(vehVehiclePO.getVehicleSeries());
        saasVehicleSoaDTO.setVehicleSeriesName(vehicleSeriesDTO.getVehicleSeriesName());

        saasVehicleSoaDTO.setVehicleTypeId(vehVehiclePO.getVehicleTypeId());

        VehicleTypeDTO vehicleTypeDTO = queryVehicleAttributeService.queryVehicleType(vehVehiclePO.getVehicleTypeId());
        saasVehicleSoaDTO.setVehicleTypeName(vehicleTypeDTO.getVehicleTypeName());


        saasVehicleSoaDTO.setVehicleColorId(vehVehiclePO.getVehicleColorId());

        VehicleColorDTO vehicleColorDTO = queryVehicleAttributeService.queryVehicleColor(vehVehiclePO.getVehicleColorId());
        saasVehicleSoaDTO.setVehicleColorName(vehicleColorDTO.getVehicleColorName());

        return saasVehicleSoaDTO;
    }
    /**
     * 查询需要从db查询的车辆id
     * @param resultList
     * @param vehicleIds
     * @return
     */
    public List<Long> queryFromDBIds(List<SaasVehicleDTO> resultList,List<Long> vehicleIds){
        List<Long> cacheIds = new ArrayList<>();
        for (SaasVehicleDTO saasVehicleDTO : resultList) {
            cacheIds.add(saasVehicleDTO.getVehicleId());
        }
        List<Long> result = new ArrayList<>();
        for (Long vehicleId : vehicleIds) {
            if(!cacheIds.contains(vehicleId)){
                result.add(vehicleId);
            }
        }
        return result;
    }

    /**
     * 封装结果
     * @param vehCacheDTOS
     * @return
     */
    private List<SaasVehicleDTO> getSaasVehicleDTO(List<VehCacheDTO> vehCacheDTOS){
        if(LocalCollectionUtils.isEmpty(vehCacheDTOS)){
            return new ArrayList<>();
        }
        List<SaasVehicleDTO> vehicleList = new ArrayList<>();
        for (VehCacheDTO vehCacheDTO : vehCacheDTOS) {
            vehicleList.add(getSaasVehicleSoaDTO(vehCacheDTO));
        }
        return vehicleList;
    }

    /**
     * 封装结果
     * @param vehCacheDTO
     * @return
     */
    private SaasVehicleDTO getSaasVehicleSoaDTO(VehCacheDTO vehCacheDTO){
        SaasVehicleDTO saasVehicleSoaDTO  = new SaasVehicleDTO();
        saasVehicleSoaDTO.setVehicleId(vehCacheDTO.getCarId());
        saasVehicleSoaDTO.setVehicleStatus(vehCacheDTO.getVehicleStatus());
        saasVehicleSoaDTO.setVehicleBrandId(vehCacheDTO.getCarBrandId());
        saasVehicleSoaDTO.setVehicleBrandName(vehCacheDTO.getCarBrandName());
        saasVehicleSoaDTO.setVehicleCategory(vehCacheDTO.getTemporaryDispatchMark());
        saasVehicleSoaDTO.setSupplerId(vehCacheDTO.getSupplierId());
        saasVehicleSoaDTO.setVehicleLicense(vehCacheDTO.getCarLicense());
        saasVehicleSoaDTO.setVehicleSeriesId(vehCacheDTO.getCarSeriesId());
        saasVehicleSoaDTO.setVehicleSeriesName(vehCacheDTO.getCarSeriesName());
        saasVehicleSoaDTO.setVehicleTypeId(Long.valueOf(vehCacheDTO.getCarTypeId()));
        saasVehicleSoaDTO.setVehicleTypeName(vehCacheDTO.getCarTypeName());
        saasVehicleSoaDTO.setVehicleColorId(vehCacheDTO.getCarColorId());
        saasVehicleSoaDTO.setVehicleColorName(vehCacheDTO.getCarColor());
        return saasVehicleSoaDTO;
    }
}
