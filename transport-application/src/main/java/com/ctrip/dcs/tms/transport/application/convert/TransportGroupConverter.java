package com.ctrip.dcs.tms.transport.application.convert;

import com.ctrip.basebiz.callcenter.splitservice.contract.BatchSplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.EnumCallDirection;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupTakeOrderTimeScopeRequestType;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.PhoneNumberServiceGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryTransportGroupCondition;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryTransportGroupSkuInfoModel;

import java.util.List;

public class TransportGroupConverter {

    public static QueryTransportGroupCondition toQueryModel(QueryTransportGroupTakeOrderTimeScopeRequestType request) {
        QueryTransportGroupCondition res = new QueryTransportGroupCondition();
        res.setSkuIdList(request.getSkuIdList());
        res.setPoiType(request.getPoiType());
        res.setLatitude(request.getLatitude());
        res.setLongitude(request.getLongitude());
        res.setParamAreaMap(request.getAreaGroupIdMap());
        res.setPoiRef(request.getPoiRef());
        res.setCarPlaceId(request.getCarPlaceId());
        res.setFilterDispatchOnly(request.getFilterDispatchOnly());
        return res;
    }

    public static QueryTransportGroupSkuInfoModel toQueryModel(List<Long> skuIdList) {
        QueryTransportGroupSkuInfoModel model = new QueryTransportGroupSkuInfoModel();
        model.setSkuIds(skuIdList);
        model.setActive(Boolean.TRUE);
        return model;
    }

    public static BatchSplitNumberRequestType buildSplitNumberReq(List<String> numbers) {
        BatchSplitNumberRequestType requestType = new BatchSplitNumberRequestType();
        requestType.setNumbers(numbers);
        // 固定值 CN
        requestType.setRegionCode(PhoneNumberServiceGateway.CHINA_REGION_CODE);
        // 呼入
        requestType.setDirection(EnumCallDirection.INBOUND);
        return requestType;
    }

}
