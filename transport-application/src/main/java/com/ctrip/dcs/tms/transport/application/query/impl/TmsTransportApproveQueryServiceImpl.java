package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.base.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.Objects;
import java.util.*;
import java.util.stream.*;


@Service
public class TmsTransportApproveQueryServiceImpl implements TmsTransportApproveQueryService {


    private final String DRVLICENSEAPPEALMATERIALS = "drvLicenseAppealMaterials";

    private final String VEHICLELICENSEAPPEALMATERIALS = "vehicleLicenseAppealMaterials";

    private final String NETAPPEALMATERIALS = "netAppealMaterials";

    @Autowired
    TmsTransportApproveRepository approveRepository;
    @Autowired
    EnumRepository enumRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;
    @Autowired
    private DrvDrvierRepository drvDrvierRepository;
    @Autowired
    private VehicleRepository vehicleRepository;

    @Override
    public Result<PageHolder<QueryTransportApproveListSOADTO>> queryApproveList(QueryTransportApproveListSOARequestType request) {

        PageHolder pageHolder;
        try {
            List<Long> drvIdList = null;
            if (!Strings.isNullOrEmpty(request.getDrvName())) {
                drvIdList = drvDrvierRepository.queryDrvIdByCondition(request.getDrvName(), null);
                if (CollectionUtils.isEmpty(drvIdList)) {
                    pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(request.getPaginator().getPageNo()).pageSize(request.getPaginator().getPageSize()).totalSize(0).build();
                    return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
                }
            }
            QueryApproveListDO req = buildDO(request);
            req.setDrvIdList(drvIdList);
            int count = approveRepository.countQueryApproveListV2(req);
            if (count == 0) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(request.getPaginator().getPageNo()).pageSize(request.getPaginator().getPageSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }
            List<TmsTransportApprovePO> approvePOList = approveRepository.queryApproveListV2(req);
            if (CollectionUtils.isEmpty(approvePOList)) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(request.getPaginator().getPageNo()).pageSize(request.getPaginator().getPageSize()).totalSize(count).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }

            pageHolder = PageHolder.of(toResultList(approvePOList)).pageIndex(request.getPaginator().getPageNo()).pageSize(request.getPaginator().getPageSize()).totalSize(count).build();
            return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    public List<QueryTransportApproveListSOADTO> toResultList(List<TmsTransportApprovePO> approvePOList){
        List<QueryTransportApproveListSOADTO> approveListSOADTOList = Lists.newArrayListWithCapacity(approvePOList.size());
        Map<Integer, String> approverStatus = enumRepository.getTransportApproveStatus();
        Map<Integer, String> approveEnentType = enumRepository.getApproveEnentType();
        Map<Integer, String> approveNodeName = enumRepository.getApproveNodeName();
        for (TmsTransportApprovePO po : approvePOList) {
            QueryTransportApproveListSOADTO soaResponseDTO = new QueryTransportApproveListSOADTO();
            BeanUtils.copyProperties(po, soaResponseDTO);
            soaResponseDTO.setApproveName(getApproveName(po.getApproveName()));
            soaResponseDTO.setDatachangeCreatetime(DateUtil.timestampToString(po.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            soaResponseDTO.setDatachangeLasttime(DateUtil.timestampToString(po.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
            soaResponseDTO.setApproveNodeName(approveNodeName.get(po.getApproveNode()));
            soaResponseDTO.setApproveStatusName(approverStatus.get(po.getApproveStatus()));
            soaResponseDTO.setEventTypeName(approveEnentType.get(po.getEventType()));
            soaResponseDTO.setRemark(po.getRemarks());
            approveListSOADTOList.add(soaResponseDTO);
        }
        return approveListSOADTOList;
    }

    @Override
    public Result<QueryTransportApproveDetailSOADTO> queryDrvDetail(QueryTransportApproveDetailSOARequestType request) {
        try {
            Long id = request.getId();
            if(id == null || id < 0){
                return Result.Builder.<QueryTransportApproveDetailSOADTO>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing)).build();
            }
            TmsTransportApprovePO approvePO = approveRepository.queryByPk(id);

            if(Objects.isNull(approvePO)){
                return Result.Builder.<QueryTransportApproveDetailSOADTO>newResult().success().withData(null).build();
            }
            //判断VBK是否有权限查看详情
            if(!judgeOperationPermissions(approvePO.getAccountId())){
                return Result.Builder.<QueryTransportApproveDetailSOADTO>newResult().fail().fail().withCode(ServiceResponseConstants.ResStatus.NO_LOGIN_ERROR_CODE).build();
            }
            Map<Integer, String> approverStatus = enumRepository.getTransportApproveStatus();
            Map<Integer, String> approveEnentType = enumRepository.getApproveEnentType();
            Map<Integer, String> approveNodeName = enumRepository.getApproveNodeName();
            QueryTransportApproveDetailSOADTO detailSOADTO = new QueryTransportApproveDetailSOADTO();
            BeanUtils.copyProperties(approvePO,detailSOADTO);
            detailSOADTO.setApproveName(getApproveName(approvePO.getApproveName()));
            detailSOADTO.setApproveNodeName(approveNodeName.get(approvePO.getApproveNode()));
            detailSOADTO.setApproveStatusName(approverStatus.get(approvePO.getApproveStatus()));
            detailSOADTO.setEventTypeName(approveEnentType.get(approvePO.getEventType()));
            detailSOADTO.setApproveContentList(this.buildApproveContentList(approvePO));
            detailSOADTO.setDatachangeCreatetime(DateUtil.timestampToString(approvePO.getDatachangeCreatetime(), DateUtil.YYYYMMDDHHMMSS));
            detailSOADTO.setDatachangeLasttime(DateUtil.timestampToString(approvePO.getDatachangeLasttime(), DateUtil.YYYYMMDDHHMMSS));
            detailSOADTO.setCheckResultList(this.getCertificateCheckResult(approvePO.getCertificateCheckResult()));
            if (approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.nucleicAcidTest.getCode().intValue()) {
                DrvEpidemicPreventionControlInfoPO infoPO = drvEpidemicPreventionControlInfoRepository.queryByDrvId(approvePO.getApproveSourceId());
                if (infoPO != null) {
                    NucleicAcidReport nucleicAcidReport = new NucleicAcidReport();
                    SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(infoPO.getNucleicAcidReportTempContent(), new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
                    });
                    if (ocrRequest != null) {
                        nucleicAcidReport.setOcrDriverName(ocrRequest.getDriverName());
                        nucleicAcidReport.setOcrIdNumber(ocrRequest.getDriverIdNumber());
                        nucleicAcidReport.setOcrNucleicAcidTestingTime(ocrRequest.getNucleicAcidTestingTime());
                        nucleicAcidReport.setOcrNucleicAcidTestingResult(ocrRequest.getNucleicAcidTestingResult());
                        nucleicAcidReport.setReportUrl(ocrRequest.getDetectionReportUrl());
                    }
                    nucleicAcidReport.setConfirmNucleicAcidTestingResult(infoPO.getNucleicAcidTestingResult());
                    if (infoPO.getNucleicAcidTestingTime() != null) {
                        nucleicAcidReport.setConfirmNucleicAcidTestingTime(DateUtil.getTimeStr(infoPO.getNucleicAcidTestingTime(), DateUtil.YYYYMMDD));
                    }
                    DrvDriverPO driverPO = drvDrvierRepository.queryByPk(approvePO.getApproveSourceId());
                    if (driverPO != null) {
                        nucleicAcidReport.setDriverName(driverPO.getDrvName());
                        nucleicAcidReport.setIdNumber(TmsTransUtil.encrypt(driverPO.getDrvIdcard(), KeyType.Identity_Card));
                    }
                    detailSOADTO.setNucleicAcidReport(nucleicAcidReport);
                }
            }
            if (approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.vaccine.getCode().intValue()) {
                DrvEpidemicPreventionControlInfoPO infoPO = drvEpidemicPreventionControlInfoRepository.queryByDrvId(approvePO.getApproveSourceId());
                if (infoPO != null) {
                    VaccinationReport vaccinationReport = new VaccinationReport();
                    SaveDriverSafetyInfoSOARequestType ocrRequest = JsonUtil.fromJson(infoPO.getVaccineReportTempContent(), new TypeReference<SaveDriverSafetyInfoSOARequestType>() {
                    });
                    if (ocrRequest != null) {
                        vaccinationReport.setOcrDriverName(ocrRequest.getDriverName());
                        vaccinationReport.setOcrIdNumber(ocrRequest.getDriverIdNumber());
                        vaccinationReport.setOcrVaccinationCount(ocrRequest.getVaccinationCount());
                        vaccinationReport.setOcrFirstVaccinationTime(ocrRequest.getFirstVaccinationTime());
                        vaccinationReport.setOcrSecondVaccinationTime(ocrRequest.getSecondVaccinationTime());
                        vaccinationReport.setReportUrl(ocrRequest.getDetectionReportUrl());
                    }
                    vaccinationReport.setConfirmVaccinationCount(infoPO.getVaccinationCount());
                    if (infoPO.getFirstVaccinationTime() != null) {
                        vaccinationReport.setConfirmFirstVaccinationTime(BaseUtil.dealWithDateToStr(infoPO.getFirstVaccinationTime()));
                    }
                    if (infoPO.getSecondVaccinationTime() != null) {
                        vaccinationReport.setConfirmSecondVaccinationTime(BaseUtil.dealWithDateToStr(infoPO.getSecondVaccinationTime()));
                    }
                    DrvDriverPO driverPO = drvDrvierRepository.queryByPk(approvePO.getApproveSourceId());
                    if (driverPO != null) {
                        vaccinationReport.setDriverName(driverPO.getDrvName());
                        vaccinationReport.setIdNumber(TmsTransUtil.encrypt(driverPO.getDrvIdcard(), KeyType.Identity_Card));
                    }
                    detailSOADTO.setVaccinationReport(vaccinationReport);
                }
            }
            if (approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.headPortraitCompliance.getCode().intValue()) {
                HeadPortraitSOADto headPortraitSOADto = new HeadPortraitSOADto();
                List<ApproveContentSOADTO> contentSOADTOList = JsonUtil.fromJson(approvePO.getApproveContent(), new TypeReference<List<ApproveContentSOADTO>>() {
                });
                if (CollectionUtils.isNotEmpty(contentSOADTOList)) {
                    ApproveContentSOADTO contentSOADTO = contentSOADTOList.get(0);
                    headPortraitSOADto.setChangeItem("drvHeadPortraitImg");
                    headPortraitSOADto.setSrcUrl(contentSOADTO.getOriginalValue());
                    headPortraitSOADto.setNewUrl(contentSOADTO.getChangeValue());
                    headPortraitSOADto.setOcrheadPortraitResult(false);
                    if (contentSOADTOList.size() > 1) {
                        ApproveContentSOADTO ocrContentSOADTO = contentSOADTOList.get(1);
                        if (ocrContentSOADTO != null) {
                            headPortraitSOADto.setOcrheadPortraitResult(Boolean.valueOf(ocrContentSOADTO.getChangeValue()));
                        }
                    }
                }
                detailSOADTO.setHeadPortraitDto(headPortraitSOADto);
            }
            Map<String,Object> resultFieldsMap = getSourceFields(approvePO.getApproveSourceType(),approvePO.getApproveSourceId());
            Integer areaScope = resultFieldsMap.get("areaScope") == null?0: (Integer) resultFieldsMap.get("areaScope");
            Long supplierId = resultFieldsMap.get("supplierId") == null?0: (Long) resultFieldsMap.get("supplierId");
            detailSOADTO.setAreaScope(areaScope);
            detailSOADTO.setSupplierId(supplierId);
            return Result.Builder.<QueryTransportApproveDetailSOADTO>newResult().success().withData(detailSOADTO).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Map<String,String> querySupplierByApproveId(List<Long> approveIds) {
        List<TmsTransportApprovePO> approvePOList = approveRepository.queryApproveListByIds(approveIds);
        if(org.springframework.util.CollectionUtils.isEmpty(approvePOList)){
            return new HashMap<>();
        }
        Map<String,String> result = new HashMap<>();
        for (TmsTransportApprovePO tmsTransportApprovePO : approvePOList) {
            result.put(tmsTransportApprovePO.getId().toString(),tmsTransportApprovePO.getSupplierId().toString());
        }
        return result;
    }

    public Boolean judgeOperationPermissions(String accountId){
        if(MapUtils.isEmpty(SessionHolder.getSessionSource())){
            return Boolean.FALSE;
        }
        String accountType = SessionHolder.getSessionSource().get("accountType");
        String nowAccountId =SessionHolder.getSessionSource().get("accountId");
        if(StringUtils.isEmpty(accountType) || StringUtils.isEmpty(nowAccountId)){
            return Boolean.FALSE;
        }
        if(Integer.parseInt(accountType) == TmsTransportConstant.AccountTypeEnum.OFFLINE.getValue().intValue()){
            return Boolean.TRUE;
        }
        if(Objects.equals(accountId,nowAccountId)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 将审批流中核核结果json转换list数据
     * @param certificateCheckResultJson
     * @return
     */
    private List<CertificateResultSOADTO> getCertificateCheckResult(String certificateCheckResultJson){
        if(StringUtils.isEmpty(certificateCheckResultJson)){
            return Collections.emptyList();
        }
        List<CertificateResultSOADTO> resultSOADTOList = Lists.newArrayList();
        try {
            List<TmsCertificateCheckPO> checkResultList = JsonUtil.fromJson(certificateCheckResultJson, new TypeReference<List<TmsCertificateCheckPO>>() {
            });
            if(!CollectionUtils.isNotEmpty(checkResultList)){
                return resultSOADTOList;
            }
            for(TmsCertificateCheckPO posoadto : checkResultList){
                CertificateResultSOADTO soadto  = new CertificateResultSOADTO();
                BeanUtils.copyProperties(posoadto,soadto);
                soadto.setInfoList(Lists.newArrayList());
                if(StringUtils.isNotEmpty(posoadto.getCheckResult())){
                    soadto.setInfoList(JsonUtil.fromJson(posoadto.getCheckResult(), new TypeReference<List<CertificateCheckSOAInfo>>() {
                    }));
                }
                resultSOADTOList.add(soadto);
            }
            return resultSOADTOList;
        }catch (Exception e){
            return Collections.emptyList();
        }
    }


    private QueryApproveListDO buildDO(QueryTransportApproveListSOARequestType requestType){
        QueryApproveListDO approveListDO = new QueryApproveListDO();
        BeanUtils.copyProperties(requestType,approveListDO);
        approveListDO.setAccountId(SessionHolder.getSessionSource().get("accountId"));
        approveListDO.setPageNo(requestType.getPaginator().getPageNo());
        approveListDO.setPageSize(requestType.getPaginator().getPageSize());
        return approveListDO;
    }

    private String getApproveName(String approveNameKey){
        if(StringUtils.isEmpty(approveNameKey)){
            return "";
        }
        String[] approveNames = approveNameKey.split("_");
        return approveNames[0] + SharkUtils.getSharkValue(approveNames[1]);
    }

    //返回网约车申诉材料
    public List<ApproveContentSOADTO> buildApproveContentList(TmsTransportApprovePO approvePO){
        List<ApproveContentSOADTO> contentSOADTOList = Lists.newArrayList();
        if(StringUtils.isEmpty(approvePO.getApproveContent())){
            return contentSOADTOList;
        }
        contentSOADTOList = JsonUtil.fromJson(approvePO.getApproveContent(), new TypeReference<List<ApproveContentSOADTO>>() {
        });
        if(CollectionUtils.isEmpty(contentSOADTOList)){
            return contentSOADTOList;
        }

        //特殊字段中文处理，车辆颜色转为中文
        for(ApproveContentSOADTO soadto : contentSOADTOList){
            if(Objects.equals(soadto.getChangeItem(),TmsTransportConstant.CertificateTypeEnum.VEHICLECOLORID.getMsg()) && StringUtils.isNotEmpty(soadto.getChangeValue()) && StringUtils.isNotEmpty(soadto.getOriginalValue())){
                soadto.setChangeValue(enumRepository.getColorName(Long.valueOf(soadto.getChangeValue())));
                soadto.setOriginalValue(enumRepository.getColorName(Long.valueOf(soadto.getOriginalValue())));
            }
        }

        Map<String,ApproveContentSOADTO> appealMaterialsMap = this.getAppealMaterials(approvePO.getApproveSourceType(),approvePO.getApproveSourceId());
        if(MapUtils.isEmpty(appealMaterialsMap)){
            return contentSOADTOList;
        }
        List<String> changeItemList = contentSOADTOList.stream().map(ApproveContentSOADTO::getChangeItem).collect(Collectors.toList());
        if(changeItemList.contains(Constant.DRV_APPROVE_NEED_KEY[0]) && appealMaterialsMap.get(DRVLICENSEAPPEALMATERIALS) != null){
            contentSOADTOList.add(appealMaterialsMap.get(DRVLICENSEAPPEALMATERIALS));
        }
        if(changeItemList.contains(Constant.VEHICLE_APPROVE_NEED_KEY[0]) && appealMaterialsMap.get(VEHICLELICENSEAPPEALMATERIALS) != null){
            contentSOADTOList.add(appealMaterialsMap.get(VEHICLELICENSEAPPEALMATERIALS));
        }
        if((changeItemList.contains(Constant.DRV_APPROVE_NEED_KEY[1]) || changeItemList.contains(Constant.VEHICLE_APPROVE_NEED_KEY[1])) && appealMaterialsMap.get(NETAPPEALMATERIALS)!=null){
            contentSOADTOList.add(appealMaterialsMap.get(NETAPPEALMATERIALS));
        }
        return contentSOADTOList;
    }


    public Map<String,Object> getSourceFields(Integer approveSourceType,Long  approveSourceId){
        Map<String,Object> resultFieldMap = Maps.newHashMap();
        Integer areaScope = null;
        Long supplierId = null;
        switch (TmsTransportConstant.ApproveSourceTypeEnum.getCode(approveSourceType)){
            case DRV:
                DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(approveSourceId);
                areaScope = drvDriverPO.getInternalScope();
                supplierId = drvDriverPO.getSupplierId();
                break;
            case VEHICLE:
                VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(approveSourceId);
                areaScope = enumRepository.getAreaScope(vehVehiclePO.getCityId());
                supplierId = vehVehiclePO.getSupplierId();
        }
        resultFieldMap.put("areaScope",areaScope);
        resultFieldMap.put("supplierId",supplierId);
        return resultFieldMap;
    }

    public Map<String,ApproveContentSOADTO> getAppealMaterials(Integer approveSourceType,Long  approveSourceId){
        String netAppealMaterials = "";
        String cardAppealMaterials = "";
        Map<String,String> AppealMaterialsMap = Maps.newHashMap();
        switch (TmsTransportConstant.ApproveSourceTypeEnum.getCode(approveSourceType)){
            case DRV:
                DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(approveSourceId);
                if(drvDriverPO != null){
                    netAppealMaterials = drvDriverPO.getNetAppealMaterials();
                    cardAppealMaterials = drvDriverPO.getDrvLicenseAppealMaterials();
                    AppealMaterialsMap.put(NETAPPEALMATERIALS,netAppealMaterials);
                    AppealMaterialsMap.put(DRVLICENSEAPPEALMATERIALS,cardAppealMaterials);
                }
                break;
            case VEHICLE:
                VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(approveSourceId);
                if(vehVehiclePO != null){
                    netAppealMaterials = vehVehiclePO.getNetAppealMaterials();
                    cardAppealMaterials = vehVehiclePO.getVehicleLicenseAppealMaterials();
                    AppealMaterialsMap.put(NETAPPEALMATERIALS,netAppealMaterials);
                    AppealMaterialsMap.put(VEHICLELICENSEAPPEALMATERIALS,cardAppealMaterials);
                }
                break;
        }
        List<ApproveContentSOADTO> dataLists = Lists.newArrayList();
        for(Map.Entry<String,String> entry : AppealMaterialsMap.entrySet()){
            if(StringUtils.isNotEmpty(entry.getValue())){
                ApproveContentSOADTO approveContentSOADTO = new ApproveContentSOADTO();
                approveContentSOADTO.setChangeItem(entry.getKey());
                approveContentSOADTO.setChangeValue(entry.getValue());
                dataLists.add(approveContentSOADTO);
            }
        }
        if(CollectionUtils.isEmpty(dataLists)){
            return Collections.emptyMap();
        }
        ;
        return dataLists.stream().collect(Collectors.toMap(ApproveContentSOADTO::getChangeItem, a -> a,(k1, k2)->k1));

    }
}
