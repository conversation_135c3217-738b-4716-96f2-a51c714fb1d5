package com.ctrip.dcs.tms.transport.application.query.driverguide.impl;

import com.ctrip.dcs.tms.transport.api.model.TransportGroup;
import com.ctrip.dcs.tms.transport.application.query.driverguide.DriverGuidQueryProcessService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverGuideDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.VehVehicleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidVehicleRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryTransportGroupModel;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.QUERY_DRIVER_WITH_NO_DRIVER_ID_LIST_AND_PHONE_LIST;
import static com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil.queryEncryptPhone;

@Service
public class DriverGuidQueryProcessServiceImpl implements DriverGuidQueryProcessService {

  private static final Logger logger = LoggerFactory.getLogger(DriverGuidQueryProcessServiceImpl.class);

  private static final String QUERY_DRIVER_WITH_DRIVER_ID_LIST_OR_PHONE_LIST = "query.driver.with.driver.id.list.or.phone.list";

  @Autowired
  DriverGuideProxy driverGuidProxy;

  @Autowired
  TransportGroupRepository transportGroupRepository;

  @Autowired
  TmsTransportQconfig qconfig;

  @Override
  public List<VehVehicleDTO> queryVehicleInfo(DriverGuidVehicleRequestDTO request) {
    return driverGuidProxy.getVehicleInfo(request);
  }

  @Override
  public List<VehVehicleDTO> searchVehicleInfo(DriverGuidVehicleRequestDTO request) {
    return driverGuidProxy.searchVehicle(request);
  }

  /**
   * 司导数据有可能不在灰度中，所以需要查询出来之后进行一次过滤
   * @param vehVehicleDtoS
   * @return
   */
  private List<VehVehicleDTO> filterVehicleByGray(List<VehVehicleDTO> vehVehicleDtoS) {
    if (CollectionUtils.isEmpty(vehVehicleDtoS)) {
      return vehVehicleDtoS;
    }

    List<Long> supplierIdList = vehVehicleDtoS.stream().map(VehVehicleDTO::getSupplierId).distinct().collect(Collectors.toList());
    Map<Long, Boolean> supplierGrayMap = Maps.newHashMap();
    for (Long supplierId : supplierIdList) {
      supplierGrayMap.put(supplierId, driverGuidProxy.getGrayControl(supplierId));
    }

    return vehVehicleDtoS.stream().filter(vehVehicleDTO -> Objects.equals(supplierGrayMap.get(vehVehicleDTO.getSupplierId()), true)).collect(Collectors.toList());

  }

  /**
   * 不接受单独只有一个driverName的查询
   * 目前也没有根据运力组进行查询的情况了
   * @param request
   * @return
   */
  @Override
  public List<DriverGuideDTO> queryDriverInfo(DriverGuidDriverRequestDTO request) {
    request.setDriverPhoneList(queryEncryptPhone(request.getDriverPhoneList()));
    if (CollectionUtils.isEmpty(request.getDriverIdList()) && CollectionUtils.isEmpty(request.getDriverPhoneList())) {
      Cat.logEvent(Constant.EventType.BRIDGE, QUERY_DRIVER_WITH_NO_DRIVER_ID_LIST_AND_PHONE_LIST);
        logger.warn(QUERY_DRIVER_WITH_NO_DRIVER_ID_LIST_AND_PHONE_LIST, "request: {}", request);
        return Lists.newArrayList();
    }

    // 判断包车产线是否有系统查询的时候，包含多个司机ID来查询的情况
    if (Objects.isNull(request.getSupplierId()) && CollectionUtils.isNotEmpty(request.getDriverIdList())) {
        Cat.logEvent("day-product-line-driver-id-size", String.valueOf(request.getDriverIdList().size()));
    }

    // 如果司机id和手机号同时存在，只根据司机ID去查询，然后在后面的filter里面根据手机号进行过滤，因为司导司机id和手机号同时存在是或的查询关系
    List<String> driverPhoneList = request.getDriverPhoneList();
    if(CollectionUtils.isNotEmpty(request.getDriverIdList())) {
      request.setDriverPhoneList(null);
    }
    Cat.logEvent(Constant.EventType.BRIDGE, QUERY_DRIVER_WITH_DRIVER_ID_LIST_OR_PHONE_LIST);
    List<DriverGuideDTO> driverInfo = driverGuidProxy.getDriverInfo(request);
    request.setDriverPhoneList(driverPhoneList);
    List<DriverGuideDTO> driverGuideDTOList = filter(driverInfo, request);

    //查询运力组
    return queryDriverInfoByTransportGroups(driverGuideDTOList, request.getSupplierId());
  }

  protected List<DriverGuideDTO> queryDriverInfoByTransportGroups(List<DriverGuideDTO> driverGuideDTOList, Long supplierId) {
    if (CollectionUtils.isEmpty(driverGuideDTOList)) {
      return driverGuideDTOList;
    }

    if (Objects.isNull(supplierId)) {
      if (qconfig.useDriverOwnerSupplierForTransportGroup()) {
        Cat.logEvent("useDriverOwnerSupplierForTransportGroup", "true");
        return queryDriverInfoByTransportGroups(driverGuideDTOList);
      }
      return driverGuideDTOList;
    }

    QueryTransportGroupModel queryModel = new QueryTransportGroupModel();
    queryModel.setSupplierId(supplierId);
    queryModel.setGroupStatus(TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode());
    //      queryModel.setTransportGroupMode(); // 要不要根据分类进行查询
    List<TspTransportGroupPO> tspTransportGroupPoList = transportGroupRepository.queryTransportGroups(queryModel);
    driverGuideDTOList.forEach(driverGuidDTO -> {
      driverGuidDTO.setTransportGroups(getTransportGroups(tspTransportGroupPoList));
    });

    return driverGuideDTOList;
  }

  private List<DriverGuideDTO> queryDriverInfoByTransportGroups(List<DriverGuideDTO> driverGuideDTOList) {
    List<Long> supplierIdList = Optional.ofNullable(driverGuideDTOList).orElse(Lists.newArrayList()).stream()
      .map(DrvDriverPO::getSupplierId).distinct().collect(Collectors.toList());
    if (CollectionUtils.isEmpty(supplierIdList)) {
      return driverGuideDTOList;
    }

    // 理论上只有一个供应商
    Map<Long, List<TspTransportGroupPO>> supplierTransportGroupMap = Maps.newHashMap();
    for (Long supplierId : supplierIdList) {
      QueryTransportGroupModel queryModel = new QueryTransportGroupModel();
      queryModel.setSupplierId(supplierId);
      queryModel.setGroupStatus(TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode());
      //      queryModel.setTransportGroupMode(); // 要不要根据分类进行查询
      supplierTransportGroupMap.put(supplierId, transportGroupRepository.queryTransportGroups(queryModel));
    }

    driverGuideDTOList.forEach(driverGuidDTO -> {
      driverGuidDTO.setTransportGroups(getTransportGroups(Optional.ofNullable(supplierTransportGroupMap.get(driverGuidDTO.getSupplierId())).orElse(Lists.newArrayList())));
    });

    return driverGuideDTOList;
  }

  protected List<TransportGroup> getTransportGroups(List<TspTransportGroupPO> tspTransportGroupPoList) {
    List<TransportGroup> list = new ArrayList<>();
    for (TspTransportGroupPO tspTransportGroupPo : Optional.ofNullable(
      tspTransportGroupPoList).orElse(Lists.newArrayList())) {
      if (!Objects.equals(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(), tspTransportGroupPo.getCategorySynthesizeCode())) {
        continue;
      }
      TransportGroup transportGroup = new TransportGroup();
      transportGroup.setTransportGroupId(tspTransportGroupPo.getTransportGroupId());
      transportGroup.setTransportGroupName(tspTransportGroupPo.getTransportGroupName());
      transportGroup.setTransportGroupMode(tspTransportGroupPo.getTransportGroupMode());
      list.add(transportGroup);
    }
    return list;
  }

  protected List<DriverGuideDTO> filter(List<DriverGuideDTO> list, DriverGuidDriverRequestDTO request) {
    // 过滤司机姓名和手机号，状态
    if (StringUtils.isNotBlank(request.getDriverName())) {
      list.removeIf(driverGuidDTO -> !Objects.equals(request.getDriverName(), driverGuidDTO.getDriverName()));
    }
    if (CollectionUtils.isNotEmpty(request.getDriverPhoneList())) {
      list.removeIf(driverGuidDTO -> !request.getDriverPhoneList().contains(driverGuidDTO.getDrvPhone()));
    }
    if (request.getStatus() != null) {
      list.removeIf(driverGuidDTO -> !Objects.equals(request.getStatus(), driverGuidDTO.getDrvStatus()));
    }
    return list;
  }

//  @Override
//  public List<DriverGuideDTO> searchDriverInfo(DriverGuidDriverRequestDTO request) {
//    request.setDriverPhoneList(queryEncryptPhone(request.getDriverPhoneList()));
//    return filterByGray(driverGuidProxy.searchDriver(request));
//  }

  @Override
  public List<Long> queryDrvIdByTransportGroups(DriverGuidDriverRequestDTO request) {
    return driverGuidProxy.queryDriverIdBySupplier(request);
  }

//  /**
//   * 过滤不在灰度中的数据
//   * @param driverGuideDtoS
//   * @return
//   */
//  private List<DriverGuideDTO> filterByGray(List<DriverGuideDTO> driverGuideDtoS) {
//    if (CollectionUtils.isEmpty(driverGuideDtoS)) {
//      return Lists.newArrayList();
//    }
//    List<Long> supplierIdList = driverGuideDtoS.stream().map(DriverGuideDTO::getSupplierId).distinct().collect(Collectors.toList());
//    Map<Long, Boolean> supplierGrayMap = Maps.newHashMap();
//    for (Long supplierId : supplierIdList) {
//      supplierGrayMap.put(supplierId, driverGuidProxy.getGrayControl(supplierId));
//    }
//
//    return driverGuideDtoS.stream().filter(driverGuideDTO -> Objects.equals(supplierGrayMap.get(driverGuideDTO.getSupplierId()), true)).collect(
//      Collectors.toList());
//  }

  @Override
  public Boolean getGrayControl(DriverGuidDriverRequestDTO request) {
    return getGrayControl(request.getSupplierId());
  }

  @Override
  public Boolean getGrayControl(Long supplierId) {
    return driverGuidProxy.getGrayControl(supplierId);
  }
}
