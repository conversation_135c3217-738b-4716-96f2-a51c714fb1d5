package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.DriverCommandHandler;
import com.ctrip.dcs.tms.transport.application.dto.DrvAddContext;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.igt.framework.common.result.Result;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 境内司机
 * <AUTHOR>
 * @date 2025/6/13
 */
@Service
public class DomesticDriverCommandHandler implements DriverCommandHandler {

  @Override
  public Result<Boolean> afterAdd(DrvAddContext context) {
    return ResponseResultUtil.success();
  }

  /**
   * ivr回调处理
   *
   * @param phoneDTO 对话对象
   */
  @Override
  public void afterIvrCall(PhoneDTO phoneDTO) {
    // Do nothing
  }

  @Override
  public Boolean support(Integer internalAreaScope) {
    return Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), internalAreaScope);
  }
}
