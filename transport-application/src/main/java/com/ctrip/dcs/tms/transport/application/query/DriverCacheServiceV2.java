package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO;

import java.util.*;

/**
 * <AUTHOR>
 */
public interface DriverCacheServiceV2 {

    String DRIVER_CACHE_SEQUENCE = "dcSequence";
    String DRIVER_V2_CACHE_PREFIX = "dcV2";
    String VEHICLE_V2_CACHE_PREFIX = "vcV2";
    String NO_RESULT_WITH_DRIVER_DATA = "no matching driver";
    String NO_RESULT_WITH_TRANSPORT_GROUP = "no matching transport group";
    String[] ADDRESS_MAP_KEY = new String[]{"latitude", "longitude", "name", "address"};

    /**
     * 单元缓存整合接口
     *
     * @param paramDTO
     * @return List<DriverInfo>
     */
    List<DriverInfo> packageQuery(QueryDriverFromCacheParamDTO paramDTO);

    /**
     * 清除司机的缓存
     * @param drvId
     */
    void clearDrvCache(Long drvId) throws InterruptedException;

    void clearDrvCacheImmediately(Long drvId) throws InterruptedException;

    /**
     * 清除车辆的缓存
     * @param vehId
     */
    void clearVehCache(Long vehId) throws InterruptedException;

}
