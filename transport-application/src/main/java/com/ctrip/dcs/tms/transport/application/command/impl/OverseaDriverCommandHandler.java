package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.DriverCommandHandler;
import com.ctrip.dcs.tms.transport.application.command.DriverCommandService;
import com.ctrip.dcs.tms.transport.application.dto.DrvAddContext;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.AreaScopeTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CatEventType;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.DrvInActiveEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.TmsDrvInactiveReasonGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.result.Result;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;

/**
 * 境外司机
 * <AUTHOR>
 * @date 2025/6/13
 */
@Service
public class OverseaDriverCommandHandler implements DriverCommandHandler {

  @Autowired
  IVRCallService ivrCallService;

  @Autowired
  TmsQmqProducer tmsQmqProducer;

  @Autowired
  TmsDrvInactiveReasonGateway tmsDrvInactiveReasonGateway;

  @Autowired
  DrvDrvierRepository drvDrvierRepository;

  @Autowired
  DriverCommandService driverCommandService;

  @Override
  public Result<Boolean> afterAdd(DrvAddContext context) {

    DrvDriverPO driverPo = context.getDrvDriverPO();

    // H5来源不进行IVR校验
    if (Objects.equals(driverPo.getDrvFrom(), TmsTransportConstant.DrvFromEnum.DRV_AUTO.getCode())) {
      Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "callIVR_skip_h5_source", Event.SUCCESS, "driverId:" + driverPo.getDrvId());
      return ResponseResultUtil.success();
    }

    // 境内的不进行IVR校验
    if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), driverPo.getInternalScope())) {
      Cat.logEvent(CatEventType.DRIVER_ACTIVATION, "callIVR_skip_domestic", Event.SUCCESS, "driverId:" + driverPo.getDrvId());
      return ResponseResultUtil.success();
    }

    // 发送 ivr 消息
    ivrCallService.asyncSendIvrByLocalTimeForIvrCall(driverPo.getCityId(), new PhoneDTO(driverPo.getDrvPhone(), driverPo.getIgtCode()),
        driverPo.getDrvLanguage());
    return ResponseResultUtil.success();
  }

  /**
   * ivr回调处理
   *
   * @param phoneDTO 对话对象
   */
  @Override
  public void afterIvrCall(PhoneDTO phoneDTO) {

    // 获取消息中的参数
    String phoneNumber = phoneDTO.getMobilePhone();
    String igtCode = phoneDTO.getCountryCode();

    DrvDriverPO driverPo = drvDrvierRepository.queryOneDrvByPhone(phoneNumber);
    if (driverPo == null) {
      return;
    }

    Long drvId = driverPo.getDrvId();
    // 上线兼容
    if (StringUtils.isBlank(igtCode)) {
      igtCode = driverPo.getIgtCode();
    }

    // 1 查询IRV结果
    boolean isConnected = ivrCallService.isPhoneVerified(
        PhoneDTO.builder().mobilePhone(phoneNumber).countryCode(igtCode).build());

    // 2 根据电话接通状态进行相应处理
    if (isConnected) {
      // 电话接通后的处理逻辑
      //2.1 删除IVR未验证卡控,激活司机
      boolean enableActive =
          tmsDrvInactiveReasonGateway.enableActiveAfterDeleteReasonByCode(drvId,
              Collections.singletonList(
                  DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode()),
              "system");
      //处于未激活的司机，进行激活操作
      if (enableActive && Objects.equals(TmsTransportConstant.DrvStatusEnum.UNACT.getCode(), driverPo.getDrvStatus())) {
        driverCommandService.updateDrvStatus(Lists.newArrayList(drvId), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(), "system");
      }
      return;
    }

    //3 电话未接通的处理逻辑
    //3.1 在未激活原因表中添加一条记录，原因是该手机IVR认证失败
    Cat.logEvent(CatEventType.IVR_CALL_RESULT, "add_ivr_failure_reason", "WARN", "drvId:" + drvId);
    tmsDrvInactiveReasonGateway.insertWhenNotExist(TmsDrvInactiveReasonPO.builder()
        .drvId(drvId)
        .reasonCode(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode())
        .reasonDesc(DrvInActiveEnum.getReasonDesc(DrvInActiveEnum.IVR_AUTHENTICATION_FAILURE.getCode()))
        .modifyUser(phoneDTO.getModifyUser())
        .active(true)
        .build());
  }

  @Override
  public Boolean support(Integer internalAreaScope) {
    return Objects.equals(AreaScopeTypeEnum.OVERSEAS.getCode(), internalAreaScope);
  }
}
