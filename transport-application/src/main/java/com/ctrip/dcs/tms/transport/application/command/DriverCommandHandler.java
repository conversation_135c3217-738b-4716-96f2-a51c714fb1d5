package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.application.dto.DrvAddContext;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.igt.framework.common.result.Result;

/**
 * <AUTHOR>
 * @date 2025/6/13
 */
public interface DriverCommandHandler {

  /**
   * 新增司机的逻辑处理
   * @param context 新增司机的上下文
   * @return 新增司机的结果
   */
  Result<Boolean> afterAdd(DrvAddContext context);

  /**
   * ivr回调处理
   * @param phoneDTO 对话对象
   */
  void afterIvrCall(PhoneDTO phoneDTO);

  Boolean support(Integer internalAreaScope);
}
