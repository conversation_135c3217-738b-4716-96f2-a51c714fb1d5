package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.CtripCommonUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SharkUtils;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2020/9/22 16:00
 */
@Component
public class DriverAccountCommandServiceImpl implements DriverAccountCommandService {

  private static final String RESET_PWD_EMAIL_TITLE = "【携程用车】忘记密码（Forgot password）";
  private static final String RESET_PWD_EMAIL_TEMPLATE = "<p>%s，您好：</p><p>您的新密码为%s，请注意保存，谨防泄露</p>";
  private static final String RESET_PWD_MSG_TEMPLATE_CODE = "310503";

  private final static Result<DrvDriverPO> RESULT_OF_DRIVER_NOT_EXIST;
  private final static Result<DrvDriverPO> RESULT_OF_DRIVER_NOT_UNIQUE;
  private final static Result<DrvDriverPO> RESULT_OF_PWD_NOT_PASS;
  private final static Result<DrvDriverPO> RESULT_OF_PWD_NOT_VALID;
  private final static Result<DrvDriverPO> RESULT_OF_CONFIRM_PWD_NOT_EQUAL;
  private final static Result<DrvDriverPO> RESULT_OF_NOTIFY_FAIL;

  static {
    RESULT_OF_DRIVER_NOT_EXIST = Result.Builder.<DrvDriverPO>newResult().fail().withCode("404").build();
    RESULT_OF_DRIVER_NOT_UNIQUE = Result.Builder.<DrvDriverPO>newResult().fail().withCode("402").build();
    RESULT_OF_PWD_NOT_PASS = Result.Builder.<DrvDriverPO>newResult().fail().withCode("403").build();
    RESULT_OF_PWD_NOT_VALID = Result.Builder.<DrvDriverPO>newResult().fail().withCode("405").build();
    RESULT_OF_CONFIRM_PWD_NOT_EQUAL = Result.Builder.<DrvDriverPO>newResult().fail().withCode("406").build();
    RESULT_OF_NOTIFY_FAIL = Result.Builder.<DrvDriverPO>newResult().fail().withCode("409").build();
  }

  @Autowired
  private DrvDrvierRepository drvDrvierRepository;
  @Autowired
  private CommonCommandService commonCommandService;
  @Autowired
  private DriverPasswordService driverPasswordService;
  @Autowired
  private EmailTemplateQconfig emailTemplateQconfig;

  @Autowired
  private DriverQueryService driverQueryService;

  @Override
  public Result<DrvDriverPO> resetPwd(String hybridAccount) {

    List<DrvDriverPO> drivers = driverQueryService.queryDrvDriverListByAccount(hybridAccount);

    if (CollectionUtils.isEmpty(drivers)) {
//      return RESULT_OF_DRIVER_NOT_EXIST;
      return CtripCommonUtils.resultDrvAccountErrorInfo("404");
    }
    if (drivers.size() > 1) {
//      return RESULT_OF_DRIVER_NOT_UNIQUE;
      return CtripCommonUtils.resultDrvAccountErrorInfo("402");
    }

    DrvDriverPO driver = drivers.get(0);
    String newPwd = driverPasswordService.genResetPwd();
    String salt = driverPasswordService.genPwdSalt();
    String encNewPwd = driverPasswordService.encryptPwd(newPwd, salt);
    driver.setLoginPwd(encNewPwd);
    driver.setSalt(salt);
    driver.setModifyUser(String.format(TmsTransportConstant.TMS_DEFAULT_USERNAME_DRIVER_FORMAT,driver.getDrvName(),driver.getDrvId()));
    drvDrvierRepository.updateDrv(driver);

    boolean isSuccess = notifyDriver(driver, newPwd);
    if (isSuccess) {
      return Result.Builder.<DrvDriverPO>newResult().success().withData(driver).build();
    }
    return RESULT_OF_NOTIFY_FAIL;
  }

  private boolean notifyDriver(DrvDriverPO driver, String newPwd) {
    if (Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(), driver.getInternalScope())) {
      Map<String, String> param = Maps.newHashMapWithExpectedSize(4);
      param.put("LoginPassword", newPwd);
      Result<Boolean> result =
          commonCommandService.sendMessageByPhone(driver.getIgtCode(), driver.getDrvPhone(), RESET_PWD_MSG_TEMPLATE_CODE, param);
      return result != null && result.isSuccess();
    }

    String content = buildResetPwdEmailContent(driver.getDrvName(), newPwd);
    Result<Boolean> result = commonCommandService.sendEmail(SharkUtils.getSharkValue(SharkKeyConstant.driverLoginEmailForgetPassword), driver.getEmail(), content);
    return result != null && result.isSuccess();
  }

  @Override
  public Result<DrvDriverPO> updatePwd(Long drvId, String oldPwd, String newPwd, String confirmPwd) {
    DrvDriverPO driver = drvDrvierRepository.queryByPk(drvId);
    if (driver == null) {
//      return RESULT_OF_DRIVER_NOT_EXIST;
      return CtripCommonUtils.resultDrvAccountErrorInfo("404");
    }
    if (!driverPasswordService.isPasswordValid(newPwd)) {
//      return RESULT_OF_PWD_NOT_VALID;
      return CtripCommonUtils.resultDrvAccountErrorInfo("405");
    }
    if (!newPwd.equals(confirmPwd)) {
//      return RESULT_OF_CONFIRM_PWD_NOT_EQUAL;
      return CtripCommonUtils.resultDrvAccountErrorInfo("406");
    }
    if (StringUtils.isNotEmpty(driver.getLoginPwd())) {
      if (!driverPasswordService.isPasswordEqual(oldPwd, driver.getLoginPwd(), driver.getSalt())) {
//        return RESULT_OF_PWD_NOT_PASS;
        return CtripCommonUtils.resultDrvAccountErrorInfo("403");
      }
    }

    String salt = driverPasswordService.genPwdSalt();
    String encNewPwd = driverPasswordService.encryptPwd(newPwd, salt);
    driver.setLoginPwd(encNewPwd);
    driver.setSalt(salt);
    driver.setModifyUser(String.format(TmsTransportConstant.TMS_DEFAULT_USERNAME_DRIVER_FORMAT,driver.getDrvName(),driver.getDrvId()));
    drvDrvierRepository.updateDrv(driver);

    return Result.Builder.<DrvDriverPO>newResult().success().withData(driver).build();
  }

  private String buildResetPwdEmailContent(String name, String resetPwd) {
    return String.format(SharkUtils.getSharkValue(SharkKeyConstant.driverLoginEmailNewpassword), name, resetPwd);
  }
}
