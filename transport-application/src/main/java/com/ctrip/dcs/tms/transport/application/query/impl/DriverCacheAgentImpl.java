package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import io.dropwizard.metrics5.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * 2021-10-28 19:32
 */
@Service
public class DriverCacheAgentImpl implements DriverCacheAgent {

    private static final Logger logger = LoggerFactory.getLogger(DriverCacheAgentImpl.class);

    private static final Integer REG_COUNT_LIMIT = 200;

    @Autowired
    private DriverCacheServiceV2 driverCacheServiceV2;
    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Override
    public List<DriverInfo> queryDriver(QueryDriverFromCacheParamDTO req) {
        ResetTimer.Context context = TransportMetric.cacheTimeConsumingTimer.time();
        try {
            if (req4Clear(req)) {
                return Collections.emptyList();
            }
            List<DriverInfo> res = driverCacheServiceV2.packageQuery(req);
            if (CollectionUtils.isNotEmpty(res) && res.size() >= REG_COUNT_LIMIT) {
                TransportMetric.queryCacheBigDataRecordInc();
            }
            toDealDefault(res);
            return res;
        } finally {
            context.stop();
        }
    }

    public void toDealDefault(List<DriverInfo> driverInfoList) {
        if (CollectionUtils.isEmpty(driverInfoList)) {
            return;
        }
        int defaultInt = 0;
        long defaultLong = 0L;
        String defaultString = "";
        for (DriverInfo driverInfo : driverInfoList) {
            if (driverInfo.getStatus() == null) {
                driverInfo.setStatus(defaultInt);
            }
            if (driverInfo.getBroadcast() == null) {
                driverInfo.setBroadcast(defaultInt);
            }
            if (driverInfo.getIsSendWorkPeriod() == null) {
                driverInfo.setIsSendWorkPeriod(defaultInt);
            }
            if (driverInfo.getCompatibleCoopMode() == null) {
                driverInfo.setCompatibleCoopMode(defaultInt);
            }
            if (driverInfo.getCoopMode() == null) {
                driverInfo.setCoopMode(defaultInt);
            }
            if (driverInfo.getDriverPhone() == null) {
                driverInfo.setDriverPhone(defaultString);
            }
            if (driverInfo.getPhoneAreaCode() == null) {
                driverInfo.setPhoneAreaCode(defaultString);
            }
            if (driverInfo.getCarId() == null) {
                driverInfo.setCarId(defaultLong);
            }
            if (driverInfo.getCarLicense() == null) {
                driverInfo.setCarLicense(defaultString);
            }
            if (driverInfo.getCarBrandId() == null) {
                driverInfo.setCarBrandId(defaultLong);
            }
            if (driverInfo.getCarBrandName() == null) {
                driverInfo.setCarBrandName(defaultString);
            }
            if (driverInfo.getCarTypeId() == null) {
                driverInfo.setCarTypeId(defaultInt);
            }
            if (driverInfo.getCarTypeName() == null) {
                driverInfo.setCarTypeName(defaultString);
            }
            if (driverInfo.getCarColorId() == null) {
                driverInfo.setCarColorId(defaultLong);
            }
            if (driverInfo.getCarColor() == null) {
                driverInfo.setCarColor(defaultString);
            }
            if (driverInfo.getIntendVehicleTypeId() == null) {
                driverInfo.setIntendVehicleTypeId(defaultString);
            }
            if (driverInfo.getCityId() == null) {
                driverInfo.setCityId(defaultLong);
            }
            if (driverInfo.getCityName() == null) {
                driverInfo.setCityName(defaultString);
            }
            if (driverInfo.getAddressLongitude() == null) {
                driverInfo.setAddressLongitude(Double.valueOf(0));
            }
            if (driverInfo.getAddressLatitude() == null) {
                driverInfo.setAddressLatitude(Double.valueOf(0));
            }
            if (driverInfo.getIsEnergy() == null) {
                driverInfo.setIsEnergy(defaultInt);
            }
            if (driverInfo.getWorkTimes() == null) {
                driverInfo.setWorkTimes(Lists.newArrayList());
            }
            if (driverInfo.getDriverLanguage() == null) {
                driverInfo.setDriverLanguage(defaultString);
            }
            if (driverInfo.getCountryId() == null) {
                driverInfo.setCountryId(defaultLong);
            }
            if (driverInfo.getCountryName() == null) {
                driverInfo.setCountryName(defaultString);
            }
            if (driverInfo.getSupplierId() == null) {
                driverInfo.setSupplierId(defaultLong);
            }
            if (driverInfo.getEmail() == null) {
                driverInfo.setEmail(defaultString);
            }
            if (driverInfo.getWechat() == null) {
                driverInfo.setWechat(defaultString);
            }
            if (driverInfo.getQunarCityCode() == null) {
                driverInfo.setQunarCityCode(defaultString);
            }
            if (driverInfo.getPicUrl() == null) {
                driverInfo.setPicUrl(defaultString);
            }
            if (driverInfo.getCarSeriesId() == null) {
                driverInfo.setCarSeriesId(defaultLong);
            }
            if (driverInfo.getCarSeriesName() == null) {
                driverInfo.setCarSeriesName(defaultString);
            }
            if (driverInfo.getMaxLuggages() == null) {
                driverInfo.setMaxLuggages(defaultInt);
            }
            if (driverInfo.getMaxPassengers() == null) {
                driverInfo.setMaxPassengers(defaultInt);
            }
            if (driverInfo.getSupplierName() == null) {
                driverInfo.setSupplierName(defaultString);
            }
            if (driverInfo.getVehicleStatus() == null) {
                driverInfo.setVehicleStatus(defaultInt);
            }
            if (driverInfo.getDrvIdcard() == null) {
                driverInfo.setDrvIdcard(defaultString);
            }
            if (driverInfo.getCertiDate() == null) {
                driverInfo.setCertiDate(defaultString);
            }
            if (driverInfo.getExpiryBeginDate() == null) {
                driverInfo.setExpiryBeginDate(defaultString);
            }
            if (driverInfo.getExpiryEndDate() == null) {
                driverInfo.setExpiryEndDate(defaultString);
            }
            if (driverInfo.getDrvLicenseNumber() == null) {
                driverInfo.setDrvLicenseNumber(defaultString);
            }
            if (driverInfo.getVin() == null) {
                driverInfo.setVin(defaultString);
            }
            if (driverInfo.isRideHailingVehCertValid() == null) {
                driverInfo.setRideHailingVehCertValid(false);
            }
            if (driverInfo.isRideHailingDrvCertValid() == null) {
                driverInfo.setRideHailingDrvCertValid(false);
            }
            if (driverInfo.getDrvConnectAddress() == null) {
                driverInfo.setDrvConnectAddress(defaultString);
            }
            if (driverInfo.getOnlineTime() == null) {
                driverInfo.setOnlineTime(defaultString);
            }
            if (driverInfo.getVehRegstDate() == null) {
                driverInfo.setVehRegstDate(defaultString);
            }
            if (driverInfo.getVehCreateTime() == null) {
                driverInfo.setVehCreateTime(defaultString);
            }
            if (driverInfo.getVehBindTime() == null) {
                driverInfo.setVehBindTime(defaultString);
            }
            if (driverInfo.getDrvcardImg() == null) {
                driverInfo.setDrvcardImg(defaultString);
            }
            if (driverInfo.getVehicleFullImg() == null) {
                driverInfo.setVehicleFullImg(defaultString);
            }
            if (driverInfo.getVehProductionLineCodeList() == null) {
                driverInfo.setVehProductionLineCodeList(Lists.newArrayList());
            }
        }
    }

    private boolean req4Clear(QueryDriverFromCacheParamDTO req) {
        if (Strings.isNullOrEmpty(req.getLevel()) || !req.getLevel().contains(clearLevel)) {
            return false;
        }
        // fixme 5 堡垒兜底删除线上日常数据功能
        String clearKey = req.getLevel().contains(DriverCacheServiceV2.DRIVER_V2_CACHE_PREFIX) ? DriverCacheServiceV2.DRIVER_V2_CACHE_PREFIX : DriverCacheServiceV2.VEHICLE_V2_CACHE_PREFIX;
        RedisUtils.remove(BaseUtil.toCacheKeySet(clearKey, BaseUtil.getLongSet(req.getDriverIds())));
        logger.info(DriverCacheAgent.DRV_CACHE_INFO_TITLE, "STRATEGY: clear cache Key:{} Ids:{}", clearKey, req.getDriverIds());
        return true;
    }

}