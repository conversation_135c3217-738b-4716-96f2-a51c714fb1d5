package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.async.task.message.DataCompareMessage;
import com.ctrip.dcs.driver.message.dto.PushMessageDTO;
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.query.DriverCacheServiceV2;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverBackgroundCheckDriverVO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.TransportGroupDriverApplyFailedReasonDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TransportCommonQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuRelationRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EVENT_FAILED;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.EventName.CLEAN_DRIVER_CACHE;

/**
 * QMQ生产者
 * <AUTHOR>
 * @Date 2020/2/27 16:32
 */
@Service("tmsQmqProducerCommandService")
public class TmsQmqProducerCommandServiceImpl implements TmsQmqProducerCommandService {

    @Autowired
    private TmsQmqProducer tmsQmqProducer;
    @Autowired
    private TransportCommonQconfig transportCommonQconfig;
    @Autowired
    private TspTransportGroupSkuRelationRepository tspTransportGroupSkuRelationRepository;
    @Autowired
    TmsTransportQconfig qconfig;
    @Autowired
    OverseasQconfig overseasQconfig;

    @Autowired
    DrvDrvierRepository drvDrvierRepository;

    @Autowired
    DriverAccountManagementHelper driverAccountManagementHelper;
    
    @Autowired
    DriverCacheServiceV2 driverCacheServiceV2;

    private static final Logger logger = LoggerFactory.getLogger(TmsQmqProducerCommandServiceImpl.class);

    /**
     * 司机冻结
     * @param drvId
     * @param freezeHour
     */
    @Override
    public void sendDrvFreezeQmq(long drvId, int freezeHour,Integer isToSendType,Integer accountType) {
        Map<String, Object> params = new HashMap();
        params.put("drvId", drvId);
        params.put("chgAllOrder", 1);
        params.put("batchChgDriverType", 1);
        params.put("startTime", DateUtil.getTimeStr(new Date()));
        params.put("endTime", DateUtil.getTimeStr(DateUtil.getDayShift(new Date(),  freezeHour)));
        params.put("accountType", accountType == null?1:accountType);
        params.put("isToSendType", isToSendType == null?2:isToSendType);
        putSupplierId(drvId,params);
        // 发送司机冻结消息
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERSTATE_CHANGED, Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERSTATE_FREEZE),params);
//        sendDrvUnfreezeQmq(drvId,freezeHour);
    }

    @Override
    public void sendDrvUnfreezeQmq(long drvId, int freezeHour) {
        Map<String, Object> params = new HashMap<>();
        params.put("drvId", drvId);
        params.put("hours", freezeHour);
        putSupplierId(drvId,params);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERSTATE_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERSTATE_UNFREEZE), freezeHour * 60 , TimeUnit.MINUTES,params);
    }

    /**
     * 批量司机冻结
     * @param drvIds
     * @param freezeHour
     */
    @Override
    public void sendDrvFreezeQmq(List<Long> drvIds, int freezeHour,Integer isToSend,Integer accountType) {
        for (Long drvId : drvIds) {
            sendDrvFreezeQmq(drvId, freezeHour,isToSend,accountType);
        }
    }

    /**
     * 司机请假
     * @param drvId
     * @param leaveBeginTime
     * @param leaveEndTime
     */
    @Override
    public void sendDrvLeaveQmq(long drvId, String leaveBeginTime, String leaveEndTime) {
        //批量改派请假时段内的订单
        Map<String, Object> params = new HashMap<>();
        params.put("driverId", drvId);
        params.put("chgAllOrder", 1);
        params.put("startTime", leaveBeginTime);
        params.put("endTime", leaveEndTime);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERLEAVE_CONFIRMED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERLEAVE_ADD), params);
        if (!"OFF".equalsIgnoreCase(transportCommonQconfig.getQmqOrderMessageSwitch())) {
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERORDER_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERORDER_BATCHCHANGE), params);
        }
    }

    @Override
    public void sendDrvCloseLeaveQmq(long drvId) {
        Map<String, Object> params = new HashMap<>();
        params.put("driverId", drvId);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERLEAVE_CONFIRMED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERLEAVE_CLOSE), params);
    }

    /**
     * 司机下线
     * @param drvIds
     */
    @Override
    public void sendDrvOfflineQmq(List<Long> drvIds,Integer isFromPenalty) {
        List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.getDrvDriverPoList(drvIds);

        //司机下线消息
        for (DrvDriverPO  drvDriverPO: drvDriverPOList) {
            Map<String, Object> params = new HashMap<>();
            params.put("drvId", drvDriverPO.getDrvId());
            params.put("supplierId", drvDriverPO.getSupplierId());
            setContent(drvDriverPO, params);
            //1 来自判罚。只有下线操作的司机状态变更才有此值
            //非 1 为空 都是不是。
            params.put("isFromPenalty", isFromPenalty);
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERSTATE_CHANGED, Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERSTATE_OFFLINE),params);
            //订单批量改派全部订单
            params.put("driverId", drvDriverPO.getDrvId());
            params.put("chgAllOrder", 0);
            if (!"OFF".equalsIgnoreCase(transportCommonQconfig.getQmqOrderMessageSwitch())) {
                tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERORDER_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERORDER_BATCHCHANGE), params);
            }
        }
    }

    /**
     * 司机上线
     * @param drvIds
     */
    @Override
    public void sendDrvOnlineQmq(List<Long> drvIds) {
        List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.getDrvDriverPoList(drvIds);

        for (DrvDriverPO  drvDriverPO: drvDriverPOList) {
            Map<String, Object> params = new HashMap<>();
            params.put("drvId", drvDriverPO.getDrvId());
            params.put("supplierId", drvDriverPO.getSupplierId());
            setContent(drvDriverPO,params);
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERSTATE_CHANGED, Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERSTATE_ONLINE),params);
        }
    }

    /**
     * 运力组下线
     * @param transportGroupId
     */
    @Override
    public void sendTransportGroupOfflineQmq(Long transportGroupId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("transportGroupId",transportGroupId);
        List<Long> bindSkuIds = tspTransportGroupSkuRelationRepository.queryBindSkuId(transportGroupId, true);
        params.put("skuids",Joiner.on(",").join(bindSkuIds));
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_GROUPSTATE_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_GROUPSTATE_OFFLINE),params);
    }

    /**
     * 运力组上线
     * @param transportGroupId
     */
    @Override
    public void sendTransportGroupOnlineQmq(Long transportGroupId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("transportGroupId",transportGroupId);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_GROUPSTATE_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_GROUPSTATE_ONLINE),params);
    }

    /**
     * 运力组基础信息变更
     * @param transportGroupId
     */
    @Override
    public void sendTransportGroupModifyQmq(Long transportGroupId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("transportGroupId",transportGroupId);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_GROUPINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_GROUPINFO_MODIFY),params);
    }

    @Override
    public void sendTransportGroupBoundSkuQmq(Long transportGroupId, List<Long> skuInfoList, Integer bindStatus) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("transportGroupId",transportGroupId);
        params.put("skuInfoList",skuInfoList);
        params.put("bindStatus",bindStatus);
        if(bindStatus.intValue() == 1){
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_GROUPSKU_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_GROUPSKU_UNBIND),params);
        }else{
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_GROUPSKU_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_GROUPSKU_BIND),params);
        }
    }

    @Override
    public void sendDrvChangeQmq(Long drvId,Integer changeType,Integer isToSendType) {
        List<DrvDriverPO> drvDriverPoList = drvDrvierRepository.getDrvDriverPoList(Lists.newArrayList(drvId));
        if (CollectionUtils.isEmpty(drvDriverPoList)) {
            return;
        }
        DrvDriverPO drvDriverPo = drvDriverPoList.get(0);

        // 清除司机缓存
        cleanDriverCache(drvId);

        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("isToSendType",isToSendType == null?1:isToSendType);//司机变更，是否改派待服务订单1.不改派,2.改派
        params.put("supplierId", drvDriverPo.getSupplierId());
        if(changeType.intValue() == 1){
            tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERINFO_ADD),qconfig.getDriverChangeDelayTime(),TimeUnit.MILLISECONDS,params);
        }else{
            tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERINFO_MODIFY),qconfig.getDriverChangeDelayTime(),TimeUnit.MILLISECONDS,params);
        }


    }

    @Override
    public void sendDrvChangeQmqAfterApprove(Long drvId, Integer changeType, Integer isToSendType) {
        List<DrvDriverPO> drvDriverPOList = drvDrvierRepository.getDrvDriverPoList(Lists.newArrayList(drvId));
        if (CollectionUtils.isEmpty(drvDriverPOList)) {
            return;
        }

        DrvDriverPO drvDriverPO = drvDriverPOList.get(0);
        //司机手机号或者邮箱变更，需要更新账号信息
        driverAccountManagementHelper.updateAccount(drvDriverPO);
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("isToSendType",isToSendType == null?1:isToSendType);//司机变更，是否改派待服务订单1.不改派,2.改派
        params.put("supplierId", drvDriverPO.getSupplierId());

        // 清除司机缓存
        cleanDriverCache(drvId);

        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERINFO_MODIFY),qconfig.getDriverChangeDelayTime(),TimeUnit.MILLISECONDS,params);
    }

    @Override
    public void sendDrvChangeQmqForUpdateTransport(Long drvId,Integer changeType,Integer isToSendType, Long supplierId) {

        cleanDriverCache(drvId);
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("isToSendType",isToSendType == null?1:isToSendType);//司机变更，是否改派待服务订单1.不改派,2.改派
        params.put("supplierId", supplierId);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERINFO_MODIFY),qconfig.getDriverChangeDelayTime(),TimeUnit.MILLISECONDS,params);
    }

    protected void cleanDriverCache(Long drvId) {
        try {
            driverCacheServiceV2.clearDrvCacheImmediately(drvId);
        } catch (InterruptedException e) {
            Map<String, String> pairs = Maps.newHashMap();
            pairs.put("drvId", String.valueOf(drvId));
            Cat.logEvent(Constant.EventType.DRIVER, CLEAN_DRIVER_CACHE, EVENT_FAILED, pairs);
        }
    }

    @Override
    public void sendDrvVehicleChangeQmq(Long drvId, Long vehicleId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("vehicleId",vehicleId);
        putSupplierId(drvId,params);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERVEHICLE_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERVEHICLE_CHANGE),500,TimeUnit.MILLISECONDS,params);
    }

    @Override
    public void sendDrvSupplierIdChangeQmq(Long drvId, Long supplierId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("supplierId",supplierId);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERSUPPLIER_CHANGE),params);
    }

    @Override
    public void sendDrvCityIdChangeQmq(Long drvId, Long cityId, Long supplierId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("cityId",cityId);
        params.put("supplierId", supplierId);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERCITY_CHANGE),qconfig.getDriverChangeDelayTime(),TimeUnit.MILLISECONDS, params);
    }

    @Override
    public void sendVehicleTypeModifyQmq(Long vehicleId, Long vehicleTypeId,Long drvId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("vehicleId",vehicleId);
        params.put("vehicleTypeId",vehicleTypeId);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERVEHICLE_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERVEHICLETYPE_MODIFY),500,TimeUnit.MILLISECONDS,params);
    }

    @Override
    public void sendDrvLoginAccountChangeQmq(Long drvId, String loginAccount) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("loginAccount",loginAccount);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERINFO_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVER_ACCOUNT_CHANGE),params);
    }

    @Override
    public void sendDrvBatchChangeQmq(List<Long> drvIds, Integer changeType,Integer isSendToType) {
        Map<Long, DrvDriverPO> driverPoMap = drvDrvierRepository.getDrvDriverPoMap(drvIds);
        for(Long drvId : drvIds){
            sendDrvChangeQmqForUpdateTransport(drvId,changeType,isSendToType, Optional.ofNullable(driverPoMap.get(drvId)).orElse(new DrvDriverPO()).getSupplierId());
        }
    }

    @Override
    public void sendIdcardBackGroundCheckQmq(List<DriverBackgroundCheckDriverVO> list) {
        Map<String,Object> param = new HashMap<>();
        param.put("content", JsonUtil.toJson(list));
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_IDCARDBACKGROUND,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_IDCARDBACKGROUND),param);
    }

    @Override
    public void sendCalculateDrvCoopModeQmq(List<Long> list, String modifyUser) {
        Map<String,Object> param = new HashMap<>();
        param.put("drvIds", Joiner.on(",").join(list));
        param.put("modifyUser", modifyUser);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERTRANSPORTGROUP_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRV_TRANSPORTGROUP_CHANGE),param);
    }

    @Override
    public void sendIdcardApproveCheckQmq(Long sourceId, Integer sourceType) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("sourceId",sourceId);
        params.put("sourceType",sourceType);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_IDCARDAPPROVE_RESULT,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_IDCARD_APPROVE),qconfig.getIdcardApproveCheckQMQTime(),TimeUnit.MINUTES,params);
    }

    @Override
    public void sendApproveCertificateCheckQmq(Long approveId,Integer approveType,String orgDrvInfo,String newDrvInfo,String orgVehicleInfo,String newVehicleInfo) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("approveId",approveId);
        params.put("approveType",approveType);
        params.put("orgDrvInfo",orgDrvInfo);
        params.put("newDrvInfo",newDrvInfo);
        params.put("orgVehicleInfo",orgVehicleInfo);
        params.put("newVehicleInfo",newVehicleInfo);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_APPROVECHECK_CHANGE,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_APPROVECHECK),500,TimeUnit.MILLISECONDS,params);
    }

    @Override
    public void sendDrvDspStatusChangeQmq(Long drvId, TmsTransportConstant.DrvDspStatusEnum changeType) {
        if (!qconfig.getDrvDspStatusChangeQmqSwitch()) {
            return;
        }
        Map<String, Object> params = Maps.newLinkedHashMapWithExpectedSize(2);
        params.put("drvId", drvId);
        params.put("changeType", changeType.getCode());
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DSP_DRIVER_STATUS_CHANGED, Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRV_DSP_STATUS), params);
    }

    @Override
    public void sendDrvDspStatusChangeQmq(String operateType,Long id, Long drvId, TmsTransportConstant.DrvDspStatusEnum changeType) {
        if (!qconfig.getDrvDspStatusChangeQmqSwitch()) {
            return;
        }
        Map<String, Object> params = Maps.newLinkedHashMapWithExpectedSize(2);
        params.put("drvId", drvId);
        params.put("changeType", changeType.getCode());
        params.put("operateType",operateType);
        params.put("id",id);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DSP_DRIVER_STATUS_CHANGED, Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRV_DSP_STATUS), params);
    }

    @Override
    public void sendVehicleModifyQmq(Long vehicleId, Long drvId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("vehicleId",vehicleId);
        putSupplierId(drvId,params);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVERVEHICLE_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DRIVERVEHICLE_MODIFY),500,TimeUnit.MILLISECONDS,params);
    }

    protected void putSupplierId(Long drvId,  Map<String,Object> params) {
        params.put("supplierId", getSupplierIdByDrvId(drvId));
    }
    private Long getSupplierIdByDrvId(Long drvId) {
        if (drvId == null || drvId == 0L) {
            return null;
        }
        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
        if (drvDriverPO == null) {
            return null;
        }
        return drvDriverPO.getSupplierId();
    }

    @Override
    public void sendTransportGroupBoundDrvQmq(Long transportGroupId, List<Long> drvId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("transportGroupId",transportGroupId);
        params.put("drvIdList", drvId);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_GROUPDRIVER_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_GROUPDRIVER_BIND),params);
    }

    @Override
    public void sendTransportGroupUnbondDrvQmq(Long transportGroupId, List<Long> drvId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("transportGroupId",transportGroupId);
        params.put("drvIdList", drvId);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_GROUPDRIVER_CHANGED,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_GROUPDRIVER_UNBIND),params);
    }

    @Override
    public void sendPushDataToCtripUniversityQmq(Long drvId) {
    }
    @Override
    public void sendRecruitingApproveAgingQMQ(Long recruitingId, Integer recruitingType, Integer approveStatus) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("recruitingId",recruitingId);
        params.put("recruitingType", recruitingType);
        params.put("approveStatus", approveStatus);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_APPROVE_AGING,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_RECRUITING_APPROVE_AGING),qconfig.getRecruitingApproveAgingThreshold(),TimeUnit.MINUTES,params);
    }

    @Override
    public void sendRecruitingCertificateCheckQMQ(Long recruitingId, Integer recruitingType, String drvRequestInfo,String vehRequestInfo,String modfyUser) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("recruitingId",recruitingId);
        params.put("recruitingType", recruitingType);
        params.put("drvRequestInfo", drvRequestInfo);
        params.put("vehRequestInfo", vehRequestInfo);
        params.put("modifyUser", modfyUser);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_CERTIFICATE_CHECK,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_RECRUITING_CERTIFICATE_CHECK),30,TimeUnit.SECONDS,params);
    }

    @Override
    public void sendRecruitingCheckStatusQMQ(Long recruitingId, Integer recruitingType, String modfyUser) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("recruitingId",recruitingId);
        params.put("recruitingType", recruitingType);
        params.put("modifyUser", modfyUser);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_CHECK_STATUS,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_RECRUITING_CHECK_STATUS),30,TimeUnit.SECONDS,params);
    }

    @Override
    public void sendSystemAutoRejectedRecruitingQMQ(Long recruitingId, Integer recruitingType) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("recruitingId",recruitingId);
        params.put("recruitingType", recruitingType);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_SYSTEM_AUTO_REJECTED_RECRUITING,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_RECRUITING_REJECTED),30,TimeUnit.SECONDS,params);
    }

    @Override
    public void sendRecruitingApproveScheduleQMQ(Long recruitingId, Integer recruitingType, Integer accountType) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("recruitingId",recruitingId);
        params.put("recruitingType", recruitingType);
        //fixme 下个版本下掉 accountType，发布兼容问题发布需要暂时保留
        params.put("accountType", accountType);
        //不能用accountType，因为和切面上下文SessionHolder的key冲突
        params.put(TmsTransportConstant.ACCOUNT_TYPE_KEY, accountType);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_RECRUITING_APPROVE_SCHEDULE,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_RECRUITING_APPROVE_SCHEDULE),30,TimeUnit.SECONDS,params);
    }


    @Override
    public void sendDiscardDriverQmq(Long drvId) {
//        if (drvId == null) {
//            return;
//        }
//        DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(drvId);
//        Map<String,Object> params = Maps.newHashMap();
//        params.put("drvId",drvDriverPO.getDrvId());
//        setContent(drvDriverPO, params);
//        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DISCARD_DRIVER,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_DISCARD_DRIVER),2,TimeUnit.SECONDS,params);
    }

    private static void setContent(DrvDriverPO drvDriverPO, Map<String, Object> params) {
        Map<String,Object> content = Maps.newHashMap();
        content.put("drvId", drvDriverPO.getDrvId());
        content.put("uid", drvDriverPO.getUid());
        content.put("cityId", drvDriverPO.getCityId());
        content.put("modifyUser", drvDriverPO.getModifyUser());
        params.put("content", JsonUtil.toJson(content));
    }

    @Override
    public void sendDriverRegisterQmq(Long drvId, Long cityId,List<Integer> proLineList) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvId);
        params.put("cityId",cityId);
        params.put("proLineList",JsonUtil.toJson(proLineList));
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_REGISTER,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_REGISTER_DRIVER),2,TimeUnit.SECONDS,params);
    }

    @Override
    public void sendTemporaryToOfficialQmq(List<Long> officialIds,Integer type) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("officialIdList",JsonUtil.toJson(officialIds));
        params.put("type",type);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_TEMPORARY_TO_OFFICIAL,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_TEMPORARY_TO_OFFICIAL),2,TimeUnit.SECONDS,params);
    }

    @Override
    public void sendApproveToOfficial(Long sourceId, Integer sourceType) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("sourceId",sourceId);
        params.put("sourceType",sourceType);
        params.put("modifyUser", SessionHolder.getRestSessionAccountName());
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_APPROVE_TO_OFFICIAL,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_APPROVE_TO_OFFICIAL),2,TimeUnit.SECONDS,params);
    }

    @Override
    public void sendTemporaryReplenishInfo(Long replenishId, Integer type,Long vehicleId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("replenishId",replenishId);
        params.put("type",type);
        params.put("vehicleId",vehicleId);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_TEMPORARY_REPLENISH_INFO,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_TEMPORARY_REPLENISH_INFO),overseasQconfig.getTemporaryNotificationHour(),TimeUnit.HOURS,params);
    }

    @Override
    public void sendDataCompareNotice(DataCompareMessage dataCompareMessage) {
        Map<String, Object> sendMap = Maps.newHashMap();
        sendMap.put("content", JsonUtil.toJson(dataCompareMessage));
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.DATA_COMPARE_TASK_TOPIC, new HashSet<>(), sendMap);
    }

    @Override
    public void sendOverseasDrvVehBatchOCR(Long id, Integer type) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("id",id);
        params.put("type",type);
        tmsQmqProducer.sendDelayMessage(TmsTransportConstant.QmqSubject.SUBJECT_OVERSEAS_DRVVEH_OCR,Sets.newHashSet(TmsTransportConstant.QmqTag.TAG_OVERSEAS_OCR),2,TimeUnit.SECONDS,params);
    }

    @Override
        public void sendPhoneEmailModifyQMQ(DrvDriverPO drvDriverPO) {

        Map<String,Object> params = Maps.newHashMap();
        params.put("drvId",drvDriverPO.getDrvId());
        params.put("phone",drvDriverPO.getDrvPhone());
        params.put("countryCode",drvDriverPO.getIgtCode());
        params.put("email",drvDriverPO.getEmail());
        params.put("uid",drvDriverPO.getUid());
        params.put("cityId",drvDriverPO.getCityId());
        params.put("modifyUser",drvDriverPO.getModifyUser());
        Map<String,Object> contentParams = Maps.newHashMap();
        contentParams.put("content", JsonUtil.toJson(params));
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DCS_TMS_TRANSPORT_PHONE_EMAIL_MODIFY,null, contentParams);
    }

    @Override
    public void sendDriverAppElistmateMessge(TransportGroupDriverApplyFailedReasonDTO applyFailedReasonDTO) {
        this.sendDriverApplyFailedMessage(Lists.newArrayList(applyFailedReasonDTO));
    }

    @Override
    public void sendDriverApplySuccessMessage(Map<Long, String> applySuccessDriverWorkPeriodMap) {
        if (Objects.isNull(applySuccessDriverWorkPeriodMap)) {
            return;
        }
        applySuccessDriverWorkPeriodMap.forEach((driverId, workPeriod) -> {
            PushMessageDTO pushMessageDTO = new PushMessageDTO();
            pushMessageDTO.setDriverIds(Lists. newArrayList(driverId));
            pushMessageDTO.setTemplateId("4062");

            Map<String,String> sharkValues = Maps.newHashMap();
            sharkValues.put("time", workPeriod);
            sharkValues.put("month", getCurrentMonth());
            pushMessageDTO.setSharkValues(sharkValues);

            Map<String,Object> contentParams = Maps.newHashMap();
            contentParams.put("data", JsonUtil.toJson(pushMessageDTO));
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.DCS_DRIVER_PUSH_MESSAGE,null, contentParams);
        });
    }

    @Override
    public void sendDriverApplyFailedMessage(List<TransportGroupDriverApplyFailedReasonDTO> failedDriverList) {
        for (TransportGroupDriverApplyFailedReasonDTO failedReasonDTO : failedDriverList) {
            PushMessageDTO pushMessageDTO = new PushMessageDTO();
            Map<String,String> sharkValues = Maps.newHashMap();
            pushMessageDTO.setDriverIds(Lists.newArrayList(failedReasonDTO.getDrvId()));
            if (failedReasonDTO.getApplyFailedType() == TmsTransportConstant.ApplyFailedTypeEnum.LEAVE_AND_FREEZE_TOTAL_HOUR_OVER_LIMIT) {
                pushMessageDTO.setTemplateId("4063");

                sharkValues.put("hour", String.valueOf(failedReasonDTO.getLeaveAndFreeze().getTotalHour()));
            }
            if (failedReasonDTO.getApplyFailedType() == TmsTransportConstant.ApplyFailedTypeEnum.DRIVER_POINT_IS_RANKED_LOW) {
                pushMessageDTO.setTemplateId("4064");

                sharkValues.put("rank", String.valueOf(failedReasonDTO.getDriverRankingInCity()));
            }

            sharkValues.put("month", getCurrentMonth());
            pushMessageDTO.setSharkValues(sharkValues);

            Map<String,Object> contentParams = Maps.newHashMap();
            contentParams.put("data", JsonUtil.toJson(pushMessageDTO));
            tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.DCS_DRIVER_PUSH_MESSAGE,null, contentParams);
        }
    }

    @Override
    public void sendDrvOutCheck(Long taskId) {
        Map<String,Object> params = Maps.newHashMap();
        params.put("taskId",taskId);
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.DCS_TMS_DRV_OUT_CHECK, null, params);
    }

    @Override
    public void sendRealNameAuth(List<Long> drvIdList) {
        if (BooleanUtils.isFalse(qconfig.getDrvRealNameAuthSwitch())) {
            logger.info("sendRealNameAuth_switch_off");
            return;
        }
        if (CollectionUtils.isEmpty(drvIdList)) {
            return;
        }
        Map<String, Object> params = Maps.newLinkedHashMapWithExpectedSize(2);
        params.put("drvIdList", JsonUtil.toJson(drvIdList));
        tmsQmqProducer.sendMessage(TmsTransportConstant.QmqSubject.SUBJECT_DRIVER_VIRTUALNUMBER_AUTH_CHANGED, null, params);
    }

    private String getCurrentMonth() {
        return String.valueOf(DateUtil.getMonth());
    }

}
