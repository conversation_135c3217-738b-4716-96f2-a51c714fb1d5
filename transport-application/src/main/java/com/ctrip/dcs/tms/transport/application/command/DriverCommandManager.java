package com.ctrip.dcs.tms.transport.application.command;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DriverCommandManager {

  @Autowired
  private List<DriverCommandHandler> driverCommandHandlers;

  public DriverCommandHandler getHandler(Integer internalScope) {
    return driverCommandHandlers.stream()
        .filter(driverCommandHandler -> driverCommandHandler.support(internalScope))
        .findFirst()
        .orElseThrow(() -> new RuntimeException("Driver command handler not found"));
  }
}
