package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctriposs.baiji.exception.*;
import com.google.common.base.Joiner;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.util.*;
import java.util.stream.*;

@Service
public class CertificateCommandServiceImpl implements CertificateCommandService {

    private static final Logger logger = LoggerFactory.getLogger(CertificateCommandServiceImpl.class);

    @Autowired
    private TmsCertificateConfigRepository configRepository;
    @Autowired
    EnumRepository enumRepository;

    @Override
    public Result<Boolean> addCertificateConfig(CertificateConfigAddSOARequestType requestType) {
        try{
            logger.info("addCertificateConfig,params:{}",requestType.toString());
            if(CollectionUtils.isEmpty(requestType.getCertificateConfig())){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing)).build();
            }
            //判断修改的配置是否重复
            List<TmsCertificateConfigPO> configPOList =  configRepository.queryConfigByCityAndProLine(requestType.getCityIds(),requestType.getProductLines());
            if(verdictRepeat(configPOList,requestType.getVehicleTypeIds())){
                return Result.Builder.<Boolean>newResult().fail().withMsg(enumRepository.getCityNameSplt(configPOList.stream().map(TmsCertificateConfigPO::getCityId).collect(Collectors.toSet()))+SharkUtils.getSharkValue(SharkKeyConstant.cityCertificateConfigExist)).build();
            }
            List<TmsCertificateConfigPO> batchInsertConfigPO =  this.addBuildBean(requestType);
            int count = configRepository.batchAddCertificateConfig(batchInsertConfigPO);
            if(count > 0){
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Boolean>newResult().fail().build();
    }

    @Override
    public Result<Boolean> updateCertificateConfig(CertificateConfigUpdateSOARequestType requestType) {
        try{
            TmsCertificateConfigPO configPO = configRepository.queryByPK(requestType.getId());
            if(Objects.isNull(configPO)){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.certificateConfigExist)).build();
            }
            if(CollectionUtils.isEmpty(requestType.getCertificateConfig())){
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing)).build();
            }
            List<Long> orgVehicleTypesList = Lists.newArrayList();
            if(StringUtils.isNotEmpty(configPO.getVehicleTypeId())){
                orgVehicleTypesList = Arrays.stream(configPO.getVehicleTypeId().split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            }
            List<Long> requestVehicleTypeIds = CollectionUtils.isEmpty(requestType.getVehicleTypeIds())?Lists.newArrayList():requestType.getVehicleTypeIds();
            //当前配置车型和参入车型相同，则表示不编辑车型
            if(!CollectionUtils.isEqualCollection(orgVehicleTypesList,requestVehicleTypeIds) && checkVehicleTypeRepetition(configPO.getCityId(),configPO.getProductLine(),requestType.getVehicleTypeIds(),configPO.getId())){
                return Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportCertificateCartypeExist)).build();
            }
            String vehicleTypes = "";
            if(CollectionUtils.isNotEmpty(requestType.getVehicleTypeIds())){
                vehicleTypes = Joiner.on(",").join(requestType.getVehicleTypeIds());
            }
            int count = configRepository.updateCertificateConfig(requestType.getId(),JsonUtil.toJson(requestType.getCertificateConfig()),requestType.getModifyUser(),vehicleTypes);
            if(count > 0){
                return Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build();
            }
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
        return Result.Builder.<Boolean>newResult().fail().build();
    }

    private List<TmsCertificateConfigPO> addBuildBean(CertificateConfigAddSOARequestType requestType){
        List<TmsCertificateConfigPO> addConfigPOList = Lists.newArrayList();
        for(Long cityId : requestType.getCityIds()){
            for(Integer productLine : requestType.getProductLines()){
                TmsCertificateConfigPO configPO = new TmsCertificateConfigPO();
                configPO.setCertificateConfig(JsonUtil.toJson(requestType.getCertificateConfig()));
                configPO.setCityId(cityId);
                configPO.setProductLine(productLine);
                configPO.setActive(Boolean.TRUE);
                configPO.setCreateUser(requestType.getModifyUser());
                configPO.setModifyUser(requestType.getModifyUser());
                if(CollectionUtils.isNotEmpty(requestType.getVehicleTypeIds())){
                    configPO.setVehicleTypeId(Joiner.on(",").join(requestType.getVehicleTypeIds()));
                }
                addConfigPOList.add(configPO);
            }
        }
        return addConfigPOList;
    }

    //判断生线+城市+车辆是否重复
    public Boolean verdictRepeat(List<TmsCertificateConfigPO> configPOList,List<Long> vehicleTypeIds){
        if(CollectionUtils.isEmpty(configPOList)){
            return Boolean.FALSE;
        }
        if(CollectionUtils.isEmpty(vehicleTypeIds)){
            return Boolean.TRUE;
        }
        //遍历配置中的车型，如果与传入的车型有交集，说明已存在
        for(TmsCertificateConfigPO config : configPOList){
            if(StringUtils.isNotEmpty(config.getVehicleTypeId())){
                List<Long> vehicleTypId = Arrays.stream(config.getVehicleTypeId().split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
                if(CollectionUtils.intersection(vehicleTypeIds,vehicleTypId).size() > 0){
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    //判断当前产线+城市中车型重复
    public Boolean checkVehicleTypeRepetition(Long cityId,Integer productLine,List<Long> vehicleTypeIds,Long id){
        List<TmsCertificateConfigPO> configPOS = configRepository.queryConfigByCityAndProLine(Arrays.asList(cityId),Arrays.asList(productLine));
        if(CollectionUtils.isEmpty(configPOS)){
            return Boolean.FALSE;
        }

        for(TmsCertificateConfigPO configPO : configPOS){
            if(Objects.equals(id,configPO.getId())){
                continue;
            }
            //删除车型时，判断当前产线+城市是否已存在车型为空的数据
            if(CollectionUtils.isEmpty(vehicleTypeIds) && StringUtils.isEmpty(configPO.getVehicleTypeId())){
                return Boolean.TRUE;
            }
            //参入车型和已存在车型是否有重复
            if(StringUtils.isNotEmpty(configPO.getVehicleTypeId()) && CollectionUtils.isNotEmpty(vehicleTypeIds)){
                List<Long> vehTypeIds = Arrays.stream(configPO.getVehicleTypeId().split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
                if(CollectionUtils.intersection(vehicleTypeIds,vehTypeIds).size() > 0){
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }
}
