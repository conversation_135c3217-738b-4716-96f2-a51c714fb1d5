package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.result.Result;

import java.util.List;

public interface EffectiveCapacityQueryService {

    /**
    　* @description: 查询有效运力车型
    　* <AUTHOR>
    　* @date 2022/11/15 19:15
    */
    Result<List<VehicleTypeSOADTO>> queryEffCapacityVehicleType();

    /**
    　* @description: 有效运力剩余库存
    　* <AUTHOR>
    　* @date 2022/11/16 10:27
    */
    Result<QueryResidualInventorySOAResponseType> queryResidualInventory(QueryResidualInventorySOARequestType requestType);

    /**
    　* @description: 有效运力数
    　* <AUTHOR>
    　* @date 2022/11/16 14:24
    */
    Result<QueryInventoryCapacitySOAResponseType> queryInventoryCapacity(QueryInventoryCapacitySOARequestType requestType);

    /**
    　* @description: 运力分析
    　* <AUTHOR>
    　* @date 2022/11/16 14:51
    */
    Result<QueryCapacityAnalyseSOAResponseType> queryCapacityAnalyse(QueryCapacityAnalyseSOARequestType requestType);



}