package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.TransportGroup;
import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO;
import com.ctrip.dcs.tms.transport.application.dto.SaasDriverDTO;
import com.ctrip.dcs.tms.transport.application.dto.SaasTransportGroupDTO;
import com.ctrip.dcs.tms.transport.application.query.DriverCacheAgent;
import com.ctrip.dcs.tms.transport.application.query.IQueryDriverForSaasService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.JsonUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.LocalCollectionUtils;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.StringUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDispatchRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.service.IQueryCityService;
import com.ctrip.dcs.tms.transport.infrastructure.service.dto.CityDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil.queryEncryptPhone;

@Component
public class QueryDriverForSaasService implements IQueryDriverForSaasService {
    private static final Logger logger = LoggerFactory.getLogger(QueryDriverForSaasService.class);
    @Autowired
    private DriverCacheAgent driverCacheAgent;
    @Autowired
    private DrvDrvierRepository drvRepository;
    @Autowired
    private IQueryCityService queryCityService;
    @Autowired
    private DrvDispatchRelationRepository drvDispatchRelationRepository;

    @Override
    public List<Long>  queryDriverId(List<String> driverPhone, Long supplierId) {
        //加密手机号
        List<String> encryptPhone = queryEncryptPhone(driverPhone);
        logger.info("queryEncryptPhone_result", JsonUtil.toJson(encryptPhone));
        if(LocalCollectionUtils.isEmpty(encryptPhone)){
            return new ArrayList<>();
        }
        List<DrvDriverPO> preResList = drvRepository.queryDriverId(encryptPhone);
        if (CollectionUtils.isEmpty(preResList)) {
            return Collections.emptyList();
        }

        List<Long> resLit = Lists.newArrayListWithCapacity(preResList.size());

        List<Long> relationDrvIdList = drvDispatchRelationRepository.queryDispatchRelationDrvBySupplierId(supplierId);

        for (DrvDriverPO driverPO : preResList) {
            if (Objects.equals(supplierId, driverPO.getSupplierId()) || relationDrvIdList.contains(driverPO.getDrvId())) {
                resLit.add(driverPO.getDrvId());
            }
        }

        return resLit;
    }

    @Override
    public List<SaasDriverDTO> query(List<Long> driverIds) {
        if(LocalCollectionUtils.isEmpty(driverIds)){
            return new ArrayList<>();
        }
        List<SaasDriverDTO> result = new ArrayList<>();
        //从司机缓存查询司机信息 司机缓存只有有效司机 上线 冻结 未废弃的司机
        QueryDriverFromCacheParamDTO paramDTO = new QueryDriverFromCacheParamDTO();
        paramDTO.setDriverIds(LocalCollectionUtils.listToString(driverIds));
        List<DriverInfo> driverInfoList = driverCacheAgent.queryDriver(paramDTO);
        result.addAll(getSaasDriverDTO(driverInfoList));
        //从数据库查询司机信息
        result.addAll(queryDriverFromDB(queryByDBDriverIds(driverIds,result)));
        return result;
    }

    /**
     * 从数据库查询司机信息
     * @param driverIds
     * @return
     */
    public List<SaasDriverDTO> queryDriverFromDB(List<Long> driverIds){
        if(LocalCollectionUtils.isEmpty(driverIds)){
            return new ArrayList<>();
        }
        List<DrvDriverPO> poList = drvRepository.queryDrvList(driverIds);
        if(LocalCollectionUtils.isEmpty(poList)){
            return new ArrayList<>();
        }
        List<SaasDriverDTO> resultList = new ArrayList<>();
        for (DrvDriverPO drvDriverPO : poList) {
            resultList.add(getSaasDriverSoaDTO(drvDriverPO));
        }
        return resultList;
    }
    /**
     * 查询需要从db查询的司机id
     * @param driverIds
     * @param result
     * @return
     */
    public List<Long> queryByDBDriverIds(List<Long> driverIds,List<SaasDriverDTO> result){
        List<Long> cacheDriverIds = new ArrayList<>();
        for (SaasDriverDTO saasDriverDTO : result) {
            cacheDriverIds.add(saasDriverDTO.getDriverId());
        }
        List<Long> resultIds = new ArrayList<>();
        for (Long driverId : driverIds) {
            if(!cacheDriverIds.contains(driverId)){
                resultIds.add(driverId);
            }
        }
        return resultIds;
    }


    /**
     * 封装司机信息
     * @param driverInfoList
     * @return
     */
    private List<SaasDriverDTO> getSaasDriverDTO(List<DriverInfo> driverInfoList){
        if(LocalCollectionUtils.isEmpty(driverInfoList)){
            return new ArrayList<>();
        }
        //司机缓存数据没有加密手机号
        Map<String,String> encryptPhoneMap = batchEncrypt(driverInfoList);
        List<SaasDriverDTO> driverList = new ArrayList<>();
        for (DriverInfo driverInfo : driverInfoList) {
            SaasDriverDTO saasDriverDTO = getSaasDriverSoaDTO(driverInfo);
            //获取加密手机号
            saasDriverDTO.setDriverPhone(encryptPhoneMap.get(driverInfo.getDriverPhone()));
            driverList.add(saasDriverDTO);
        }
        return driverList;
    }

    /**
     * 加密手机号
     * @param driverInfoList
     * @return
     */
    public Map<String,String> batchEncrypt(List<DriverInfo> driverInfoList){
        List<String> encryptParams = new ArrayList<>();
        for (DriverInfo driverInfo : driverInfoList) {
            encryptParams.add(driverInfo.getDriverPhone());
        }
        Map<String,String> result = TmsTransUtil.batchEncrypt(encryptParams,KeyType.Phone);
        if(LocalCollectionUtils.isEmpty(result)){
            result = new HashMap<>();
        }
        //如果加密失败则使用未加密值
        for (String phone : encryptParams) {
            String encryptPhone = result.get(phone);
            if(StringUtil.isEmpty(encryptPhone)){
                result.put(phone,phone);
            }
        }
        return result;
    }
    /**
     * 封装司机信息
     * @param drvDriverPO
     * @return
     */
    private SaasDriverDTO getSaasDriverSoaDTO(DrvDriverPO drvDriverPO){
        SaasDriverDTO saasDriverDTO = new SaasDriverDTO();
        saasDriverDTO.setDriverId(drvDriverPO.getDrvId());
        saasDriverDTO.setSupplierId(drvDriverPO.getSupplierId());
        saasDriverDTO.setDriverStatus(drvDriverPO.getDrvStatus());
        saasDriverDTO.setDriverName(drvDriverPO.getDrvName());
        saasDriverDTO.setCityId(drvDriverPO.getCityId());
        CityDTO cityDTO = queryCityService.query(drvDriverPO.getCityId());
        saasDriverDTO.setCityName(cityDTO.getCityName());
        saasDriverDTO.setDriverPhone(drvDriverPO.getDrvPhone());
        saasDriverDTO.setDriverPhoneAreaCode(drvDriverPO.getIgtCode());
        saasDriverDTO.setDriverCategory(drvDriverPO.getTemporaryDispatchMark());
        saasDriverDTO.setVehicleId(drvDriverPO.getVehicleId());
        saasDriverDTO.setTransportGroupList(new ArrayList<>());
        saasDriverDTO.setDriverLanguage(drvDriverPO.getDrvLanguage());
        return saasDriverDTO;
    }

    /**
     * 封装司机信息
     * @param driverInfo
     * @return
     */
    private SaasDriverDTO getSaasDriverSoaDTO(DriverInfo driverInfo){
        SaasDriverDTO saasDriverDTO = new SaasDriverDTO();
        saasDriverDTO.setDriverId(driverInfo.getDriverId());
        saasDriverDTO.setSupplierId(driverInfo.getSupplierId());
        saasDriverDTO.setDriverStatus(driverInfo.getStatus());
        saasDriverDTO.setDriverName(driverInfo.getDriverName());
        saasDriverDTO.setCityId(driverInfo.getCityId());
        saasDriverDTO.setCityName(driverInfo.getCityName());
        saasDriverDTO.setDriverPhone(driverInfo.getDriverPhone());
        saasDriverDTO.setDriverPhoneAreaCode(driverInfo.getPhoneAreaCode());
        saasDriverDTO.setDriverCategory(driverInfo.getDrvTemporaryMark());
        saasDriverDTO.setVehicleId(driverInfo.getCarId());
        saasDriverDTO.setTransportGroupList(getTransportGroup(driverInfo.getTransportGroups()));
        saasDriverDTO.setDriverLanguage(driverInfo.getDriverLanguage());
        return saasDriverDTO;
    }

    /**
     * 封装运力组信息
     * @param transportGroups
     * @return
     */
    private List<SaasTransportGroupDTO> getTransportGroup(List<TransportGroup> transportGroups){
        List<SaasTransportGroupDTO> transportGroupDTOS = new ArrayList<>();
        for (TransportGroup transportGroup : transportGroups) {
            SaasTransportGroupDTO saasTransportGroupDTO = new SaasTransportGroupDTO();
            saasTransportGroupDTO.setTransportGroupId(transportGroup.getTransportGroupId());
            saasTransportGroupDTO.setTransportGroupStatus(transportGroup.getApplyStatus());
            transportGroupDTOS.add(saasTransportGroupDTO);
        }
        return transportGroupDTOS;
    }
}
