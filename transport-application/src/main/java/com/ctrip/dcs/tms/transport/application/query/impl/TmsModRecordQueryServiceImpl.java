package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.CommonEnum.RecordTypeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.parser.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.clogging.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.igt.framework.infrastructure.constant.*;
import com.google.common.collect.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/3/1 17:48
 */
@Service("tmsModRecordQueryService")
public class TmsModRecordQueryServiceImpl implements TmsModRecordQueryService {

    private static final Logger logger = LoggerFactory.getLogger(TmsModRecordQueryService.class);

    @Autowired
    private ModRecordRespository<TmsModRecordPO> modRecordRespository;

    @Autowired
    private TransportGroupTmsModRecordParser transportGroupTmsModRecordParser;

    @Autowired
    private DriverTmsModRecordParser driverTmsModRecordParser;

    @Autowired
    private VehicleTmsModRecordParser vehicleTmsModRecordParser;

    @Autowired
    private GenericTmsModRecordParser genericTmsModRecordParser;

    @Autowired
    DriverDispatchTmsModRecordParser driverDispatchTmsModRecordParser;

    @Resource
    TransportGroupRepository transportGroupRepository;

    @Resource
    DrvDrvierRepository drvDrvierRepository;

    @Resource
    VehicleRepository vehicleRepository;
    @Resource
    DrvRecruitingRepository drvRecruitingRepository;

    @Resource
    VehicleRecruitingRepository vehicleRecruitingRepository;
    @Resource
    DrvDispatchRelationRepository drvDispatchRelationRepository;

    @Override
    public Result<List<ModRecordSOADTO>> queryModRecordList(ModRecordSOARequestType requestType) {
        Result.Builder<List<ModRecordSOADTO>> builder = Result.Builder.<List<ModRecordSOADTO>>newResult();
        try {
            List<ModRecordSOADTO> modRecordSOADTOS = Lists.newArrayList();
            List<TmsModRecordPO> tmsModRecordPOList = Lists.newArrayList();
            if(requestType.getPaginator() == null){
                tmsModRecordPOList = modRecordRespository.queryModRecordList(requestType.getRrdId(), requestType.getRrdType(), null);
            }else {
                tmsModRecordPOList = modRecordRespository.queryModRecordListPage(requestType.getRrdId(), requestType.getRrdType(),requestType.getPaginator().getPageNo(),requestType.getPaginator().getPageSize());
            }
            if (CollectionUtils.isEmpty(tmsModRecordPOList)) {
                return builder.success().build();
            }
            Integer rrdType = requestType.getRrdType();
            List<TmsModRecordVO> tmsModRecordVOList;
            switch (rrdType){
                case 1:
                    tmsModRecordVOList = transportGroupTmsModRecordParser.parse(tmsModRecordPOList);
                    break;
                case 2:
                    tmsModRecordVOList = driverTmsModRecordParser.parse(tmsModRecordPOList);
                    break;
                case 3:
                    tmsModRecordVOList = vehicleTmsModRecordParser.parse(tmsModRecordPOList);
                    break;
                case 13:
                    tmsModRecordVOList = driverDispatchTmsModRecordParser.parse(tmsModRecordPOList);
                    break;
                default:
                    tmsModRecordVOList = genericTmsModRecordParser.parse(tmsModRecordPOList);
            }

            for (TmsModRecordVO tmsModRecordVO : tmsModRecordVOList) {
                ModRecordSOADTO modRecordSOADTO = new ModRecordSOADTO();
                modRecordSOADTO.setId(tmsModRecordVO.getId());
                modRecordSOADTO.setModType(tmsModRecordVO.getModType());
                modRecordSOADTO.setModTypeName(tmsModRecordVO.getModTypeName());
                modRecordSOADTO.setModContent(JsonUtil.toJson(tmsModRecordVO.getModContent()));
                modRecordSOADTO.setModUser(tmsModRecordVO.getModUser());
                modRecordSOADTO.setDatachangeCreatetime(tmsModRecordVO.getDatachangeCreatetime());
                modRecordSOADTO.setDatachangeLasttime(tmsModRecordVO.getDatachangeLasttime());
                modRecordSOADTOS.add(modRecordSOADTO);
            }
            return builder.success().withData(modRecordSOADTOS).build();
        }catch (Exception e){
            logger.warn(e);
        }
        return builder.fail().withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).withMsg("queryModRecordList exception").build();
    }

    @Override
    public int queryModRecordListCount(ModRecordSOARequestType requestType) {
        return modRecordRespository.queryModRecordListCount(requestType.getRrdId(),requestType.getRrdType());
    }

    @Override
    public Result<Void> preCheck(ModRecordSOARequestType requestType) {
        Result.Builder<Void> builder = Result.Builder.newResult();
        Long supplierId = requestType.getSupplierId();
        //用供应商ID进行越权校验，若无供应商ID，则当前用户身份非供应商，直接跳过
        if (supplierId == null || supplierId == 0L) {
            return builder.success().build();
        }

        RecordTypeEnum recordType = RecordTypeEnum.findTypeByCode(requestType.getRrdType());
        if (recordType == null) {
            return builder.success().build();
        }

        Long realSupplierId = selectSupplierIdByRrdType(recordType, requestType.getRrdId());
        //鉴权不通过， 供应商身份只能查询属于自己的业务数据修改记录
        if (realSupplierId >= 0 && !Objects.equals(supplierId, realSupplierId)) {
            return builder.fail().withCode(ErrorCodeEnum.SUPPLIER_AUTHENTICATION_FAILED.getCode()).withMsg("supplierId Authentication failed.").build();
        }
        return builder.success().build();
    }

    private Long selectSupplierIdByRrdType(RecordTypeEnum recordType, Long rrdId) {
        Long realSupplierId = -1L;
        switch (recordType) {
            case TRANSPORT_GROUP:
                TspTransportGroupPO transportGroupPO = transportGroupRepository.getTspTransportGroupRepo().queryByPk(rrdId);
                realSupplierId = Objects.isNull(transportGroupPO) ? -1L : transportGroupPO.getSupplierId();
                break;
            case DRIVER:
                DrvDriverPO drvDriverPO = drvDrvierRepository.queryByPk(rrdId);
                realSupplierId = Objects.isNull(drvDriverPO) ? -1L : drvDriverPO.getSupplierId();
                break;
            case VEHICLE:
                VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(rrdId);
                realSupplierId = Objects.isNull(vehVehiclePO) ? -1L : vehVehiclePO.getSupplierId();
                break;
            case RECRUIT:
                DrvRecruitingPO recruitingPO = drvRecruitingRepository.queryByPK(rrdId);
                realSupplierId = Objects.isNull(recruitingPO) ? -1L : recruitingPO.getSupplierId();
                break;
            case VEHRECRUIT:
                VehicleRecruitingPO vehicleRecruitingPO = vehicleRecruitingRepository.queryByPK(rrdId);
                realSupplierId = Objects.isNull(vehicleRecruitingPO) ? -1L : vehicleRecruitingPO.getSupplierId();
                break;
            default:
        }
        return realSupplierId;
    }

}
