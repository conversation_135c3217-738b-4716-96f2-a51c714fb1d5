package com.ctrip.dcs.tms.transport.application.command;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.common.result.*;

/**
 * <AUTHOR>
 * @Description 证件
 * @Date 14:45 2020/9/11
 * @Param
 * @return
 **/
public interface CertificateCommandService {

    /**
     * 添加证件配置
     * @param requestType
     * @return
     */
    Result<Boolean> addCertificateConfig(CertificateConfigAddSOARequestType requestType);

    /**
     * 添加证件配置
     * @param requestType
     * @return
     */
    Result<Boolean> updateCertificateConfig(CertificateConfigUpdateSOARequestType requestType);

}
