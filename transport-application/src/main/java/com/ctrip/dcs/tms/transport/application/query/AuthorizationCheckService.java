package com.ctrip.dcs.tms.transport.application.query;

import java.util.*;
import com.ctrip.igt.framework.common.result.Result;

public interface AuthorizationCheckService {

    boolean checkAuthorizationDriver(List<Long> idList, Long supplierId);

    boolean checkAuthorizationDrvVehRecruiting(List<Long> idList, Long supplierId);

    boolean checkAuthorizationTransportGroup(List<Long> idList, Long supplierId);

    boolean checkAuthorizationVehicle(List<Long> idList, Long supplierId);

    boolean checkAuthorizationVehicleRecruiting(List<Long> idList, Long supplierId);

    boolean decisionRoute(Integer materialTypeId,List<Long> idList, Long supplierId);

    /**
    　* @description: 废弃数据-判断操作人是否有权限
    　* <AUTHOR>
    　* @date 2022/8/17 15:44
    */
    Result<Boolean> operationAuthJudge(List<String> permissionCodes,String code);
}