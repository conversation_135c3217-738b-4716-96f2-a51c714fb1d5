package com.ctrip.dcs.tms.transport.application.query.impl;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import com.ctrip.dcs.price.compute.price.facade.dto.AddressDTO;
import com.ctrip.dcs.price.compute.price.facade.param.QueryPoiAreaInfoRequestType;
import com.ctrip.dcs.price.compute.price.facade.param.QueryPoiAreaInfoResponseType;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.ComputePriceSoaServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.dcs.pms.product.api.QueryProductSkuListRequestType;
import com.ctrip.dcs.pms.product.api.QueryProductSkuListResponseType;
import com.ctrip.dcs.pms.product.api.QueryProductSkuWithRegionListRequestType;
import com.ctrip.dcs.pms.product.api.dto.BatchProductPropertyDTO;
import com.ctrip.dcs.pms.product.api.dto.SkuDTO;
import com.ctrip.dcs.pms.product.api.dto.SkuListSearchFilterDTO;
import com.ctrip.dcs.price.sku.pricing.facade.dto.SimpleAreaGroupDTO;
import com.ctrip.dcs.price.sku.pricing.facade.dto.SimpleCityAreaInfoDTO;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryAreaGroupByIdRequestType;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryAreaGroupByIdResponseType;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryPointAreaInfoRequestType;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryPointAreaInfoResponseType;
import com.ctrip.dcs.scm.sdk.domain.ContractRepository;
import com.ctrip.dcs.scm.sdk.domain.contract.Contract;
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService;
import com.ctrip.dcs.tms.transport.application.convert.TransportGroupConverter;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupInOrderConfigDTO;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupTimeSegmentConfigDTO;
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService;
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.DriverPointsQueryProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverPoint;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.PmsProductServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.SkuPriceSoaServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.monitoring.TransportMetric;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.MsgQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.IThreadPoolService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.FixedLocation;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @Date 2020/3/13 17:20
 */
@Service("transportQueryService")
public class TransportGroupQueryServiceImpl implements TransportGroupQueryService {

    private static final Logger logger = LoggerFactory.getLogger(TransportGroupQueryServiceImpl.class);

    /**
     * 线程任务组 一组 20个执行
     * */
    private static final Integer FUTURE_TASK_GROUP = 20;

    @Autowired
    private TransportGroupRepository transportGroupRepository;

    @Autowired
    private TspTransportGroupSkuArearRelationRepository arearRelationRepository;

    @Autowired
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;

    @Autowired
    private TspTransportGroupSkuArearRelationRepository tspTransportGroupSkuArearRelationRepository;

    @Autowired
    private PmsProductServiceClientProxy productServiceClientProxy;

    @Autowired
    private SkuPriceSoaServiceClientProxy skuPriceSoaServiceClientProxy;

    @Autowired
    private EnumRepository enumRepository;

    @Autowired
    private InOrderConfigRepository inOrderConfigRepository;

    @Autowired
    private MsgQconfig msgQconfig;

    @Autowired
    private TmsTransportQconfig tmsTransportQconfig;

    @Autowired
    private WorkShiftRepository workShiftRepository;
    @Autowired
    private DriverPointsQueryProxy driverPointsQueryProxy;

    @Autowired
    private CommonCommandService commandService;

    @Autowired
    private AuthorizationCheckService authorizationCheckService;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private ProductionLineUtil productionLineUtil;

    @Autowired
    DrvDriverLeaveRepository drvDriverLeaveRepository;
    @Autowired
    TspTransportGroupSkuRelationRepository transportGroupSkuRelationRepository;

    @Autowired
    private SensitiveDataControl control;

    @Resource
    private PhoneNumberServiceGateway phoneNumberSplitServiceProxy;

    @Resource
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    IThreadPoolService threadPoolService;

    @Autowired
    TmsTransportQconfig transportQconfig;

    @Autowired
    TransportGroupDriverApplicationRecordRepository transportGroupDriverApplicationRecordRepository;

    @Autowired
    DriverGateway driverGateway;

    @Autowired
    TransportgroupGateway transportgroupGateway;

    @Autowired
    TransportgroupSkuRelationGateway transportgroupSkuRelationGateway;

    @Autowired
    IntoOrderConfigGateway intoOrderConfigGateway;

    @Autowired
    DriverTransportGroupRelationGateway driverTransportGroupRelationGateway;

    @Autowired
    ComputePriceSoaServiceClientProxy computePriceSoaServiceClientProxy;

    /**
     * 查询运力组报名信息
     * @param requestType
     * @return
     */
    @Override
    public Result<QueryTransportGroupApplyInfoSOAResponseType> queryTransportGroupApplyInfo(QueryTransportGroupApplyInfoSOARequestType requestType) {
        Result.Builder<QueryTransportGroupApplyInfoSOAResponseType> builder = Result.Builder.newResult();
        QueryTransportGroupApplyInfoSOAResponseType resultSoaType = new QueryTransportGroupApplyInfoSOAResponseType();
        TspTransportGroupPO transportGroupPO = transportGroupRepository.queryTransportGroupDetail(requestType.getTransportGroupId());
        if(Objects.isNull(transportGroupPO)){
            return builder.fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgTransportGroupNotFound)).build();
        }
        List<TspTransportGroupWorkShiftPO> workShiftPOS = workShiftRepository.queryWorkShifts(requestType.getTransportGroupId(), TmsTransportConstant.WorkShiftActiveEnum.VALID.getCode());
        //查询运力组班次报名信息
        List<WorkShiftApplyInfoPO> shiftApplyInfoPOS = workShiftRepository.queryWorkShiftApplyInfo(requestType.getTransportGroupId());
        List<TransportGroupApplyInfoSOAType> collect = workShiftPOS.stream().map(workShiftPO -> {
            TransportGroupApplyInfoSOAType soaType = new TransportGroupApplyInfoSOAType();
            soaType.setId(workShiftPO.getId());
            soaType.setOrder(workShiftPO.getOrder());
            soaType.setStartTime(workShiftPO.getStarTime());
            soaType.setEndTime(workShiftPO.getEndTime());
            soaType.setDriverUpperLimit(workShiftPO.getDriverUpperLimit());
            for (WorkShiftApplyInfoPO shiftApplyInfoPO : shiftApplyInfoPOS) {
                if (Objects.equals(workShiftPO.getId(), shiftApplyInfoPO.getWorkShiftId())) {
                    soaType.setDriverBindedNum(shiftApplyInfoPO.getDriverBindedNum());
                    soaType.setDriverAppliedSuccessNum(shiftApplyInfoPO.getDriverAppliedSuccessNum());
                    soaType.setDriverAppliedSuccessValidNum(shiftApplyInfoPO.getDriverAppliedSuccessValidNum());
                    if (Objects.nonNull(soaType.getDriverAppliedSuccessValidNum()) && soaType.getDriverAppliedSuccessValidNum() > 0) {
                        BigDecimal bigDecimal1 = new BigDecimal(soaType.getDriverUpperLimit());
                        BigDecimal bigDecimal2 = new BigDecimal(soaType.getDriverAppliedSuccessValidNum());
                        soaType.setDriverValidRate(Double.parseDouble(StringUtil.numberFormat(bigDecimal2.doubleValue(),bigDecimal1.doubleValue(),2)));
                    }
                    if (Objects.nonNull(soaType.getDriverAppliedSuccessNum()) && soaType.getDriverAppliedSuccessNum() > 0) {
                        BigDecimal bigDecimal1 = new BigDecimal(soaType.getDriverUpperLimit());
                        BigDecimal bigDecimal2 = new BigDecimal(soaType.getDriverAppliedSuccessNum());
                        soaType.setDrvApplySuccessRate(Double.parseDouble(StringUtil.numberFormat(bigDecimal2.doubleValue(),bigDecimal1.doubleValue(),2)));
                    }
                    break;
                }
            }
            if (Objects.isNull(soaType.getDriverBindedNum())) {
                soaType.setDriverBindedNum(0L);
            }
            if (Objects.isNull(soaType.getDriverAppliedSuccessNum())) {
                soaType.setDriverAppliedSuccessNum(0L);
            }
            if (Objects.isNull(soaType.getDriverAppliedSuccessValidNum())) {
                soaType.setDriverAppliedSuccessValidNum(0L);
            }
            if (Objects.isNull(soaType.getDriverValidRate())) {
                soaType.setDriverValidRate(0.0);
            }
            if (Objects.isNull(soaType.getDrvApplySuccessRate())) {
                soaType.setDrvApplySuccessRate(0.0);
            }
            return soaType;
        }).collect(Collectors.toList());
        resultSoaType.setData(collect);
        resultSoaType.setCityName(enumRepository.getCityName(transportGroupPO.getPointCityId()));
        resultSoaType.setVehicleTypeName(enumRepository.getVehicleTypeName(transportGroupPO.getVehicleTypeId()));
        resultSoaType.setGroupStatus(transportGroupPO.getGroupStatus());
        return builder.success().withData(resultSoaType).build();
    }

    @Override
    public Result<PageHolder<DriverRelationSOADTO>> queryRelationDrvList(DriverRelationListRequestSOAType requestSOAType) {
        Result.Builder<PageHolder<DriverRelationSOADTO>> pageHolderBuilder = Result.Builder.<PageHolder<DriverRelationSOADTO>>newResult();
        TspTransportGroupPO tspTransportGroupPO = transportGroupRepository.queryTransportGroupDetail(requestSOAType.getTransportGroupId());
        if (Objects.isNull(tspTransportGroupPO)) {
            return pageHolderBuilder
                    .fail()
                    .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                    .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQuerySupplierFail) + " transportGroupId：" + requestSOAType.getTransportGroupId())
                    .build();
        }
        // 临时开关开启 并且 如果是供应商级别的查询
        if (tmsTransportQconfig.getRelationDrvListSwitch() && checkAuthentication()) {
            //自营可以垮运力组查询一次查询多个，而供应商只能查询一个，其他皆拒绝
            if (CollectionUtils.isEmpty(requestSOAType.getSupplierIdList()) ||
                    (requestSOAType.getSupplierIdList().size() == 1 && !authorizationCheckService.checkAuthorizationTransportGroup(ImmutableList.of(requestSOAType.getTransportGroupId()), requestSOAType.getSupplierIdList().get(0)))) {
                return pageHolderBuilder
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.NO_LOGIN_ERROR_CODE)
                        .withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportQueryFail) + " transportGroupId：" + requestSOAType.getTransportGroupId())
                        .build();
            }
        }
        QueryDriverRelationDetailParam queryDriverRelationDetailParam = new QueryDriverRelationDetailParam();
        queryDriverRelationDetailParam.setTransportGroupId(requestSOAType.getTransportGroupId());
        queryDriverRelationDetailParam.setIsHasRelation(requestSOAType.getIsHasRelation());
        queryDriverRelationDetailParam.setCoopMode(requestSOAType.getCoopMode());
        queryDriverRelationDetailParam.setDrvLanguage(requestSOAType.getDrvLanguage());
        queryDriverRelationDetailParam.setDrvName(requestSOAType.getDrvName());
        queryDriverRelationDetailParam.setDrvPhone(TmsTransUtil.encrypt(requestSOAType.getDrvPhone(),KeyType.Phone));
        queryDriverRelationDetailParam.setDrvStatus(requestSOAType.getDrvStatus());
        if (CollectionUtils.isEmpty(requestSOAType.getSupplierIdList())) {
            queryDriverRelationDetailParam.setSupplierIdList(Lists.newArrayList(tspTransportGroupPO.getSupplierId()));
        }else {
            queryDriverRelationDetailParam.setSupplierIdList(requestSOAType.getSupplierIdList());
        }
        queryDriverRelationDetailParam.setPaginator(requestSOAType.getPaginator());
        //全职报名制运力组关联司机特殊逻辑
        if (Objects.equals(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),tspTransportGroupPO.getTransportGroupMode())) {
            //报名制运力组默认添加运力组城市+车型参数
            //同城市=点位城市+运力区域城市
            List<Long> cityIds = getAreaCityIds(tspTransportGroupPO.getAreaGroupType(),tspTransportGroupPO.getAreaGroupId());
            cityIds.add(tspTransportGroupPO.getPointCityId());
            queryDriverRelationDetailParam.setCityId(cityIds);
            queryDriverRelationDetailParam.setVehicleTypeId(Lists.newArrayList(tspTransportGroupPO.getVehicleTypeId()));
            //报名制运力组添加报名状态、工作班次参数
            queryDriverRelationDetailParam.setApplyStatus(requestSOAType.getApplyStatus());
            queryDriverRelationDetailParam.setWorkShiftId(requestSOAType.getWorkShiftId());
        }else {
            queryDriverRelationDetailParam.setCityId(requestSOAType.getCityId());
            queryDriverRelationDetailParam.setVehicleTypeId(requestSOAType.getVehicleTypeId());
        }
        PageHolder pageHolder;
        PaginatorDTO pageInfo = queryDriverRelationDetailParam.getPaginator();
        //默认关联关系
        if(requestSOAType.getRelationType() == null){
            requestSOAType.setRelationType(TmsTransportConstant.DrvDispatchRelationTypeEnum.CORRELATION.getCode());
        }
        //派遣关系查询，则查询当前运力组所属供应商下的派遣司机
        if(Objects.equals(TmsTransportConstant.DrvDispatchRelationTypeEnum.DISPATCH.getCode(),requestSOAType.getRelationType())){
            queryDriverRelationDetailParam.setDispatchSupplierId(tspTransportGroupPO.getSupplierId());
        }

        /**
         * 是否全职
         * */
        boolean isFullTime = tspTransportGroupPO.getTransportGroupMode().intValue() == TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode().intValue() ||tspTransportGroupPO.getTransportGroupMode().intValue() == TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode().intValue();
        /**
         * 是否人工调度
         * */
        boolean isHumanDispatch = tspTransportGroupPO.getTransportGroupMode().intValue() == TmsTransportConstant.TransportGroupModeEnum.RGDD.getCode().intValue() || tspTransportGroupPO.getTransportGroupMode().intValue() == TmsTransportConstant.TransportGroupModeEnum.YXRGDD.getCode().intValue() || tspTransportGroupPO.getTransportGroupMode().intValue() == TmsTransportConstant.TransportGroupModeEnum.DDRGDD.getCode().intValue();
        /**
         * 运力组展示产线
         */
        List<Integer> showProductionLineList = productionLineUtil.getShowProductionLineList(tspTransportGroupPO.getCategorySynthesizeCode());
        /**
         * 是否包车产线
         */
        boolean isDayProductLine = productionLineUtil.isProductLineCodeCheck(showProductionLineList, productionLineUtil.getDAYProductLineCode());
        /**
         * 是否接送机产线
         */
        boolean isJNTProductLine = productionLineUtil.isProductLineCodeCheck(showProductionLineList, productionLineUtil.getJNTProductLineCode());
        Boolean ispTpProductLine = productionLineUtil.isProductLineCodeCheck(showProductionLineList, productionLineUtil.getPTPProductLineCode());
        queryDriverRelationDetailParam.setJNTProductLine(isJNTProductLine);
        queryDriverRelationDetailParam.setPTpProductLine(ispTpProductLine);

        List<Long> fullTimeRuleOutDrvIdList = null;


        //        if (isDayProductLine || ispTpProductLine) {
//            queryDriverRelationDetailParam.setTransportGroupModeList(Lists.newArrayList(TmsTransportConstant.TransportGroupModeEnum.QZSJ.getCode(), TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode()));
//            queryDriverRelationDetailParam.setProductLines(Lists.newArrayList(CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue()));
//            fullTimeRuleOutDrvIdList = queryDrvIdByValidTransportAndConditions(queryDriverRelationDetailParam);
//        } else if (isJNTProductLine && isFullTime) {
//            queryDriverRelationDetailParam.setProductLines(Lists.newArrayList(CommonEnum.UseProductionLineEnum.CHARTER_TRANSPORTATION.getValue(),CommonEnum.UseProductionLineEnum.PTP_TRANSPORTATION.getValue()));
//            fullTimeRuleOutDrvIdList = queryDrvIdByValidTransportAndConditions(queryDriverRelationDetailParam);
//        }

        /**
         * 产品逻辑：http://conf.ctripcorp.com/pages/viewpage.action?pageId=477552583
         * 全职（全职指派模式及全职指派-报名制模式）的接送机运力组，和包车运力组互斥。任何司机不可同时绑定二者
         * 如司机已绑定全职接送机运力组，包车运力组关联司机时，无法搜到该司机；
         * 如司机已绑定包车运力组，全职接送机运力组关联司机时，无法搜到该司机；
         */
        queryDriverRelationDetailParam.setFullTimeRuleOutDrvIdList(fullTimeRuleOutDrvIdList);
        queryDriverRelationDetailParam.setHumanDispatch(isHumanDispatch);

        List<DriverRelationDetailPO> driverRelationDetailPOS;
        int total = 0;
        if (queryDriverRelationDetailParam.getIsHasRelation() == 0) {
            if(queryDriverRelationDetailParam.getDispatchSupplierId() == null || queryDriverRelationDetailParam.getDispatchSupplierId() <=0){
                //查询已关联
                driverRelationDetailPOS = tspTransportGroupDriverRelationRepository.queryRelationDrvList(queryDriverRelationDetailParam);
            }else{
                //查询已关联派遣列表
                driverRelationDetailPOS = tspTransportGroupDriverRelationRepository.queryDispatchRelationDrvList(queryDriverRelationDetailParam);
            }
            if (pageInfo != null) {
                if(queryDriverRelationDetailParam.getDispatchSupplierId() == null || queryDriverRelationDetailParam.getDispatchSupplierId() <=0){
                    total = tspTransportGroupDriverRelationRepository.queryRelationDrvCount(queryDriverRelationDetailParam);
                }else {
                    total = tspTransportGroupDriverRelationRepository.queryDispatchRelationDrvCount(queryDriverRelationDetailParam);
                }
            }
        }else {
            if (Objects.equals(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),tspTransportGroupPO.getTransportGroupMode()) &&
                    (Objects.equals(queryDriverRelationDetailParam.getApplyStatus(), TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode()) ||
                            Objects.equals(queryDriverRelationDetailParam.getApplyStatus(), TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode()))) {
                driverRelationDetailPOS = Lists.newArrayList();
                total = 0;
            }else{
                if(queryDriverRelationDetailParam.getDispatchSupplierId() == null || queryDriverRelationDetailParam.getDispatchSupplierId() <=0){
                    //查询未关联
                    driverRelationDetailPOS = tspTransportGroupDriverRelationRepository.queryNotRelationDrvList(queryDriverRelationDetailParam);
                }else {
                    //查询未关联-派遣
                    driverRelationDetailPOS = tspTransportGroupDriverRelationRepository.queryDispatchNotRelationDrvList(queryDriverRelationDetailParam);
                }
                if (pageInfo != null) {
                    if(queryDriverRelationDetailParam.getDispatchSupplierId() == null || queryDriverRelationDetailParam.getDispatchSupplierId() <=0){
                        total = tspTransportGroupDriverRelationRepository.queryNotRelationDrvCount(queryDriverRelationDetailParam);
                    }else {
                        total = tspTransportGroupDriverRelationRepository.queryDispatchNotRelationDrvCount(queryDriverRelationDetailParam);
                    }
                }
            }
        }
        List<DriverRelationSOADTO> driverRelationSOADTOS = driverRelationDetailPOToDTO(driverRelationDetailPOS,tspTransportGroupPO.getTransportGroupMode(),tspTransportGroupPO.getPointCityId(), tspTransportGroupPO.getTransportGroupId(), requestSOAType.getWorkShiftId(), Objects.equals(requestSOAType.getIsHasRelation(), 0));
        if (Objects.nonNull(pageInfo)) {
            pageHolder = PageHolder.of(driverRelationSOADTOS).pageIndex(pageInfo.getPageNo()).pageSize(pageInfo.getPageSize()).totalSize(total).build();
        }else {
            pageHolder = PageHolder.of(driverRelationSOADTOS).pageIndex(1).pageSize(driverRelationSOADTOS.size() == 0 ? 10 : driverRelationSOADTOS.size()).totalSize(driverRelationSOADTOS.size()).build();
        }
        return pageHolderBuilder.success().withData(pageHolder).build();
    }

    /**
     * 查询运力组关联的行政区域对应的城市ID
     * @param areaGroupType
     * @param areaGroupId
     * @return
     */
    public List<Long> getAreaCityIds(Integer areaGroupType,Long areaGroupId){
        try {

            Map<Long,List<SimpleAreaGroupDTO>> areaGroupResponseMap =  getAreaCityIdByGroupIds(Arrays.asList(areaGroupId));
            if (MapUtils.isEmpty(areaGroupResponseMap)) {
                logger.info("queryTransportAreaListByGroupId", "Result:null areaGroupType:{} areaGroupId:{}", areaGroupType, areaGroupId);
                return new ArrayList<>();
            }
            if(TmsTransportConstant.AreaTypeTypeEnum.DIY_REGION.getCode().intValue() == areaGroupType){
                return getDiyRegionCity(areaGroupResponseMap.get(areaGroupId));
            }
            List<SimpleAreaGroupDTO> areaGroupDTOList = areaGroupResponseMap.get(areaGroupId);
            if(CollectionUtils.isEmpty(areaGroupDTOList)){
                return new ArrayList<>();
            }
            SimpleAreaGroupDTO simpleAreaGroupDTO = areaGroupDTOList.get(0);
            if(Objects.isNull(simpleAreaGroupDTO)){
                return new ArrayList<>();
            }
            Map<String, String> dataMap = simpleAreaGroupDTO.getRegionInfo();
            Set<Long> cityIdSets = Sets.newHashSet();
            for (Map.Entry<String, String> entry : dataMap.entrySet()) {
                String entryKey = entry.getKey();
                if(StringUtils.isEmpty(entryKey)){
                    continue;
                }
                String[] entryKeys = StringUtils.split(entryKey,"/");
                cityIdSets.add(Long.parseLong(entryKeys[entryKeys.length - 1]));
            }
            return new ArrayList(cityIdSets);
        }catch (Exception e){
            logger.error("queryTransportAreaListByGroupId", "areaGroupType:{} areaGroupId:{}", areaGroupType, areaGroupId);
        }
        return new ArrayList<>();
    }

    public List<Long> getDiyRegionCity(List<SimpleAreaGroupDTO> areaGroupDTOList){
        if(CollectionUtils.isEmpty(areaGroupDTOList)){
            return new ArrayList<>();
        }
        SimpleAreaGroupDTO simpleAreaGroupDTO = areaGroupDTOList.get(0);
        if(Objects.isNull(simpleAreaGroupDTO)){
            return new ArrayList<>();
        }
        List<SimpleCityAreaInfoDTO> areaInfoDTOList = simpleAreaGroupDTO.getCityAreaInfoList();
        List<Long> cityIdList = new ArrayList<>();
        for (SimpleCityAreaInfoDTO simpleCityAreaInfoDTO : areaInfoDTOList) {
            cityIdList.add(simpleCityAreaInfoDTO.getCityId());
        }
        return cityIdList;
    }

    /**
     * 查询运力组关联的行政区域对应的城市ID
     * @param areaGroupId
     * @return
     */
    @Override
    public Map<Long,List<SimpleAreaGroupDTO>> getAreaCityIdByGroupIds(List<Long> areaGroupId){
        if(CollectionUtils.isEmpty(areaGroupId)){
            return Maps.newHashMap();
        }
        try {
            QueryAreaGroupByIdRequestType requestType = new QueryAreaGroupByIdRequestType();
            requestType.setAreaGroupIds(areaGroupId);
            QueryAreaGroupByIdResponseType areaGroupResponseType = skuPriceSoaServiceClientProxy.queryAreaGroupById(requestType);
            if (areaGroupResponseType == null || CollectionUtils.isEmpty(areaGroupResponseType.getAreaGroups())) {
                logger.info("getAreaCityIdByGroupIds", "Result:null areaGroupId:{}",JsonUtil.toJson(areaGroupId));
                return Maps.newHashMap();
            }
            return areaGroupResponseType.getAreaGroups().stream().collect(Collectors.groupingBy(SimpleAreaGroupDTO::getAreaGroupId));
        }catch (Exception e){
            logger.error("queryTransportAreaListByGroupId", "areaGroupId:{}", areaGroupId);
        }
        return Maps.newHashMap();
    }

    public List<DriverRelationSOADTO> driverRelationDetailPOToDTO(List<DriverRelationDetailPO> poList,Integer transportGroupMode,Long pointCityId, Long transportGroupId, Long workShiftId, boolean relationDriver){
        Map<String, String> languageMap = enumRepository.getDrvLanguageMap();
        if(CollectionUtils.isEmpty(poList)){
            return Lists.newArrayList();
        }
        Map<Long, DriverPoint> driverPointMap = Maps.newHashMap();
        Map<Long, TransportGroupDriverApplicationRecordPO> driverApplicationRecordPOMap = Maps.newHashMap();
        //只有全职报名运力组有城市分排名
        List<Long> drvIds = Lists.newArrayList();
        poList.forEach(driverRel ->drvIds.add(driverRel.getDrvId()));
        if (Objects.equals(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),transportGroupMode)) {
            List<DriverPoint> driverPointList = driverPointsQueryProxy.queryDriverPointList(Joiner.on(",").join(drvIds),pointCityId);
            if(CollectionUtils.isNotEmpty(driverPointList)){
                driverPointMap = driverPointList.stream().collect(Collectors.toMap(DriverPoint::getDriverId, Function.identity(), (key1, key2) -> key2));
                if (relationDriver) { // 如果是已关联司机才会查询司机报名记录
                    driverApplicationRecordPOMap = getDriverApplicationRecordPOHashMap(drvIds,transportGroupId, workShiftId);
                }
            }
        }
        Map<Long, DriverPoint> finalDriverPointMap = driverPointMap;
        Map<Long,Integer> drvLeaveStatusMap = getDrvLeaveStatus(drvIds);
        Map<Long, TransportGroupDriverApplicationRecordPO> finalDriverApplicationRecordPOMap =
          driverApplicationRecordPOMap;
        List<DriverRelationSOADTO> collect = poList.stream().map(po -> {
            DriverRelationSOADTO soadto = new DriverRelationSOADTO();
            soadto.setDrvId(po.getDrvId());
            soadto.setSupplierName(enumRepository.getSupplierName(po.getSupplierId()));
            soadto.setDrvName(po.getDrvName());
            soadto.setCityRank(finalDriverPointMap.get(po.getDrvId())==null?0L:finalDriverPointMap.get(po.getDrvId()).getCityRanking());
            soadto.setCityName(enumRepository.getCityName(po.getCityId()));
            soadto.setDrvFromStr(enumRepository.getDrvFrom().get(po.getDrvFrom()));
            soadto.setDrvLanguageName(enumRepository.getDrvLanguageName(po.getDrvLanguage(), languageMap));
            soadto.setDrvPhone(control.getSensitiveData(po.getDrvPhone(), KeyType.Phone));
            soadto.setStatus(po.getDrvStatus());
            soadto.setStatusName(enumRepository.getDrvStatusName().get(po.getDrvStatus()));
            soadto.setVehicleTypeName(enumRepository.getVehicleTypeName(po.getVehicleTypeId()));
            soadto.setApplyStatusName(enumRepository.getApplyStatusName(po.getApplyStatus()));
            soadto.setProLineName(productionLineUtil.getProductionLineNames(po.getCategorySynthesizeCode()));
            soadto.setDrvLeaveStatus(drvLeaveStatusMap.get(soadto.getDrvId()));
            soadto.setAreaScope(po.getInternalScope());
            soadto.setDrvLeaveStatusName(TmsTransportConstant.DrvLeaveStatusEnum.getText(soadto.getDrvLeaveStatus()));
            soadto.setApplyFailedReason(getApplyFailedReason(po.getApplyStatus(), finalDriverApplicationRecordPOMap.get(po.getDrvId())));
            soadto.setApplyFailedReasonDetail(
              getApplyFailedReasonDetail(po.getApplyStatus(), finalDriverApplicationRecordPOMap.get(po.getDrvId())));
            return soadto;
        }).collect(Collectors.toList());
        return collect;
    }

    protected String getApplyFailedReason(Integer applyStatus, TransportGroupDriverApplicationRecordPO transportGroupDriverApplicationRecordPO) {
        if (Objects.equals(applyStatus, TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode()) || transportGroupDriverApplicationRecordPO == null) {
            return null;
        }
        return transportGroupDriverApplicationRecordPO.getApplyFailedReason();
    }

    protected String getApplyFailedReasonDetail(Integer applyStatus, TransportGroupDriverApplicationRecordPO transportGroupDriverApplicationRecordPO) {
        if (Objects.equals(applyStatus, TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode()) || transportGroupDriverApplicationRecordPO == null) {
            return null;
        }
        return transportGroupDriverApplicationRecordPO.getApplyFailedReasonDetail();
    }

    protected Map<Long, TransportGroupDriverApplicationRecordPO> getDriverApplicationRecordPOHashMap(List<Long> drvIds, Long transportGroupId, Long workShiftId) {
        Map<Long, TransportGroupDriverApplicationRecordPO> recordPOHashMap = Maps.newHashMap();
        if (!transportQconfig.getLogTransportgroupDriverApplyLog()) {
            return recordPOHashMap;
        }

        TransportGroupDriverApplicationRecordPO condition = new TransportGroupDriverApplicationRecordPO();
        condition.setTransportGroupId(transportGroupId);
        condition.setWorkShiftId(workShiftId);
        condition.setActive(true);
        List<TransportGroupDriverApplicationRecordPO> recordPOList =
          transportGroupDriverApplicationRecordRepository.queryByCondition(condition);

        recordPOList.stream().sorted(Comparator.comparing(TransportGroupDriverApplicationRecordPO::getDataChangeCreateTime)).collect(Collectors.toList());
        for (TransportGroupDriverApplicationRecordPO recordPO : recordPOList) {
            recordPOHashMap.put(recordPO.getDrvId(), recordPO);
        }

        return recordPOHashMap;
    }


    @Override
    public Result<List<Long>> queryOptionalTransportGroupMode(QueryOptionalTransportGroupModeSOARequestType request) {
        Result.Builder<List<Long>> builder = Result.Builder.<List<Long>>newResult();
        if (Objects.isNull(request.getSalesMode()) || Objects.isNull(request.getSupplierId())) {
            return builder.success().withData(Lists.newArrayList()).build();
        }
        String optionalTransportGroupMode = tmsTransportQconfig.getOptionalTransportGroupMode();
        Map<String,Object> config = JacksonSerializer.INSTANCE().deserialize(optionalTransportGroupMode, Map.class);

        String defaultItem = config.get("default").toString();
        String configItem = "";

        Map<String,Object> configItems = JacksonSerializer.INSTANCE().deserialize(JacksonUtil.serialize(config.get(String.valueOf(request.getSalesMode()))), Map.class);
        if (MapUtils.isNotEmpty(configItems) && configItems.get(String.valueOf(request.getSupplierId()))!= null) {
            configItem = configItems.get(String.valueOf(request.getSupplierId())).toString();
        }
        if (Strings.isNullOrEmpty(configItem)) {
            configItem = defaultItem;
        }
        List<Long> collect = Stream.of(configItem.split(",")).map(Long::valueOf).collect(Collectors.toList());
        return builder.success().withData(collect).build();
    }

    @Override
    public Result<Map<String, String>> queryTransportGroupConfig(QueryTransportGroupConfigSOARequestType request) {
        Result.Builder<Map<String, String>> builder = Result.Builder.<Map<String, String>>newResult();
        Map<String, String> config = JsonUtil.fromJson(tmsTransportQconfig.getTransportGroupConfig(), new TypeReference<Map<String, String>>() {
        });
        if (CollectionUtils.isEmpty(request.getKeys())) {
            return builder.success().withData(config).build();
        }
        Map<String,String> data = Maps.newHashMap();
        request.getKeys().forEach(key -> data.put(key,config.getOrDefault(key,"")));
        return builder.success().withData(data).build();
    }

    @Override
    public boolean isDispatcherMode(Integer transportGroupMode) {
        return TmsTransportConstant.TransportGroupModeEnum.RGDD.getCode().equals(transportGroupMode)
                        || TmsTransportConstant.TransportGroupModeEnum.DDRGDD.getCode().equals(transportGroupMode)
                            || TmsTransportConstant.TransportGroupModeEnum.YXRGDD.getCode().equals(transportGroupMode);
    }

    @Override
    public boolean isApplyMode(Integer transportGroupMode) {
        return TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode().equals(transportGroupMode);
    }

    @Override
    public List<SkuInfoSOADTO> querySkuInfoByTransportGroupId(Long transportGroupId) {
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOList = arearRelationRepository.queryTransportGroupSkuInfoList(transportGroupId);
        if (CollectionUtils.isEmpty(skuAreaRelationPOList)) {
            return Collections.emptyList();
        }

        List<SkuInfoSOADTO> data = Lists.newArrayList();
        for (TspTransportGroupSkuAreaRelationPO areaRelationPO : skuAreaRelationPOList) {
            SkuInfoSOADTO skuInfoSOADTO = new SkuInfoSOADTO();
            BeanUtils.copyProperties(areaRelationPO, skuInfoSOADTO);
            data.add(skuInfoSOADTO);
        }
        return data;
    }

    @Override
    public Result<PageHolder<QueryBingIngSkuSOADTO>> queryBindSkuRelationList(QueryBingIngSkuSOARequestType queryBingIngSkuRequestType) {
        Long transportGroupId = queryBingIngSkuRequestType.getTransportGroupId();
//            运力组关联的商品SKUID
        PageHolder pageHolder = null;
        try {
            //查询类型 1.已绑定商品，2.全量商品
            Integer queryType = queryBingIngSkuRequestType.getBingType();
            if(queryType == null || (!Objects.equals(queryType,1) && !Objects.equals(queryType,2))){
                return Result.Builder.<PageHolder<QueryBingIngSkuSOADTO>>newResult().fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(msgQconfig.getTransportMsgRequiredParameterMissing())
                        .withData(null)
                        .build();
            }

            List<Long> skuIdList = new ArrayList<>();//查询全量商品列表
            //查询已绑定，skuId 为空，直接返回，1.已绑定商品，2.全量商品
            if(queryType == 1){
                skuIdList = this.getSkuId(transportGroupId);
                if(CollectionUtils.isEmpty(skuIdList)){
                    pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(queryBingIngSkuRequestType.paginator.getPageNo()).pageSize(queryBingIngSkuRequestType.paginator.getPageSize()).totalSize(0).build();
                    return Result.Builder.<PageHolder<QueryBingIngSkuSOADTO>>newResult().success().withData(pageHolder).build();
                }
            }

            TspTransportGroupPO transportGroupPO  =  transportGroupRepository.getTspTransportGroupRepo().queryByPk(queryBingIngSkuRequestType.getTransportGroupId());
            if(transportGroupPO == null){
                return Result.Builder.<PageHolder<QueryBingIngSkuSOADTO>>newResult().fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(msgQconfig.getTransportMsgTransportGroupNotFound())
                        .withData(null)
                        .build();
            }
            queryBingIngSkuRequestType.setCityId(Arrays.asList(transportGroupPO.getPointCityId()));

            boolean isJNTProductLine = productionLineUtil.isProductLineCodeCheck(productionLineUtil.getShowProductionLineList(transportGroupPO.getCategorySynthesizeCode()), productionLineUtil.getJNTProductLineCode());

            //如果前端不传点位,并且运力是模式 人工调度、优先人工调度、兜底人工调度   默认进单配置所有点位
            if(CollectionUtils.isEmpty(queryBingIngSkuRequestType.getLocationCode()) && this.checkTransPortMode(transportGroupPO.getTransportGroupMode()) && isJNTProductLine){
                //获取进单配置
                List<TspIntoOrderConfigPO> intoOrderConfigPOS = inOrderConfigRepository.queryInOrderConfigs(transportGroupId, TmsTransportConstant.IntoOrderConfigActiveEnum.VALID.getCode());
                if(CollectionUtils.isEmpty(intoOrderConfigPOS)){
                    pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(queryBingIngSkuRequestType.paginator.getPageNo()).pageSize(queryBingIngSkuRequestType.paginator.getPageSize()).totalSize(0).build();
                    return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
                }
                List<String> localCodeList = Lists.newArrayList();
                intoOrderConfigPOS.forEach(config -> {
                    if(Objects.equals(transportGroupPO.getPointCityId(),config.getCityId())){
                        localCodeList.add(config.getLocationCode());
                    }
                });
                queryBingIngSkuRequestType.setLocationCode(localCodeList);
            }
            //报名制运力组模式需要特殊处理流程
            if (Objects.equals(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),transportGroupPO.getTransportGroupMode())) {
                queryBingIngSkuRequestType.setVehicleTypeId(Lists.newArrayList(transportGroupPO.getVehicleTypeId()));
            }
            //通过运力组关联合同Id，查询对应的服务商
            Map<String,Object> map = enumRepository.queryServiceProvider(transportGroupPO.getContractId());
            if(Objects.isNull(map.get("serviceProviderId"))) {
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(queryBingIngSkuRequestType.paginator.getPageNo()).pageSize(queryBingIngSkuRequestType.paginator.getPageSize()).totalSize(0).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }
            //查询全量商品列表
            QueryProductSkuWithRegionListRequestType regionListRequestType = new QueryProductSkuWithRegionListRequestType();
            regionListRequestType.setPagingSetting(queryBingIngSkuRequestType.getPaginator());

            Integer lineCode = CommonEnum.UseProductionLineEnum.AIRPORT_TRANSPORTATION.getValue();
            List<Integer> lineCodeList = productionLineUtil.getShowProductionLineList(transportGroupPO.getCategorySynthesizeCode());
            if (CollectionUtils.isNotEmpty(lineCodeList)) {
                lineCode = lineCodeList.get(0);
            }
            if (queryBingIngSkuRequestType.getProLineId() != null) {
                lineCode = queryBingIngSkuRequestType.getProLineId();
            }
            QueryProductSkuListResponseType regionListResponseType =  productServiceClientProxy.queryProductSkuList(buildListRequestType(queryBingIngSkuRequestType,skuIdList,
                    getServiceProviderIds(Long.valueOf(map.get("serviceProviderId").toString()), transportGroupPO.getContractId(), map.get("saleModeId").toString()),lineCode.longValue()));
            if (regionListResponseType == null || !Objects.equals(regionListResponseType.responseResult.getReturnCode(), TmsTransportConstant.ResultStatusTypeEnum.SUCCESS_CODE.getCode())) {
                logger.info("get RPC LIST，params:{}", regionListResponseType.toString());
                return Result.Builder.<PageHolder<QueryBingIngSkuSOADTO>>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(msgQconfig.getTransportMsgRPCListFail())
                        .withData(null)
                        .build();
            }
            List<QueryBingIngSkuSOADTO> dataList = getData(regionListResponseType,transportGroupId);
            if(CollectionUtils.isEmpty(dataList)){
                pageHolder = PageHolder.of(new ArrayList<>()).pageIndex(queryBingIngSkuRequestType.paginator.getPageNo()).pageSize(queryBingIngSkuRequestType.paginator.getPageSize()).totalSize(0).build();
                return Result.Builder.<PageHolder<DrvLeaveDetailPO>>newResult().success().withData(pageHolder).build();
            }
            pageHolder = PageHolder.of(dataList).pageIndex(regionListResponseType.getPagination().getPageNo()).pageSize(regionListResponseType.getPagination().getPageSize()).totalSize(regionListResponseType.getPagination().getTotalSize()).build();
            return Result.Builder.<PageHolder<QueryBingIngSkuSOADTO>>newResult().success().withData(pageHolder).build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    public List<Long> getServiceProviderIds(Long serviceProviderId, Long contractId, String saleModeId) {
        List<Long> serviceProviderIds = Lists.newArrayListWithCapacity(2);
        serviceProviderIds.add(serviceProviderId);
        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) {
            return serviceProviderIds;
        }
        if (String.valueOf(TmsTransportConstant.SaleModeEnum.ZY.getCode()).equals(saleModeId) || commandService.judgeSupplierIsZYById(contract.getSupplierId())) {
            if (tmsTransportQconfig.getPreferentialServiceProviderId() != null && tmsTransportQconfig.getPreferentialServiceProviderId() > 0) {
                serviceProviderIds.add(tmsTransportQconfig.getPreferentialServiceProviderId());
            }
        }
        return serviceProviderIds;
    }

    //运力组模式：人工调度、优先人工调度、兜底人工调度
    private Boolean checkTransPortMode(Integer transportGroupMode){
        if(Objects.equals(transportGroupMode, TmsTransportConstant.TransportGroupModeEnum.RGDD.getCode())||
                Objects.equals(transportGroupMode, TmsTransportConstant.TransportGroupModeEnum.YXRGDD.getCode())||
                Objects.equals(transportGroupMode, TmsTransportConstant.TransportGroupModeEnum.DDRGDD.getCode())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private List<Long> getSkuId(Long transportGroupId){
        if(transportGroupId == null || transportGroupId <= 0 ){
            return Collections.emptyList();
        }
        List<SkuInfoSOADTO> skuInfoSOADTOS = this.querySkuInfoByTransportGroupId(transportGroupId);
        if(CollectionUtils.isEmpty(skuInfoSOADTOS)){
            return Collections.emptyList();
        }
        Set<Long> skuIdSet = Sets.newHashSet();
        for(SkuInfoSOADTO soadto : skuInfoSOADTOS){
            skuIdSet.add(soadto.getSkuId());
        }
        return new ArrayList<>(skuIdSet);
    }


    protected QueryProductSkuListRequestType buildListRequestType(QueryBingIngSkuSOARequestType queryBingIngSkuRequestType, List<Long> skuIds,List<Long> serviceProviderIds, Long lineCode) {
        QueryProductSkuListRequestType regionListRequestType = new QueryProductSkuListRequestType();
        regionListRequestType.setPagingSetting(queryBingIngSkuRequestType.getPaginator());
        SkuListSearchFilterDTO filterDTO = new SkuListSearchFilterDTO();
        filterDTO.setSkuIds(skuIds);
        filterDTO.setPropertyList(buildPropertyList(queryBingIngSkuRequestType));
        filterDTO.setConnectModes(Arrays.asList(1,0));
        filterDTO.setServiceProviderIds(serviceProviderIds);
        // lineType  101里程  201市内 202 周边游单程 203周边游往返 204线路游
        filterDTO.setLineTypes(queryBingIngSkuRequestType.getLineTypes());
        filterDTO.setCategoryCodes(getIntersection(productionLineUtil.getCategoryCodeList(queryBingIngSkuRequestType.getCategoryId()), productionLineUtil.getSubCategoryCode(lineCode)));
        regionListRequestType.setInclusionFilter(filterDTO);
        return regionListRequestType;
    }

    /**
     * 获取category的交集
     * 1 如果页面传入categoryId作为搜索条件，则取当前运力组的category和搜索条件的category的交集
     * 2 如果页面没有传入categoryId作为搜索条件，则取当前运力组的category
     * @param categoryCodeListFormProLine
     * @param categoryCodeListFromSearch
     * @return
     */

    public List<String> getIntersection(List<String> categoryCodeListFromSearch, List<String> categoryCodeListFormProLine) {
        if (org.springframework.util.CollectionUtils.isEmpty(categoryCodeListFromSearch)) {
            return categoryCodeListFormProLine;
        }
        categoryCodeListFormProLine.retainAll(categoryCodeListFromSearch);
        return categoryCodeListFormProLine;
    }


    private List<BatchProductPropertyDTO> buildPropertyList(QueryBingIngSkuSOARequestType queryBingIngSkuRequestType) {
        List<BatchProductPropertyDTO> propertyList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(queryBingIngSkuRequestType.getCityId())) {
            propertyList.add(buildBatchProductPropertyLongDTO(queryBingIngSkuRequestType.getCityId(), ProductPropertyType.CityId));
        }
        if (CollectionUtils.isNotEmpty(queryBingIngSkuRequestType.getVehicleTypeId())) {
            propertyList.add(buildBatchProductPropertyLongDTO(queryBingIngSkuRequestType.getVehicleTypeId(), ProductPropertyType.VehicleTypeId));
        }
        if (CollectionUtils.isNotEmpty(queryBingIngSkuRequestType.getLocationCode())) {
            propertyList.add(buildBatchProductPropertyStringDTO(queryBingIngSkuRequestType.getLocationCode(),ProductPropertyType.FixedLocationCode));
        }
        return propertyList;
    }

    private BatchProductPropertyDTO buildBatchProductPropertyLongDTO(List<Long> longColum,ProductPropertyType propertyType) {
        BatchProductPropertyDTO propertyDTO = new BatchProductPropertyDTO();
        propertyDTO.setCode(propertyType.getCode());
        propertyDTO.setId(propertyType.getId());
        List<String> valueCodeList = Lists.newArrayList();
        for (Long serviceAreaId : longColum) {
            valueCodeList.add(String.valueOf(serviceAreaId));
        }
        propertyDTO.setValueCodes(valueCodeList);
        return propertyDTO;
    }

    private BatchProductPropertyDTO buildBatchProductPropertyStringDTO(List<String> strColum, ProductPropertyType propertyType) {
        BatchProductPropertyDTO propertyDTO = new BatchProductPropertyDTO();
        propertyDTO.setId(propertyType.getId());
        propertyDTO.setCode(propertyType.getCode());
        propertyDTO.setValueCodes(strColum);
        return propertyDTO;
    }

    protected List<QueryBingIngSkuSOADTO> getData(QueryProductSkuListResponseType regionListResponseType,Long transportGroupId) {
        List<QueryBingIngSkuSOADTO> skuDTOS = Lists.newArrayList();
        List<SkuDTO> skuList = regionListResponseType.getSkuList();
        if(CollectionUtils.isEmpty(skuList)){
            return Collections.emptyList();
        }
        TspTransportGroupPO transportGroupPO  =  transportGroupRepository.getTspTransportGroupRepo().queryByPk(transportGroupId);
        List<Long> skuIdList ;
        if (Objects.equals(TmsTransportConstant.TransportGroupModeEnum.QZSJA.getCode(),transportGroupPO.getTransportGroupMode())) {
            skuIdList = this.getSkuId(transportGroupId);
        }else {
            skuIdList = this.getSkuId(transportGroupId);
        }
        for (SkuDTO skuDTO : skuList) {
            QueryBingIngSkuSOADTO bingIngSkuDTO = new QueryBingIngSkuSOADTO();
            bingIngSkuDTO.setSkuId(skuDTO.getSkuId());
            bingIngSkuDTO.setServiceAreaType(skuDTO.getServiceType());
            bingIngSkuDTO.setCategoryName(skuDTO.getCategoryName());
            ServiceProvider serviceProvider = enumRepository.getSkuServiceProvider(skuDTO.getServiceProviderId());
            bingIngSkuDTO.setBrandName(serviceProvider != null ? serviceProvider.getBrandLocalName() : "");

            Integer locationType = PropertyUtils.tryGetLong(ProductPropertyType.FixedLocationType, skuDTO.getPropertyList(), 1L).intValue();
            String fixedLocationCode = PropertyUtils.tryGet(ProductPropertyType.FixedLocationCode, skuDTO.getPropertyList(), "");
            FixedLocation fixedLocation = enumRepository.getFixedLocation(locationType,fixedLocationCode);
            if(fixedLocation != null){
                bingIngSkuDTO.setLocationCode(fixedLocation.getLocation().getLocationCode());
                bingIngSkuDTO.setLocationCodeName(fixedLocation.getLocation().getLocationName());
            }
            Long cityId = PropertyUtils.tryGetLong(ProductPropertyType.CityId, skuDTO.getPropertyList(), 1L);
            bingIngSkuDTO.setCityId(cityId);
            bingIngSkuDTO.setCityName(enumRepository.getCityName(cityId));
            Long vehicleTypeId = PropertyUtils.tryGetLong(ProductPropertyType.VehicleTypeId, skuDTO.getPropertyList(), 1L);
            bingIngSkuDTO.setVehicleTypeId(vehicleTypeId);
            bingIngSkuDTO.setVehicleTypeName(enumRepository.getVehicleTypeName(vehicleTypeId));
            bingIngSkuDTO.setBingStatus(0);
            if(skuIdList.contains(skuDTO.getSkuId())){
                bingIngSkuDTO.setBingStatus(1);
            }
            bingIngSkuDTO.setActive(skuDTO.active);
            bingIngSkuDTO.setProLineName(productionLineUtil.getCategoryName(skuDTO.getCategoryId()));
            bingIngSkuDTO.setLineType(skuDTO.lineType);
            skuDTOS.add(bingIngSkuDTO);
        }
        return skuDTOS;
    }

    @Override
    public Result<TspTransportGroupPO> queryTransportGroupDetail(Long transportGroupId) {
        TspTransportGroupPO transportGroupPO = transportGroupRepository.queryTransportGroupDetail(transportGroupId);
        return Result.Builder.<TspTransportGroupPO>newResult()
                .success()
                .withData(transportGroupPO)
                .build();
    }

    private Result getEmptyRes(PaginatorDTO pageInfo) {
        int pageNo = pageInfo == null ? 0 : pageInfo.getPageNo();
        int pageSize = pageInfo == null ? 10 : pageInfo.getPageSize();
        PageHolder emptyRes = PageHolder.of(Collections.emptyList()).pageIndex(pageNo).pageSize(pageSize).totalSize(0).build();
        return Result.Builder.<PageHolder<TspTransportGroupPO>>newResult()
                .success()
                .withData(emptyRes)
                .build();
    }

    @Override
    public Result queryTransportGroupList(TspTransportGroupPO transportGroupPO,QueryTransportGroupListSOARequestType soaRequestType) {
        List<Long> contractIdList = enumRepository.queryContractByServiceProviderId(soaRequestType.getServiceProviderId());
        QueryTransportGroupListParam queryTransportGroupListParam = new QueryTransportGroupListParam(transportGroupPO,soaRequestType.getPaginator(),contractIdList
                ,soaRequestType.getTransportGroupIdList(),soaRequestType.getSupplierIdList(),soaRequestType.getSalfSupplierCityList(),soaRequestType.getVehicleTypeIdList());
        List<TspTransportGroupPO> groupPOList = Collections.emptyList();
        PageHolder pageHolder;
        PaginatorDTO pageInfo = queryTransportGroupListParam.getPageInfo();
        if (CollectionUtils.isNotEmpty(soaRequestType.getSkuIdList())) {
            List<TspTransportGroupSkuAreaRelationPO> relationPOS = arearRelationRepository.queryTransportGroupSkuIds(TransportGroupConverter.toQueryModel(soaRequestType.getSkuIdList()));
            if (CollectionUtils.isEmpty(relationPOS)) {
                return getEmptyRes(pageInfo);
            }
            List<Long> transportGroupIdList = Lists.newArrayListWithCapacity(relationPOS.size());
            for (TspTransportGroupSkuAreaRelationPO relationPO : relationPOS) {
                transportGroupIdList.add(relationPO.getTransportGroupId());
            }
            if (CollectionUtils.isEmpty(queryTransportGroupListParam.getTransportGroupIdList())) {
                queryTransportGroupListParam.setTransportGroupIdList(transportGroupIdList);
            } else {
                Set<Long> intersection = Sets.intersection(Sets.newHashSet(queryTransportGroupListParam.getTransportGroupIdList()), Sets.newHashSet(transportGroupIdList));
                if (CollectionUtils.isEmpty(intersection)) {
                    return getEmptyRes(pageInfo);
                }
                queryTransportGroupListParam.setTransportGroupIdList(Lists.newArrayList(intersection));
            }
        }
        if (pageInfo != null) {
            Long total = transportGroupRepository.countTransportGroupList(queryTransportGroupListParam);
            if (total != null && total > 0) {
                groupPOList = transportGroupRepository.queryTransportGroupList(queryTransportGroupListParam);
            }
            pageHolder = PageHolder.of(groupPOList).pageIndex(pageInfo.getPageNo()).pageSize(pageInfo.getPageSize()).totalSize(total == null ? 0 : total.intValue()).build();
        } else {
            groupPOList = transportGroupRepository.queryTransportGroupList(queryTransportGroupListParam);
            pageHolder = PageHolder.of(groupPOList).pageIndex(1).pageSize(groupPOList.size() == 0 ? 10 : groupPOList.size()).totalSize(groupPOList.size()).build();
        }
        return Result.Builder.<PageHolder<TspTransportGroupPO>>newResult()
                .success()
                .withData(pageHolder)
                .build();
    }

    @Override
    public Result<List<TransportGroupDetailSOAType>> queryTransportGroups(QueryTransportGroupModel queryModel) {
        try {
            List<TspTransportGroupPO> tspTransportGroupPOS = transportGroupRepository.queryTransportGroups(queryModel);
            if (CollectionUtils.isEmpty(tspTransportGroupPOS)) {
                logger.info("no macth transportGroup");
                return Result.Builder.<List<TransportGroupDetailSOAType>>newResult().success().withData(Collections.EMPTY_LIST).build();
            }
            logger.info("macthed transportGroup："+JsonUtil.toJson(tspTransportGroupPOS));
            List<Long> transportGroupIds;
            if ((queryModel.getLatitude() != null && queryModel.getLongitude() != null && !Strings.isNullOrEmpty(queryModel.getPoiType()))
                || !Strings.isNullOrEmpty(queryModel.getPoiRef()) || !Strings.isNullOrEmpty(queryModel.getCarPlaceId())) {
                QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
                queryPointAreaInfoRequestType.setCoordinateType(queryModel.getPoiType());
                if (queryModel.getLatitude() != null) {
                    queryPointAreaInfoRequestType.setPointLat(new BigDecimal(queryModel.getLatitude()));
                }
                if (queryModel.getLongitude() != null) {
                    queryPointAreaInfoRequestType.setPointLng(new BigDecimal(queryModel.getLongitude()));
                }
                queryPointAreaInfoRequestType.setPoiRef(queryModel.getPoiRef());
                queryPointAreaInfoRequestType.setCarplaceId(queryModel.getCarPlaceId());
                queryPointAreaInfoRequestType.setAreaGroupIds(tspTransportGroupPOS.stream().map(TspTransportGroupPO::getAreaGroupId).distinct().collect(Collectors.toList()));
                QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType);
                if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(queryPointAreaInfoResponseType.getResstatus().getRcode()) || queryPointAreaInfoResponseType.getAreaGroupInfo() == null) {
                    return Result.Builder.<List<TransportGroupDetailSOAType>>newResult()
                        .fail()
                        .withMsg(msgQconfig.getTransportMsgSelectPointServiceAreaInfoError())
                        .build();
                }
                for (int i = tspTransportGroupPOS.size() - 1; i >= 0; i--) {
                    if (!"true".equalsIgnoreCase(queryPointAreaInfoResponseType.getAreaGroupInfo().get(String.valueOf(tspTransportGroupPOS.get(i).getAreaGroupId())))) {
                        tspTransportGroupPOS.remove(i);
                    }
                }
            }
            transportGroupIds = tspTransportGroupPOS.stream().map(TspTransportGroupPO::getTransportGroupId).distinct().collect(Collectors.toList());
            Map<Long, List<TspIntoOrderConfigPO>> collect = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(transportGroupIds)) {
                List<TspIntoOrderConfigPO> intoOrderConfigPOS = inOrderConfigRepository.queryInOrderConfigs(transportGroupIds, TmsTransportConstant.IntoOrderConfigActiveEnum.VALID.getCode());
                collect = intoOrderConfigPOS.stream().collect(Collectors.groupingBy(TspIntoOrderConfigPO::getTransportGroupId));
            }
            logger.info("result transportGroup："+JsonUtil.toJson(tspTransportGroupPOS));
            List<TransportGroupDetailSOAType> baseInfoSOATypes = poToGroupDetailType(tspTransportGroupPOS, collect);
            return Result.Builder.<List<TransportGroupDetailSOAType>>newResult()
                    .success()
                    .withData(baseInfoSOATypes)
                    .build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    private List<TransportGroupDetailSOAType> poToGroupDetailType(List<TspTransportGroupPO> groupPOS, Map<Long, List<TspIntoOrderConfigPO>> collect){
        List<TransportGroupDetailSOAType> baseInfoSOATypes = Lists.newArrayList();
        for (TspTransportGroupPO groupPO : groupPOS) {
            TransportGroupDetailSOAType soaType = new TransportGroupDetailSOAType();
            soaType.setTransportGroupId(groupPO.getTransportGroupId());
            soaType.setSupplierId(groupPO.getSupplierId());
            soaType.setTransportGroupName(groupPO.getTransportGroupName());
            soaType.setTransportGroupMode(groupPO.getTransportGroupMode());
            soaType.setDispatcher(groupPO.getDispatcher());
            soaType.setDispatcherPhone(control.getSensitiveData(groupPO.getDispatcherPhone(), KeyType.Phone));
            soaType.setDispatcherLanguage(groupPO.getDispatcherLanguage());
            soaType.setTakeOrderLimitTime(groupPO.getTakeOrderLimitTime());
            List<InOrderConfigSOAType> list = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(collect.get(groupPO.getTransportGroupId()))) {
                collect.get(groupPO.getTransportGroupId()).forEach(intoOrderConfigPO -> {
                    InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
                    inOrderConfigSOAType.setId(intoOrderConfigPO.getId());
                    inOrderConfigSOAType.setCountryId(intoOrderConfigPO.getCountryId());
                    inOrderConfigSOAType.setCountryName(intoOrderConfigPO.getCountryName());
                    inOrderConfigSOAType.setCityId(intoOrderConfigPO.getCityId());
                    inOrderConfigSOAType.setLocationCode(intoOrderConfigPO.getLocationCode());
                    inOrderConfigSOAType.setTransportGroupId(intoOrderConfigPO.getTransportGroupId());
                    inOrderConfigSOAType.setLocationType(intoOrderConfigPO.getLocationType());
                    inOrderConfigSOAType.setActive(intoOrderConfigPO.getActive());
                    inOrderConfigSOAType.setCreateUser(intoOrderConfigPO.getCreateUser());
                    inOrderConfigSOAType.setModifyUser(intoOrderConfigPO.getModifyUser());
                    inOrderConfigSOAType.setDatachangeCreatetime(DateUtil.timestampToString(intoOrderConfigPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
                    inOrderConfigSOAType.setDatachangeLasttime(DateUtil.timestampToString(intoOrderConfigPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
                    if (!Strings.isNullOrEmpty(intoOrderConfigPO.getConfig())) {
                            inOrderConfigSOAType.setConfigItems(JsonUtil.fromJson(intoOrderConfigPO.getConfig(),new TypeReference<List<ConfigItemSOAType>>(){}));
                    }
                    list.add(inOrderConfigSOAType);
                });
            }
            soaType.setCreateUser(groupPO.getCreateUser());
            soaType.setGroupStatus(groupPO.getGroupStatus());
            soaType.setGroupStatusName(enumRepository.getTransportGroupStatusMap().get(groupPO.getGroupStatus()));
            soaType.setModifyUser(groupPO.getModifyUser());
            soaType.setInOrderConfigs(JsonUtil.toJson(list));
            soaType.setDatachangeCreatetime(DateUtil.timestampToString(groupPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
            soaType.setDatachangeLasttime(DateUtil.timestampToString(groupPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
            soaType.setIgtCode(groupPO.getIgtCode());
            soaType.setProductLine(productionLineUtil.getShowProductionLineList(groupPO.getCategorySynthesizeCode()).get(0));
            soaType.setDispatcherEmail(control.getSensitiveData(groupPO.getInformEmail(), KeyType.Mail));
            soaType.setCityId(String.valueOf(groupPO.getPointCityId()));
            soaType.setCountryId(groupPO.getCountryId());
            soaType.setShortTransportGroup(groupPO.getShortTransportGroup());
            baseInfoSOATypes.add(soaType);
        }
        return baseInfoSOATypes;
    }

    @Override
    public Result<Boolean> canBind(DrvDriverPO driverPO, Integer mode) {
        TmsTransportConstant.TransportGroupModeEnum transportGroupMode = TmsTransportConstant.TransportGroupModeEnum.getModeEnum(mode);
        Result.Builder<Boolean> finallyResult = Result.Builder.newResult();
        if (driverPO == null || transportGroupMode == null) {
            return finallyResult.success().withData(false).build();
        }
        switch (transportGroupMode) {
            case QZSJA:
            case QZSJ:
                if (driverPO.getVehicleId() == null || driverPO.getVehicleId().intValue() == 0) {
                    return finallyResult.success().withData(false).build();
                }
                if (Strings.isNullOrEmpty(driverPO.getWorkPeriod())) {
                    return finallyResult.success().withData(false).build();
                }
                return finallyResult.success().withData(true).build();
            case JZSJ:
                if (driverPO.getVehicleId() == null || driverPO.getVehicleId().intValue() == 0) {
                    return finallyResult.success().withData(false).build();
                }
                return finallyResult.success().withData(true).build();
            default:
                return finallyResult.success().withData(true).build();
        }
    }

    @Override
    public Result<List<QueryAllTransGroupSOADTO>> queryAllTransportGroupsList(QueryAllTransGroupListDO queryModel) {
        try{
            List<TspTransportGroupPO> resultList = transportGroupRepository.queryAllTransportGroup(queryModel);
            if(CollectionUtils.isEmpty(resultList)){
                return Result.Builder.<List<QueryAllTransGroupSOADTO>>newResult().success().withData(Lists.newArrayList()).build();
            }
            Map<Integer,String> groupMode = enumRepository.getTransportGroupMode();
            List<QueryAllTransGroupSOADTO> soadtoList = Lists.newArrayList();
            resultList.forEach(po -> {
                QueryAllTransGroupSOADTO soadto = new QueryAllTransGroupSOADTO();
                BeanUtils.copyProperties(po,soadto);
                soadto.setTransportGroupModeName(groupMode.get(soadto.getTransportGroupMode()));
                soadtoList.add(soadto);
            });
            return Result.Builder.<List<QueryAllTransGroupSOADTO>>newResult().success().withData(soadtoList).build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public Result<List<TransportGroupListSOAType>> queryTransportGroupListByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Result.Builder.<List<TransportGroupListSOAType>>newResult().fail().withMsg("No data was found").build();
        }
        try {
            List<TspTransportGroupPO> transportList = transportGroupRepository.queryTspTransportByIds(idList);
            if (CollectionUtils.isEmpty(transportList)) {
                return Result.Builder.<List<TransportGroupListSOAType>>newResult().success().withData(Lists.newArrayList()).build();
            }
            List<TransportGroupListSOAType> result = Lists.newArrayListWithCapacity(transportList.size());
            for (TspTransportGroupPO groupPO : transportList) {
                TransportGroupListSOAType soaType = new TransportGroupListSOAType();
                BeanUtils.copyProperties(groupPO, soaType);
                soaType.setSupplierName(enumRepository.getSupplierName(groupPO.getSupplierId()));
                soaType.setTransportGroupModeName(enumRepository.getTransportGroupMode().get(groupPO.getTransportGroupMode()));
                Map<String, String> languageMap = enumRepository.getDrvLanguageMap();
                soaType.setDispatcherLanguageName(enumRepository.getDrvLanguageName(groupPO.getDispatcherLanguage(), languageMap));
                soaType.setGroupStatusName(enumRepository.getTransportGroupStatusMap().get(groupPO.getGroupStatus()));
                soaType.setDatachangeCreatetime(DateUtil.timestampToString(groupPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
                soaType.setDatachangeLasttime(DateUtil.timestampToString(groupPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
                result.add(soaType);
            }
            return Result.Builder.<List<TransportGroupListSOAType>>newResult().success().withData(result).build();
        } catch (SQLException e) {
            logger.error("queryTransportGroupListByIdList params:{} error:{}", idList, e);
            return Result.Builder.<List<TransportGroupListSOAType>>newResult().fail().withMsg(e.getLocalizedMessage()).build();
        }
    }

    @Override
    public Result<List<SkuTransportGroupDTO>> querySkuTransportGroup(List<Long> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return Result.Builder.<List<SkuTransportGroupDTO>>newResult().success().withData(Lists.newArrayList()).build();
        }
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOList = tspTransportGroupSkuArearRelationRepository.queryTransportGroupSkuInfoListBySkuIdList(skuIdList);
        if (CollectionUtils.isEmpty(skuAreaRelationPOList)) {
            return Result.Builder.<List<SkuTransportGroupDTO>>newResult().success().withData(Lists.newArrayList()).build();
        }
        List<Long> transportIdList = Lists.newArrayListWithCapacity(skuAreaRelationPOList.size());
        Map<Long, Set<Long>> skuTransportMap = Maps.newHashMap();
        for (TspTransportGroupSkuAreaRelationPO areaRelationPO : skuAreaRelationPOList) {
            transportIdList.add(areaRelationPO.getTransportGroupId());
            Set<Long> tmpSet = skuTransportMap.get(areaRelationPO.getSkuId());
            if (tmpSet == null) {
                tmpSet = Sets.newHashSet();
                skuTransportMap.put(areaRelationPO.getSkuId(),tmpSet);
            }
            tmpSet.add(areaRelationPO.getTransportGroupId());
        }
        try {
            List<TspTransportGroupPO> tspTransportGroupPOS = transportGroupRepository.queryTspTransportByIds(transportIdList);
            if (CollectionUtils.isEmpty(tspTransportGroupPOS)) {
                return Result.Builder.<List<SkuTransportGroupDTO>>newResult().success().withData(Lists.newArrayList()).build();
            }
            List<SkuTransportGroupDTO> result = Lists.newArrayListWithCapacity(skuTransportMap.size());
            Map<Long,Long> transportNameAreaGroup = Maps.newHashMap();
            for (TspTransportGroupPO transportGroupDTO : tspTransportGroupPOS) {
                transportNameAreaGroup.put(transportGroupDTO.getTransportGroupId(),transportGroupDTO.getAreaGroupId());
            }
            for (Map.Entry<Long, Set<Long>> entry : skuTransportMap.entrySet()) {
                Set<Long> transportIdSet = entry.getValue();
                SkuTransportGroupDTO skuTransportGroupDTO = new SkuTransportGroupDTO();
                skuTransportGroupDTO.setSkuId(entry.getKey());
                List<TransportGroupAreaDTO> areaDTOList = Lists.newArrayListWithCapacity(entry.getValue().size());
                skuTransportGroupDTO.setTransportGroupAreaList(areaDTOList);
                for (Long transportId : transportIdSet) {
                    if (transportNameAreaGroup.containsKey(transportId)) {
                        TransportGroupAreaDTO areaDTO = new TransportGroupAreaDTO();
                        areaDTO.setTransportGroupId(transportId);
                        areaDTO.setAreaGroupId(transportNameAreaGroup.get(transportId));
                        areaDTOList.add(areaDTO);
                    }
                }
                skuTransportGroupDTO.setTransportGroupAreaList(areaDTOList);
                result.add(skuTransportGroupDTO);
            }
            return Result.Builder.<List<SkuTransportGroupDTO>>newResult().success().withData(result).build();
        } catch (Exception e) {
            return Result.Builder.<List<SkuTransportGroupDTO>>newResult().fail().withMsg(e.getLocalizedMessage()).build();
        }
    }

    @Override
    public Result<List<LocationDTOSOA>> queryLocationByGroup(Long transportGroupId) {
        try{
            List<LocationDTOSOA> locationDTOSOAList = Lists.newArrayList();
            TspTransportGroupPO transportGroupPO  =  transportGroupRepository.getTspTransportGroupRepo().queryByPk(transportGroupId);
            if(transportGroupPO == null){
                return Result.Builder.<List<LocationDTOSOA>>newResult()
                        .fail()
                        .withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE)
                        .withMsg(msgQconfig.getTransportMsgTransportGroupNotFound())
                        .withData(locationDTOSOAList)
                        .build();
            }
            //运力组模式：人工调度、优先人工调度、兜底人工调度 只有这三个模式下
            if(!Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.RGDD.getCode())&&
                    !Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.YXRGDD.getCode())&&
                    !Objects.equals(transportGroupPO.getTransportGroupMode(), TmsTransportConstant.TransportGroupModeEnum.DDRGDD.getCode())){
                List<LocationDTOSOA> locationDTOSOAList1 = enumRepository.queryLocalByCity(transportGroupPO.getPointCityId());
                return Result.Builder.<List<LocationDTOSOA>>newResult().success().withData(locationDTOSOAList1).build();
            }

            //获取进单配置
            List<TspIntoOrderConfigPO> intoOrderConfigPOS = inOrderConfigRepository.queryInOrderConfigs(transportGroupId, TmsTransportConstant.IntoOrderConfigActiveEnum.VALID.getCode());
            if(CollectionUtils.isEmpty(intoOrderConfigPOS)){
                return Result.Builder.<List<LocationDTOSOA>>newResult()
                        .success()
                        .withData(locationDTOSOAList)
                        .build();
            }
            intoOrderConfigPOS.forEach(config ->{
                if(Objects.equals(transportGroupPO.getPointCityId(),config.getCityId())){
                    LocationDTOSOA dtosoa = new LocationDTOSOA();
                    dtosoa.setLocationCode(config.getLocationCode());
                    dtosoa.setLocationName(enumRepository.getLocationName(config.getLocationCode(),config.getLocationType()));
                    locationDTOSOAList.add(dtosoa);
                }
            } );
            return Result.Builder.<List<LocationDTOSOA>>newResult().success().withData(locationDTOSOAList).build();
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    private boolean checkAuthentication() {
        if (SessionHolder.getSessionSource() != null && String.valueOf(TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue()).equals(SessionHolder.getSessionSource().get("accountType"))) {
            return true;
        }
        return false;
    }

    public Map<Long,Integer> getDrvLeaveStatus(List<Long> drvIds){
        Map<Long,Integer> drvLeaveStatusMap = Maps.newHashMap();
        if(CollectionUtils.isEmpty(drvIds)){
            return drvLeaveStatusMap;
        }
        for(Long drvId : drvIds){
            drvLeaveStatusMap.put(drvId,0);
        }
        List<DrvLeaveDetailPO> drvDriverLeavePOList = drvDriverLeaveRepository.queryDrvLeaveIng(drvIds);
        if(CollectionUtils.isEmpty(drvDriverLeavePOList)){
            return drvLeaveStatusMap;
        }
        Set<Long> drvIdLeaveSet = drvDriverLeavePOList.stream().map(DrvLeaveDetailPO::getDrvId).collect(Collectors.toSet());
        for(Iterator<Long> iterator = drvIdLeaveSet.iterator();iterator.hasNext();){
            drvLeaveStatusMap.put(iterator.next(),1);
        }
        return drvLeaveStatusMap;
    }

    @Override
    public Result<List<QueryTransportGroupsForDspInfo>> queryTransportGroupsForDsp(QueryTransportGroupModel queryModel) {
        try {
            if(CollectionUtils.isEmpty(queryModel.getSkuIds())){
                return Result.Builder.<List<TransportGroupDetailSOAType>>newResult().success().withData(Collections.EMPTY_LIST).build();
            }
            //通过入参skuIds，查询出所有sku关联的运力组ID
            List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOS = transportgroupSkuRelationGateway.querySkuRelationListBySkuIds(queryModel.getSkuIds());
            if (CollectionUtils.isEmpty(skuAreaRelationPOS)) {
                return Result.Builder.<List<TransportGroupDetailSOAType>>newResult().success().withData(Collections.EMPTY_LIST).build();
            }
            Set<Long> tspTransportGroupIds = skuAreaRelationPOS.stream().map(TspTransportGroupSkuAreaRelationPO::getTransportGroupId).collect(Collectors.toSet());
            Map<Long,List<Long>> skuAreaRelationPOSMap = skuAreaRelationPOS.stream().collect(Collectors.groupingBy(TspTransportGroupSkuAreaRelationPO::getSkuId,
                    Collectors.mapping(TspTransportGroupSkuAreaRelationPO::getTransportGroupId,Collectors.toList())));
            List<TspTransportGroupPO> transportGroupPOList = transportgroupGateway.queryTspTransportByIds(new ArrayList<>(tspTransportGroupIds));

            //根据dispatchOnly过滤是否仅派单不进单
            transportGroupPOList = filterDispatchOnlyTransport(queryModel.getFilterDispatchOnly(), transportGroupPOList);

            if (CollectionUtils.isEmpty(transportGroupPOList)) {
                return Result.Builder.<List<TransportGroupDetailSOAType>>newResult().success().withData(Collections.EMPTY_LIST).build();
            }
            List<TspTransportGroupPO> transportGroupPOS = Lists.newArrayList();
            for(Iterator<TspTransportGroupPO> iterator = transportGroupPOList.iterator();iterator.hasNext();){
                TspTransportGroupPO transportGroupPO = iterator.next();
                if(Objects.equals(transportGroupPO.getGroupStatus(), TmsTransportConstant.TransportGroupStatusEnum.ONLINE.getCode())){
                    transportGroupPOS.add(transportGroupPO);
                }
            }
            if(CollectionUtils.isEmpty(transportGroupPOS)){
                return Result.Builder.<List<TransportGroupDetailSOAType>>newResult().success().withData(Collections.EMPTY_LIST).build();
            }

            //筛选出符合点位的运力组
            if ((queryModel.getLatitude() != null && queryModel.getLongitude() != null && !Strings.isNullOrEmpty(queryModel.getPoiType())) || !Strings.isNullOrEmpty(queryModel.getPoiRef()) || !Strings.isNullOrEmpty(queryModel.getCarPlaceId())) {
                QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
                queryPointAreaInfoRequestType.setCoordinateType(queryModel.getPoiType());
                if (queryModel.getLatitude() != null) {
                    queryPointAreaInfoRequestType.setPointLat(new BigDecimal(queryModel.getLatitude()));
                }
                if (queryModel.getLongitude() != null) {
                    queryPointAreaInfoRequestType.setPointLng(new BigDecimal(queryModel.getLongitude()));
                }
                queryPointAreaInfoRequestType.setPoiRef(queryModel.getPoiRef());
                queryPointAreaInfoRequestType.setCarplaceId(queryModel.getCarPlaceId());
                queryPointAreaInfoRequestType.setAreaGroupIds(transportGroupPOS.stream().map(TspTransportGroupPO::getAreaGroupId).distinct().collect(Collectors.toList()));
                QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType);
                if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(queryPointAreaInfoResponseType.getResstatus().getRcode()) || queryPointAreaInfoResponseType.getAreaGroupInfo() == null) {
                    return Result.Builder.<List<QueryTransportGroupsForDspInfo>>newResult()
                            .fail()
                            .withMsg(msgQconfig.getTransportMsgSelectPointServiceAreaInfoError())
                            .build();
                }
                for (int i = transportGroupPOS.size() - 1; i >= 0; i--) {
                    if (!"true".equalsIgnoreCase(queryPointAreaInfoResponseType.getAreaGroupInfo().get(String.valueOf(transportGroupPOS.get(i).getAreaGroupId())))) {
                        transportGroupPOS.remove(i);
                    }
                }
            }

            //筛选出符合点位的运力组(新)
            if (CollectionUtils.isNotEmpty(queryModel.getPoiList())) {
                List<PoiDTO> poiList = queryModel.getPoiList().stream().filter(poiDTO -> (poiDTO.getLatitude() != null && poiDTO.getLongitude() != null && !Strings.isNullOrEmpty(poiDTO.getPoiType())) || !Strings.isNullOrEmpty(poiDTO.getPoiRef()) || !Strings.isNullOrEmpty(poiDTO.getCarPlaceId())).collect(
                    Collectors.toList());
                if (CollectionUtils.isNotEmpty(poiList)) {
                    QueryPoiAreaInfoRequestType queryPoiAreaInfoRequestType = new QueryPoiAreaInfoRequestType();
                    queryPoiAreaInfoRequestType.setPoiList(convert2PoiList(poiList));
                    queryPoiAreaInfoRequestType.setAreaGroupIds(transportGroupPOS.stream().map(TspTransportGroupPO::getAreaGroupId).distinct().collect(Collectors.toList()));
                    QueryPoiAreaInfoResponseType newAreaGroup =
                        computePriceSoaServiceClientProxy.queryPoiAreaInfo(queryPoiAreaInfoRequestType);
                    if (!ServiceResponseConstants.ResStatus.SUCCESS_CODE.equals(newAreaGroup.getResponseResult().getReturnCode()) || newAreaGroup.getAreaGroupInfo() == null) {
                        return Result.Builder.<List<QueryTransportGroupsForDspInfo>>newResult()
                            .fail()
                            .withMsg(msgQconfig.getTransportMsgSelectPointServiceAreaInfoError())
                            .build();
                    }
                    for (int i = transportGroupPOS.size() - 1; i >= 0; i--) {
                        if (!"true".equalsIgnoreCase(newAreaGroup.getAreaGroupInfo().get(String.valueOf(transportGroupPOS.get(i).getAreaGroupId())))) {
                            transportGroupPOS.remove(i);
                        }
                    }
                }
            }

            if(CollectionUtils.isEmpty(transportGroupPOS)){
                return Result.Builder.<List<TransportGroupDetailSOAType>>newResult().success().withData(Collections.EMPTY_LIST).build();
            }
            List<Long> transportGroupIds = transportGroupPOS.stream().map(TspTransportGroupPO::getTransportGroupId).distinct().collect(Collectors.toList());
            Map<Long,TspTransportGroupPO> transportGroupPOMap = transportGroupPOS.stream().collect(Collectors.toMap(TspTransportGroupPO::getTransportGroupId, Function.identity(), (key1, key2) -> key2));
            Map<Long, List<TspIntoOrderConfigPO>> collect = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(transportGroupIds)) {
                List<TspIntoOrderConfigPO> intoOrderConfigPOS = intoOrderConfigGateway.queryInOrderConfigs(transportGroupIds);
                collect = intoOrderConfigPOS.stream().collect(Collectors.groupingBy(TspIntoOrderConfigPO::getTransportGroupId));
            }
            List<QueryTransportGroupsForDspInfo> baseInfoSOATypes = resultGroupForDsp(skuAreaRelationPOSMap,transportGroupPOMap, collect);
            return Result.Builder.<List<QueryTransportGroupsForDspInfo>>newResult()
                    .success()
                    .withData(baseInfoSOATypes)
                    .build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    public List<AddressDTO> convert2PoiList(List<PoiDTO> poiList) {
        return poiList.stream().map(poiDTO -> new AddressDTO(poiDTO.getLatitude(),poiDTO.getLongitude(), poiDTO.getPoiType(),null, poiDTO.getCarPlaceId(),poiDTO.getPoiRef())
        ).collect(Collectors.toList());
    }

    @Override
    public Result<List<QueryApplyTransGroupsSkuForDspInfo>> queryApplyTransGroupsSkuForDsp(QueryApplyTransGroupsSkuForDspRequestType requestType) {
        logger.info("resultGroupForDsp","params:",requestType.getCityIds());
        try {
            List<TspTransportGroupPO> tspTransportGroupPOS =  transportGroupRepository.queryApplyTransportGroupByCityIds(requestType.getCityIds(),requestType.getCarTypeIds());
            if(CollectionUtils.isEmpty(tspTransportGroupPOS)){
                return Result.Builder.<List<TransportGroupDetailSOAType>>newResult().success().withData(Collections.EMPTY_LIST).build();
            }
            List<Long> groupIds = tspTransportGroupPOS.stream().map(TspTransportGroupPO::getTransportGroupId).collect(Collectors.toList());
            List<TspTransportGroupSkuAreaRelationPO> skuRelationPOS =  transportGroupSkuRelationRepository.querySkuRelationList(groupIds,Boolean.TRUE);
            return Result.Builder.<List<QueryApplyTransGroupsSkuForDspInfo>>newResult().success().withData(resultGroupForDsp(tspTransportGroupPOS,skuRelationPOS)).build();
        }catch (Exception e){
            throw new BaijiRuntimeException(e);
        }
    }

    @Override
    public Result<List<OrderTimeScope>> queryTransportGroupTakeOrderTimeScope(QueryTransportGroupCondition condition) {
        List<List<Long>> skuIdListArr = Lists.partition(condition.getSkuIdList(), FUTURE_TASK_GROUP);
        List<Future<List<OrderTimeScope>>> queryFuture = Lists.newArrayListWithCapacity(skuIdListArr.size());
        for (List<Long> partIdList : skuIdListArr) {
            queryFuture.add(CThreadPool.pool(TransportThreadGroupConstant.orderTimeScopeFutureThreadPoolName).submit(() -> queryOrderTimeScope(partIdList, condition)));
        }
        List<OrderTimeScope> res = Lists.newLinkedList();
        for (Future<List<OrderTimeScope>> future : queryFuture) {
            try {
                List<OrderTimeScope> scopeList = future.get(3000, TimeUnit.MILLISECONDS);
                res.addAll(scopeList);
            } catch (Exception e) {
                logger.error("queryTransportGroupTakeOrderTimeScopeError","condition:{}", JsonUtil.toJson(condition), e);
                TransportMetric.queryTransportGroupTakeOrderTimeScopeErrorInc();
            }
        }
        return Result.Builder.<List<OrderTimeScope>>newResult().success().withData(res).build();
    }

    @Override
    public List<TransportGroupBasePO> queryDriverGroupRelationPO(List<Long> drvIdList, List<Long> groupIdList) {
        return doQueryDriverGroupRelationPO(drvIdList, groupIdList);
    }

    @Override
    public List<TransportGroupBasePO> queryDriverGroupRelationPOCache(List<Long> drvIdList, List<Long> groupIdList) {
        if (CollectionUtils.isEmpty(drvIdList)) {
            return queryDriverGroupRelationPO(drvIdList, groupIdList);
        }

        // 查询有效关系
        List<TspTransportGroupDriverRelationPO> groupRelationPOList = driverTransportGroupRelationGateway.queryDriverGroupRelation(drvIdList);
        if (CollectionUtils.isEmpty(groupRelationPOList)) {
            return Collections.emptyList();
        }
        // 查询有效关系且上线的运力组
        List<TspTransportGroupPO> groupPOList = transportgroupGateway.queryTspTransportBaseByIds(groupRelationPOList.stream().map(TspTransportGroupDriverRelationPO::getTransportGroupId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(groupPOList)) {
            return Collections.emptyList();
        }
        return getRes(groupPOList.stream().collect(Collectors.toMap(TspTransportGroupPO::getTransportGroupId, Function.identity(), (key1, key2) -> key2)), groupRelationPOList);
    }


    private List<TransportGroupBasePO> doQueryDriverGroupRelationPO(List<Long> drvIdList, List<Long> groupIdList) {
        if (CollectionUtils.isNotEmpty(drvIdList)) {
            QueryDriverGroupRelationConditionDTO conditionDTO = new QueryDriverGroupRelationConditionDTO();
            conditionDTO.setDrvIdList(drvIdList);
            conditionDTO.setTransportGroupIdList(groupIdList);
            // 查询有效关系
            List<TspTransportGroupDriverRelationPO> groupRelationPOList = tspTransportGroupDriverRelationRepository.queryDriverGroupRelation(conditionDTO);
            if (CollectionUtils.isEmpty(groupRelationPOList)) {
                return Collections.emptyList();
            }
            // 从有效关系中取出运力组id
            List<Long> relationTransportGroupIdList = groupRelationPOList.stream().map(TspTransportGroupDriverRelationPO::getTransportGroupId).collect(Collectors.toList());
            // 查询有效关系且上线的运力组
            List<TspTransportGroupPO> groupPOList = transportGroupRepository.queryTspTransportBaseByIds(relationTransportGroupIdList);
            if (CollectionUtils.isEmpty(groupPOList)) {
                return Collections.emptyList();
            }
            return getRes(groupPOList.stream().collect(Collectors.toMap(TspTransportGroupPO::getTransportGroupId, Function.identity(), (key1, key2) -> key2)), groupRelationPOList);
        } else {
            // 查询上线的运力组
            List<TspTransportGroupPO> groupPOList = transportGroupRepository.queryTspTransportBaseByIds(groupIdList);
            if (CollectionUtils.isEmpty(groupPOList)) {
                return Collections.emptyList();
            }
            Map<Long, TspTransportGroupPO> groupIdMap = groupPOList.stream().collect(Collectors.toMap(TspTransportGroupPO::getTransportGroupId, Function.identity(), (key1, key2) -> key2));
            QueryDriverGroupRelationConditionDTO conditionDTO = new QueryDriverGroupRelationConditionDTO();
            conditionDTO.setTransportGroupIdList(Lists.newArrayList(groupIdMap.keySet()));
            // 查询上线的运力组且有效的关系
            List<TspTransportGroupDriverRelationPO> groupRelationPOList = tspTransportGroupDriverRelationRepository.queryDriverGroupRelation(conditionDTO);
            if (CollectionUtils.isEmpty(groupRelationPOList)) {
                return Lists.newArrayList();
            }
            return getRes(groupIdMap, groupRelationPOList);
        }
    }

    private List<TransportGroupBasePO> getRes(Map<Long, TspTransportGroupPO> groupIdMap, List<TspTransportGroupDriverRelationPO> groupRelationPOList) {
        List<TransportGroupBasePO> res = Lists.newArrayListWithCapacity(groupRelationPOList.size());
        for (TspTransportGroupDriverRelationPO relationPO : groupRelationPOList) {
            TspTransportGroupPO transportGroupPO;
            if ((transportGroupPO = groupIdMap.get(relationPO.getTransportGroupId())) == null) {
                continue;
            }
            TransportGroupBasePO transportGroupBasePO = new TransportGroupBasePO();
            transportGroupBasePO.setTransportGroupId(transportGroupPO.getTransportGroupId());
            transportGroupBasePO.setTransportGroupName(transportGroupPO.getTransportGroupName());
            transportGroupBasePO.setTransportGroupMode(transportGroupPO.getTransportGroupMode());
            transportGroupBasePO.setDrvId(relationPO.getDrvId());
            transportGroupBasePO.setApplyStatus(relationPO.getApplyStatus());
            res.add(transportGroupBasePO);
        }
        return res;
    }

    @Override
    public Boolean grayTransportId(Long transportId) {
        String grayConfigValue = tmsTransportQconfig.getApplyTransportGrayConfig();
        if (StringUtils.isEmpty(grayConfigValue)) {
            return Boolean.FALSE;
        }
        //-1代表全量
        if (BaseUtil.getLongSet(grayConfigValue).contains(-1L) || BaseUtil.getLongSet(grayConfigValue).contains(transportId)) {
            return  Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<TransportGroupInOrderConfigDTO> queryInOrderConfig(List<Long> transportGroupIds, Date bookTime,Long cityId,String locationCode, Integer filterDispatchOnly) {
        try{
            //查询运力组的产线
            List<TspTransportGroupPO> groupPOList = transportGroupRepository.queryProductLine(transportGroupIds);
            if (CollectionUtils.isEmpty(groupPOList)) {
                return Collections.emptyList();
            }

            //如果是询价链路，过滤仅派单运力组
            groupPOList = filterDispatchOnlyTransport(filterDispatchOnly, groupPOList);

            if (CollectionUtils.isEmpty(groupPOList)) {
                return Collections.emptyList();
            }

            Map<Long,TspTransportGroupPO> transportGroupPOMap = new HashMap<>();
            for (TspTransportGroupPO tspTransportGroupPO : groupPOList) {
                transportGroupPOMap.put(tspTransportGroupPO.getTransportGroupId(),tspTransportGroupPO);
            }
            //查询运力组进单配置
            List<TspIntoOrderConfigPO> intoOrderConfigPOS = inOrderConfigRepository.queryInOrderConfigs(new ArrayList<>(transportGroupPOMap.keySet()),TmsTransportConstant.IntoOrderConfigActiveEnum.VALID.getCode(),cityId,locationCode);
            if(CollectionUtils.isEmpty(intoOrderConfigPOS)){
                return new ArrayList<>();
            }

            //组装返回数据
            List<TransportGroupInOrderConfigDTO> result = new ArrayList<>();
            for (TspIntoOrderConfigPO intoOrderConfigPO : intoOrderConfigPOS) {
                TransportGroupInOrderConfigDTO configDTO = new TransportGroupInOrderConfigDTO();
                //没有符合条件的进单配置 为无效数据
                List<TransportGroupTimeSegmentConfigDTO> configDTOList = getTimeSegmentConfig(intoOrderConfigPO.getConfig(),bookTime);
                if(CollectionUtils.isEmpty(configDTOList)){
                    continue;
                }
                configDTO.setTimeSegmentConfigDTOList(configDTOList);
                configDTO.setTransportGroupId(intoOrderConfigPO.getTransportGroupId());
                configDTO.setCityId(intoOrderConfigPO.getCityId());
                configDTO.setLocationCode(intoOrderConfigPO.getLocationCode());
                configDTO.setLocationType(intoOrderConfigPO.getLocationType());
                configDTO.setTotalOrderCount(queryTotalOrderCount(intoOrderConfigPO.getConfig()));
                TspTransportGroupPO tspTransportGroupPO = transportGroupPOMap.get(intoOrderConfigPO.getTransportGroupId());
                configDTO.setProductLine(tspTransportGroupPO.getCategorySynthesizeCode());
                configDTO.setSupplierId(tspTransportGroupPO.getSupplierId());
                configDTO.setTakeOrderLimitTime(tspTransportGroupPO.getTakeOrderLimitTime());
                result.add(configDTO);
            }
            return result;
        }catch (Exception e){
            Map<String,String> tag = new HashMap<>();
            tag.put("transportGroupId",transportGroupIds.get(0).toString());
            logger.error("TransportGroupQueryServiceImpl_queryInOrderConfig_ex",e,tag);
            throw new BizException("TransportGroupQueryServiceImpl_queryInOrderConfig_ex");
        }
    }

    @Override
    public Result<QueryTransportInformResponseType> queryTransportInform(Long transportGroupId) {
        TspTransportGroupPO groupPO = transportGroupRepository.queryTransportGroupDetail(transportGroupId);
        // 无效运力组数据
        if (groupPO == null) {
            return Result.Builder.<QueryTransportInformResponseType>newResult().fail().
                    withCode(ServiceResponseConstants.ResStatus.EXCEPTION_CODE).
                    withMsg(String.format("unable to find available transport group. transportGroupId:%s", transportGroupId)).build();
        }
        // 结果
        QueryTransportInformResponseType res = new QueryTransportInformResponseType();
        // fixme 调度语言 - 注意坑 枚举 pro fat 枚举值大小写不一样
        if (Strings.isNullOrEmpty(groupPO.getDispatcherLanguage())) {
            res.setSupportLanguageList(Lists.newArrayList(Constant.DEFAULT_LANGUAGE));
        } else {
            // 产品逻辑 - 一定要含英语
            Set<String> languageList = BaseUtil.getStrSet(groupPO.getDispatcherLanguage());
            languageList.add(Constant.DEFAULT_LANGUAGE);
            res.setSupportLanguageList(Lists.newArrayList(languageList));
        }
        // 预拆码list
        List<String> numbers = Lists.newArrayListWithExpectedSize(2);
        // 主备电话
        String mainPhone = groupPO.getDispatcherPhone();
        if (!Strings.isNullOrEmpty(mainPhone)) {
            String plaintextPhoneNumber = TmsTransUtil.decrypt(mainPhone, KeyType.Phone);
            if (Constant.CHINA_SPECIAL_NATION_IGT_CODE.contains(groupPO.getIgtCode())) {
                res.setMainPhoneCountryCode(groupPO.getIgtCode());
                res.setMainPhoneCityCode("");
                res.setMainPhoneBodyNumber(plaintextPhoneNumber);
            } else {
                mainPhone = new StringBuilder("00").append(groupPO.getIgtCode()).append(plaintextPhoneNumber).toString();
                numbers.add(mainPhone);
            }
        }
        // 备选电话
        String backupPhone = groupPO.getStandbyPhone();
        if (!Strings.isNullOrEmpty(backupPhone)) {
            String plaintextPhoneNumber = TmsTransUtil.decrypt(backupPhone, KeyType.Phone);
            if (Constant.CHINA_SPECIAL_NATION_IGT_CODE.contains(groupPO.getStandbyIgtCode())) {
                res.setBackupPhoneCountryCode(groupPO.getStandbyIgtCode());
                res.setBackupPhoneCityCode("");
                res.setBackupPhoneBodyNumber(plaintextPhoneNumber);
            } else {
                backupPhone = new StringBuilder("00").append(groupPO.getStandbyIgtCode()).append(plaintextPhoneNumber).toString();
                numbers.add(backupPhone);
            }
        }
        // 直接结束
        if (Strings.isNullOrEmpty(backupPhone) && Strings.isNullOrEmpty(mainPhone)) {
            return Result.Builder.<QueryTransportInformResponseType>newResult().success().withData(res).build();
        }
        // 拆码结果
        Map<String, NumberDTO> splitRes = phoneNumberSplitServiceProxy.batchSplitNumber(TransportGroupConverter.buildSplitNumberReq(numbers));
        // 填充 主备电话信息组
        NumberDTO mainPhoneSplitRes;
        if ((mainPhoneSplitRes = splitRes.get(mainPhone)) != null) {
            res.setMainPhoneCountryCode(BaseUtil.notEmptyProcessing(mainPhoneSplitRes.getCountryCode()));
            res.setMainPhoneCityCode(BaseUtil.notEmptyProcessing(mainPhoneSplitRes.getCityCode()));
            res.setMainPhoneBodyNumber(BaseUtil.notEmptyProcessing(mainPhoneSplitRes.getBodyNumber()));
        }
        // 填充 备用电话信息组
        NumberDTO backupPhoneSplitRes;
        if ((backupPhoneSplitRes = splitRes.get(backupPhone)) != null) {
            res.setBackupPhoneCountryCode(BaseUtil.notEmptyProcessing(backupPhoneSplitRes.getCountryCode()));
            res.setBackupPhoneCityCode(BaseUtil.notEmptyProcessing(backupPhoneSplitRes.getCityCode()));
            res.setBackupPhoneBodyNumber(BaseUtil.notEmptyProcessing(backupPhoneSplitRes.getBodyNumber()));
        }
        return Result.Builder.<QueryTransportInformResponseType>newResult().success().withData(res).build();
    }

    @Override
    public List<Long> queryDrvIdByTransportGroups(List<Long> transportGroupIdList,Integer temporaryDispatchMark) {

        if (CollectionUtils.isEmpty(transportGroupIdList)) {
            return Collections.emptyList();
        }

        List<Long> drvIdList = tspTransportGroupDriverRelationRepository.queryDrvIdListByTransportGroups(transportGroupIdList);

        if (CollectionUtils.isEmpty(drvIdList)) {
            return Collections.emptyList();
        }
        List<Long> resultList = Lists.newArrayList(Sets.newHashSet(drvIdList));
        //新增司机临派标识过滤
        if(temporaryDispatchMark == null){
            return resultList;
        }

        if (tmsTransportQconfig.queryDriverPerformance()) {
            return driverGateway.queryDrvIdByMarkPage(resultList,temporaryDispatchMark);
        }

        return getResultData(resultList,temporaryDispatchMark);

    }

    @SneakyThrows
    public List<Long> getResultData(List<Long> resultList,Integer temporaryDispatchMark) {

        if (!tmsTransportQconfig.getInQueryBatchSwitch()) {
            //按公司要求，查询数据，限制在200以内，当前分页为150，查询出司机ID
            List<List<Long>> drvIdListPage = Lists.partition(resultList, Constant.SQL_REQUEST_ID_LIMIT_ROW_COUNT);
            List<Long> resultData = Lists.newArrayList();
            for(List<Long> drvids : drvIdListPage){
                List<Long> resultDrvIdList = drvDrvierRepository.queryDrvIdByMarkPage(drvids,temporaryDispatchMark);
                if(CollectionUtils.isEmpty(resultDrvIdList)){
                    continue;
                }
                resultData.addAll(resultDrvIdList);
            }
            return resultData;
        }

        //按公司要求，查询数据，限制在200以内，当前分页为200，查询出司机ID
        List<List<Long>> drvIdListPage = Lists.partition(resultList, tmsTransportQconfig.getInQueryBatchSize());

        List<CompletableFuture<List<Long>>> futures =
          drvIdListPage.stream().map(drvIds -> CompletableFuture.supplyAsync(() -> drvDrvierRepository.queryDrvIdByMarkPage(drvIds, temporaryDispatchMark), threadPoolService.getInQueryDriverPool())).collect(
            Collectors.toList());
        // 获取结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));

        return allFutures.thenApply(v ->
          futures.stream()
            .map(CompletableFuture::join)
            .flatMap(List::stream)
            .collect(Collectors.toList())
        ).get();
    }

    @Override
    public Result<Boolean> inOrderUpperLimit(List<InOrderConfigSOAType> inOrderConfigs,Long cityId){

        Result<Boolean> resultData = Result.Builder.<Boolean>newResult().success().withData(Boolean.FALSE).build();
        //只判断供应商角色
        if(CollectionUtils.isEmpty(inOrderConfigs)){
            return resultData;
        }

        //境内运力组无需判断
        if(enumRepository.getAreaScope(cityId) == 0){
            return resultData;
        }

        Integer inOrderUpperLimit =  tmsTransportQconfig.getInOrderUpperLimit();
        for(InOrderConfigSOAType soaType : inOrderConfigs){
            if(CollectionUtils.isEmpty(soaType.getConfigItems())){
                continue;
            }
            for(ConfigItemSOAType itemSOAType : soaType.getConfigItems()){
                if(Objects.isNull(itemSOAType)){
                    continue;
                }
                //进单配置上限值如果小于配置的值，则进单配置设置不成功
                if(itemSOAType.getOrderCount()!=null && itemSOAType.getOrderCount() < inOrderUpperLimit){
                    return Result.Builder.<Boolean>newResult().success().withMsg(SharkUtils.getSharkValueDefault(SharkKeyConstant.enterNumerLimit)).withData(Boolean.TRUE).build();
                }
            }
        }
        return resultData;
    }

    /**
     * 获取已绑定该运力组的车辆
     *
     * @param transportGroupId
     */
    @Override
    public List<Long> queryRelationDrvIdListByTransportGroupId(Long transportGroupId) {
        return tspTransportGroupDriverRelationRepository.queryRelationDrvIdListByTransportGroupId(transportGroupId);
    }

    /**
     * 司机对应的运力组ID
     *
     * @param drvIds
     * @return
     */
    @Override
    public List<TspTransportGroupDriverRelationPO> queryTransportGroupIdByDrvIds(List<Long> drvIds) {
        if(CollectionUtils.isEmpty(drvIds)) {
            return Lists.newArrayList();
        }
        return tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(drvIds);
    }

    /**
     * 封装运力组进单配置时间段配置
     * @param config
     * @return
     */
    private List<TransportGroupTimeSegmentConfigDTO> getTimeSegmentConfig(String config,Date bookTime){
        List<TransportGroupTimeSegmentConfigDTO> timeSegmentConfigDTOList = JsonUtil.fromJson(config, new TypeReference<List<TransportGroupTimeSegmentConfigDTO>>() {});
        List<TransportGroupTimeSegmentConfigDTO> result = new ArrayList<>();
        for (TransportGroupTimeSegmentConfigDTO configDTO : timeSegmentConfigDTOList) {
            if(!containBookTime(bookTime,configDTO)){
                continue;
            }
            result.add(configDTO);
        }
        return result;
    }

    /**
     * 运力组进单配置时段是否包含用车时间
     * @param bookTime
     * @param configDTO
     * @return
     */
    protected boolean containBookTime(Date bookTime,TransportGroupTimeSegmentConfigDTO configDTO){
        if (bookTime == null) {
            return true;
        }
        String[] timeArray = configDTO.getTime().split("-");
        String dayStr = DateUtil.dateToString(bookTime,DateUtil.YYYYMMDD);
        String startDateStr = StringUtil.concat(dayStr," ",timeArray[0],":00");
        //补偿增加59秒
        String endDateStr = StringUtil.concat(dayStr," ",timeArray[1],":59");
        Date startDate = DateUtil.getDate(startDateStr,DateUtil.YYYYMMDDHHMMSS);
        Date endDate = DateUtil.getDate(endDateStr,DateUtil.YYYYMMDDHHMMSS);
        if(startDate == null || endDate == null){
            return false;
        }
        if(startDate.equals(bookTime) || endDate.equals(bookTime)){
            return true;
        }
        if(bookTime.after(startDate) && bookTime.before(endDate)){
            return true;
        }
        return false;
    }
    public List<OrderTimeScope> queryOrderTimeScope(List<Long> partIdList, QueryTransportGroupCondition condition) {
        Map<Long, OrderTimeScope> resMap = initOrderTimeScope(partIdList);
        if (tmsTransportQconfig.getTakeTimeScopeSwitch()) {
            return Lists.newArrayList(resMap.values());
        }
        List<TspTransportGroupSkuAreaRelationPO> relationPOS = arearRelationRepository.queryTransportGroupSkuIds(TransportGroupConverter.toQueryModel(partIdList));
        if (CollectionUtils.isEmpty(relationPOS)) { //case 1: 无sku关联运力组
            return Lists.newArrayList(resMap.values());
        }
        List<Long> transportGroupIdList = Lists.newArrayListWithCapacity(relationPOS.size());
        for (TspTransportGroupSkuAreaRelationPO relationPO : relationPOS) {
            transportGroupIdList.add(relationPO.getTransportGroupId());
        }
        List<TspTransportGroupPO> groupPOS = transportGroupRepository.queryTspTransportBaseByIds(transportGroupIdList);

        groupPOS = filterDispatchOnlyTransport(condition.getFilterDispatchOnly(), groupPOS);

        if (CollectionUtils.isEmpty(groupPOS)) { //case 2: 无上线运力组
            return Lists.newArrayList(resMap.values());
        }
        // skuId - transportIdSet
        Map<Long, Set<Long>> skuTransportGroupMap = buildSkuTransportGroupMap(relationPOS);
        // transportGroupId - takeOrderTime
        Map<Long, Integer> transportGroupIdTimeMap = Maps.newHashMapWithExpectedSize(groupPOS.size());
        // transportGroupId - areaGroupId
        Map<Long, Long> transportGroupIdAreaGroupIdMap = Maps.newHashMapWithExpectedSize(groupPOS.size());
        for (TspTransportGroupPO groupPO : groupPOS) {
            transportGroupIdTimeMap.put(groupPO.getTransportGroupId(), groupPO.getTakeOrderLimitTime());
            transportGroupIdAreaGroupIdMap.put(groupPO.getTransportGroupId(), groupPO.getAreaGroupId());
        }
        if (condition.isHasPointData()) {
            // areaGroupId - valid
//            Map<Long, Boolean> areaGroupIdMap = queryAreaGroupMap(condition, Lists.newArrayList(transportGroupIdAreaGroupIdMap.values()));
            Map<Long, Boolean> areaGroupIdMap = queryAreaGroupMapNew(condition,Lists.newArrayList(transportGroupIdAreaGroupIdMap.values()));
            // 交集聚合 areaGroupIdMap & transportGroupIdAreaGroupIdMap
            for (Iterator<Long> iterator = transportGroupIdAreaGroupIdMap.keySet().iterator(); iterator.hasNext();) {
                if (!areaGroupIdMap.getOrDefault(transportGroupIdAreaGroupIdMap.get(iterator.next()), Boolean.FALSE)) {
                    iterator.remove();
                }
            }
            if (MapUtils.isEmpty(transportGroupIdAreaGroupIdMap)) {
                return Lists.newArrayList(resMap.values());
            }
        }
        // 交集聚合 skuTransportGroupMap & transportGroupIdAreaGroupIdMap
        for (Iterator<Long> iterator = skuTransportGroupMap.keySet().iterator(); iterator.hasNext();) {
            Set<Long> transportIdSet = skuTransportGroupMap.getOrDefault(iterator.next(), Sets.newHashSet());
            if (CollectionUtils.isEmpty(transportIdSet)) {
                continue;
            }
            for (Iterator<Long> transportIdSetIterator = transportIdSet.iterator(); transportIdSetIterator.hasNext();) {
                if (transportGroupIdAreaGroupIdMap.containsKey(transportIdSetIterator.next())) {
                    continue;
                }
                transportIdSetIterator.remove();
            }
        }
        // 装配
        for (Map.Entry<Long, OrderTimeScope> entry : resMap.entrySet()) {
            OrderTimeScope scope = entry.getValue();
            Set<Long> transportIdSet = skuTransportGroupMap.get(entry.getKey());
            if (CollectionUtils.isEmpty(transportIdSet)) {
                continue;
            }
            List<Integer> scopeList = Lists.newArrayListWithCapacity(transportIdSet.size());
            for (Long transportId : transportIdSet) {
                Integer takeOrderTime = transportGroupIdTimeMap.get(transportId);
                if (takeOrderTime == null || takeOrderTime <= 0) {
                    continue;
                }
                scopeList.add(takeOrderTime);
            }
            if (CollectionUtils.isEmpty(scopeList)) {
                continue;
            }
            Collections.sort(scopeList);
            if(Objects.isNull(scope)){
                continue;
            }
            scope.setIsHasData(Boolean.TRUE);
            scope.setMinOrderTakeTime(scopeList.get(0));
            scope.setMaxOrderTakeTime(scopeList.get(scopeList.size() - 1));
        }
        return Lists.newArrayList(resMap.values());
    }

    protected List<TspTransportGroupPO> filterDispatchOnlyTransport(Integer filterDispatchOnlyTransportGroup,
      List<TspTransportGroupPO> groupPOS) {
        //根据dispatchOnly过滤是否仅派单不进单
        if (Objects.equals(TransportDispatchEnum.DISPATCH_ONLY.getCode(), filterDispatchOnlyTransportGroup)) {
            groupPOS = groupPOS.stream()
              .filter(groupPO -> !Objects.equals(TransportDispatchEnum.DISPATCH_ONLY.getCode(), groupPO.getDispatchOnly()))
              .collect(Collectors.toList());
        }
        return groupPOS;
    }

    private Map<Long, Boolean> queryAreaGroupMap(QueryTransportGroupCondition condition, List<Long> areaGroupIdList) {
        Map<Long, Boolean> resMap = Maps.newHashMapWithExpectedSize(areaGroupIdList.size());
        QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
        queryPointAreaInfoRequestType.setAreaGroupIds(areaGroupIdList);
        queryPointAreaInfoRequestType.setPointLat(condition.getLatitude());
        queryPointAreaInfoRequestType.setPointLng(condition.getLongitude());
        queryPointAreaInfoRequestType.setCoordinateType(condition.getPoiType());
        queryPointAreaInfoRequestType.setCarplaceId(condition.getCarPlaceId());
        queryPointAreaInfoRequestType.setPoiRef(condition.getPoiRef());
        try {
            QueryPointAreaInfoResponseType responseType = skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType);
            if (responseType == null || MapUtils.isEmpty(responseType.getAreaGroupInfo())) {
                logger.warn("queryAreaGroupMapNoData","condition:{} areaGroupIdList:{} responseType:{}", JsonUtil.toJson(condition), JsonUtil.toJson(areaGroupIdList), JsonUtil.toJson(responseType));
                return resMap;
            }
            String FLAG = "true";
            for (Map.Entry<String, String> entry : responseType.getAreaGroupInfo().entrySet()) {
                if (FLAG.equalsIgnoreCase(entry.getValue())) {
                    resMap.put(Long.valueOf(entry.getKey()), Boolean.TRUE);
                }
            }
            return resMap;
        } catch (Exception e) {
            logger.error("queryAreaGroupMapError","condition:{} areaGroupIdList:{}", JsonUtil.toJson(condition), JsonUtil.toJson(areaGroupIdList), e);
            TransportMetric.queryPointAreaInfoErrorInc();
            return resMap;
        }
    }

    private Map<Long, Set<Long>> buildSkuTransportGroupMap(List<TspTransportGroupSkuAreaRelationPO> relationPOS) {
        Map<Long, Set<Long>> resMap = Maps.newHashMap();
        for (TspTransportGroupSkuAreaRelationPO relationPO : relationPOS) {
            Set<Long> transportGroupIdSet = resMap.getOrDefault(relationPO.getSkuId(), Sets.newHashSet());
            transportGroupIdSet.add(relationPO.getTransportGroupId());
            resMap.put(relationPO.getSkuId(), transportGroupIdSet);
        }
        return resMap;
    }

    private Map<Long, OrderTimeScope> initOrderTimeScope(List<Long> skuIdList) {
        Map<Long, OrderTimeScope> resMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(skuIdList)) {
            return resMap;
        }
        for (Long skuId : skuIdList) {
            OrderTimeScope scope = new OrderTimeScope();
            scope.setIsHasData(Boolean.FALSE);
            scope.setMaxOrderTakeTime(0);
            scope.setMinOrderTakeTime(0);
            scope.setSkuId(skuId);
            resMap.put(skuId, scope);
        }
        return resMap;
    }

    public List<QueryTransportGroupsForDspInfo> resultGroupForDsp(Map<Long,List<Long>> skuAreaRelationPOSMap,Map<Long,TspTransportGroupPO> transportGroupPOMap,Map<Long, List<TspIntoOrderConfigPO>> collect){
        List<QueryTransportGroupsForDspInfo> resultInfoList = Lists.newArrayList();
        for(Map.Entry<Long,List<Long>> entry : skuAreaRelationPOSMap.entrySet()){
            Long skuId = entry.getKey();
            List<Long> transportIds = entry.getValue();
            QueryTransportGroupsForDspInfo resultInfo = new QueryTransportGroupsForDspInfo();
            List<TransportGroupDetailSOAType> baseInfoSOATypes = Lists.newArrayList();
            Set<Long> killRepeat = Sets.newHashSet();
            for(Long transPortId : transportIds){
                if(MapUtils.isEmpty(transportGroupPOMap) || transportGroupPOMap.get(transPortId) == null){
                    continue;
                }
                //去重
                if(!killRepeat.add(transPortId)){
                    continue;
                }
                TspTransportGroupPO groupPO = transportGroupPOMap.get(transPortId);
                TransportGroupDetailSOAType soaType = new TransportGroupDetailSOAType();
                soaType.setTransportGroupId(groupPO.getTransportGroupId());
                soaType.setSupplierId(groupPO.getSupplierId());
                soaType.setSupplierName(enumRepository.getSupplierName(groupPO.getSupplierId()));
                soaType.setTransportGroupName(groupPO.getTransportGroupName());
                soaType.setTransportGroupMode(groupPO.getTransportGroupMode());
                soaType.setDispatcher(groupPO.getDispatcher());
                soaType.setProductLine(groupPO.getCategorySynthesizeCode());
//                soaType.setDispatcherPhone(TmsTransUtil.decrypt(groupPO.getDispatcherPhone(), KeyType.Phone));
                soaType.setDispatcherEmail(groupPO.getInformEmail());
                soaType.setTakeOrderLimitTime(groupPO.getTakeOrderLimitTime());
                List<InOrderConfigSOAType> list = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(collect.get(groupPO.getTransportGroupId()))) {
                    collect.get(groupPO.getTransportGroupId()).stream().sorted(Comparator.comparing(TspIntoOrderConfigPO::getId)).forEach(intoOrderConfigPO -> {
                        InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
                        inOrderConfigSOAType.setId(intoOrderConfigPO.getId());
                        inOrderConfigSOAType.setCountryId(intoOrderConfigPO.getCountryId());
                        inOrderConfigSOAType.setCountryName(intoOrderConfigPO.getCountryName());
                        inOrderConfigSOAType.setCityId(intoOrderConfigPO.getCityId());
                        inOrderConfigSOAType.setLocationCode(intoOrderConfigPO.getLocationCode());
                        inOrderConfigSOAType.setTransportGroupId(intoOrderConfigPO.getTransportGroupId());
                        inOrderConfigSOAType.setLocationType(intoOrderConfigPO.getLocationType());
                        inOrderConfigSOAType.setActive(intoOrderConfigPO.getActive());
                        inOrderConfigSOAType.setCreateUser(intoOrderConfigPO.getCreateUser());
                        inOrderConfigSOAType.setModifyUser(intoOrderConfigPO.getModifyUser());
                        inOrderConfigSOAType.setDatachangeCreatetime(DateUtil.timestampToString(intoOrderConfigPO.getDatachangeCreatetime(),DateUtil.YYYYMMDDHHMMSS));
                        inOrderConfigSOAType.setDatachangeLasttime(DateUtil.timestampToString(intoOrderConfigPO.getDatachangeLasttime(),DateUtil.YYYYMMDDHHMMSS));
                        if (!Strings.isNullOrEmpty(intoOrderConfigPO.getConfig())) {
                            inOrderConfigSOAType.setConfigItems(JsonUtil.fromJson(intoOrderConfigPO.getConfig(),new TypeReference<List<ConfigItemSOAType>>(){}));
                        }
                        list.add(inOrderConfigSOAType);
                    });
                }
                soaType.setInOrderConfigs(CollectionUtils.isEmpty(list)?"":JsonUtil.toJson(list));
                soaType.setBizAreaType(enumRepository.getAreaScope(groupPO.getPointCityId()));
                baseInfoSOATypes.add(soaType);
            }
            if(CollectionUtils.isEmpty(baseInfoSOATypes)){
                continue;
            }
            resultInfo.setSkuId(skuId);
            resultInfo.setTransportGroupList(baseInfoSOATypes);
            resultInfoList.add(resultInfo);

        }
        return resultInfoList.stream().sorted(Comparator.comparing(QueryTransportGroupsForDspInfo::getSkuId)).collect(Collectors.toList());
    }

    public List<QueryApplyTransGroupsSkuForDspInfo> resultGroupForDsp(List<TspTransportGroupPO> tspTransportGroupPOS,List<TspTransportGroupSkuAreaRelationPO> skuRelationPOS){
        List<QueryApplyTransGroupsSkuForDspInfo> resultInfoList = Lists.newArrayList();
        Map<Long,List<Long>> skuRelationPOSMap = Maps.newHashMap();
        if(CollectionUtils.isNotEmpty(skuRelationPOS)){
            skuRelationPOSMap = skuRelationPOS.stream().collect(Collectors.groupingBy(TspTransportGroupSkuAreaRelationPO::getTransportGroupId,
                    Collectors.mapping(TspTransportGroupSkuAreaRelationPO::getSkuId,Collectors.toList())));
        }
        for(TspTransportGroupPO tspTransportGroupPO : tspTransportGroupPOS){
            QueryApplyTransGroupsSkuForDspInfo skuForDspInfo = new QueryApplyTransGroupsSkuForDspInfo();
            skuForDspInfo.setCityId(tspTransportGroupPO.getPointCityId());
            skuForDspInfo.setTransportGroupId(tspTransportGroupPO.getTransportGroupId());
            skuForDspInfo.setCarTypeId(tspTransportGroupPO.getVehicleTypeId());
            skuForDspInfo.setShortTransportGroup(tspTransportGroupPO.getShortTransportGroup());
            List<Long> skuIds = skuRelationPOSMap.get(tspTransportGroupPO.getTransportGroupId());
            skuForDspInfo.setSkuIds(CollectionUtils.isEmpty(skuIds)?Collections.EMPTY_LIST:skuIds);
            resultInfoList.add(skuForDspInfo);

        }
        return resultInfoList;
    }

    public Map<Long,Boolean> queryAreaGroupMapNew(QueryTransportGroupCondition condition,List<Long> transportGroupIdAreaGroupIds){
        if (tmsTransportQconfig.getScopeQueryAreaGroupMapSwitch()) {
            Map<Long, Boolean> resMap = queryAreaGroupMap(condition, transportGroupIdAreaGroupIds);
            CThreadPool.pool(TransportThreadGroupConstant.minorFutureThreadPoolName).execute(() -> compareResult(condition, resMap, getAreaGroupIdMap(condition,transportGroupIdAreaGroupIds)));
            return resMap;
        }
        return getAreaGroupIdMap(condition,transportGroupIdAreaGroupIds);
    }

    public Boolean compareResult(QueryTransportGroupCondition condition,Map<Long,Boolean> orgResultMap,Map<Long,Boolean> newResultMap){
        for(Map.Entry<Long, Boolean> entry : orgResultMap.entrySet()){
            if(!Objects.equals(entry.getValue(),newResultMap.get(entry.getKey()))){
                logger.warn("queryAreaGroupMapNewCompareResult","condition:{},orgResultMap:{},newResultMap:{}",JsonUtil.toJson(condition),JsonUtil.toJson(orgResultMap),JsonUtil.toJson(newResultMap));
                break;
            }
        }
        return Boolean.TRUE;

    }

    public Map<Long, Boolean> getAreaGroupIdMap(QueryTransportGroupCondition condition,List<Long> transportGroupIdAreaGroupIds){
        //兼容原逻辑，如果新入参不传，则走原逻辑，查询接口点位信息
        if(MapUtils.isEmpty(condition.getParamAreaMap())){
           return queryAreaGroupMap(condition, transportGroupIdAreaGroupIds);
        }

        //将入参map中的key转为long
        Map<Long,Boolean> resultMap = Maps.newHashMap();
        for(Map.Entry<String, Boolean> entry : condition.getParamAreaMap().entrySet()){
            String key = entry.getKey();
            Boolean value = entry.getValue();
            if(StringUtils.isNotEmpty(key)){
                resultMap.put(Long.valueOf(key),value);
            }
        }

        //筛选出运力组点位信息不在入参中的信息
        List<Long> transportAreaIds = Lists.newArrayList();
        for(Long areaGroupId : transportGroupIdAreaGroupIds){
            if(!resultMap.containsKey(areaGroupId)){
                transportAreaIds.add(areaGroupId);
            }
        }
        if(CollectionUtils.isEmpty(transportAreaIds)){
            return resultMap;
        }
        //查询不在入参中的点位信息，再次调取接口，将返回值和入参合并
        Map<Long,Boolean> queryAreaMap = queryAreaGroupMap(condition, transportAreaIds);
        queryAreaMap.putAll(resultMap);
        return queryAreaMap;
    }

    /**
     * 计算所有时段的全天库存
     * @param config
     * @return
     */
    public Integer queryTotalOrderCount(String config){
        try{
            List<TransportGroupTimeSegmentConfigDTO> timeSegmentConfigDTOList = JsonUtil.fromJson(config, new TypeReference<List<TransportGroupTimeSegmentConfigDTO>>() {});
            Integer totalOrderCount = 0;
            for (TransportGroupTimeSegmentConfigDTO configDTO : timeSegmentConfigDTOList) {
                Integer orderCount = configDTO.getOrderCount()==null?0:configDTO.getOrderCount();
                totalOrderCount = totalOrderCount + orderCount;
            }
            return totalOrderCount;
        }catch (Exception e){
            logger.warn("queryTotalOrderCount_ex",config,e,new HashMap<>());
            return 0;
        }
    }
}
