package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.dto.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.util.*;

@Component
public class PermissionServiceImpl implements IPermissionService {
    @Autowired
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Autowired
    private DrvRecruitingRepository drvRecruitingRepository;

    public List<Long> queryDriverRecruitingSupplierId(QueryDriverRecruitingSupplierIdParamDTO paramDTO){
        if(StringUtils.isEmpty(paramDTO.getDriverPhone()) && StringUtils.isEmpty(paramDTO.getDriverIdCard()) && StringUtils.isEmpty(paramDTO.getVehicleLicense()) && CollectionUtils.isEmpty(paramDTO.getDrvRecruitingIds())){
            return new ArrayList<>();
        }
        List<DrvRecruitingPO> drvRecruitingPOS = drvRecruitingRepository.queryDriverRecruitingInfo(paramDTO.getDriverPhone(),paramDTO.getDriverIdCard(),paramDTO.getVehicleLicense(),paramDTO.getDrvRecruitingIds());
        if(CollectionUtils.isEmpty(drvRecruitingPOS)){
            return new ArrayList<>();
        }
        Set<Long> supplierIds = new HashSet<>();
        for (DrvRecruitingPO drvRecruitingPO : drvRecruitingPOS) {
            supplierIds.add(drvRecruitingPO.getSupplierId());
        }
        return new ArrayList<>(supplierIds);
    }
}
