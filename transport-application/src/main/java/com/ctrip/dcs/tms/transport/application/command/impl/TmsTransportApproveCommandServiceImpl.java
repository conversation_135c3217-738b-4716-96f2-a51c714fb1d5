package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.*;
import com.ctrip.igt.framework.common.result.*;
import com.ctrip.platform.dal.dao.annotation.*;
import com.ctriposs.baiji.exception.*;
import com.fasterxml.jackson.core.type.*;
import com.google.common.base.*;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;

import java.beans.*;
import java.lang.reflect.*;
import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.Objects;
import java.util.*;
import java.util.stream.*;

@Service
public class TmsTransportApproveCommandServiceImpl<T> implements TmsTransportApproveCommandService<T> {

    private static final Logger logger = LoggerFactory.getLogger(TmsTransportApproveCommandServiceImpl.class);

    @Autowired
    private TmsTransportApproveRepository approveRepository;
    @Autowired
    EnumRepository enumRepository;

    @Autowired
    private VehicleRepository vehicleRepository;

    @Autowired
    private DrvDrvierRepository drvDrvierRepository;

    @Autowired
    private TmsCertificateCheckRepository checkRepository;
    @Autowired
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Autowired
    private DriverSafetyCommandService driverSafetyCommandService;

    @Autowired
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;

    @Autowired
    private CertificateCheckCommandService certificateCheckCommandService;

    @Autowired
    TmsTransportQconfig qconfig;

    @Autowired
    NetCardCheckUtil netCardCheckUtil;
    @Autowired
    private CommonConfig config;
    @Autowired
    private OverageQConfig overageQConfig;

    @Autowired
    private InternationalEntryService internationalEntryService;

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Boolean> updateApproveStatus(TransportApproveStatusUpdateSOARequestType requestType) {
        try {
            if (CollectionUtils.isEmpty(requestType.getIds()) || requestType.getApproveStatus() == null) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing)).build();
            }
            if (MapUtils.isEmpty(SessionHolder.getSessionSource())) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportUnLogin)).build();
            }

            if (requestType.getIds().size() > 1 && Objects.nonNull(requestType.getIsCompliant())) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportCompliantStatusExist)).build();
            }

            //查询id对应的审批信息
            List<TmsTransportApprovePO> approvePOList = approveRepository.queryApproveListByIds(requestType.getIds());
            // todo修改shark
            if (CollectionUtils.isEmpty(approvePOList)) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportMsgRequiredParameterMissing)).build();
            }
            //TODO 不能操作自己发起的审批
            List<Long> oneselfIds = Lists.newArrayList();
            List<Long> drvIds = Lists.newArrayList();
            List<Long> vehicleIds = Lists.newArrayList();
            List<Long> approveStatusError = Lists.newArrayList();
            List<Integer> checkStatus = Lists.newArrayList();
            String updateCertificateChecInfo = "";
            Map<Long, String> modifyUserMap = Maps.newHashMap();
            for (TmsTransportApprovePO approvePO : approvePOList) {
                modifyUserMap.put(approvePO.getId(), approvePO.getModifyUser());
                //检查审批状态是否正确
                if (!Objects.equals(approvePO.getApproveStatus(), TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode())) {
                    approveStatusError.add(approvePO.getId());
                }
                //检查是否有自己发起的审批
                if (Objects.equals(SessionHolder.getSessionSource().get("accountId"), approvePO.getAccountId())) {
                    oneselfIds.add(approvePO.getId());
                }
                if (Objects.equals(TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(), approvePO.getApproveSourceType())) {
                    drvIds.add(approvePO.getApproveSourceId());
                }
                if (Objects.equals(TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode(), approvePO.getApproveSourceType())) {
                    vehicleIds.add(approvePO.getApproveSourceId());
                }

                if (approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate.getCode().intValue()) {

                    if (CollectionUtils.isEmpty(requestType.getCheckStatusList()) && StringUtils.isNotEmpty(approvePO.getCertificateCheckResult())) {
                        List<TmsCertificateCheckPO> checkResultList = JsonUtil.fromJson(approvePO.getCertificateCheckResult(), new TypeReference<List<TmsCertificateCheckPO>>() {
                        });
                        if (CollectionUtils.isNotEmpty(checkResultList)) {
                            for (TmsCertificateCheckPO checkPO : checkResultList) {
                                if (Objects.equals(checkPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.REVIEW.getCode()) ||
                                        Objects.equals(checkPO.getCheckStatus(), TmsTransportConstant.CheckStatusEnum.CHECKING.getCode())) {
                                    checkStatus.add(checkPO.getCheckStatus());
                                }
                            }
                        }
                    }

                    if (requestType.getIds().size() == 1 && CollectionUtils.isNotEmpty(requestType.getCheckStatusList())) {
                        List<TmsCertificateCheckPO> checkResultList = JsonUtil.fromJson(approvePO.getCertificateCheckResult(), new TypeReference<List<TmsCertificateCheckPO>>() {
                        });
                        for (TmsCertificateCheckPO checkPO : checkResultList) {
                            for (UpdateApproveCertificateStatusSOADTO soadto : requestType.getCheckStatusList()) {
                                if (Objects.equals(checkPO.getCertificateType(), soadto.getCertificateType())) {
                                    checkPO.setCheckStatus(soadto.getCheckStatus());
                                    break;
                                }
                            }
                        }
                        updateCertificateChecInfo = JsonUtil.toJson(checkResultList);
                        approvePO.setCertificateCheckResult(updateCertificateChecInfo);
                    }
                }


                if (approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.OVERSEASVEHMOD.getCode().intValue()) {
                }

            }

            if (CollectionUtils.isNotEmpty(checkStatus)) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportCertificateStatusExist)).build();
            }
            //返回前端不合法的审批ID
            if (CollectionUtils.isNotEmpty(oneselfIds)) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(JsonUtil.toJson(oneselfIds) + "," + SharkUtils.getSharkValue(SharkKeyConstant.transportNoOperationOneselfApprove)).build();
            }
            //当前审批信息包含错误的审批状态,(只可操作待审批信息)
            if (CollectionUtils.isNotEmpty(approveStatusError)) {
                return Result.Builder.<Boolean>newResult().fail().withMsg(JsonUtil.toJson(approveStatusError) + "," + SharkUtils.getSharkValue(SharkKeyConstant.transporApproveStatusFail)).build();
            }

           // todo修改shark
//            if (CollectionUtils.isNotEmpty(vehicleIds) && vehicleIds.size() == 1 && Objects.nonNull(requestType.getIsCompliant())) {
//                VehVehiclePO vehVehiclePO = vehicleRepository.queryByPk(vehicleIds.get(0));
//                Integer auditStatus = vehVehiclePO.getAuditStatus();
//                if (!Objects.equals(VehicleAuditStatusEnum.getEnumByCode(auditStatus), VehicleAuditStatusEnum.UNDISPOSED)) {
//                    return Result.Builder.<Boolean>newResult().fail().withMsg(SharkUtils.getSharkValue(SharkKeyConstant.transportCompliantStatusExist)).build();
//                }
//            }

            int count = approveRepository.updateApproveStatus(UpdateApproveStatusDO.buildDO(requestType, updateCertificateChecInfo));
            if (count > 0) {
                for (int i = approvePOList.size() - 1; i >= 0; i--) {
                    TmsTransportApprovePO approvePO = approvePOList.get(i);
                    if (approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.nucleicAcidTest.getCode().intValue() ||
                            approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.vaccine.getCode().intValue()) {
                        if (requestType.getApproveStatus().intValue() == TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode()) {
                            syncDriverPreventionControlInfo(approvePO.getApproveSourceId(), approvePO.getEventType(), requestType.getRemarks(), true);
                        } else if (requestType.getApproveStatus().intValue() == TmsTransportConstant.TransportApproveStatusEnum.APPROVAL_REJECTED.getCode()) {
                            syncDriverPreventionControlInfo(approvePO.getApproveSourceId(), approvePO.getEventType(), requestType.getRemarks(), false);
                        }
                        approvePOList.remove(i);
                    }
                    if (approvePO.getEventType().intValue() == TmsTransportConstant.EnentTypeEnum.headPortraitCompliance.getCode().intValue()) {
                        doDrvHeadApprove(approvePO, requestType);
                        approvePOList.remove(i);
                    }
                }

                //审批通过，将审核内容同步到司机、车辆表中
                if (CollectionUtils.isNotEmpty(approvePOList) && Objects.equals(TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode(), requestType.getApproveStatus())) {
                    this.optationApprovad(approvePOList, modifyUserMap, requestType.getIsCompliant(), requestType.getNonComplianceReason());
                }
                //境外车辆编辑事件-审批不通过，则打不通过标签
                if (CollectionUtils.isNotEmpty(approvePOList) && Objects.equals(TmsTransportConstant.TransportApproveStatusEnum.APPROVAL_REJECTED.getCode(), requestType.getApproveStatus())) {
                    insertOverseasTags(approvePOList, TmsTransportConstant.CheckStatusEnum.ERROR);
                }
                //同步核验信息表
                if (CollectionUtils.isNotEmpty(approvePOList) && Objects.equals(requestType.getApproveStatus(), TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode())) {
                    this.syncCertificateCheckDB(approvePOList);
                }

                return Result.Builder.<Boolean>newResult().success().build();
            }
            return Result.Builder.<Boolean>newResult().fail().build();
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw (BizException)e;
            }
            throw new BaijiRuntimeException(e);
        }
    }

    public void doDrvHeadApprove(TmsTransportApprovePO approvePO, TransportApproveStatusUpdateSOARequestType requestType) {
        DrvDriverPO driverPO = drvDrvierRepository.queryByPk(approvePO.getApproveSourceId());
        if (driverPO == null) {
            return;
        }
        List<ApproveContentSOADTO> contentSOADTOList = JsonUtil.fromJson(approvePO.getApproveContent(), new TypeReference<List<ApproveContentSOADTO>>() {
        });
        if (CollectionUtils.isEmpty(contentSOADTOList)) {
            return;
        }
        if (requestType.getApproveStatus().intValue() != TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode() && requestType.getApproveStatus().intValue() != TmsTransportConstant.TransportApproveStatusEnum.APPROVAL_REJECTED.getCode()) {
            return;
        }
        String newHeadImg = contentSOADTOList.get(0).getChangeValue();
        boolean ocrResult = false;
        if (contentSOADTOList.size() == 2) {
            ApproveContentSOADTO orcContentSOADTO = contentSOADTOList.get(1);
            if (!Strings.isNullOrEmpty(orcContentSOADTO.getChangeValue())) {
                ocrResult = Boolean.valueOf(orcContentSOADTO.getChangeValue());
            }
        }
        driverPO.setDrvHeadImg(newHeadImg);
        drvDrvierRepository.updateDrv(driverPO);
        boolean isPass = requestType.getApproveStatus().intValue() == TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode();
        certificateCheckCommandService.insertCheckRecordDB(driverPO.getDrvId(),
                TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(),TmsTransportConstant.CertificateTypeEnum.HEAD_PORTRAIT_COMPLIANCE.getCode()
                , "", "", Lists.newArrayList(), isPass ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(),isPass ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(), Constant.SYSTEM);
        certificateCheckCommandService.insertCheckRecordDB(driverPO.getDrvId(),
                TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(),TmsTransportConstant.CertificateTypeEnum.OCR_HEAD_PORTRAIT.getCode()
                , "", "", Lists.newArrayList(), ocrResult ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(),ocrResult ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(), Constant.SYSTEM);
        tmsQmqProducerCommandService.sendDrvChangeQmqAfterApprove(driverPO.getDrvId(), 2,1);
        realNameAuth(Collections.singletonList(driverPO));
    }

    @Override
    @DalTransactional(logicDbName = TmsTransportConstant.TMS_TRANSPORT_DBNAME)
    public Result<Long> insertApprove(AddApproveDTO<T> addApproveDTO, TmsTransportConstant.EnentTypeEnum eventTypeEnum, Long cityId) {
        try {
            //境外编辑多个字段，生成多条编辑审核数据
            if(!addApproveDTO.getDomesticFlag()){
                //如果是临派标识，则进入另一个事件类型
                return insertOverseasApprove(addApproveDTO,addApproveDTO.getTemporaryDispatchMarkFlag()?TmsTransportConstant.EnentTypeEnum.TEMPORARYDISPATCH:TmsTransportConstant.EnentTypeEnum.OVERSEASVEHMOD);
            }
            String approveStr = getAssembleJsonList(getSource(addApproveDTO.getRrdId(), addApproveDTO.getRecordTypeEnum()), addApproveDTO.getNewMod(), addApproveDTO.getAttributeKeyValue(), getNeedArr(addApproveDTO.getRecordTypeEnum(), cityId),getPairField(addApproveDTO.getDomesticFlag(), addApproveDTO.getCityId(), addApproveDTO.getRecordTypeEnum()));
            logger.info("approveStr", "{}", approveStr);
            if (StringUtils.isEmpty(approveStr)) {
                return Result.Builder.<Long>newResult().success().withData(0L).build();
            }
            TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
            approvePO.setEventType(eventTypeEnum.getCode());
            approvePO.setApproveName(addApproveDTO.getApproveName() + "_" + getApproveNameKey(addApproveDTO.getRecordTypeEnum()));
            approvePO.setSupplierId(addApproveDTO.getSupplierId());
            approvePO.setApproveSourceId(addApproveDTO.getRrdId());
            approvePO.setApproveSourceType(addApproveDTO.getRecordTypeEnum().getCode());
            approvePO.setApproveFrom(StringUtils.isEmpty(SessionHolder.getSessionSource().get("accountType")) ? Integer.valueOf(1) : Integer.valueOf(SessionHolder.getSessionSource().get("accountType")));
            approvePO.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode());
            approvePO.setApproveContent(approveStr);
            approvePO.setCreateUser(addApproveDTO.getModifyUser());
            approvePO.setModifyUser(addApproveDTO.getModifyUser());
            approvePO.setAccountId(MapUtils.isEmpty(SessionHolder.getSessionSource()) ? "0" : SessionHolder.getSessionSource().get("accountId"));
            approvePO.setCertificateCheckResult("");
            Long count = approveRepository.insertTmsTransportApprovePO(approvePO);
            return Result.Builder.<Long>newResult().success().withData(count).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    private List<List<String>> getPairField(Boolean domesticFlag, Long cityId, TmsTransportConstant.ApproveSourceTypeEnum recordTypeEnum) {
        if (domesticFlag) {
            switch (recordTypeEnum) {
                case VEHICLE:
                    return qconfig.getPairField();
                default:
                    break;
            }
        } else {
            switch (recordTypeEnum) {
                case VEHICLE:
                    OcrComplianceDTO inComplianceRuleGary = internationalEntryService.isInComplianceRuleGary(cityId);
                    if (Objects.nonNull(inComplianceRuleGary) && CollectionUtils.isNotEmpty(inComplianceRuleGary.getEditFieldList())) {
                        return Collections.singletonList(inComplianceRuleGary.getEditFieldList());
                    }
                case DRV:
                    return qconfig.getOverseasDrvPairField();
                default:
                    break;
            }
        }
        return Lists.newArrayList();
    }

    public void syncDriverPreventionControlInfo(Long drvId, int eventType, String remark, Boolean isPass) throws SQLException {
        DrvDriverPO driverPO = drvDrvierRepository.queryByPk(drvId);
        if (driverPO == null) {
            return;
        }
        DrvEpidemicPreventionControlInfoPO infoPO = drvEpidemicPreventionControlInfoRepository.queryByDrvId(drvId);
        SaveDriverSafetyInfoSOARequestType ocrRequest;
        Boolean isNucleicAcidTest = eventType == TmsTransportConstant.EnentTypeEnum.nucleicAcidTest.getCode().intValue();
        String baseJson = isNucleicAcidTest ? infoPO.getNucleicAcidReportTempContent() : infoPO.getVaccineReportTempContent();
        if (isNucleicAcidTest) {
            ocrRequest = JsonUtil.fromJson(baseJson, new TypeReference<SaveDriverSafetyInfoSOARequestType>() {});
            driverPO.setNucleicAcidReportImg(ocrRequest.getDetectionReportUrl());
        } else {
            ocrRequest = JsonUtil.fromJson(baseJson, new TypeReference<SaveDriverSafetyInfoSOARequestType>() {});
            driverPO.setVaccineReportImg(ocrRequest.getDetectionReportUrl());
        }
        drvDrvierRepository.updateDrv(driverPO);
        boolean isOverdue = false;
        if (isNucleicAcidTest && checkNucleicAcidTime(infoPO.getNucleicAcidTestingTime(), driverPO.getCityId())) {
            isPass = false;
            isOverdue = true;
        }
        if (isPass) {
            if (isNucleicAcidTest) {
                infoPO.setNucleicAcidReportStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
                infoPO.setNucleicAcidReportResultStatus(EpidemicPreventionControlEnum.ReportResultEnum.PASS.getCode());
            } else {
                infoPO.setVaccineReportStatus(TmsTransportConstant.CheckStatusEnum.THROUGH.getCode());
                infoPO.setVaccineReportResultStatus(EpidemicPreventionControlEnum.ReportResultEnum.PASS.getCode());
            }
        } else {
            if (isNucleicAcidTest) {
                infoPO.setNucleicAcidReportStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                infoPO.setNucleicAcidReportResultStatus(isOverdue ? EpidemicPreventionControlEnum.ReportResultEnum.OVERDUE.getCode() : EpidemicPreventionControlEnum.ReportResultEnum.HUMAN_AUDIT_FAILURE.getCode());
            } else {
                infoPO.setVaccineReportStatus(TmsTransportConstant.CheckStatusEnum.ERROR.getCode());
                infoPO.setVaccineReportResultStatus(EpidemicPreventionControlEnum.ReportResultEnum.HUMAN_AUDIT_FAILURE.getCode());
            }
        }
        insertCheckTag(drvId, baseJson, isNucleicAcidTest, remark,isPass);
        drvEpidemicPreventionControlInfoRepository.updateEpidemicPreventionControlInfo(infoPO);
    }

    private Boolean checkNucleicAcidTime(Date nucleicAcidTestingTime, Long cityId) {
        if (nucleicAcidTestingTime == null) {
            return true;
        }
        Result<EpidemicPreventionControlEnum.ReportResultEnum> result = driverSafetyCommandService.checkNucleicAcidTestingReport(cityId, nucleicAcidTestingTime, EpidemicPreventionControlEnum.ResultStatusEnum.NEGATIVE.getMessage(), nucleicAcidTestingTime, EpidemicPreventionControlEnum.ResultStatusEnum.NEGATIVE.getMessage());
        if (result == null || result.getData() != EpidemicPreventionControlEnum.ReportResultEnum.PASS) {
            return true;
        }
        return false;
    }

    public void insertCheckTag(Long drvId, String content, Boolean isNucleicAcidDetectionType, String remark, boolean isPass) {
        CertificateCheckSOAInfo checkSOAInfo = new CertificateCheckSOAInfo();
        checkSOAInfo.setColumnKey(isNucleicAcidDetectionType ? SharkKeyConstant.WORKBENCH_NUCLEIC_ACID_REPORT_RETURN_INFO : SharkKeyConstant.WORKBENCH_VACCINE_REPORT_RETURN_INFO);
        checkSOAInfo.setColumnValue(SharkUtils.getSharkValue(checkSOAInfo.getColumnKey()));
        checkSOAInfo.setResultValue(remark);
        certificateCheckCommandService.insertCheckRecordDB(drvId,
                TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode(),
                isNucleicAcidDetectionType ? TmsTransportConstant.CertificateTypeEnum.NUCLEIC_ACID.getCode() : TmsTransportConstant.CertificateTypeEnum.VACCINE.getCode()
                , "", content, Lists.newArrayList(checkSOAInfo),isPass ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(),isPass ? TmsTransportConstant.CheckStatusEnum.THROUGH.getCode() : TmsTransportConstant.CheckStatusEnum.ERROR.getCode(), Constant.SYSTEM);
    }

    @Override
    public Boolean checkColumnApproveIng(AddApproveDTO<T> addApproveDTO) {
        //查询已修改并进审批流中属性
        Map<String, String[]> fields = compareFields(getSource(addApproveDTO.getRrdId(), addApproveDTO.getRecordTypeEnum()), addApproveDTO.getNewMod(),
                addApproveDTO.getDomesticFlag() ? getNeedArr(addApproveDTO.getRecordTypeEnum(), null) : needInApproveFields(addApproveDTO.getRecordTypeEnum(), addApproveDTO.getOcrPassStatusList(),
                    addApproveDTO.getTemporaryDispatchMarkFlag(), addApproveDTO.getIncomplianceRuleFlag(), addApproveDTO.getAuditStatus(), addApproveDTO.getCityId()));
        if (MapUtils.isEmpty(fields)) {
            return Boolean.FALSE;
        }
        List<TmsTransportApprovePO> approvePOList = approveRepository.queryApproveBySourceId(addApproveDTO.getRrdId(), addApproveDTO.getRecordTypeEnum().getCode(), TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode(),addApproveDTO.getEventType());
        if (CollectionUtils.isEmpty(approvePOList)) {
            return Boolean.FALSE;
        }
        for(TmsTransportApprovePO approvePO : approvePOList){
            if (StringUtils.isEmpty(approvePO.getApproveContent())) {
                continue;
            }
            List<ApproveContentSOADTO> contentSOADTOList = JsonUtil.fromJson(approvePO.getApproveContent(), new TypeReference<List<ApproveContentSOADTO>>() {
            });
            if (CollectionUtils.isEmpty(contentSOADTOList)) {
                continue;
            }
            List<String> changeItem = contentSOADTOList.stream().map(ApproveContentSOADTO::getChangeItem).collect(Collectors.toList());
            for (Map.Entry<String, String[]> entry : fields.entrySet()) {
                if (changeItem.contains(entry.getKey())) {
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean keepOnlyApprove(DrvDriverPO driverPO, Boolean isNucleicAcidDetectionType) {
        try {
            QueryApproveListDO params = new QueryApproveListDO();
            params.setApproveSourceId(driverPO.getDrvId());
            params.setEventType(isNucleicAcidDetectionType ? TmsTransportConstant.EnentTypeEnum.nucleicAcidTest.getCode() : TmsTransportConstant.EnentTypeEnum.vaccine.getCode());
            params.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode());
            if (approveRepository.countQueryApproveList(params) <= 0) {
                return approveRepository.insertTmsTransportApprovePO(getApprovePO(driverPO, isNucleicAcidDetectionType)) > 0;
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    private TmsTransportApprovePO getApprovePO(DrvDriverPO driverPO, Boolean isNucleicAcidDetectionType) {
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        String approveName = new StringBuilder(driverPO.getDrvId().toString()).append("_").append(SharkKeyConstant.REPORT_REVIEW_TITLE).toString();
        TmsTransportConstant.EnentTypeEnum resultEnum = isNucleicAcidDetectionType ? TmsTransportConstant.EnentTypeEnum.nucleicAcidTest : TmsTransportConstant.EnentTypeEnum.vaccine;
        approvePO.setEventType(resultEnum.getCode());
        approvePO.setApproveName(approveName);
        approvePO.setSupplierId(driverPO.getSupplierId());
        approvePO.setApproveSourceId(driverPO.getDrvId());
        approvePO.setApproveFrom(TmsTransportConstant.AccountTypeEnum.B_SYSTEM.getValue());
        approvePO.setApproveSourceType(TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode());
        approvePO.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode());
        approvePO.setApproveContent("");
        approvePO.setCreateUser(driverPO.getDrvName());
        approvePO.setModifyUser(driverPO.getDrvName());
        approvePO.setAccountId(MapUtils.isEmpty(SessionHolder.getSessionSource()) ? "0" : SessionHolder.getSessionSource().get("accountId"));
        approvePO.setCertificateCheckResult("");
        return approvePO;
    }

    public T getSource(Long rrdId, TmsTransportConstant.ApproveSourceTypeEnum approveSourceTypeEnum) {
        if (rrdId == null) {
            return null;
        }
        switch (approveSourceTypeEnum) {
            case DRV:
                return (T) drvDrvierRepository.queryByPk(rrdId);
            case VEHICLE:
                return (T) vehicleRepository.queryByPk(rrdId);
            default:
                return null;
        }
    }

    public String getApproveNameKey(TmsTransportConstant.ApproveSourceTypeEnum approveSourceTypeEnum) {
        switch (approveSourceTypeEnum) {
            case DRV:
                return SharkKeyConstant.transportDrvUpdateApproveName;
            case VEHICLE:
                return SharkKeyConstant.transportVehicleUpdateApproveName;
            default:
                return null;
        }
    }

    public String getAssembleJsonList(T sourceMod, T newMod, Map<String, String> attributeKeyValue, String[] needArr, List<List<String>> pairField) {
        Map<String, String[]> fields = compareFields(sourceMod, newMod, needArr);
        if (fields == null || fields.isEmpty()) {
            return null;
        }
        pairField(fields, sourceMod, newMod, pairField);
        logger.info("pairField", "{}", JsonUtil.toJson(fields));
        List<Map<String, String>> jsonStrList = Lists.newArrayListWithCapacity(fields.size());
        for (Map.Entry<String, String[]> entry : fields.entrySet()) {
            Map<String, String> jsonMap = Maps.newHashMapWithExpectedSize(4);
            jsonMap.put("originalValue", entry.getValue()[0]);
            jsonMap.put("changeValue", entry.getValue()[1]);
            jsonMap.put("changeItemName", attributeKeyValue.get(entry.getKey()));
            jsonMap.put("changeItem", entry.getKey());
            jsonStrList.add(jsonMap);
        }
        fields.clear();
        return JacksonUtil.serialize(jsonStrList);
    }

    private void pairField(Map<String, String[]> fields, T sourceMod, T newMod, List<List<String>> pairField) {
        // 空值检查
        if (fields == null || sourceMod == null || newMod == null || CollectionUtils.isEmpty(pairField)) {
            return;
        }

        Set<String> fieldSet = new HashSet<>();
        for (List<String> strings : pairField) {
            if (strings == null || strings.isEmpty()) {
                continue;
            }
            for (String string : strings) {
                if (fields.containsKey(string)) {
                    fieldSet.addAll(strings);
                    break;
                }
            }
        }

        if (fieldSet.isEmpty()) {
            return;
        }

        removeKeysFromFieldList(new ArrayList<>(fieldSet), fields);

        try {
            Map<String, String[]> fieldMap = getFiledValue(sourceMod, newMod, new ArrayList<>(fieldSet));
            if (MapUtils.isNotEmpty(fieldMap)) {
                fields.putAll(fieldMap);
                logger.info("fieldMap", "{}", JsonUtil.toJson(fieldMap));
                logger.info("fields", "{}", JsonUtil.toJson(fieldMap));

            }
        } catch (Exception e) {
            // 处理异常，例如记录日志
            logger.error("pairField",e);
        }
    }

private Map<String, String[]> getFiledValue(T sourceMod, T newMod, List<String> needList) {
    try {
        Map<String, String[]> map = Maps.newHashMap();
        Class<?> clazz = sourceMod.getClass();
        PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz, Object.class).getPropertyDescriptors();

        for (PropertyDescriptor pd : pds) {
            String fieldName = pd.getName();
            if (!needList.contains(fieldName)) {
                continue;
            }

            Method readMethod = pd.getReadMethod();
            Object o1 = readMethod.invoke(sourceMod);
            Object o2 = readMethod.invoke(newMod);

            o1 = convertTimestampToDate(o1);
            o2 = convertTimestampToDate(o2);

            String[] values = createStringArray(o1, o2);
            map.put(fieldName, values);
        }

        // 清理 Introspector 缓存，防止内存泄漏
        Introspector.flushFromCaches(clazz);

        return map;
    } catch (Exception e) {
        // 记录异常信息
        logger.error("getFiledValue", e);
        // 返回空的 Map，而不是 null，以避免调用方处理 null 的复杂性
        return Maps.newHashMap();
    }
}

private Object convertTimestampToDate(Object obj) {
    if (obj instanceof Timestamp) {
        return new Date(((Timestamp) obj).getTime());
    }
    return obj;
}

private String[] createStringArray(Object o1, Object o2) {
    String[] str = new String[2];
    str[0] = o1 == null ? null : o1.toString();
    str[1] = o2 == null ? null : o2.toString();
    return str;
}
    /**
     * 从 fieldList 中移除所有在 fields 中作为键出现的元素。
     *
     * @param fieldList 要从中移除元素的列表
     * @param fields 包含键值对的映射
     */
    public static void removeKeysFromFieldList(List<String> fieldList, Map<String, String[]> fields) {
        // 空值检查
        if (fieldList == null || fields == null) {
            throw new IllegalArgumentException("Input lists or maps cannot be null");
        }

        // 使用 HashSet 优化 contains 操作
        Set<String> keysToRemove = new HashSet<>(fields.keySet());

        // 使用 Iterator 遍历并移除元素
        Iterator<String> iterator = fieldList.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            if (keysToRemove.contains(key)) {
                iterator.remove();
            }
        }
    }

    @SuppressWarnings("rawtypes")
    public Map<String, String[]> compareFields(T sourceMod, T newMod, String[] needArr) {
        try {
            Map<String, String[]> map = Maps.newHashMap();
            List<String> needList;
            if (needArr == null || needArr.length <= 0) {
                return Maps.newHashMap();
            }
            needList = Arrays.asList(needArr);
            Class clazz = sourceMod.getClass();
            PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz, Object.class).getPropertyDescriptors();
            for (PropertyDescriptor pd : pds) {
                String fileName = pd.getName();
                if (!needList.contains(fileName)) {
                    continue;
                }
                Method readMethod = pd.getReadMethod();
                Object o1 = readMethod.invoke(sourceMod);
                Object o2 = readMethod.invoke(newMod);
                if (o1 instanceof Timestamp) {
                    o1 = new Date(((Timestamp) o1).getTime());
                }
                if (o2 instanceof Timestamp) {
                    o2 = new Date(((Timestamp) o2).getTime());
                }
                if (o1 == null && o2 == null) {
                    continue;
                } else if (o1 == null && o2 != null) {
                    String[] str = new String[2];
                    str[0] = null;
                    str[1] = o2.toString();
                    map.put(fileName, str);
                    continue;
                } else if (o1 != null && o2 == null) {
                    String[] str = new String[2];
                    str[0] = o1.toString();
                    str[1] = null;
                    map.put(fileName, str);
                    continue;
                }
                if (!o1.equals(o2)) {
                    if (o1.toString().equals(o2.toString())) {
                        continue;
                    }
                    String[] str = new String[2];
                    str[0] = o1.toString();
                    str[1] = o2.toString();
                    map.put(fileName, str);
                }
            }
            return map;
        } catch (Exception e) {
            return null;
        }
    }

    public String[] getNeedArr(TmsTransportConstant.ApproveSourceTypeEnum recordTypeEnum, Long cityId) {
        switch (recordTypeEnum) {
            case DRV:
                if (netCardCheckUtil.isNetCardNoNeedCheck(cityId)) {
                    return Constant.DRV_APPROVE_NEED_KEY__EXCEPT_NET_CERT;
                }
                return Constant.DRV_APPROVE_NEED_KEY;
            case VEHICLE:
                if (netCardCheckUtil.isNetCardNoNeedCheck(cityId)) {
                    return Constant.VEHICLE_APPROVE_NEED_KEY_EXCEPT_NET_CERT;
                }
                return Constant.VEHICLE_APPROVE_NEED_KEY;
            default:
                return null;
        }
    }


    public void optationApprovad(List<TmsTransportApprovePO> approvePOList, Map<Long, String> modifyUserMap, Integer isCompliant, String nonComplianceReason) {
        try {
            List<DrvDriverPO> updateDrvList = Lists.newArrayList();
            List<VehVehiclePO> updateVehicleList = Lists.newArrayList();
            List<Long> updateCheckList = new ArrayList<>();
            for (TmsTransportApprovePO approvePO : approvePOList) {
                Map<String, String> stringStringMap = Maps.newHashMap();
                String modifyUser = modifyUserMap.get(approvePO.getId());
                if (StringUtils.isNotEmpty(approvePO.getApproveContent())) {
                    List<ApproveContentSOADTO> contentSOADTOS = JsonUtil.fromJson(approvePO.getApproveContent(), new TypeReference<List<ApproveContentSOADTO>>() {
                    });
                    for(ApproveContentSOADTO soadto : contentSOADTOS){
                        if(soadto.getChangeValue() == null){
                            continue;
                        }
                        if (Objects.equals(soadto.getChangeItem(), TmsTransportConstant.CertificateTypeEnum.VEHICLE_AUDITSTATUS.getMsg()) && Objects.nonNull(isCompliant)) {
                            stringStringMap.put(soadto.getChangeItem(), isCompliant.toString());
                            updateCheckList.add(approvePO.getApproveSourceId());
                        }else{
                            stringStringMap.put(soadto.getChangeItem(),soadto.getChangeValue());
                        }
                    }
                }
                if(MapUtils.isEmpty(stringStringMap)){
                    continue;
                }
                if (Objects.equals(TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode(), approvePO.getApproveSourceType())) {
                    DrvDriverPO drvDriverPO = new DrvDriverPO();
                    drvDriverPO.setDrvId(approvePO.getApproveSourceId());
                    drvDriverPO.setModifyUser(modifyUser);
                    drvDriverPO.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode());
                    for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                        setValue(drvDriverPO, entry.getKey(), entry.getValue());
                    }
                    updateDrvList.add(drvDriverPO);
                }
                if (Objects.equals(TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode(), approvePO.getApproveSourceType())) {
                    VehVehiclePO vehiclePO = new VehVehiclePO();
                    vehiclePO.setVehicleId(approvePO.getApproveSourceId());
                    vehiclePO.setModifyUser(modifyUser);
                    vehiclePO.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode());
                    for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                        setValue(vehiclePO, entry.getKey(), entry.getValue());
                    }
                    updateVehicleList.add(vehiclePO);
                }
            }
            if (CollectionUtils.isNotEmpty(updateDrvList)) {
                drvDrvierRepository.batchUpdateDrv(updateDrvList);
                //qmq
                for(DrvDriverPO drvDriverPO : updateDrvList){
                    tmsQmqProducerCommandService.sendDrvChangeQmqAfterApprove(drvDriverPO.getDrvId(), 2,1);
                }
                //如果是境外司机审核通过，则打通过标签
                // TODO(yorick - 2025/6/17):
                insertOverseasTags(approvePOList, TmsTransportConstant.CheckStatusEnum.THROUGH);
            }
            if (CollectionUtils.isNotEmpty(updateVehicleList)) {
                vehicleRepository.batchUpdateVehicle(updateVehicleList);
                //编辑审核后,同步车辆qmq
                List<Long> vehicleIdList = updateVehicleList.stream().map(VehVehiclePO::getVehicleId).collect(Collectors.toList());
                List<DrvDriverPO> drvDriverPOS = drvDrvierRepository.queryDrvByVehicleIds(vehicleIdList);
                for(DrvDriverPO drvDriverPO : drvDriverPOS){
                    tmsQmqProducerCommandService.sendVehicleModifyQmq(drvDriverPO.getVehicleId(), drvDriverPO.getDrvId());
                }
                //todo 同步司机车牌信息
                syncOverseasDrvVehLic(updateVehicleList,drvDriverPOS);
                //如果是境外车辆审核通过，则打通过标签
                insertOverseasTags(approvePOList, TmsTransportConstant.CheckStatusEnum.THROUGH);
                // 处理车辆超龄类型
                processVehicleAge(vehicleIdList);
                complianceCheck(isCompliant, nonComplianceReason, updateCheckList);
            }

            //编辑审核通过后，将满足条件的司机或车辆 从临派置为正式
            for (TmsTransportApprovePO approvePO : approvePOList) {
                if(Objects.equals(TmsTransportConstant.EnentTypeEnum.TEMPORARYDISPATCH.getCode(),approvePO.getEventType())){
                    tmsQmqProducerCommandService.sendApproveToOfficial(approvePO.getApproveSourceId(),approvePO.getApproveSourceType());
                }
            }

        } catch (Exception e) {
            if (e instanceof BizException) {
                throw (BizException) e;
            }
            throw new BaijiRuntimeException(e);
        }
    }

    private void complianceCheck(Integer isCompliant, String nonComplianceReason, List<Long> updateCheckList) {
        if (CollectionUtils.isNotEmpty(updateCheckList)) {
            updateCheckList.forEach(id ->{
                try {
                    TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
                    checkPO.setCheckId(id);
                    checkPO.setCheckType(TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode());
                    checkPO.setCertificateType(TmsTransportConstant.CertificateTypeEnum.VEHICLE_AUDITSTATUS.getCode());
                    checkPO.setCheckContent(nonComplianceReason);
                    checkPO.setCheckStatus(VehicleAuditStatusEnum.getEnumByCode(isCompliant).getSingleApproveStatus());
                    checkPO.setCreateUser(SessionHolder.getRestSessionAccountName());
                    checkPO.setModifyUser(SessionHolder.getRestSessionAccountName());
                    checkPO.setThirdCheckStatus(VehicleAuditStatusEnum.getEnumByCode(isCompliant).getSingleApproveStatus());
                    checkRepository.insertTmsCertificateCheck(checkPO);
                } catch (SQLException e) {
                    logger.error("complianceCheck error", e);
                }
            });
        }
    }

    private void realNameAuth(List<DrvDriverPO> drvDriverPOS) {
        try {
            if (CollectionUtils.isEmpty(drvDriverPOS)) {
                return;
            }
            List<DrvDriverPO> collect = drvDriverPOS.stream().filter(d -> Objects.nonNull(d.getDrvHeadImg())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                return;
            }
            tmsQmqProducerCommandService.sendRealNameAuth(collect.stream().map(DrvDriverPO::getDrvId).collect(Collectors.toList()));
        } catch (Exception e) {
            logger.warn("sendRealNameAuth error", e);
        }
    }

    private void processVehicleAge(List<Long> vehicleIdList) {
        try {
            List<VehVehiclePO> vehVehiclePOS = vehicleRepository.queryVehVehicleByIds(vehicleIdList);
            if (CollectionUtils.isEmpty(vehVehiclePOS)) {
                return;
            }

            List<Long> cityIdList = vehVehiclePOS.stream().map(VehVehiclePO::getCityId).collect(Collectors.toList());
            if (BooleanUtils.isTrue(config.getOverageGraySwitch())) {
                cityIdList = cityIdList.stream().filter(item -> config.getCityIdList().contains(item)).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(cityIdList)) {
                return;
            }

            List<City> cities = enumRepository.queryByCityIds(cityIdList);
            if (CollectionUtils.isEmpty(cities)) {
                return;
            }
            List<Long> mainLandCityId = cities.stream().filter(item -> BooleanUtils.isTrue(item.isChineseMainland())).map(City::getId).collect(Collectors.toList());
            List<VehVehiclePO> updateVehicleList = Lists.newArrayList();
            vehVehiclePOS.forEach(vehicle ->{
                if (mainLandCityId.contains(vehicle.getCityId())) {
                    LocalDateTime registerTime = getRegisterTime(vehicle);
                    Double overage = getOverage(vehicle);
                    boolean isOverage = checkOverAge(registerTime, overage);
                    String vehicleAgeType = judgeOverAge(isOverage, vehicle);
                    if (StringUtils.isNotEmpty(vehicleAgeType)) {
                        VehVehiclePO update = new VehVehiclePO();
                        update.setVehicleId(vehicle.getVehicleId());
                        update.setVehicleAgeType(vehicleAgeType);
                        updateVehicleList.add(update);
                    }
                }
            });

            if (CollectionUtils.isNotEmpty(updateVehicleList)) {
                vehicleRepository.batchUpdateVehicle(updateVehicleList);
            }

        } catch (SQLException e) {
            logger.error("processVehicleAge error", e);
        }
    }

    public String judgeOverAge(boolean isOverage, VehVehiclePO vehicle) {
        String vehicleAgeType = vehicle.getVehicleAgeType();
        if (Objects.equals(vehicleAgeType, VehicleAgeTypeEnum.NOT_OVERAGE.getCode())) {
            if (isOverage) {
                return VehicleAgeTypeEnum.OVERAGE.getCode();
            }
        } else if (Objects.equals(vehicleAgeType, VehicleAgeTypeEnum.OVERAGE.getCode())) {
            if (!isOverage) {
                return VehicleAgeTypeEnum.NOT_OVERAGE.getCode();
            }
        }
        return null;
    }

    private boolean checkOverAge(LocalDateTime registerTime, Double overage) {
        LocalDateTime localDateTime = registerTime.plusMonths(BigDecimal.valueOf(overage).multiply(BigDecimal.valueOf(12L)).intValue());
        return localDateTime.isBefore(LocalDateTime.now());
    }

    private static LocalDateTime getRegisterTime(VehVehiclePO vehiclePO) {
        LocalDateTime regstDate;
        if (Objects.isNull(vehiclePO.getRegstDate()) || org.apache.commons.lang3.StringUtils.equals(Constant.DEFAULT_YEAR, vehiclePO.getRegstDate().toString())) {
            regstDate = vehiclePO.getDatachangeCreatetime().toLocalDateTime();
        } else {
            LocalDate localDate = DateUtil.convertStringToDate(vehiclePO.getRegstDate().toString(), DateUtil.YYYYMMDD);
            LocalTime localTime = LocalTime.of(23, 59, 59);
            regstDate = LocalDateTime.of(localDate, localTime);
        }
        return regstDate;
    }


    protected Double getOverage(VehVehiclePO vehicle) {
        Long cityId = vehicle.getCityId(); // 城市id
        Long vehicleTypeId = vehicle.getVehicleTypeId(); // 车型id
        OverageDTO overageMap = overageQConfig.getOverageMap(cityId, vehicleTypeId); // 配置的超龄信息
        logger.info("getOverage", "{} {}", vehicle.getVehicleId(), JsonUtil.toJson(overageMap));
        return overageMap.getOverage();
    }

    public static void setValue(Object obj, String fieldName, Object value) {
        try {
            if(Objects.equals(fieldName,TmsTransportConstant.CertificateTypeEnum.VEHICLECOLORID.getMsg())){
                value = Long.valueOf(value.toString());
            }
            if (Objects.equals(fieldName, TmsTransportConstant.CertificateTypeEnum.VEHICLE_AUDITSTATUS.getMsg())) {
                value = Integer.valueOf(value.toString());
            }
            Field field = obj.getClass().getDeclaredField(fieldName);
            Class<?> type = field.getType();
            if (type == String.class) {
                field.setAccessible(true);
                field.set(obj, value);
            } else if (type == java.sql.Date.class) {
                field.setAccessible(true);
                field.set(obj, java.sql.Date.valueOf((String) value));
            } else {
                field.setAccessible(true);
                field.set(obj, value);
            }

        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    /**
     * 审批流中核验信息同步到核验表
     */
    public void syncCertificateCheckDB(List<TmsTransportApprovePO> approvePOList) {
        if (CollectionUtils.isEmpty(approvePOList)) {
            return;
        }
        try {
            List<TmsCertificateCheckPO> resultList = Lists.newArrayList();
            for (TmsTransportApprovePO po : approvePOList) {
                if (StringUtils.isEmpty(po.getCertificateCheckResult())) {
                    continue;
                }
                List<TmsCertificateCheckPO> checkResultList = JsonUtil.fromJson(po.getCertificateCheckResult(), new TypeReference<List<TmsCertificateCheckPO>>() {
                });
                resultList = (List<TmsCertificateCheckPO>) CollectionUtils.union(checkResultList, resultList);
            }
            if (CollectionUtils.isEmpty(resultList)) {
                return;
            }
            checkRepository.batchInsertCheckRecord(resultList);
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    //境外司机或车辆进编辑审核，每一个字段生成一条审核记录
    public Result<Long> insertOverseasApprove(AddApproveDTO<T> addApproveDTO, TmsTransportConstant.EnentTypeEnum eventTypeEnum) {
        try {
            List<String> approveFieldList = getOverseasList(getSource(addApproveDTO.getRrdId(), addApproveDTO.getRecordTypeEnum()), addApproveDTO.getNewMod(), addApproveDTO.getAttributeKeyValue(),
                needInApproveFields(addApproveDTO.getRecordTypeEnum(), addApproveDTO.getOcrPassStatusList(), addApproveDTO.getTemporaryDispatchMarkFlag(), addApproveDTO.getIncomplianceRuleFlag(),
                    addApproveDTO.getAuditStatus(), addApproveDTO.getCityId()),getPairField(addApproveDTO.getDomesticFlag(),addApproveDTO.getCityId(), addApproveDTO.getRecordTypeEnum()));
            if (CollectionUtils.isEmpty(approveFieldList)) {
                return Result.Builder.<Long>newResult().success().withData(0L).build();
            }
            Long count = 0L;
            for(String aapproveFieldMap : approveFieldList){
                TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
                approvePO.setEventType(eventTypeEnum.getCode());
                approvePO.setApproveName(addApproveDTO.getApproveName() + "_" + getApproveNameKey(addApproveDTO.getRecordTypeEnum()));
                approvePO.setSupplierId(addApproveDTO.getSupplierId());
                approvePO.setApproveSourceId(addApproveDTO.getRrdId());
                approvePO.setApproveSourceType(addApproveDTO.getRecordTypeEnum().getCode());
                approvePO.setApproveFrom(StringUtils.isEmpty(SessionHolder.getSessionSource().get("accountType")) ? Integer.valueOf(1) : Integer.valueOf(SessionHolder.getSessionSource().get("accountType")));
                approvePO.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode());
                approvePO.setApproveContent(aapproveFieldMap);
                approvePO.setCreateUser(addApproveDTO.getModifyUser());
                approvePO.setModifyUser(addApproveDTO.getModifyUser());
                approvePO.setAccountId(MapUtils.isEmpty(SessionHolder.getSessionSource()) ? "0" : SessionHolder.getSessionSource().get("accountId"));
                approvePO.setCertificateCheckResult("");
                approvePO.setApproveSchedule(TmsTransportConstant.ApproveScheduleStatusEnum.done.getCode());
                count = approveRepository.insertTmsTransportApprovePO(approvePO);
            }
            return Result.Builder.<Long>newResult().success().withData(count).build();
        } catch (Exception e) {
            throw new BaijiRuntimeException(e);
        }
    }

    public List<String> getOverseasList(T sourceMod, T newMod, Map<String, String> attributeKeyValue, String[] needArr, List<List<String>> pairField) {
        // Log input parameters
        logger.info("getOverseasList start - sourceMod: {}, newMod: {}, attributeKeyValue: {}, needArr: {}, pairField: {}",
                JsonUtil.toJson(sourceMod), JsonUtil.toJson(newMod), JsonUtil.toJson(attributeKeyValue),
                Arrays.toString(needArr), JsonUtil.toJson(pairField));

        // Get field changes
        Map<String, String[]> fields = compareFields(sourceMod, newMod, needArr);
        logger.info("getOverseasList compareFields result: {}", JsonUtil.toJson(fields));

        if (MapUtils.isEmpty(fields)) {
            logger.info("getOverseasList end - no field changes detected, returning empty list");
            return Collections.emptyList();
        }

        // Apply pair field logic
        pairField(fields, sourceMod, newMod, pairField);
        logger.info("getOverseasList after pairField processing: {}", JsonUtil.toJson(fields));

        // Create a copy to avoid mutating the original map
        Map<String, String[]> fieldsToProcess = new HashMap<>(fields);

        // Group fields by pair field groups and individual fields
        List<List<String>> fieldGroups = groupFieldsByPairField(fieldsToProcess, pairField);
        logger.info("getOverseasList fieldGroups: {}", JsonUtil.toJson(fieldGroups));

        // Convert field groups to JSON strings
        List<String> result = fieldGroups.stream()
                .map(fieldGroup -> convertFieldGroupToJson(fieldGroup, fieldsToProcess, attributeKeyValue))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        logger.info("getOverseasList end - result: {}", JsonUtil.toJson(result));
        return result;
    }

    /**
     * Groups fields by pair field configuration
     * @param fields The fields to group
     * @param pairField The pair field configuration
     * @return List of field groups
     */
    private List<List<String>> groupFieldsByPairField(Map<String, String[]> fields, List<List<String>> pairField) {
        List<List<String>> fieldGroups = new ArrayList<>();
        Set<String> processedFields = new HashSet<>();

        // Process pair field groups
        if (CollectionUtils.isNotEmpty(pairField)) {
            for (List<String> pairGroup : pairField) {
                if (CollectionUtils.isEmpty(pairGroup)) {
                    continue;
                }

                // Find fields in this pair group that have changes
                List<String> groupFields = pairGroup.stream()
                        .filter(field -> fields.containsKey(field) && !processedFields.contains(field))
                        .collect(Collectors.toList());

                if (!groupFields.isEmpty()) {
                    fieldGroups.add(groupFields);
                    processedFields.addAll(groupFields);
                }
            }
        }

        // Process remaining individual fields
        fields.keySet().stream()
                .filter(field -> !processedFields.contains(field))
                .forEach(field -> fieldGroups.add(Collections.singletonList(field)));

        return fieldGroups;
    }

    /**
     * Converts a field group to JSON string
     * @param fieldGroup The group of fields
     * @param fields The field values map
     * @param attributeKeyValue The attribute key-value mapping
     * @return JSON string representation
     */
    private String convertFieldGroupToJson(List<String> fieldGroup, Map<String, String[]> fields, Map<String, String> attributeKeyValue) {
        if (CollectionUtils.isEmpty(fieldGroup)) {
            return null;
        }

        List<Map<String, String>> jsonList = fieldGroup.stream()
                .filter(fields::containsKey)
                .map(fieldKey -> createFieldChangeMap(fieldKey, fields.get(fieldKey), attributeKeyValue))
                .collect(Collectors.toList());

        return CollectionUtils.isEmpty(jsonList) ? null : JsonUtil.toJson(jsonList);
    }

    /**
     * Creates a map representing a field change
     * @param fieldKey The field key
     * @param fieldValues The field values [original, new]
     * @param attributeKeyValue The attribute key-value mapping
     * @return Map representing the field change
     */
    private Map<String, String> createFieldChangeMap(String fieldKey, String[] fieldValues, Map<String, String> attributeKeyValue) {
        Map<String, String> changeMap = new HashMap<>(4);
        changeMap.put("originalValue", fieldValues[0]);
        changeMap.put("changeValue", fieldValues[1]);
        changeMap.put("changeItemName", attributeKeyValue.get(fieldKey));
        changeMap.put("changeItem", fieldKey);
        return changeMap;
    }

    //境外编辑审核需要审核的字段 可配置 {"1":"","2":"vehicle"}
    public String[] needInApproveFields(TmsTransportConstant.ApproveSourceTypeEnum recordTypeEnum, List<OcrPassStatusModelSOA> ocrPassStatusList, Boolean temporaryFlag, Boolean incomplianceRuleFlag, Integer auditStatus,Long cityId){
        String [] empty = new String[]{};
        try {
            if(Objects.equals(recordTypeEnum.getCode(),TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE.getCode())){
                return vehicleInApproveFields(ocrPassStatusList,incomplianceRuleFlag,auditStatus,cityId);
            }
            //司机新增临派逻辑
            if(temporaryFlag){
                return drvInApproveFields(ocrPassStatusList);
            }
            String needFields =  qconfig.getOverseasApproveFields();
            if(StringUtils.isEmpty(needFields)){
                return empty;
            }
            Map<Integer,String> needFieldsMap = JsonUtil.fromJson(needFields, new TypeReference<Map<Integer, String>>() {
            });
            if(MapUtils.isEmpty(needFieldsMap)){
                return empty;
            }
            String needFieldsV = needFieldsMap.get(recordTypeEnum.getCode());
            if(StringUtils.isEmpty(needFieldsV)){
                return empty;
            }
            return needFieldsV.split(",");
        }catch (Exception e){
            logger.error("needInApproveFieldsError","params:{},e:{}",recordTypeEnum.getCode(),e);
            return new String[]{};
        }
    }

    //操作境外审核数据，加入标签数据
    public Boolean insertOverseasTags(List<TmsTransportApprovePO> approvePOList, TmsTransportConstant.CheckStatusEnum checkStatusEnum){
        if(CollectionUtils.isEmpty(approvePOList)){
            return Boolean.FALSE;
        }
        try {
            //境外的数据
            List<TmsTransportApprovePO> overseasApprovePOList = Lists.newArrayList();
            for(Iterator<TmsTransportApprovePO> iterator = approvePOList.iterator();iterator.hasNext();){
                TmsTransportApprovePO approvePO = iterator.next();
                if(!Objects.isNull(approvePO) && (Objects.equals(TmsTransportConstant.EnentTypeEnum.OVERSEASVEHMOD.getCode(),approvePO.getEventType())||
                        Objects.equals(TmsTransportConstant.EnentTypeEnum.TEMPORARYDISPATCH.getCode(),approvePO.getEventType()))){
                    overseasApprovePOList.add(approvePO);
                }
            }
            if(CollectionUtils.isEmpty(overseasApprovePOList)){
                return Boolean.FALSE;
            }
            //按每个来源ID分组,一组中有多个字段审核打标签
            Map<Long,List<TmsTransportApprovePO>> longListMap = overseasApprovePOList.stream().collect(Collectors.groupingBy(TmsTransportApprovePO::getApproveSourceId));
            for(Map.Entry<Long,List<TmsTransportApprovePO>> entry : longListMap.entrySet()){
                Long vehicleId = entry.getKey();
                List<TmsTransportApprovePO> approvePOS = entry.getValue();
                List<Integer> CertificateType = Lists.newArrayList();
                for(TmsTransportApprovePO approvePO : approvePOS){
                    Integer checkType = TmsTransportConstant.CertificateCheckTypeEnum.VEHICLE.getCode();
                    if(StringUtils.isNotEmpty(approvePO.getApproveContent())){
                        List<Map<String,String>> contentlist = JsonUtil.fromJson(approvePO.getApproveContent(), new TypeReference<List<Map<String,String>>>() {
                        });
                        Map<String,String> conentMap = contentlist.get(0);
                        if(MapUtils.isEmpty(conentMap)){
                            continue;
                        }
                        //获取当前编辑审核字段
                        String changeItem = conentMap.get("changeItem");
                        if(StringUtils.isEmpty(changeItem)){
                            continue;
                        }
                        //车辆信息按指定字段分类型
                        if(Objects.equals(TmsTransportConstant.CertificateTypeEnum.VEHICLELICENSE.getMsg(),changeItem)){
                            CertificateType.add(TmsTransportConstant.CertificateTypeEnum.VEHICLELICENSE.getCode());
                        }
//                        if(Objects.equals(TmsTransportConstant.CertificateTypeEnum.VEHICLECOLORID.getMsg(),changeItem)){
//                            CertificateType.add(TmsTransportConstant.CertificateTypeEnum.VEHICLECOLORID.getCode());
//                        }
                        // (yorick - 2025/6/17): 插入check表的地方
                        if(Objects.equals(TmsTransportConstant.CertificateTypeEnum.VEHICLEFULLIMG.getMsg(),changeItem)){
                            CertificateType.add(TmsTransportConstant.CertificateTypeEnum.VEHICLEFULLIMG.getCode());
                        }
//                        if(Objects.equals(TmsTransportConstant.CertificateTypeEnum.VEHICLE_AUDITSTATUS.getMsg(),changeItem)){
//                            CertificateType.add(TmsTransportConstant.CertificateTypeEnum.VEHICLE_AUDITSTATUS.getCode());
//                        }
                        if(Objects.equals(TmsTransportConstant.CertificateTypeEnum.DRVNAME.getMsg(),changeItem)){
                            CertificateType.add(TmsTransportConstant.CertificateTypeEnum.DRVNAME.getCode());
                            checkType = TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode();
                        }
                        if(Objects.equals(TmsTransportConstant.CertificateTypeEnum.DRVCARDIMG.getMsg(),changeItem)){
                            CertificateType.add(TmsTransportConstant.CertificateTypeEnum.DRVCARDIMG.getCode());
                            checkType = TmsTransportConstant.CertificateCheckTypeEnum.DRV.getCode();
                        }
                    }
                    if(CollectionUtils.isEmpty(CertificateType)){
                        continue;
                    }
                    //插入标签
                    PropertyAddTagSOARequestType soaRequestType = new PropertyAddTagSOARequestType();
                    soaRequestType.setCheckIdList(Arrays.asList(vehicleId));
                    soaRequestType.setCertificateType(CertificateType);
                    soaRequestType.setCheckType(checkType);
                    soaRequestType.setModifyUser(SessionHolder.getRestSessionAccountName());
                    soaRequestType.setCheckStatus(checkStatusEnum.getCode());
                    certificateCheckCommandService.insertPropertyAddTag(soaRequestType);
                }
            }
            return Boolean.TRUE;
        }catch (Exception e){
            logger.error("insertOverseasTagsError",e);
        }
        return Boolean.FALSE;
    }

    public String[] vehicleInApproveFields(List<OcrPassStatusModelSOA> ocrPassStatusList, Boolean incomplianceRuleFlag, Integer auditStatus, Long cityId){
        //如果传入的校验值为空，则都进编辑审批
        OcrComplianceDTO inComplianceRuleGary = internationalEntryService.isInComplianceRuleGary(cityId);
        List<String> editFieldList = Lists.newArrayList();
        if (Objects.nonNull(inComplianceRuleGary) && BooleanUtils.isTrue(incomplianceRuleFlag)) {
            editFieldList = inComplianceRuleGary.getEditFieldList();
        }

        if(CollectionUtils.isEmpty(ocrPassStatusList)){
            List<String> resultList = new ArrayList<>(Arrays.asList(Constant.VEHICLE_OVERSEAS_ALL_APPROVE_NEED_KEY));
            editFieldList.addAll(resultList);
            return editFieldList.stream().distinct().toArray(String[]::new);
        }

        boolean vehicleNumberNoPass = Boolean.FALSE;//车牌不通过

        for(OcrPassStatusModelSOA modelSOA : ocrPassStatusList){
            if(Objects.equals(modelSOA.getPassStatus(), TmsTransportConstant.OverseasOCRPassStatusEnum.no_pass.getCode())){
                // 车牌号不通过
                if(Objects.equals(modelSOA.getOcrItem(),TmsTransportConstant.ApproveItemEnum.vehicle_number.getCode())){
                    vehicleNumberNoPass = Boolean.TRUE;
                }
            }
        }
        //车牌进编辑审批
        String[] vehicleOverseasVehiclelicenseApproveNeedKey;
        if (vehicleNumberNoPass) {
            vehicleOverseasVehiclelicenseApproveNeedKey = Constant.VEHICLE_OVERSEAS_VEHICLELICENSE_APPROVE_NEED_KEY;
        } else {
            //如果都是OCR通过，则只有车身图片进编辑审核
            vehicleOverseasVehiclelicenseApproveNeedKey = Constant.VEHICLE_OVERSEAS_VEHICLEFULLIMG_NEED_KEY;
        }
        List<String> resultList = new ArrayList<>(Arrays.asList(vehicleOverseasVehiclelicenseApproveNeedKey));
        editFieldList.addAll(resultList);
        return editFieldList.stream().distinct().toArray(String[]::new);
    }

    public String[] drvInApproveFields(List<OcrPassStatusModelSOA> ocrPassStatusList){
        //如果传入的校验值为空，则都进编辑审批
        if(CollectionUtils.isEmpty(ocrPassStatusList)){
            return Constant.OVERSEAS_TEMPORARY_DRV_APPROVE_NEED_KEY;
        }

        for(OcrPassStatusModelSOA modelSOA : ocrPassStatusList){
            if(Objects.equals(modelSOA.getPassStatus(), TmsTransportConstant.OverseasOCRPassStatusEnum.pass.getCode())){
                return Constant.OVERSEAS_TEMPORARY_DRV_DRVCARD_APPROVE_NEED_KEY;
            }
        }
        return Constant.OVERSEAS_TEMPORARY_DRV_APPROVE_NEED_KEY;
    }

    //修改车牌数据，同步到司机表
    public Boolean syncOverseasDrvVehLic(List<VehVehiclePO> updateVehicleList,List<DrvDriverPO> drvDriverPOS){

        if(CollectionUtils.isEmpty(drvDriverPOS) || CollectionUtils.isEmpty(updateVehicleList)){
            return Boolean.FALSE;
        }
        try {
            //遍历编辑审批通过的车辆信息，key = vehcileId ,v = VehicleLicense
            Map<Long,String> vehicleMap = Maps.newHashMap();
            for(VehVehiclePO vehVehiclePO : updateVehicleList){
                if(StringUtils.isNotEmpty(vehVehiclePO.getVehicleLicense())){
                    vehicleMap.put(vehVehiclePO.getVehicleId(),vehVehiclePO.getVehicleLicense());
                }
            }
            if(MapUtils.isEmpty(vehicleMap)){
                return Boolean.FALSE;
            }
            for(DrvDriverPO drvDriverPO : drvDriverPOS){
                //只处理境外的数据，境内车牌没有修改的权限
                if(Objects.equals(AreaScopeTypeEnum.DOMESTIC.getCode(),drvDriverPO.getInternalScope())){
                    continue;
                }
                Long vehicleId = drvDriverPO.getVehicleId();
                if(vehicleId == null || vehicleId == 0){
                    continue;
                }

                //如果车辆中的车牌为空或者司机中的车牌和修改后的车牌一样，则无需同步变更
                String vehicleLicense = vehicleMap.get(vehicleId);
                if(StringUtils.isEmpty(vehicleLicense) || Objects.equals(vehicleLicense,drvDriverPO.getVehicleLicense())){
                    continue;
                }
                drvDrvierRepository.syncDrvVehicleLicense(drvDriverPO.getDrvId(),vehicleLicense,SessionHolder.getRestSessionAccountName());
                //司机信息变更，发送变更消息
                tmsQmqProducerCommandService.sendDrvChangeQmq(drvDriverPO.getDrvId(), 2,1);
            }
            return Boolean.TRUE;
        }catch (Exception e){
            logger.error("syncOverseasDrvVehLicError","drvDriverPOS:{}",JsonUtil.toJson(drvDriverPOS),e);
            return Boolean.FALSE;
        }
    }
}
