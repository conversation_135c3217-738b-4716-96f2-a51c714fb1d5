package com.ctrip.dcs.tms.transport.application.convert;

import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdCountSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDriverIdSOARequestType;
import com.ctrip.dcs.tms.transport.api.resource.driver.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.PaginatorDTO;
import com.google.common.collect.*;
import org.apache.commons.collections.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class DrvResourceConverter {

    private final String[] RESOURCE_BASE = new String[]{"drv_id", "city_id", "drv_name", "drv_head_img", "drv_phone", "drv_status", "coop_mode", "igt_code", "supplier_id", "category_synthesize_code", "email", "drv_license_number", "certificate_number", "drv_idcard", "paiay_email"};

    @Autowired
    private ProductionLineUtil productionLineUtil;

    public DrvBase convertDrvBase(DrvDriverPO driverPO) {
        if (driverPO == null) {
            return null;
        }
        DrvBase res = new DrvBase();
        res.setSupplierId(driverPO.getSupplierId());
        res.setPhoneAreaCode(driverPO.getIgtCode());
        res.setDrvCoopMode(driverPO.getCoopMode());
        res.setDrvStatus(driverPO.getDrvStatus());
        res.setDrvPhone(driverPO.getDrvPhone());
        res.setPicUrl(driverPO.getDrvHeadImg());
        res.setDrvName(driverPO.getDrvName());
        res.setCityId(driverPO.getCityId());
        res.setDrvId(driverPO.getDrvId());
        res.setProLineIdList(productionLineUtil.getShowProductionLineList(driverPO.getCategorySynthesizeCode()));
        res.setEmail(driverPO.getEmail());
        res.setDrvLicenseNumber(driverPO.getDrvLicenseNumber());
        res.setCertificateNumber(driverPO.getCertificateNumber());
        res.setDrvIdcard(driverPO.getDrvIdcard());
        res.setPaiayEmail(driverPO.getPaiayEmail());
        return res;
    }

    public List<DrvBase> convertDrvBase(List<DrvDriverPO> poList) {
        if (poList == null || poList.size() == 0) {
            return Lists.newArrayList();
        }
        List<DrvBase> resList = Lists.newArrayListWithExpectedSize(poList.size());
        for (DrvDriverPO driverPO : poList) {
            resList.add(convertDrvBase(driverPO));
        }
        return resList;
    }

    public QueryDrvResourceConditionDTO convertCondition(QueryDriver4BaseSOARequestType req) {
        return QueryDrvResourceConditionDTO.newCondition().withCityIdList(req.getCityIdList()).withDriverPhoneList(req.getDriverPhoneList()).withDrvCoopModeList(req.getDrvCoopModeList())
                .withDrvIdList(req.getDrvIdList()).withDrvNameList(req.getDrvNameList()).withDrvStatusList(req.getDrvStatusList()).withSupplierIdList(req.getSupplierIdList()).withPaginator(req.getPaginator())
                .withFields(RESOURCE_BASE).withProLineIdList(productionLineUtil.getAllIncludeProductionLineList(req.getProLineList())).build();
    }

    public QueryDrvResourceConditionDTO convertCondition(QueryDriverIdCountSOARequestType req) {
        return QueryDrvResourceConditionDTO.newCondition()
                .withCountryIdList(req.getCountryIdList())
                .withCityIdList(req.getCityIdList())
                .withVehicleTypeIdList(req.getVehicleTypeIdList())
                .withDrvStatusList(req.getDrvStatusList())
                .withProLineIdList(productionLineUtil.getIncludeProductionLineList(req.getProLineList())).build();
    }

    public QueryDrvResourceConditionDTO convertCondition(QueryDriverIdSOARequestType req) {
        return QueryDrvResourceConditionDTO.newCondition()
                .withCountryIdList(req.getCountryIdList())
                .withCityIdList(req.getCityIdList())
                .withVehicleTypeIdList(req.getVehicleTypeIdList())
                .withDrvStatusList(req.getDrvStatusList())
                .withBoundaryDrvId(req.getBoundaryDrvId())
                .withFields(new String[]{"drv_id"})
                .withPaginator(new PaginatorDTO(1, req.getPageSize()))
                .withProLineIdList(productionLineUtil.getIncludeProductionLineList(req.getProLineList())).build();
    }

}