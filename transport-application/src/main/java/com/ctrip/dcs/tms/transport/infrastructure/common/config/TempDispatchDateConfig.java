package com.ctrip.dcs.tms.transport.infrastructure.common.config;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DateRangeDTO;
import lombok.Data;
import org.springframework.stereotype.Service;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.JsonConfig;

import java.util.List;
import java.util.Map;

@Service
@Data
public class TempDispatchDateConfig {

  private Map<String, List<DateRangeDTO>> dateRange;

  private final JsonConfig<TempDispatchDateConfig>
    config = JsonConfig.get("temp.dispatch.display.date.json", Feature.create().setFailOnNotExists(false).build(), TempDispatchDateConfig.class);

  public List<DateRangeDTO> getTempDispatchDateConfig(String countryId) {
    return config.current().getDateRange().get(countryId);
  }
}
