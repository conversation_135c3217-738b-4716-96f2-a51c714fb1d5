package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NetCertCheckRuleDTO;

public interface INetCertNoCheckRuleService {
    public NetCertCheckRuleDTO queryNetCertNoCheckRule(String cityId);
    public boolean checkDriverNetCertNo(String cityId,String netCertNo);
    public boolean checkVehicleNetCertNo(String cityId,String netCertNo);
    public boolean checkDriverNetCertNo(String cityId,String netCertNo,Long driverId);
}
