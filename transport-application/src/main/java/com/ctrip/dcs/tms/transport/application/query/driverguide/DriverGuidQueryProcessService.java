package com.ctrip.dcs.tms.transport.application.query.driverguide;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverGuideDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.VehVehicleDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidDriverRequestDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverGuidVehicleRequestDTO;

import java.util.List;

public interface DriverGuidQueryProcessService {

  List<VehVehicleDTO> queryVehicleInfo(DriverGuidVehicleRequestDTO request);

  List<VehVehicleDTO> searchVehicleInfo(DriverGuidVehicleRequestDTO request);
  List<DriverGuideDTO> queryDriverInfo(DriverGuidDriverRequestDTO request);
//  List<DriverGuideDTO> searchDriverInfo(DriverGuidDriverRequestDTO request);
  List<Long> queryDrvIdByTransportGroups(DriverGuidDriverRequestDTO request);

  Boolean getGrayControl(DriverGuidDriverRequestDTO request);

  Boolean getGrayControl(Long supplierId);
}
