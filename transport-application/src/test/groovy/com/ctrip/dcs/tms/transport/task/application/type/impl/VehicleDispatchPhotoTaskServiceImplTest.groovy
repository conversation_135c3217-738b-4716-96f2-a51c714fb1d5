package com.ctrip.dcs.tms.transport.task.application.type.impl

import com.ctrip.dcs.tms.transport.api.model.IsNeedCreateTaskRequestType
import com.ctrip.dcs.tms.transport.api.model.SaveIsNeedCreateTaskRequestType
import com.ctrip.dcs.tms.transport.api.model.UploadVehicleDispatchPhotoListRequestType
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailDTO
import com.ctrip.dcs.tms.transport.api.model.VehicleDispatchPhotoDTO
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService
import com.ctrip.dcs.tms.transport.application.dto.SimpleDriverInfoDTO
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.NeedCreateTaskEnum
import com.ctrip.dcs.tms.transport.task.infrastructure.common.enums.TaskTypeEnum
import com.ctrip.dcs.tms.transport.task.infrastructure.common.qconfig.TaskVehicleDispatchBusinessQconfig
import com.ctrip.dcs.tms.transport.task.infrastructure.port.repository.impl.TaskVehicleDispatchPhotoApprovalRepository
import com.ctrip.dcs.tms.transport.task.infrastructure.value.UploadVehicleDispatchPhotoList
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhoto
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhotoItemConfig
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhotoTaskConfig
import com.ctrip.dcs.tms.transport.task.infrastructure.value.VehicleDispatchPhotoTaskQConfigData
import com.ctrip.igt.framework.common.result.Result
import spock.lang.Specification
import spock.lang.Unroll

class VehicleDispatchPhotoTaskServiceImplTest extends Specification {
    def testObj = new VehicleDispatchPhotoTaskServiceImpl()
    def vehicleQueryService = Mock(VehicleQueryService)
    def driverQueryService = Mock(DriverQueryService)
    def repository = Mock(TaskVehicleDispatchPhotoApprovalRepository)
    def taskBusinessQconfig = Mock(TaskVehicleDispatchBusinessQconfig)
    def tmsQmqProducerCommandService = Mock(TmsQmqProducerCommandService)

    def setup() {

        testObj.taskBusinessQconfig = taskBusinessQconfig
        testObj.vehicleQueryService = vehicleQueryService
        testObj.repository = repository
        testObj.driverQueryService = driverQueryService
        testObj.tmsQmqProducerCommandService = tmsQmqProducerCommandService
    }

    @Unroll
    def "queryTaskConfigTest"() {
        given: "设定相关方法入参"
        vehicleQueryService.queryVehicleDetail(_) >> Result.Builder.<VehicleDetailDTO>newResult().success().withData(new VehicleDetailDTO(vehicleId: 5044719L, vehicleTypeId: 1L)).build()
        taskBusinessQconfig.getVehicleTaskConfig() >> ["String": new VehicleDispatchPhotoTaskQConfigData(vehicleTypeIdList: [1L], photoList: [new VehicleDispatchPhotoItemConfig(photoTypeName: "photoTypeName", photoTypeDes: "photoTypeDes")])]

        when:
        def result = testObj.queryTaskConfig(taskConfigParam)

        then: "验证返回结果里属性值是否符合预期"
        result.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        taskConfigParam || expectedResult
        "{\"taskType\": \"vehicle_dispatch_photo\",\"taskConfigParam\": \"{\\\"vehicleId\\\":5044719}\"}"            || true
    }

    @Unroll
    def "isNeedCreateTaskTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverQueryService.querySimpleDriver(_) >> new SimpleDriverInfoDTO(cityId: 1L)
        taskBusinessQconfig.getVehicleDispatchGrayCityList() >> [1L]

        when:
        def result = testObj.isNeedCreateTask(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.data == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                || expectedResult
        new IsNeedCreateTaskRequestType(drvId: 1L) || NeedCreateTaskEnum.NEED
    }

    @Unroll
    def "saveIsNeedCreateTaskTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.saveIsNeedCreateTask(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.data == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                || expectedResult
        new SaveIsNeedCreateTaskRequestType(driverId: 1L, flag: 0) || true
    }

    @Unroll
    def "matchTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.match(taskTypeEnum)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        taskTypeEnum                        || expectedResult
        TaskTypeEnum.VEHICLE_DISPATCH_PHOTO || true
    }

    @Unroll
    def "uploadVehicleDispatchPhotoListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        vehicleQueryService.queryVehicleDetail(_) >> Result.Builder.<VehicleDetailDTO> newResult().success().withData(new VehicleDetailDTO()).build()
        driverQueryService.querySimpleDriver(_) >> new SimpleDriverInfoDTO()
        repository.insert(_) >> 0

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.saveIsNeedCreateTask(_) >> null
        spy.check(_, _, _) >> checkResult
        spy.convert(_, _, _) >> new UploadVehicleDispatchPhotoList()
        when:
        def result = spy.uploadVehicleDispatchPhotoList(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                             ||checkResult|| expectedResult
        new UploadVehicleDispatchPhotoListRequestType(vehicleId: 1L, drvId: 1L) ||Result.Builder.newResult().success().build()|| true
        new UploadVehicleDispatchPhotoListRequestType(vehicleId: 1L, drvId: 1L) ||Result.Builder.newResult().fail().build()|| false
    }

    @Unroll
    def "checkTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.check(requestType, driver, vehicleDetail)

        then: "验证返回结果里属性值是否符合预期"
        (result == null ? Result.Builder.newResult().fail().withCode(null).build() : result).code == expectedResult
        where: "表格方式验证多种分支调用场景"
        vehicleDetail | requestType                                                                               | driver                    || expectedResult
        Result.Builder.<VehicleDetailDTO>newResult().success().withData(new VehicleDetailDTO()).build()  | new UploadVehicleDispatchPhotoListRequestType(photoList: [new VehicleDispatchPhotoDTO()]) | null || "1515017"
        Result.Builder.<VehicleDetailDTO>newResult().success().withData(new VehicleDetailDTO()).build()   | new UploadVehicleDispatchPhotoListRequestType(photoList: [new VehicleDispatchPhotoDTO()]) | new SimpleDriverInfoDTO() || null
        Result.Builder.<VehicleDetailDTO>newResult().success().withData(null).build()   | new UploadVehicleDispatchPhotoListRequestType(photoList: [new VehicleDispatchPhotoDTO()]) | new SimpleDriverInfoDTO() || "1515016"
        Result.Builder.<VehicleDetailDTO>newResult().success().withData(new VehicleDetailDTO()).build()   | new UploadVehicleDispatchPhotoListRequestType(photoList: []) | new SimpleDriverInfoDTO() || "1515019"
    }

    @Unroll
    def "convertTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.convertPhotoList(_) >> [new VehicleDispatchPhoto()]
        when:
        def result = spy.convert(requestType, vehicle, driver)

        then: "验证返回结果里属性值是否符合预期"
        result.drvId == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                                                                                   | driver                                                            | vehicle                                                                   || expectedResult
        new UploadVehicleDispatchPhotoListRequestType(vehicleId: 1L, drvId: 1L, businessId: "businessId", photoList: [new VehicleDispatchPhotoDTO()]) | new SimpleDriverInfoDTO(driverName: "driverName", supplierId: 1L) | new VehicleDetailDTO(vehicleTypeId: 1L, vehicleLicense: "vehicleLicense") || 1L
    }

    @Unroll
    def "convertPhotoListTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.convertPhotoList(photoList)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        photoList                       || expectedResult
        [new VehicleDispatchPhotoDTO()] || [new VehicleDispatchPhoto()]
    }

    @Unroll
    def "convertPhotoTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.convertPhoto(photo)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        photo                                                           || expectedResult
        new VehicleDispatchPhotoDTO(photoType: 0, photoUrl: "photoUrl") || new VehicleDispatchPhoto(photoType: 0, photoUrl: "photoUrl")
    }

    @Unroll
    def "getConfigTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        taskBusinessQconfig.getVehicleTaskConfig() >> ["String": new VehicleDispatchPhotoTaskQConfigData(vehicleTypeIdList: [1L], photoList: [new VehicleDispatchPhotoItemConfig()])]

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getMultiLanguage(_) >> new VehicleDispatchPhotoTaskConfig()
        when:
        def result = spy.getConfig(vehicleTypeId)

        then: "验证返回结果里属性值是否符合预期"
        result.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        vehicleTypeId || expectedResult
        1L            || true
    }

    @Unroll
    def "getMultiLanguageTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getMultiLanguage(photoList)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        photoList                                                                                          || expectedResult
        [new VehicleDispatchPhotoItemConfig(photoTypeName: "photoTypeName", photoTypeDes: "photoTypeDes")] || new VehicleDispatchPhotoTaskConfig(photoList: [new VehicleDispatchPhotoItemConfig(photoTypeName: "photoTypeName", photoTypeDes: "photoTypeDes")])
    }
}
