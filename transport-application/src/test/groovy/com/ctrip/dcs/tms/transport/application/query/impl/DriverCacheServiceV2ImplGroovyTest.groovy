package com.ctrip.dcs.tms.transport.application.query.impl

import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO

import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TransportGroupBasePO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvInfoCacheDto
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import com.google.common.collect.Lists
import org.mockito.Spy
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> ZhangZhen
 * @create 2023/6/30 11:05
 */
class DriverCacheServiceV2ImplGroovyTest extends Specification {

    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def driverGroupRelationRepository = Mock(DriverGroupRelationRepository)
    def driverQueryService = Mock(DriverQueryService)
    def vehicleQueryService = Mock(VehicleQueryService)
    def transportGroupRepository = Mock(TransportGroupRepository)
    def tmsTransportQconfig = Mock(TmsTransportQconfig)
    def transportGroupQueryService = Mock(TransportGroupQueryService)

    def driverCacheServiceV2Impl = new DriverCacheServiceV2Impl(
            drvDrvierRepository: drvDrvierRepository,
            driverGroupRelationRepository: driverGroupRelationRepository,
            driverQueryService: driverQueryService,
            vehicleQueryService: vehicleQueryService,
            tmsTransportQconfig: tmsTransportQconfig,
            transportGroupRepository: transportGroupRepository,
            transportGroupQueryService: transportGroupQueryService
    )

    def "packageQuery history request Test"() {
        given:
        tmsTransportQconfig.getAllowTransportGroupOnlyMode() >> 3
        def req = new QueryDriverFromCacheParamDTO()
        req.setTransportGroupIds("1")
        def groups = new TspTransportGroupPO()
        groups.setTransportGroupId(1L)
        transportGroupRepository.queryTspTransportBaseByIds(_) >> Lists.newArrayList(groups)
        driverGroupRelationRepository.queryActiveDrvIdByTransportIdListNew(_, _) >> Lists.newArrayList(1, 2)
        def drvDriverPO = new DrvDriverPO()
        drvDriverPO.setVehicleId(1)
        drvDriverPO.setDrvId(1)
        drvDrvierRepository.queryDrvIdAndVehicleIdByCondition(_) >> Lists.newArrayList(drvDriverPO)
        when:
        def res = driverCacheServiceV2Impl.packageQuery(req)
        then:
        res != null
    }

    def "packageQuery new request Test"() {
        given:
        tmsTransportQconfig.getAllowTransportGroupOnlyMode() >> 1
        def req = new QueryDriverFromCacheParamDTO()
        req.setTransportGroupIds("1")
        def groups = new TspTransportGroupPO()
        groups.setTransportGroupId(1L)
        transportGroupRepository.queryTspTransportBaseByIds(_) >> Lists.newArrayList(groups)
        driverGroupRelationRepository.queryActiveDrvIdByTransportIdListNew(_, _) >> Lists.newArrayList(1, 2)
        def drvDriverPO = new DrvDriverPO()
        drvDriverPO.setVehicleId(1)
        drvDriverPO.setDrvId(1)
        drvDrvierRepository.queryDrvIdAndVehicleIdByCondition(_) >> Lists.newArrayList(drvDriverPO)
        when:
        def res = driverCacheServiceV2Impl.packageQuery(req)
        then:
        res != null
    }

    def "packageQuery new 1 request Test"() {
        given:
        tmsTransportQconfig.getAllowTransportGroupOnlyMode() >> 1
        def req = new QueryDriverFromCacheParamDTO()
        req.setTransportGroupIds("1")
        def groups = new TspTransportGroupPO()
        groups.setTransportGroupId(1L)
        transportGroupRepository.queryTspTransportBaseByIds(_) >> Lists.newArrayList(groups)
        driverGroupRelationRepository.queryActiveDrvIdByTransportIdListNew(_, _) >> Lists.newArrayList(1, 2)
        def drvDriverPO = new DrvDriverPO()
        drvDriverPO.setVehicleId(1)
        drvDriverPO.setDrvId(1)
        drvDrvierRepository.queryDrvIdAndVehicleIdByCondition(_) >> Lists.newArrayList(drvDriverPO)
        HashMap<Long, List<TransportGroupBasePO>> exceptResMap = new HashMap<>()
        exceptResMap.put(1, Lists.newArrayList(new TransportGroupBasePO(transportGroupId: 1)))
        def spy = Spy(driverCacheServiceV2Impl)
        spy.getGroupBasePOMap(_, _) >> Lists.newArrayList(exceptResMap)
        when:
        def res = driverCacheServiceV2Impl.packageQuery(req)
        then:
        res != null
    }

    def "isQueryTransportMode Test"() {
        given:
        tmsTransportQconfig.getAllowTransportGroupOnlyMode() >> expectModeConfig
        def req = new QueryDriverFromCacheParamDTO()
        req.setTransportGroupIds(expectTransportGroupIdList)
        req.setQueryTargetTransportMode(expectMode)
        when:
        def res = driverCacheServiceV2Impl.isQueryTransportMode(req)
        then:
        res == expectRes
        where:
        expectModeConfig | expectTransportGroupIdList | expectMode   || expectRes
        0                | ""                         | null          | Boolean.FALSE
        1                | ""                         | null          | Boolean.FALSE
        2                | ""                         | null          | Boolean.FALSE
        3                | ""                         | null          | Boolean.FALSE
        null             | ""                         | null          | Boolean.FALSE

        0                | "1"                        | null          | Boolean.FALSE
        1                | "1"                        | null          | Boolean.TRUE
        2                | "1"                        | null          | Boolean.FALSE
        3                | "1"                        | null          | Boolean.FALSE
        null             | "1"                        | null          | Boolean.FALSE

        0                | "1"                        | Boolean.FALSE | Boolean.FALSE
        1                | "1"                        | Boolean.FALSE | Boolean.TRUE
        2                | "1"                        | Boolean.FALSE | Boolean.FALSE
        3                | "1"                        | Boolean.FALSE | Boolean.FALSE
        null             | "1"                        | Boolean.FALSE | Boolean.FALSE

        0                | "1"                        | Boolean.TRUE  | Boolean.FALSE
        1                | "1"                        | Boolean.TRUE  | Boolean.TRUE
        2                | "1"                        | Boolean.TRUE  | Boolean.TRUE
        3                | "1"                        | Boolean.TRUE  | Boolean.FALSE
        null             | "1"                        | Boolean.TRUE  | Boolean.FALSE
    }


    def "clearCache"() {
        given:
        tmsTransportQconfig.getRedisTtransitionSwitch() >> false
        when:
        driverCacheServiceV2Impl.clearCache(key)
        then:
        assert res
        where :
        key | res
        "11"| true
    }


    def "clearCache1"() {
        given:
        tmsTransportQconfig.getRedisTtransitionSwitch() >> true
        when:
        driverCacheServiceV2Impl.clearCache(key)
        then:
        assert res
        where :
        key | res
        "11"| true
    }

    @Unroll
    def "getGroupBasePOMapTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        transportGroupQueryService.queryDriverGroupRelationPOCache(_, _) >> [new TransportGroupBasePO(drvId: 1L)]

        when:
        def result = driverCacheServiceV2Impl.getGroupBasePOMap(drvIdList, transportIdList)

        then: "验证返回结果里属性值是否符合预期"
        result.get(1L).get(0).getDrvId() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvIdList | transportIdList || expectedResult
        [1L]      | [1L]            || 1L
    }

    @Unroll
    def "assembleTest"() {
        given: "设定相关方法入参"
        when:
        def result = driverCacheServiceV2Impl.assemble(cacheDto, info)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        cacheDto                                                                                   | info                                                                                                                        || expectedResult
        new DrvInfoCacheDto(coopMode: 0, broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: 0) | new com.ctrip.dcs.tms.transport.api.model.DriverInfo(broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: 0, coopMode: 0) || null
        new DrvInfoCacheDto(coopMode: null, broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: 0) | new com.ctrip.dcs.tms.transport.api.model.DriverInfo(broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: 0, coopMode: 0) || null
        new DrvInfoCacheDto(coopMode: 0, broadcast: null, isSendWorkPeriod: 0, compatibleCoopMode: 0) | new com.ctrip.dcs.tms.transport.api.model.DriverInfo(broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: 0, coopMode: 0) || null
        new DrvInfoCacheDto(coopMode: 0, broadcast: 0, isSendWorkPeriod: null, compatibleCoopMode: 0) | new com.ctrip.dcs.tms.transport.api.model.DriverInfo(broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: 0, coopMode: 0) || null
        new DrvInfoCacheDto(coopMode: 0, broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: null) | new com.ctrip.dcs.tms.transport.api.model.DriverInfo(broadcast: 0, isSendWorkPeriod: 0, compatibleCoopMode: 0, coopMode: 0) || null
    }

}
