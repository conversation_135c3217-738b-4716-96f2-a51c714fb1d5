package com.ctrip.dcs.tms.transport.application.query.impl

import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.application.query.VehicleQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DrvCacheDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DriverGroupRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import spock.lang.Specification
import spock.lang.Unroll

class DriverCacheServiceV2ImplSpockTest extends Specification {
    def testObj = new DriverCacheServiceV2Impl()
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def driverGroupRelationRepository = Mock(DriverGroupRelationRepository)
    def driverQueryService = Mock(DriverQueryService)
    def vehicleQueryService = Mock(VehicleQueryService)
    def transportGroupRepository = Mock(TransportGroupRepository)
    def tmsTransportQconfig = Mock(TmsTransportQconfig)
    def transportGroupQueryService = Mock(TransportGroupQueryService)

    def setup() {

        testObj.transportGroupRepository = transportGroupRepository
        testObj.tmsTransportQconfig = tmsTransportQconfig
        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.transportGroupQueryService = transportGroupQueryService
        testObj.driverGroupRelationRepository = driverGroupRelationRepository
        testObj.vehicleQueryService = vehicleQueryService
        testObj.driverQueryService = driverQueryService
    }

    @Unroll
    def "getFromCacheAndFilterByConditionTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverQueryService.queryDrvCacheList(_) >> [new DrvCacheDTO(driverId: 1L, driverName: "driverName", status: 0, coopMode: 1, carId: 1L)]

        when:
        def result = testObj.getFromCacheAndFilterByCondition(req, longSet)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        req                                                                                                                                                       | longSet    || expectedResult
        new com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverConditionDTO(driverName: "driverName", status: 0, coopModes: "1") | [1L] as Set<Long> || [(1L): 1L]
        new com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverConditionDTO(driverName: null, status: 1, coopModes: "1") | [1L] as Set<Long> || [:]
        new com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverConditionDTO(driverName: "driverName1", status: 1, coopModes: "1") | [1L] as Set<Long> || [:]
        new com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverConditionDTO(driverName: "driverName", status: null, coopModes: "2") | [1L] as Set<Long> || [:]
        new com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverConditionDTO(driverName: "driverName", status: 0, coopModes: "1") | [1L] as Set<Long> || [(1L): 1L]
    }
}
