package com.ctrip.dcs.tms.transport.application.command.impl

import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverAccountRegisterResultDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TransportCommonQconfig
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.igt.framework.common.result.Result
import spock.lang.Specification
import spock.lang.Unroll

class DriverAccountManagementHelperSpockTest extends Specification {
    def testObj = new DriverAccountManagementHelper()
    def driverDomainServiceProxy = Mock(DriverDomainServiceProxy)
    def tmsQmqProducerCommandService = Mock(TmsQmqProducerCommandService)
    def commonQconfig = Mock(TransportCommonQconfig)

    def setup() {

        testObj.commonQconfig = commonQconfig
        testObj.tmsQmqProducerCommandService = tmsQmqProducerCommandService
        testObj.driverDomainServiceProxy = driverDomainServiceProxy
    }

    @Unroll
    def "registerDriverUserAccountTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainServiceProxy.registerNewAccount(_) >> new DriverAccountRegisterResultDTO(drvId: 1L, uid: "uid", needRegisterAccount: true, registerSuccess: true, errorCode: "errorCode", errorMsg: "errorMsg")
        commonQconfig.getRegisterDriverAccountWithNewInterfaceSwitch() >> accountSwitch

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.isNeedCallDriverDomain(_) >> true
        when:
        def result = spy.registerDriverUserAccount(drvDriverPO)

        then: "验证返回结果里属性值是否符合预期"
        result.toString() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDriverPO                                                                  || expectedResult ||accountSwitch
        new DrvDriverPO(drvId: 1L, drvPhone: "drvPhone", email: "email") || new DriverAccountRegisterResultDTO(drvId: 1L, uid: "uid", needRegisterAccount: true, registerSuccess: true, errorCode: "errorCode", errorMsg: "errorMsg").toString() || "ON"
        new DrvDriverPO(uid: "uid", drvId: 1L, drvPhone: "drvPhone", email: "email") || new DriverAccountRegisterResultDTO(drvId: 1L, uid: null, needRegisterAccount: false, registerSuccess: false).toString() || "ON"
        new DrvDriverPO(uid: "uid", drvId: 1L, drvPhone: "drvPhone", email: "email") || new DriverAccountRegisterResultDTO(drvId: 1L, uid: null, needRegisterAccount: false, registerSuccess: false).toString() || "OFF"
    }

    @Unroll
    def "registerDriverUserAccountRetureFailedTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainServiceProxy.registerNewAccount(_) >> driverAccountRegisterResultDTO
        commonQconfig.getRegisterDriverAccountWithNewInterfaceSwitch() >> accountSwitch
        driverDomainServiceProxy.registerAccountGetUid(_ as Long,_ as String,_ as String,_ as String,_ as String) >> driverAccountRegisterResultDTO2
        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.isNeedCallDriverDomain(_) >> true
        when:
        def result = spy.registerDriverUserAccount(drvDriverPO)

        then: "验证返回结果里属性值是否符合预期"
        result.toString() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDriverPO                                                                  ||accountSwitch||driverAccountRegisterResultDTO||driverAccountRegisterResultDTO2|| expectedResult
        new DrvDriverPO(uid: "uid", drvId: 1L, drvPhone: "drvPhone", email: "email") ||"ON"||new DriverAccountRegisterResultDTO(drvId: 1L, uid: "uid", needRegisterAccount: true, registerSuccess: false, errorCode: "errorCode", errorMsg: "errorMsg") ||null||new DriverAccountRegisterResultDTO(drvId: 1L, uid: null, needRegisterAccount: false, registerSuccess: false).toString()
        new DrvDriverPO(uid: "uid", drvId: 1L, drvPhone: "drvPhone", email: "email") ||"ON"||new DriverAccountRegisterResultDTO(drvId: 1L, uid: "", needRegisterAccount: true, registerSuccess: false, errorCode: "errorCode", errorMsg: "errorMsg") || null||new DriverAccountRegisterResultDTO(drvId: 1L, uid: null, needRegisterAccount: false, registerSuccess: false).toString()
//        new DrvDriverPO(uid: null, drvId: 1L, drvPhone: "drvPhone", email: "email") ||"OFF"||new DriverAccountRegisterResultDTO(drvId: 1L, uid: "", needRegisterAccount: true, registerSuccess: false, errorCode: "errorCode", errorMsg: "errorMsg") || new DriverAccountRegisterResultDTO(drvId: 1L, uid: "", needRegisterAccount: true, registerSuccess: false, errorCode: "errorCode", errorMsg: "errorMsg")||new DriverAccountRegisterResultDTO(drvId: 1L, uid: null, needRegisterAccount: false, registerSuccess: false).toString()
    }


    @Unroll
    def "isInGrayProcessTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        commonQconfig.getRegisterDriverAccountSwitch() >> "ON"

        when:
        def result = testObj.isNeedCallDriverDomain(drvDriverPO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDriverPO                            || expectedResult
        new DrvDriverPO(drvId: 1L, cityId: 1L,uid:"") || true
    }

    @Unroll
    def "isInCityGrayProcessTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        commonQconfig.getRegisterDriverAccountSwitch() >> "getRegisterDriverAccountSwitchResponse"
        driverDomainServiceProxy.queryAccountProcess(_,_) >> inGrayProcess

        when:
        def result = testObj.isInCityGrayProcess(drvDriverPO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDriverPO                            ||inGrayProcess || expectedResult
        new DrvDriverPO(drvId: 1L, cityId: 1L,uid:"uid") || true|| true
        new DrvDriverPO(drvId: 1L, cityId: 1L,uid:"uid") || false|| false
    }

    @Unroll
    def "batchRegisterDriverAccountWhenNotExistTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.registerDriverUserAccount(_) >> new DriverAccountRegisterResultDTO()
        when:
        def result = spy.batchRegisterDriverAccountWhenNotExist(driverPOList)

        then: "验证返回结果里属性值是否符合预期"
        result.toString() == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverPOList        || expectedResult
        [new DrvDriverPO()] || [new DriverAccountRegisterResultDTO()].toString()
    }

    @Unroll
    def "batchRegisterDriverAccountWhenNotExistTest2"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.registerDriverUserAccount(_) >> new DriverAccountRegisterResultDTO(registerSuccess: false, needRegisterAccount: true)
        when:
        def result = spy.batchRegisterDriverAccountWhenNotExist(driverPOList)

        then: "验证返回结果里属性值是否符合预期"
        result.toString() == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverPOList        || expectedResult
        [new DrvDriverPO()] || [new DriverAccountRegisterResultDTO(registerSuccess: false, needRegisterAccount: true)].toString()
    }

    @Unroll
    def "updateAccountTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainServiceProxy.updateAccount(_) >> driverResult

        when:
        def result = testObj.updateAccount(drvDriverPO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDriverPO       ||driverResult|| expectedResult
        new DrvDriverPO() || Result.Builder.<Void>newResult().success().build()||null
    }

    @Unroll
    def "updateAccountThrowExceptionTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverDomainServiceProxy.updateAccount(_) >> driverResult

        when:
        def result = testObj.updateAccount(drvDriverPO)

        then: "验证返回结果里属性值是否符合预期"
        def ex = thrown(BizException);
        ex.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvDriverPO       ||driverResult|| expectedResult
        new DrvDriverPO() || Result.Builder.<Void>newResult().fail().withCode("error").build()||"1515012"
    }
}
