package com.ctrip.dcs.tms.transport.application.query.impl

import com.ctrip.dcs.tms.transport.api.model.QueryDrvVehRecruitingModRrdSOADTO
import com.ctrip.dcs.tms.transport.application.query.CertificateCheckQueryService
import com.ctrip.dcs.tms.transport.application.query.DrvVehRecruitingQueryService
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvRecruitingRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsApproveStepRecordRespository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsRecruitingApproveStepChildRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsRecruitingApproveStepRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRecruitingRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.ctrip.igt.framework.common.result.Result
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

class RecruitingQueryServiceImplSpockTest extends Specification {
    def testObj = new RecruitingQueryServiceImpl()
    def vehicleRecruitingRepository = Mock(VehicleRecruitingRepository)
    def drvRecruitingRepository = Mock(DrvRecruitingRepository)
    def vehicleRepository = Mock(VehicleRepository)
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def enumRepository = Mock(EnumRepository)
    def approvalProcessAuthQconfig = Mock(ApprovalProcessAuthQconfig)
    def productionLineUtil = Mock(ProductionLineUtil)
    def stepRepository = Mock(TmsRecruitingApproveStepRepository)
    def childRepository = Mock(TmsRecruitingApproveStepChildRepository)
    def checkQueryService = Mock(CertificateCheckQueryService)
    def recordRespository = Mock(TmsApproveStepRecordRespository)
    def qconfig = Mock(TmsTransportQconfig)
    def drvVehRecruitingQueryService = Mock(DrvVehRecruitingQueryService)

    def setup() {

        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.stepRepository = stepRepository
        testObj.qconfig = qconfig
        testObj.checkQueryService = checkQueryService
        testObj.drvRecruitingRepository = drvRecruitingRepository
        testObj.vehicleRepository = vehicleRepository
        testObj.childRepository = childRepository
        testObj.recordRespository = recordRespository
        testObj.drvVehRecruitingQueryService = drvVehRecruitingQueryService
        testObj.productionLineUtil = productionLineUtil
        testObj.approvalProcessAuthQconfig = approvalProcessAuthQconfig
        testObj.vehicleRecruitingRepository = vehicleRecruitingRepository
        testObj.enumRepository = enumRepository
    }

    @Unroll
    def "getRemarkTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvVehRecruitingQueryService.queryDrvVehRecruitingModRrd(_ as List, _ as Integer) >> Result.Builder.<List<QueryDrvVehRecruitingModRrdSOADTO>>newResult().success() .withData(Lists.newArrayList(new QueryDrvVehRecruitingModRrdSOADTO())) .build()

        when:
        def result = testObj.getRemark(recruitingSOAResponseDTOList, recruitingType)

        then: "验证返回结果里属性值是否符合预期"
        result.get(0).recruitingId == expectedResult
        where: "表格方式验证多种分支调用场景"
        recruitingSOAResponseDTOList                                                                                                                                         | recruitingType || expectedResult
        [new com.ctrip.dcs.tms.transport.api.model.RecruitingSOAResponseDTO(recruitingId: 1L, approveStatus: 0, vehRecruitingId: 1L, drvRecruitingId: 1L, remark: "remark")] | 0              || 1L
    }
}
