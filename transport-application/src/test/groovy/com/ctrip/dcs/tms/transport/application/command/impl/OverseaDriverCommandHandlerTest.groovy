package com.ctrip.dcs.tms.transport.application.command.impl

import com.ctrip.dcs.tms.transport.application.command.DriverCommandService
import com.ctrip.dcs.tms.transport.application.dto.DrvAddContext
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.PhoneDTO
import com.ctrip.dcs.tms.transport.infrastructure.gateway.TmsDrvInactiveReasonGateway
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService
import spock.lang.Specification
import spock.lang.Unroll

class OverseaDriverCommandHandlerTest extends Specification {
    def testObj = new OverseaDriverCommandHandler()
    def ivrCallService = Mock(IVRCallService)
    def tmsQmqProducer = Mock(TmsQmqProducer)
    def tmsDrvInactiveReasonGateway = Mock(TmsDrvInactiveReasonGateway)
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def driverCommandService = Mock(DriverCommandService)

    def setup() {

        testObj.ivrCallService = ivrCallService
        testObj.tmsQmqProducer = tmsQmqProducer
        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.driverCommandService = driverCommandService
        testObj.tmsDrvInactiveReasonGateway = tmsDrvInactiveReasonGateway
    }

    @Unroll
    def "addDrvTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        ivrCallService.isPhoneVerified(_) >> [1L]

        when:
        def result = testObj.afterAdd(context)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        context                                                                                                                                        || expectedResult
        new DrvAddContext(drvDriverPO: new DrvDriverPO(drvId: 1L, cityId: 1L, igtCode: "igtCode", drvPhone: "drvPhone", drvFrom: 0, internalScope: 0)) || "200"
        new DrvAddContext(drvDriverPO: new DrvDriverPO(drvId: 1L, cityId: 1L, igtCode: "igtCode", drvPhone: "drvPhone", drvFrom: 0, internalScope: 1)) || "200"
        new DrvAddContext(drvDriverPO: new DrvDriverPO(drvId: 1L, cityId: 1L, igtCode: "igtCode", drvPhone: "drvPhone", drvFrom: 1, internalScope: 1)) || "200"
        new DrvAddContext(drvDriverPO: new DrvDriverPO(drvId: 1L, cityId: 1L, igtCode: "igtCode", drvPhone: "drvPhone", drvFrom: 1, internalScope: 0)) || "200"
    }

    @Unroll
    def "supportTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.support(internalAreaScope)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        internalAreaScope || expectedResult
        1                 || Boolean.TRUE
        0                 || Boolean.FALSE
    }

    @Unroll
    def "afterIvrCallTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        ivrCallService.isPhoneVerified(_) >> phoneVerified
        tmsDrvInactiveReasonGateway.enableActiveAfterDeleteReasonByCode(_, _, _) >> true
        drvDrvierRepository.queryOneDrvByPhone(_) >> new DrvDriverPO(drvId: 1L, igtCode: "igtCode", drvStatus: 0)
        driverCommandService.updateDrvStatus(_, _, _) >> null

        when:
        def result = testObj.afterIvrCall(phoneDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        phoneDTO  || phoneVerified                                                                                     || expectedResult
        new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode", modifyUser: "modifyUser")|| true || null
        new PhoneDTO(mobilePhone: "mobilePhone", countryCode: "countryCode", modifyUser: "modifyUser") || false || null
    }
}
