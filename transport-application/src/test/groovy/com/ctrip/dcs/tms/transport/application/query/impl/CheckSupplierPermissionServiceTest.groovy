package com.ctrip.dcs.tms.transport.application.query.impl

import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.SupplierTransportManagementPermissionQconfig
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.scm.sdk.domain.serviceprovider.ServiceProvider
import com.ctrip.igt.framework.common.result.Result
import com.google.common.collect.Maps
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification

class CheckSupplierPermissionServiceTest extends Specification{
    def "check"(){
        given:
        def transportGroupQueryService = Mock(TransportGroupQueryService)
        def enumRepository = Mock(EnumRepository)
        def overseasQconfig = Mock(OverseasQconfig)
        def permissionService = new CheckSupplierPermissionService(transportGroupQueryService:transportGroupQueryService,enumRepository:enumRepository,overseasQconfig:overseasQconfig)
        def groupResult = Mock(Result)
        when:
        transportGroupQueryService.queryTransportGroupDetail(_) >> groupResult
        groupResult.isSuccess() >> success
        groupResult.getData() >> group
        def result = permissionService.check(1L)
        then:
        result == checkResult
        where:
        success | group || checkResult
        false | null || true
        true | null || true
        true | new TspTransportGroupPO() || true
    }

    def "checkByContractId"(){
        given:
        def transportGroupQueryService = Mock(TransportGroupQueryService)
        def enumRepository = Mock(EnumRepository)
        def overseasQconfig = Mock(OverseasQconfig)
        def supplierTransportManagementPermissionQconfig = Mock(SupplierTransportManagementPermissionQconfig)
        def productionLineUtil = Mock(ProductionLineUtil)
        def permissionService = new CheckSupplierPermissionService(productionLineUtil: productionLineUtil,supplierTransportManagementPermissionQconfig:supplierTransportManagementPermissionQconfig,transportGroupQueryService:transportGroupQueryService,enumRepository:enumRepository,overseasQconfig:overseasQconfig)
        def provider = ServiceProvider.newBuilder().withId(1L).build()
        when:
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","2");
        params.put("accountType","1");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        enumRepository.getServiceProviderByContractId(_) >> provider
        overseasQconfig.getFiltrationServiceprovideridList() >> providerIds
        productionLineUtil.getCategoryCode(_) >> "day"
        supplierTransportManagementPermissionQconfig.hasPermission(_,_,_) >> true
        def result = permissionService.checkByContractId(1L, 3, 1L)
        then:
        result == checkResult
        where:
        providerIds || checkResult
        Arrays.asList(1L) || true
        Arrays.asList(2L) || true
    }
}
