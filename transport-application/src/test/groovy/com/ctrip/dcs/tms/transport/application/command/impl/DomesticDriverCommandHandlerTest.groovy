package com.ctrip.dcs.tms.transport.application.command.impl

import com.ctrip.dcs.tms.transport.application.dto.DrvAddContext
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil
import spock.lang.Specification
import spock.lang.Unroll

class DomesticDriverCommandHandlerTest extends Specification {
    def testObj = new DomesticDriverCommandHandler()

    @Unroll
    def "addDrvTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.afterAdd(context)

        then: "验证返回结果里属性值是否符合预期"
        result.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        context             || expectedResult
        new DrvAddContext() || true
    }

    @Unroll
    def "supportTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.support(internalAreaScope)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        internalAreaScope || expectedResult
        0                 || Boolean.TRUE
    }
}
