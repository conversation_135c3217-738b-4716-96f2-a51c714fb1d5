package com.ctrip.dcs.tms.transport.task.application.command.impl

import com.ctrip.dcs.tms.transport.api.model.UploadVehicleDispatchPhotoListRequestType
import com.ctrip.dcs.tms.transport.task.application.type.VehicleDispatchPhotoTaskService
import spock.lang.Specification
import spock.lang.Unroll

class VehicleDispatchPhotoCommandServiceImplTest extends Specification {
    def testObj = new VehicleDispatchPhotoCommandServiceImpl()
    def vehicleDispatchPhotoTaskService = Mock(VehicleDispatchPhotoTaskService)

    def setup() {

        testObj.vehicleDispatchPhotoTaskService = vehicleDispatchPhotoTaskService
    }

    @Unroll
    def "uploadVehicleDispatchPhotoListTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        vehicleDispatchPhotoTaskService.uploadVehicleDispatchPhotoList(_) >> null

        when:
        def result = testObj.uploadVehicleDispatchPhotoList(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                     || expectedResult
        new UploadVehicleDispatchPhotoListRequestType() || null
    }
}
