package com.ctrip.dcs.tms.transport.application.command.impl

import com.ctrip.dcs.tms.transport.application.command.DriverCommandService
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService
import com.ctrip.dcs.tms.transport.application.helper.ShortTransportGroupHelper
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService
import com.ctrip.dcs.tms.transport.application.query.TmsPmsproductQueryService
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.DriverPointsQueryProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupDriverApplicationRecordRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportTrackRecordRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuArearRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.WorkShiftRepository
import com.ctriposs.baiji.rpc.server.util.ResponseUtil
import spock.lang.Specification
import spock.lang.Unroll

class TransportGroupCommandServiceImplSpockTest extends Specification {
    def testObj = new TransportGroupCommandServiceImpl()
    def tmsPmsproductQueryService = Mock(TmsPmsproductQueryService)
    def transportGroupRepository = Mock(TransportGroupRepository)
    def tspTransportGroupDriverRelationRepository = Mock(TspTransportGroupDriverRelationRepository)
    def tspTransportGroupSkuRelationRepository = Mock(TspTransportGroupSkuRelationRepository)
    def tspTransportGroupSkuArearRelationRepository = Mock(TspTransportGroupSkuArearRelationRepository)
    def tmsQmqProducerCommandService = Mock(TmsQmqProducerCommandService)
    def transportGroupQueryService = Mock(TransportGroupQueryService)
    def drvDrvierRepository = Mock(DrvDrvierRepository)
    def workShiftRepository = Mock(WorkShiftRepository)
    def driverPointsQueryProxy = Mock(DriverPointsQueryProxy)
    def driverCommandService = Mock(DriverCommandService)
    def qconfig = Mock(TmsTransportQconfig)
    def productionLineUtil = Mock(ProductionLineUtil)
    def transportTrackRecordRepository = Mock(TransportTrackRecordRepository)
    def driverQueryService = Mock(DriverQueryService)
    def enumRepository = Mock(EnumRepository)
    def vehicleRepository = Mock(VehicleRepository)
    def driverGuideProxy = Mock(DriverGuideProxy)
    def transportGroupDriverApplicationRecordRepository = Mock(TransportGroupDriverApplicationRecordRepository)
    def shortTransportgroupHelper = Mock(ShortTransportGroupHelper)
    def mobileHelper = Mock(MobileHelper)

    def setup() {

        testObj.tspTransportGroupDriverRelationRepository = tspTransportGroupDriverRelationRepository
        testObj.transportTrackRecordRepository = transportTrackRecordRepository
        testObj.drvDrvierRepository = drvDrvierRepository
        testObj.transportGroupQueryService = transportGroupQueryService
        testObj.transportGroupDriverApplicationRecordRepository = transportGroupDriverApplicationRecordRepository
        testObj.tspTransportGroupSkuRelationRepository = tspTransportGroupSkuRelationRepository
        testObj.qconfig = qconfig
        testObj.tspTransportGroupSkuArearRelationRepository = tspTransportGroupSkuArearRelationRepository
        testObj.transportGroupRepository = transportGroupRepository
        testObj.vehicleRepository = vehicleRepository
        testObj.driverCommandService = driverCommandService
        testObj.workShiftRepository = workShiftRepository
        testObj.driverPointsQueryProxy = driverPointsQueryProxy
        testObj.tmsQmqProducerCommandService = tmsQmqProducerCommandService
        testObj.tmsPmsproductQueryService = tmsPmsproductQueryService
        testObj.productionLineUtil = productionLineUtil
        testObj.driverGuideProxy = driverGuideProxy
        testObj.enumRepository = enumRepository
        testObj.shortTransportgroupHelper = shortTransportgroupHelper
        testObj.driverQueryService = driverQueryService
        testObj.mobileHelper = mobileHelper
    }

    @Unroll
    def "commonCheckTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        enumRepository.getAreaScope(_) >> 0
        mobileHelper.isMobileVerified(_, _, _, _) >> isMobileVerified

        when:
        def result = testObj.commonCheck(transportGroupDetail, transportGroupPO)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        isMobileVerified|transportGroupDetail                                                                                                                                                                                                                            | transportGroupPO                                                                                                                                                                                                                                || expectedResult
        ResponseResultUtil.success() |new TspTransportGroupPO(standbyIgtCode: "standbyIgtCode", standbyPhone: "standbyPhone", dispatcherPhone: "dispatcherPhone", igtCode: "igtCode", pointCityId: 1L) | new TspTransportGroupPO(standbyIgtCode: "standbyIgtCode", standbyPhone: "standbyPhone", dispatcherPhone: "dispatcherPhone", igtCode: "igtCode", pointCityId: 1L) || "200"
        ResponseResultUtil.failed("500", "Error")|new TspTransportGroupPO(standbyIgtCode: "standbyIgtCode", standbyPhone: "standbyPhone", dispatcherPhone: "dispatcherPhone", igtCode: "igtCode", pointCityId: 1L) | new TspTransportGroupPO(standbyIgtCode: "standbyIgtCode", standbyPhone: "standbyPhone", dispatcherPhone: "dispatcherPhone", igtCode: "igtCode", pointCityId: 1L) || "500"
    }
}
