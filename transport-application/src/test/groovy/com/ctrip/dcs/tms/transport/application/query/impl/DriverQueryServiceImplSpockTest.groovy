package com.ctrip.dcs.tms.transport.application.query.impl

import com.ctrip.dcs.tms.transport.api.model.DrvDriverSOAResponseDTO
import com.ctrip.dcs.tms.transport.api.model.DrvPreCheckRequestType
import com.ctrip.dcs.tms.transport.application.command.CommonCommandService
import com.ctrip.dcs.tms.transport.application.convert.DrvResourceConverter
import com.ctrip.dcs.tms.transport.application.query.CertificateCheckQueryService
import com.ctrip.dcs.tms.transport.application.query.TransportGroupQueryService
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverAccountDetail
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverDomainServiceProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.InfrastructureServiceClientProxy
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverLeavePO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvFreezeRecordPO
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TmsDrvInactiveReasonPO
import com.ctrip.dcs.tms.transport.infrastructure.common.config.DistributedLockConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.config.TempDispatchDateConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.GlobalApplyDriverLeaveDurationVO
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.TransportGroupDriverApplyProcessDTO
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ApprovalProcessAuthQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.BusinessQConfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SensitiveDataControl
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDispatchRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDriverLeaveRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvEpidemicPreventionControlInfoRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvFreezeRecordRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvVehLegendRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.OverseasOcrRecordRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvFreezeRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TmsDrvInactiveReasonRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.VehicleRepository
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService
import com.ctrip.igt.framework.common.result.Result
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants
import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

class DriverQueryServiceImplSpockTest extends Specification {
    def testObj = new DriverQueryServiceImpl()
    def repository = Mock(DrvDrvierRepository)
    def enumRepository = Mock(EnumRepository)
    def relationRepository = Mock(TspTransportGroupDriverRelationRepository)
    def vehicleRepository = Mock(VehicleRepository)
    def groupRepository = Mock(TransportGroupRepository)
    def authQconfig = Mock(ApprovalProcessAuthQconfig)
    def checkQueryService = Mock(CertificateCheckQueryService)
    def transportQconfig = Mock(TmsTransportQconfig)
    def productionLineUtil = Mock(ProductionLineUtil)
    def tmsDrvFreezeRepository = Mock(TmsDrvFreezeRepository)
    def leaveRepository = Mock(DrvDriverLeaveRepository)
    def commonCommandService = Mock(CommonCommandService)
    def drvEpidemicPreventionControlInfoRepository = Mock(DrvEpidemicPreventionControlInfoRepository)
    def dict = Mock(Map)
    def drvResourceConverter = Mock(DrvResourceConverter)
    def control = Mock(SensitiveDataControl)
    def transportGroupQueryService = Mock(TransportGroupQueryService)
    def drvDispatchRelationRepository = Mock(DrvDispatchRelationRepository)
    def drvFreezeRecordRepository = Mock(DrvFreezeRecordRepository)
    def overseasOcrRecordRepository = Mock(OverseasOcrRecordRepository)
    def overseasQconfig = Mock(OverseasQconfig)
    def drvVehLegendRepository = Mock(DrvVehLegendRepository)
    def infrastructureServiceClientProxy = Mock(InfrastructureServiceClientProxy)
    def lockConfig = Mock(DistributedLockConfig)
    def drvInactiveReasonRepository = Mock(TmsDrvInactiveReasonRepository)
    def driverDomainServiceProxy = Mock(DriverDomainServiceProxy)
    def mobileHelper = Mock(MobileHelper)
    def businessQConfig = Mock(BusinessQConfig)
    def tempDispatchDateConfig = Mock(TempDispatchDateConfig)
    def ivrCallService = Mock(IVRCallService)

    def setup() {

        testObj.transportGroupQueryService = transportGroupQueryService
        testObj.repository = repository
        testObj.leaveRepository = leaveRepository
        testObj.commonCommandService = commonCommandService
        testObj.checkQueryService = checkQueryService
        testObj.overseasOcrRecordRepository = overseasOcrRecordRepository
        testObj.lockConfig = lockConfig
        testObj.dict = dict
        testObj.productionLineUtil = productionLineUtil
        testObj.drvFreezeRecordRepository = drvFreezeRecordRepository
        testObj.groupRepository = groupRepository
        testObj.enumRepository = enumRepository
        testObj.drvResourceConverter = drvResourceConverter
        testObj.drvDispatchRelationRepository = drvDispatchRelationRepository
        testObj.authQconfig = authQconfig
        testObj.infrastructureServiceClientProxy = infrastructureServiceClientProxy
        testObj.drvEpidemicPreventionControlInfoRepository = drvEpidemicPreventionControlInfoRepository
        testObj.control = control
        testObj.relationRepository = relationRepository
        testObj.vehicleRepository = vehicleRepository
        testObj.overseasQconfig = overseasQconfig
        testObj.tmsDrvFreezeRepository = tmsDrvFreezeRepository
        testObj.drvVehLegendRepository = drvVehLegendRepository
        testObj.transportQconfig = transportQconfig
        testObj.drvInactiveReasonRepository = drvInactiveReasonRepository
        testObj.driverDomainServiceProxy = driverDomainServiceProxy
        testObj.mobileHelper = mobileHelper
        testObj.businessQConfig = businessQConfig
        testObj.tempDispatchDateConfig = tempDispatchDateConfig
        testObj.ivrCallService = ivrCallService
    }

    @spock.lang.Unroll
    def "buildIvrVerifyInfoTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        businessQConfig.isVerifyPhone(_) >> isVerifyPhone
        ivrCallService.isPhoneVerified(_) >> ["String": Boolean.TRUE]

        when:
        def result = testObj.buildIvrVerifyInfo(collect)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        isVerifyPhone|collect                                                                                                                                                        || expectedResult
        true|[new DrvDriverSOAResponseDTO(drvPhone: "drvPhone", areaScope: 0, phoneNumberVerified: Boolean.TRUE, igtCode: "igtCode")] || null
        true|[new DrvDriverSOAResponseDTO(drvPhone: "", areaScope: 0, phoneNumberVerified: Boolean.TRUE, igtCode: "igtCode")] || null
        false|[new DrvDriverSOAResponseDTO(drvPhone: "drvPhone", areaScope: 0, phoneNumberVerified: Boolean.TRUE, igtCode: "igtCode")] || null
    }

    @Unroll
    def "querySimpleDriverTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        repository.queryByPk(_) >> new DrvDriverPO(drvId: 1L, drvName: "drvName", supplierId: 1L, cityId: 1L, drvIdcard: "drvIdcard", drvLicenseNumber: "drvLicenseNumber", drvLicenseName: "drvLicenseName")

        when:
        def result = testObj.querySimpleDriver(driverId)

        then: "验证返回结果里属性值是否符合预期"
        result.driverId == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverId || expectedResult
        1L       || 1L
    }

    @Unroll
    def "calculationGlobalFreezeDurationTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvFreezeRecordRepository.queryDrvFreezeRecordList(_) >> [new DrvFreezeRecordPO()]

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.calculationDrvFreeze(_, _, _, _, _, _) >> [(1L): 1L]
        spy.initDrvDurationMap(_) >> [(1L): 1L]
        when:
        def result = spy.calculationGlobalFreezeDuration(driverLeaveDurationVO, applyProcessDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverLeaveDurationVO                                                                                                                                                                                                                                                                                                                                         | applyProcessDTO                                                                                    || expectedResult
        new GlobalApplyDriverLeaveDurationVO(drvList: [1L], beginBeforeCheckDateTime: new Timestamp(System.currentTimeMillis()), endBeforeCheckDateTime: new Timestamp(System.currentTimeMillis()), beginAfterCheckDateTime: new Timestamp(System.currentTimeMillis()), endAfterCheckDateTime: new Timestamp(System.currentTimeMillis()), durationA: 0, durationB: 0) | new TransportGroupDriverApplyProcessDTO(rule: TmsTransportConstant.TransportDriverApplyRuleEnum.A) || [(1L): Boolean.TRUE]
    }

    @Unroll
    def "calculationDrvLeaveTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.initDrvDurationMap(_) >> [(1L): 1L]
        spy.calculateLeaveHour(_, _) >> 0L
        spy.calculationDrvFrezzeHour(_, _, _, _, _, _) >> [(1L): 1L]
        when:
        def result = spy.calculationDrvLeave(drvIds, drvDriverLeavePOS, beginCheckDateTime, endCheckDateTime, drvCalculateBeginTimeMap, applyProcessDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvIds | beginCheckDateTime                        | endCheckDateTime                          | applyProcessDTO                           | drvDriverLeavePOS                                                                                                                                                                                                   | drvCalculateBeginTimeMap                          || expectedResult
        [1L]   | new Timestamp(System.currentTimeMillis()) | new Timestamp(System.currentTimeMillis()) | TransportGroupDriverApplyProcessDTO.builder().rule(TmsTransportConstant.TransportDriverApplyRuleEnum.A).build() | [new DrvDriverLeavePO(drvId: 1L, leaveBeginTime: new Timestamp(System.currentTimeMillis()), leaveEndTime: new Timestamp(System.currentTimeMillis()), datachangeDeltime: new Timestamp(System.currentTimeMillis()))] | [(1L): new Timestamp(System.currentTimeMillis())] || [(1L): 1L]
    }

    @Unroll
    def "calculateLeaveHourTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.calculateBeginEndHour(_, _, _, _) >> 0L
        when:
        def result = spy.calculateLeaveHour(leaveBeginTime, leaveEndTime)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        leaveBeginTime                            | leaveEndTime                              || expectedResult
        new Timestamp(System.currentTimeMillis()) | new Timestamp(System.currentTimeMillis()) || 0L
    }

    @Unroll
    def "calculateBeginEndHourTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.calculateBeginEndHour(leaveBeginTime, leaveBeginDate, leaveEndDate, leaveEndTime)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        leaveEndDate   | leaveBeginTime                            | leaveBeginDate   | leaveEndTime                              || expectedResult
        "leaveEndDate" | new Timestamp(System.currentTimeMillis()) | "leaveBeginDate" | new Timestamp(System.currentTimeMillis()) || 0L
    }

    @Unroll
    def "calculationDrvFrezzeHourTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvFreezeRecordRepository.queryDrvFreezeRecordList(_) >> [new DrvFreezeRecordPO()]

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.calculationDrvFreeze(_, _, _, _, _, _) >> [(1L): 1L]
        when:
        def result = spy.calculationDrvFrezzeHour(drvList, beginCheckDateTime, endCheckDateTime, drvDurationMap, drvCalculateBeginTimeMap, applyProcessDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        beginCheckDateTime                        | endCheckDateTime                          | drvDurationMap | applyProcessDTO                           | drvList | drvCalculateBeginTimeMap                          || expectedResult
        new Timestamp(System.currentTimeMillis()) | new Timestamp(System.currentTimeMillis()) | [(1L): 1L]     | new TransportGroupDriverApplyProcessDTO() | [1L]    | [(1L): new Timestamp(System.currentTimeMillis())] || [(1L): 1L]
    }

    @Unroll
    def "calculationDrvFreezeTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.calculationDrvFreeze(freezeRecordList, beginCheckDateTime, endCheckDateTime, drvCalculateBeginTimeMap, drvDurationMap, applyProcessDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        freezeRecordList                                                                                                                                                                                                  | beginCheckDateTime                        | endCheckDateTime                                             | drvDurationMap | applyProcessDTO                                                                                 | drvCalculateBeginTimeMap                                             || expectedResult
        [new DrvFreezeRecordPO(drvId: 1L, freezeStatus: 0, firstFreezeTime: new Timestamp(System.currentTimeMillis()), freezeHour: 0, freezeFrom: "freezeFrom", unfreezeTime: new Timestamp(System.currentTimeMillis()))] | new Timestamp(System.currentTimeMillis()) | new java.sql.Timestamp(java.lang.System.currentTimeMillis()) | [(1L): 1L]     | TransportGroupDriverApplyProcessDTO.builder().rule(TmsTransportConstant.TransportDriverApplyRuleEnum.A).build() | [(1L): new java.sql.Timestamp(java.lang.System.currentTimeMillis())] || [(1L): 1L]
    }

    @Unroll
    def "checkPhoneNumberTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        repository.drvDispatchcheckDrvPhoneOnly(_) >> new DrvDriverPO()

        and: "Spy相关接口"
        def spy = Spy(testObj)
//        spy.overseasDrvDispatchCheck(_, _, _) >> null
        when:
        def result = spy.checkPhoneNumber(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.code == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                                                                               || expectedResult
        new com.ctrip.dcs.tms.transport.api.model.CheckPhoneNumberRequestType(drvPhone: "drvPhone", areaScope: 1, supplierId: 1L) || TmsTransportConstant.ResultCode.code100023
        new com.ctrip.dcs.tms.transport.api.model.CheckPhoneNumberRequestType(drvPhone: "drvPhone", areaScope: 0, supplierId: 1L) || "500"
    }

    @spock.lang.Unroll
    def "overseasDrvDispatchCheckTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.overseasDrvDispatchCheck(sharkValue, nowSupplierId, drvDriverPO)

        then: "验证返回结果里属性值是否符合预期"
        result.code == expectedResult
        where: "表格方式验证多种分支调用场景"
        nowSupplierId | drvDriverPO                                                                                                                                         | sharkValue   || expectedResult
        1L            | new com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO(temporaryDispatchMark: 0, drvId: 1L, supplierId: 1L) | "sharkValue" || "500"
        1L            | new com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvDriverPO(temporaryDispatchMark: 0, drvId: 1L, supplierId: 2L) | "sharkValue" || TmsTransportConstant.ResultCode.code100023
    }

    @Unroll
    def "getInActiveRemarkTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        drvInactiveReasonRepository.query(_) >> [new TmsDrvInactiveReasonPO(drvId: 1L, reasonCode: 0)]

        when:
        def result = testObj.getInActiveRemark(dtoList)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        dtoList                                                                                  || expectedResult
        [new DrvDriverSOAResponseDTO(drvId: 1L, drvStatus: 0, inactiveRemark: "inactiveRemark")] || null
        [new DrvDriverSOAResponseDTO(drvId: 1L, drvStatus: 0, inactiveRemark: "inactiveRemark", areaScope: 1)] || null
    }

    @Unroll
    def "drvPreCheckTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        repository.checkDrvOnly(_, _) >> checkDrvOnly
        repository.drvDispatchcheckDrvPhoneOnly(_) >> drvPO
        driverDomainServiceProxy.queryAccountByMobilePhone(_) >> new DriverAccountDetail(uid: "uid", name: "name")
        driverDomainServiceProxy.queryAccountByEmail(_) >> new DriverAccountDetail(uid: "uid", name: "name")
        mobileHelper.isMobileValid(_,_,_) >> mobileValid
        businessQConfig.isDrvPreCheckOff() >> Boolean.FALSE
        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.overseasDrvDispatchCheck(_, _, _) >> Result.Builder.<Long>newResult().success().build()
        when:
        def result = spy.drvPreCheck(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.getCode() == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                                       ||  mobileValid || drvPO    || checkDrvOnly      || expectedResult
        new DrvPreCheckRequestType(drvPhone: "drvPhone", igtCode: "igtCode", areaScope: 0, supplierId: 1L, email: "email") || Result.Builder.<Boolean>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build() || new DrvDriverPO() ||  Boolean.TRUE || "500"
        new DrvPreCheckRequestType(drvPhone: "", igtCode: "igtCode", areaScope: 0, supplierId: 1L, email: "email") || Result.Builder.<Boolean>newResult().success().withCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE).build()|| new DrvDriverPO()||  Boolean.TRUE || null
        new DrvPreCheckRequestType(drvPhone: "134", igtCode: "igtCode", areaScope: 0, supplierId: 1L, email: "email") || Result.Builder.<Boolean>newResult().fail().withCode(ServiceResponseConstants.ResStatus.FAILURE).build() || new DrvDriverPO()||  Boolean.TRUE || "0"
        new DrvPreCheckRequestType(drvPhone: "134", igtCode: "igtCode", areaScope: 1, supplierId: 1L, email: "email") || Result.Builder.<Boolean>newResult().success().build() || new DrvDriverPO() ||  Boolean.TRUE ||  null
        new DrvPreCheckRequestType(drvPhone: "134", igtCode: "igtCode", areaScope: 1, supplierId: 1L, email: "") || Result.Builder.<Boolean>newResult().success().build() || null ||  Boolean.TRUE || null
        new DrvPreCheckRequestType(drvPhone: "134", igtCode: "igtCode", areaScope: 1, supplierId: 1L, email: "email") || Result.Builder.<Boolean>newResult().success().build() || null ||  Boolean.TRUE || "500"
        new DrvPreCheckRequestType(drvPhone: "134", igtCode: "igtCode", areaScope: 1, supplierId: 1L, email: "email") || Result.Builder.<Boolean>newResult().success().build() || null ||  Boolean.TRUE || "500"
        new DrvPreCheckRequestType(drvPhone: "134", igtCode: "igtCode", areaScope: 1, supplierId: 1L, email: "email") || Result.Builder.<Boolean>newResult().success().build() || null ||  Boolean.FALSE || null
    }

    @spock.lang.Unroll
    def "enableTempDispatchBaseOnDateTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tempDispatchDateConfig.getTempDispatchDateConfig(_) >> [new com.ctrip.dcs.tms.transport.infrastructure.common.dto.DateRangeDTO()]

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getCountryId(_) >> "0L"
        when:
        def result = spy.enableTempDispatchBaseOnDate(graySupplier, cityId, useCarTime)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        useCarTime   | cityId | graySupplier || expectedResult
        null | 1L     | Boolean.TRUE || Boolean.TRUE
        "" | 1L     | Boolean.TRUE || Boolean.FALSE
    }
}
