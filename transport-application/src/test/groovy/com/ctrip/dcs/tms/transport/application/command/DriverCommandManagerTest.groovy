package com.ctrip.dcs.tms.transport.application.command

import com.ctrip.dcs.tms.transport.application.command.impl.DomesticDriverCommandHandler
import com.ctrip.dcs.tms.transport.application.command.impl.OverseaDriverCommandHandler
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

class DriverCommandManagerTest extends Specification {
    def testObj = new DriverCommandManager()
    def overseaDriverCommandHandler = Mock(OverseaDriverCommandHandler)
    def driverCommandHandlers = Lists.newArrayList(overseaDriverCommandHandler)

    def setup() {
        testObj.driverCommandHandlers = driverCommandHandlers
    }

    @Unroll
    def "getHandlerTest"() {
        given: "设定相关方法入参"
        overseaDriverCommandHandler.support(_ as Integer) >> true;
        when:
        def result = testObj.getHandler(internalScope)

        then: "验证返回结果里属性值是否符合预期"
        (result != null) == expectedResult
        where: "表格方式验证多种分支调用场景"
        internalScope || expectedResult
        0             || true
    }
}
