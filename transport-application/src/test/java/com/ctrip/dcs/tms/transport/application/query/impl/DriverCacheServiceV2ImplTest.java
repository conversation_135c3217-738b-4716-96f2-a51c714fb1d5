package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.dto.QueryDriverFromCacheParamDTO;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ExecutorService;

@RunWith(MockitoJUnitRunner.class)
public class DriverCacheServiceV2ImplTest {

    @InjectMocks
    private DriverCacheServiceV2Impl driverCacheServiceV2;

    @Mock
    private DriverGroupRelationRepository driverGroupRelationRepository;

    @Mock
    private DriverQueryService driverQueryService;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private VehicleQueryService vehicleQueryService;

    @Mock
    private ExecutorService dscFutureThreadPoolService;

    @Mock
    private TmsTransportQconfig qconfig;

    @Mock
    private TransportGroupRepository transportGroupRepository;

    @Test
    public void testPackageQueryByDrv() {
        QueryDriverFromCacheParamDTO req = new QueryDriverFromCacheParamDTO();
        req.setDriverIds("1");
        List<DriverInfo>  list =  driverCacheServiceV2.packageQuery(req);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testPackageQueryByTransport() {
        QueryDriverFromCacheParamDTO req = new QueryDriverFromCacheParamDTO();
        req.setTransportGroupIds("1");
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setVehicleId(1L);
//        Mockito.when(driverGroupRelationRepository.queryActiveDrvIdByTransportIdList(null, Lists.newArrayList(1L))).thenReturn(Lists.newArrayList(1L));
//        Mockito.when(drvDrvierRepository.queryDrvIdAndVehicleIdByCondition(req)).thenReturn(Lists.newArrayList(driverPO));
        List<DriverInfo>  list = driverCacheServiceV2.packageQuery(req);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void testPackageData() {
        DrvCacheDTO driverPO = new DrvCacheDTO();
        driverPO.setDriverId(1L);
        driverPO.setCarId(1L);
        VehCacheDTO vehCacheDTO = new VehCacheDTO();
        Map<Long, VehCacheDTO> map = Maps.newHashMap();
        map.put(1L, vehCacheDTO);
        TransportGroupBasePO transportGroupBasePO = new TransportGroupBasePO();
        Map<Long, List<TransportGroupBasePO>> tm = Maps.newHashMap();
        tm.put(1L, Lists.newArrayList(transportGroupBasePO));
        List<DriverInfo>  list = driverCacheServiceV2.packageData(Lists.newArrayList(driverPO), map, tm,Maps.newHashMap(),Maps.newHashMap());
        Assert.assertTrue(!list.isEmpty());
    }

    @Test
    public void testClearVehCache() throws InterruptedException {
        driverCacheServiceV2.clearVehCache(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void testClearDrvCache() throws InterruptedException {
        Boolean result  = true;
        driverCacheServiceV2.clearDrvCache(1L);
        Assert.assertEquals(result, Boolean.TRUE);
    }

    @Test
    public void testClearDrvCacheImmediately() {
        Boolean result  = true;
        driverCacheServiceV2.clearDrvCacheImmediately(1L);
        Assert.assertEquals(result, Boolean.TRUE);
    }

    @Test
    public void testClearDrvCacheImmediately2() {
        Boolean result  = true;
        driverCacheServiceV2.clearDrvCacheImmediately(0L);
        Assert.assertEquals(result, Boolean.TRUE);
    }


    @Test
    public void queryTransportDrvIdListNew() throws InterruptedException {
        List<TspTransportGroupPO> transportGroupPOS = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPOS.add(transportGroupPO);
        Mockito.when(transportGroupRepository.queryTspTransportBaseByIds(Arrays.asList(1L))).thenReturn(transportGroupPOS);
        Mockito.when(driverGroupRelationRepository.queryActiveDrvIdByTransportIdListNew(Arrays.asList(1L),Arrays.asList(1L))).thenReturn(Arrays.asList(1L));
        List<Long> result = driverCacheServiceV2.queryTransportDrvIdListNew(Arrays.asList(1L),"1");
        Assert.assertTrue(result.size() > 0);
    }

}
