package com.ctrip.dcs.tms.transport.application.query;

import com.ctrip.dcs.tms.transport.application.command.impl.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.infrastructureservice.executor.contract.*;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CommonCommandServiceTest {

    @InjectMocks
    private CommonCommandServiceImpl commonCommandService;
    @Mock
    private EnumRepository enumRepository;
    @Mock
    SelfServiceProviderConfig selfServiceProviderConfig;
    @Mock
    ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Mock
    InfrastructureServiceClientProxy infrastructureServiceClientProxy;

    @Mock
    EnumRepositoryHelper enumRepositoryHelper;

    @Test
    public void test() {
        Mockito.when(approvalProcessAuthQconfig.getMessageCode()).thenReturn("111");
        Mockito.when(approvalProcessAuthQconfig.getChannelId()).thenReturn("111");
        SendMesByPhoneRequestType requestType = new SendMesByPhoneRequestType();
        SendMesByPhoneResponseType responseType = new SendMesByPhoneResponseType();
//        Mockito.when(infrastructureServiceClientProxy.sendMessageByPhone(requestType)).thenReturn(responseType);
        Result<String>  result = commonCommandService.sendMessageByPhone4China("13501317970","");
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void judgeSupplierIsZYByIdTest() {
        commonCommandService.judgeSupplierIsZYById(null);
        SelfServiceProviderModel selfServiceProviderModel = new SelfServiceProviderModel();
        selfServiceProviderModel.setSelfSupplierId(1L);
        Mockito.when(selfServiceProviderConfig.getSelfServiceProviders()).thenReturn(ImmutableList.of(selfServiceProviderModel));
        Boolean b = commonCommandService.judgeSupplierIsZYById(1L);
        Assert.assertTrue(b);
    }

}