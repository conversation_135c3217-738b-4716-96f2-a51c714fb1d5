package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.messaging.qmq.TmsQmqProducer;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.DriverAccountRegisterResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.impl.EnumRepositoryHelper;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.dal.DalRepository;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.*;
import java.util.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant.*;

@RunWith(MockitoJUnitRunner.class)
public class RecruitingCommandServiceImplTest {

    @InjectMocks
    private RecruitingCommandServiceImpl recruitingCommandService;

    @Mock
    private TmsModRecordCommandService tmsModRecordCommandService;
    @Mock
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Mock
    private DrvRecruitingRepository drvRecruitingRepository;
    @Mock
    private VehicleCommandService vehicleCommandService;
    @Mock
    private CommonCommandService commonCommandService;
    @Mock
    private DriverCommandService driverCommandService;
    @Mock
    private VehicleQueryService vehicleQueryService;
    @Mock
    private TmsPmsproductQueryService tmsPmsproductQueryService;
    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    private ModRecordRespository modRecordRespository;
    @Mock
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;
    @Mock
    private ApprovalProcessAuthQconfig approvalProcessAuthQconfig;
    @Mock
    private EnumRepository enumRepository;
    @Mock
    private CertificateCheckQueryService queryService;
    @Mock
    private TmsCertificateCheckRepository checkRepository;
    @Mock
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;
    @Mock
    private DrvVehRecruitingCommandService drvVehRecruitingCommandService;
    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Mock
    private TmsTransportQconfig tmsTransportQconfig;
    @Mock
    TmsRecruitingApproveStepRepository stepRepository;
    @Mock
    TmsRecruitingApproveStepChildRepository childRepository;
    @Mock
    CommonCommandService commandService;
    @Mock
    private DriverQueryService driverQueryService;
    @Mock
    OverseasQconfig overseasQconfig;
    @Mock
    DriverAccountManagementHelper driverAccountManagementHelper;

    @Mock
    NetCardCheckUtil netCardCheckUtil;

    @Mock
    EnumRepositoryHelper enumRepositoryHelper;

    @Mock
    TmsQmqProducer tmsQmqProducer;

    @Mock
    TmsDrvInactiveReasonRepository tmsDrvInactiveReasonRepository;

    @Mock
    ProductionLineUtil productionLineUtil;

    @Mock
    IVRCallService ivrCallService;

    DalRepository<DrvRecruitingPO> drvRecruitingRepo = Mockito.mock(DalRepository.class);

    @Test
    public void testRecruitingUpdatePass() throws SQLException {
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvId(1L);
        List<DrvRecruitingPO> recruitingPOS = Lists.newArrayList(drvRecruitingPO);
        Mockito.when(drvRecruitingRepository.getDrvRecruitingList(Lists.newArrayList(1L))).thenReturn(recruitingPOS);
        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(1L),"1",true,"1",1, new ArrayList<>());
        Assert.assertTrue(result.isSuccess());
    }

    /**
     * Test for recruitingUpdatePass method with overseas driver in whitelist requiring IVR verification
     * This test verifies that overseas drivers in the whitelist now require IVR verification to be activated
     */
    @Test
    public void testRecruitingUpdatePassForOverseasDriverInWhitelist() throws SQLException {

        DrvRecruitingPO drvRecruitingPO1 = new DrvRecruitingPO();
        drvRecruitingPO1.setDrvRecruitingId(1L);
        drvRecruitingPO1.setApproverStatus(4);
        drvRecruitingPO1.setCityId(1L);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO1);

        Mockito.when(drvRecruitingRepository.getDrvRecruitingRepo()).thenReturn(drvRecruitingRepo);

        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTO.setRegisterSuccess(true);
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setUid("111");
        Mockito.when(driverAccountManagementHelper.registerDriverUserAccount(Mockito.any())).thenReturn(registerResultDTO);

        // Setup test data
        Long drvId = 100L;
        Long supplierId = 200L;
        Long cityId = 300L;
        String operator = "testOperator";
        String drvPhone = "***********";
        String igtCode = "86";

        // Create driver recruiting PO
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setDrvId(0L);
        drvRecruitingPO.setSupplierId(supplierId);
        drvRecruitingPO.setCityId(cityId);
        drvRecruitingPO.setInternalScope(1); // Overseas
        drvRecruitingPO.setActive(true);
        drvRecruitingPO.setDrvFrom(2); // Not from H5
        drvRecruitingPO.setDrvPhone(drvPhone);
        drvRecruitingPO.setIgtCode(igtCode);

        // Create driver PO that will be returned by driverCommandService.addDrvOfficial
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(drvId);
        driverPO.setSupplierId(supplierId);
        driverPO.setCityId(cityId);
        driverPO.setDrvPhone(drvPhone);
        driverPO.setIgtCode(igtCode);
        driverPO.setDrvFrom(2); // Not from H5

        // Setup mocks
        List<DrvRecruitingPO> recruitingPOS = Lists.newArrayList(drvRecruitingPO);
        Mockito.when(drvRecruitingRepository.getDrvRecruitingList(Lists.newArrayList(drvId))).thenReturn(recruitingPOS);
        Mockito.when(enumRepository.getAreaScope(cityId)).thenReturn(1); // Overseas

        // Mock the whitelist check
        Mockito.when(overseasQconfig.getLoginActiveWithedSupplierList()).thenReturn(Lists.newArrayList(supplierId)); // Supplier is in whitelist
//        Mockito.when(enumRepositoryHelper.getLocaleCode()).thenReturn("zh-CN");

        // Mock driver creation
        Mockito.when(driverCommandService.addDrvOfficial(Mockito.any(DrvDriverPO.class))).thenAnswer(invocation -> {
            DrvDriverPO inputDriverPO = invocation.getArgument(0);
            // Verify driver status is set to UNACT (not activated)
            Assert.assertEquals(DrvStatusEnum.UNACT.getCode(), inputDriverPO.getDrvStatus());
            return Result.Builder.<Long>newResult().success().withData(drvId).build();
        });

        // Execute the method under test
        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(drvId), operator, true, "Test remark", 1, new ArrayList<>());

        // Verify results
        Assert.assertTrue(result.isSuccess());

        // Verify IVR verification was called
        Mockito.verify(ivrCallService).sendIvrVerifyDelayMessage(Mockito.anyInt(), Mockito.any());

        // Verify no inactive reason for "not logged in" was added since driver is in whitelist
        Mockito.verify(tmsDrvInactiveReasonRepository, Mockito.never())
               .insert(Mockito.argThat(reason -> reason.getReasonCode().equals(DrvInActiveEnum.NOT_LOGIN.getCode())));
    }

//    /**
//     * Test for recruitingUpdatePass method with driver from H5 page that should be activated directly without IVR check
//     * This test verifies that for drivers where inLogInActiveCity returns false, if from H5 page, activate directly without IVR check
//     */
//    @Test
//    public void testRecruitingUpdatePassForH5Driver() throws SQLException {
//        // Setup test data
//        Long drvId = 101L;
//        Long supplierId = 201L;
//        Long cityId = 301L;
//        String operator = "testOperator";
//        String drvPhone = "13900139000";
//        String igtCode = "86";
//
//        // Create driver recruiting PO
//        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
//        drvRecruitingPO.setDrvId(drvId);
//        drvRecruitingPO.setSupplierId(supplierId);
//        drvRecruitingPO.setCityId(cityId);
//        drvRecruitingPO.setInternalScope(1); // Overseas
//        drvRecruitingPO.setActive(true);
//        drvRecruitingPO.setDrvFrom(1); // From H5 (DRV_AUTO)
//        drvRecruitingPO.setDrvPhone(drvPhone);
//        drvRecruitingPO.setIgtCode(igtCode);
//
//        // Setup mocks
//        List<DrvRecruitingPO> recruitingPOS = Lists.newArrayList(drvRecruitingPO);
//        Mockito.when(drvRecruitingRepository.getDrvRecruitingList(Lists.newArrayList(drvId))).thenReturn(recruitingPOS);
//        Mockito.when(enumRepository.getAreaScope(cityId)).thenReturn(1); // Overseas
//
//        // Mock the whitelist check - this driver's supplier is NOT in whitelist
//        Mockito.when(overseasQconfig.getLoginActiveWithedSupplierList()).thenReturn(Lists.newArrayList(999L)); // Different supplier ID
//        Mockito.when(overseasQconfig.getLoginActiveGrayCityList()).thenReturn(Lists.newArrayList(-1L)); // All cities in gray list
//
//        // Mock driver creation
//        Mockito.when(driverCommandService.addDrvOfficial(Mockito.any(DrvDriverPO.class))).thenAnswer(invocation -> {
//            DrvDriverPO inputDriverPO = invocation.getArgument(0);
//            // Verify driver status is set to ONLINE (activated) because it's from H5
//            Assert.assertEquals(DrvStatusEnum.ONLINE.getCode(), inputDriverPO.getDrvStatus());
//            return Result.Builder.<Long>newResult().success().withData(drvId).build();
//        });
//
//        // Execute the method under test
//        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(drvId), operator, true, "Test remark", 1);
//
//        // Verify results
//        Assert.assertTrue(result.isSuccess());
//
//        // Verify IVR verification was NOT called for H5 driver
//        Mockito.verify(driverDomainService, Mockito.never()).callPhoneForVerify(Mockito.any());
//
//        // Verify no inactive reason was added
//        Mockito.verify(tmsDrvInactiveReasonRepository, Mockito.never()).insert(Mockito.any());
//    }
//
//    /**
//     * Test for recruitingUpdatePass method with non-whitelist, non-H5 driver that requires IVR verification
//     * This test verifies that for drivers where inLogInActiveCity returns false and not from H5 page,
//     * IVR verification is performed without inserting "not logged in" reason
//     */
//    @Test
//    public void testRecruitingUpdatePassForNonWhitelistNonH5Driver() throws SQLException {
//        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
//        registerResultDTO.setNeedRegisterAccount(true);
//        registerResultDTO.setRegisterSuccess(true);
//        registerResultDTO.setDrvId(1L);
//        registerResultDTO.setUid("111");
//        Mockito.when(driverAccountManagementHelper.registerDriverUserAccount(Mockito.any())).thenReturn(registerResultDTO);
//
//        // Setup test data
//        Long drvId = 102L;
//        Long supplierId = 202L;
//        Long cityId = 302L;
//        String operator = "testOperator";
//        String drvPhone = "***********";
//        String igtCode = "86";
//
//        // Create driver recruiting PO
//        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
//        drvRecruitingPO.setDrvId(drvId);
//        drvRecruitingPO.setSupplierId(supplierId);
//        drvRecruitingPO.setCityId(cityId);
//        drvRecruitingPO.setInternalScope(1); // Overseas
//        drvRecruitingPO.setActive(true);
//        drvRecruitingPO.setDrvFrom(2); // Not from H5
//        drvRecruitingPO.setDrvPhone(drvPhone);
//        drvRecruitingPO.setIgtCode(igtCode);
//
//        // Setup mocks
//        List<DrvRecruitingPO> recruitingPOS = Lists.newArrayList(drvRecruitingPO);
//        Mockito.when(drvRecruitingRepository.getDrvRecruitingList(Lists.newArrayList(drvId))).thenReturn(recruitingPOS);
//        Mockito.when(enumRepository.getAreaScope(cityId)).thenReturn(1); // Overseas
//
//        // Mock the whitelist check - this driver's supplier is NOT in whitelist
//        Mockito.when(overseasQconfig.getLoginActiveWithedSupplierList()).thenReturn(Lists.newArrayList(999L)); // Different supplier ID
//        Mockito.when(overseasQconfig.getLoginActiveGrayCityList()).thenReturn(Lists.newArrayList(302L)); // This city is in gray list
//
//        // Mock driver creation
//        Mockito.when(driverCommandService.addDrvOfficial(Mockito.any(DrvDriverPO.class))).thenAnswer(invocation -> {
//            DrvDriverPO inputDriverPO = invocation.getArgument(0);
//            // Verify driver status is set to UNACT (not activated)
//            Assert.assertEquals(DrvStatusEnum.UNACT.getCode(), inputDriverPO.getDrvStatus());
//            return Result.Builder.<Long>newResult().success().withData(drvId).build();
//        });
//
//        // Mock IVR call
//        Mockito.when(driverDomainService.callPhoneForVerify(Mockito.any())).thenAnswer(invocation -> {
//            CallPhoneForVerifyRequestType request = invocation.getArgument(0);
//            // Verify IVR call is made with correct parameters
//            Assert.assertEquals(drvPhone, request.getPhoneNumber());
//            Assert.assertEquals(igtCode, request.getCountryCode());
//
//            CallPhoneForVerifyResponseType response = new CallPhoneForVerifyResponseType();
//            response.setCallTaskId(67890L);
//            return response;
//        });
//
//        // Execute the method under test
//        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(drvId), operator, true, "Test remark", 1);
//
//        // Verify results
//        Assert.assertTrue(result.isSuccess());
//
//        // Verify IVR verification was called
//        Mockito.verify(driverDomainService).callPhoneForVerify(Mockito.any());
//
//        // Verify "not logged in" inactive reason was NOT added since inLogInActiveCity returns false
//        Mockito.verify(tmsDrvInactiveReasonRepository, Mockito.never())
//               .insert(Mockito.argThat(reason -> reason.getReasonCode().equals(DrvInActiveEnum.NOT_LOGIN.getCode())));
//    }
//
//    /**
//     * Test for recruitingUpdatePass method with driver that should have "not logged in" inactive reason added
//     * This test verifies that for drivers where inLogInActiveCity returns true, the "not logged in" inactive reason is added
//     * and IVR verification is performed
//     */
//    @Test
//    public void testRecruitingUpdatePassWithNotLoggedInReason() throws SQLException {
//        // Setup test data
//        Long drvId = 103L;
//        Long supplierId = 203L;
//        Long cityId = 303L;
//        String operator = "testOperator";
//        String drvPhone = "13600136000";
//        String igtCode = "86";
//
//        // Create driver recruiting PO
//        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
//        drvRecruitingPO.setDrvId(drvId);
//        drvRecruitingPO.setSupplierId(supplierId);
//        drvRecruitingPO.setCityId(cityId);
//        drvRecruitingPO.setInternalScope(1); // Overseas
//        drvRecruitingPO.setActive(true);
//        drvRecruitingPO.setDrvFrom(2); // Not from H5
//        drvRecruitingPO.setDrvPhone(drvPhone);
//        drvRecruitingPO.setIgtCode(igtCode);
//
//        // Setup mocks
//        List<DrvRecruitingPO> recruitingPOS = Lists.newArrayList(drvRecruitingPO);
//        Mockito.when(drvRecruitingRepository.getDrvRecruitingList(Lists.newArrayList(drvId))).thenReturn(recruitingPOS);
//        Mockito.when(enumRepository.getAreaScope(cityId)).thenReturn(1); // Overseas
//
//        // Mock the whitelist check - this driver's supplier is NOT in whitelist and city is in gray list
//        // This will make inLogInActiveCity return true
//        Mockito.when(overseasQconfig.getLoginActiveWithedSupplierList()).thenReturn(Lists.newArrayList(999L)); // Different supplier ID
//        Mockito.when(overseasQconfig.getLoginActiveGrayCityList()).thenReturn(Lists.newArrayList(303L)); // This city is in gray list
//
//        // Mock driver creation
//        Mockito.when(driverCommandService.addDrvOfficial(Mockito.any(DrvDriverPO.class))).thenAnswer(invocation -> {
//            DrvDriverPO inputDriverPO = invocation.getArgument(0);
//            // Verify driver status is set to UNACT (not activated)
//            Assert.assertEquals(DrvStatusEnum.UNACT.getCode(), inputDriverPO.getDrvStatus());
//            return Result.Builder.<Long>newResult().success().withData(drvId).build();
//        });
//
//        // Mock IVR call
//        Mockito.when(driverDomainService.callPhoneForVerify(Mockito.any())).thenAnswer(invocation -> {
//            CallPhoneForVerifyRequestType request = invocation.getArgument(0);
//            // Verify IVR call is made with correct parameters
//            Assert.assertEquals(drvPhone, request.getPhoneNumber());
//            Assert.assertEquals(igtCode, request.getCountryCode());
//
//            CallPhoneForVerifyResponseType response = new CallPhoneForVerifyResponseType();
//            response.setCallTaskId(54321L);
//            return response;
//        });
//
//        // Execute the method under test
//        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(drvId), operator, true, "Test remark", 1);
//
//        // Verify results
//        Assert.assertTrue(result.isSuccess());
//
//        // Verify IVR verification was called
//        Mockito.verify(driverDomainService).callPhoneForVerify(Mockito.any());
//
//        // Verify "not logged in" inactive reason was added since inLogInActiveCity returns true
//        Mockito.verify(tmsDrvInactiveReasonRepository)
//               .insert(Mockito.argThat(reason ->
//                   reason.getDrvId().equals(drvId) &&
//                   reason.getReasonCode().equals(DrvInActiveEnum.NOT_LOGIN.getCode()) &&
//                   reason.getReasonDesc().equals(DrvInActiveEnum.getReasonDesc(DrvInActiveEnum.NOT_LOGIN.getCode()))
//               ));
//    }

    @Test
    public void getCityId() {
        VehicleAddSOARequestType vehicleRequest = new VehicleAddSOARequestType();
        vehicleRequest.setCityId(1L);

        Long cityId = recruitingCommandService.getCityId(vehicleRequest, null);
        Assert.assertEquals(1L,cityId.longValue());

        DrvAddSOARequestType drvRequest = new DrvAddSOARequestType();
        drvRequest.setCityId(2L);

        cityId = recruitingCommandService.getCityId(null, drvRequest);
        Assert.assertEquals(2L,cityId.longValue());

        cityId = recruitingCommandService.getCityId(null, null);
      Assert.assertNull(cityId);

    }

    @Test
    public void recruitingUpdateStatusTest() throws SQLException {
//        Mockito.when(drvRecruitingRepository.updateDrvRecApproverStatus(Lists.newArrayList(1L),4,"","",null,true)).thenReturn(1);
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
//        Mockito.when(drvRecruitingRepository.getDrvRecruitingList(Lists.newArrayList(1L))).thenReturn(Lists.newArrayList(drvRecruitingPO));
//        Mockito.doAnswer(invocation -> null).when(tmsQmqProducerCommandService).sendRecruitingApproveAgingQMQ(1L,1,1);
        Result<Boolean> result = recruitingCommandService.recruitingUpdateStatus(Lists.newArrayList(1L),4,"","",1,1, new ArrayList<>());
        Assert.assertTrue(result.isSuccess() ==false);
    }

    @Test
    public void test1() {
        try {
            TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
            checkPO.setCheckStatus(1);
            TmsCertificateCheckPO checkPO1 = new TmsCertificateCheckPO();
            checkPO1.setCheckStatus(1);
            DrvRecruitingPO recruitingPO = new DrvRecruitingPO();
            recruitingPO.setOcrVaccineData("{}");
            recruitingPO.setOcrNucleicAcidData("{}");
            recruitingPO.setVaccinationTimeList("2021-01-01,2021-01-02,2021-01-03");
            recruitingCommandService.dealTmsCertificateCheck(1L, recruitingPO, checkPO, checkPO1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertTrue(true);
    }

    @Test
    public void initSingleApprovalData() {
        try {
            TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
            checkPO.setCheckStatus(1);
            TmsCertificateCheckPO checkPO1 = new TmsCertificateCheckPO();
            checkPO1.setCheckStatus(1);
            DrvRecruitingPO recruitingPO = new DrvRecruitingPO();
            recruitingPO.setOcrVaccineData("{}");
            recruitingPO.setOcrNucleicAcidData("{}");
            recruitingPO.setVaccinationTimeList("2021-01-01,2021-01-02,2021-01-03");
            VehicleAddSOARequestType vehicleRequest = new VehicleAddSOARequestType();
            vehicleRequest.setNetAppealMaterials("111");
            DrvAddSOARequestType drvRequest = new DrvAddSOARequestType();
            drvRequest.setNetVehiclePeoImg("11");
            drvRequest.setVaccineReportImg("11");
            drvRequest.setNucleicAcidReportImg("11");
            List< InitOcrChildCheckStatusSOADTO > checkStatusSOADTOList = Lists.newArrayList();
            InitOcrChildCheckStatusSOADTO soadto = new InitOcrChildCheckStatusSOADTO();
            soadto.setApproveItem(1);
            soadto.setCheckStatus(1);
            Map<String,Object> nucleicAcidMap = Maps.newHashMap();
            nucleicAcidMap.put(NUCLEICACIDVACCINE_ITEM+1,1);
            Mockito.when(tmsTransportQconfig.getInitSingleValue()).thenReturn("{\"1\":{\"1\":\"1,2\",\"2\":\"1\",\"3\":\"2\",\"4\":\"1\",\"5\":\"1,2\",\"6\":\"1,2\",\"7\":\"1\",\"8\":\"1\"},\"2\":{\"101\":\"1,2\",\"102\":\"1,2\",\"103\":\"1,2\"}}");
            TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
            stepPO.setApproveSourceId(1L);
            stepPO.setApproveType(1);
            stepPO.setApproveItem(1);
//            Mockito.when(stepRepository.insert(stepPO)).thenReturn(1L);
            TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
            childPO.setRecruitingApproveStepId(1L);
            childPO.setChildItem(1);
//            Mockito.when(childRepository.insert(childPO)).thenReturn(1L);
            Boolean b = recruitingCommandService.initSingleApprovalData(1L, 1,"1",vehicleRequest,drvRequest,checkStatusSOADTOList,nucleicAcidMap);
            Assert.assertTrue(b);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void initH5SingleApprovalData() {
        try {
            TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
            checkPO.setCheckStatus(1);
            TmsCertificateCheckPO checkPO1 = new TmsCertificateCheckPO();
            checkPO1.setCheckStatus(1);
            DrvRecruitingPO recruitingPO = new DrvRecruitingPO();
            recruitingPO.setOcrVaccineData("{}");
            recruitingPO.setOcrNucleicAcidData("{}");
            recruitingPO.setVaccinationTimeList("2021-01-01,2021-01-02,2021-01-03");
            VehicleAddSOARequestType vehicleRequest = new VehicleAddSOARequestType();
            vehicleRequest.setNetAppealMaterials("111");
            DrvAddSOARequestType drvRequest = new DrvAddSOARequestType();
            drvRequest.setNetVehiclePeoImg("11");
            drvRequest.setVaccineReportImg("11");
            drvRequest.setNucleicAcidReportImg("11");
            List< InitOcrChildCheckStatusSOADTO > checkStatusSOADTOList = Lists.newArrayList();
            InitOcrChildCheckStatusSOADTO soadto = new InitOcrChildCheckStatusSOADTO();
            soadto.setApproveItem(1);
            soadto.setCheckStatus(1);
            Map<String,Object> nucleicAcidMap = Maps.newHashMap();
            nucleicAcidMap.put(NUCLEICACIDVACCINE_ITEM+1,1);
            Mockito.when(tmsTransportQconfig.getInitSingleValue()).thenReturn("{\"1\":{\"1\":\"1,2\",\"2\":\"1\",\"3\":\"2\",\"4\":\"1\",\"5\":\"1,2\",\"6\":\"1,2\",\"7\":\"1\",\"8\":\"1\"},\"2\":{\"101\":\"1,2\",\"102\":\"1,2\",\"103\":\"1,2\"}}");
            TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
            stepPO.setApproveSourceId(1L);
            stepPO.setApproveType(1);
            stepPO.setApproveItem(1);
//            Mockito.when(stepRepository.insert(stepPO)).thenReturn(1L);
            TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
            childPO.setRecruitingApproveStepId(1L);
            childPO.setChildItem(1);
//            Mockito.when(childRepository.insert(childPO)).thenReturn(1L);
            DrvVehRecruitingAddSOARequestType soaRequestType = new DrvVehRecruitingAddSOARequestType();
            soaRequestType.setNetVehiclePeoImg("11");
            soaRequestType.setCityId(1L);
            Mockito.when(netCardCheckUtil.isNetCardNoNeedCheck(Mockito.anyLong())).thenReturn(true);
            Boolean b = recruitingCommandService.initH5SingleApprovalData(1L, 1L,soaRequestType,"11",nucleicAcidMap);
            Assert.assertTrue(b);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void singleApproval(){
        SingleApprovalSOARequestType soaRequestType = new SingleApprovalSOARequestType();
        soaRequestType.setRecruitingId(1L);
        soaRequestType.setRecruitingType(1);
        soaRequestType.setModifyUser("1");
        List<SingleApprovalSOADTO> singleList = Lists.newArrayList();
        SingleApprovalSOADTO soadto = new SingleApprovalSOADTO();
        soadto.setApproveStatus(1);
        soadto.setApproveStepId(1L);
        soadto.setApproveReason("111");
        List<ApproveStepChildListSOADTO> childList = Lists.newArrayList();
        ApproveStepChildListSOADTO childListSOADTO = new ApproveStepChildListSOADTO();
        childListSOADTO.setChildItem(1);
        childListSOADTO.setApproveStepChildId(1L);
        childListSOADTO.setCheckStatus(1);
        childList.add(childListSOADTO);
        soadto.setChildList(childList);
        singleList.add(soadto);
        soaRequestType.setSingleList(singleList);
        Map<String, String> map = Maps.newHashMap();
        map.put("accountType","2");
        SessionHolder.setSessionSource(map);
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setDrvRecruitingId(1L);
        drvRecruitingPO.setApproverStatus(4);
        drvRecruitingPO.setCityId(1L);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        List<TmsRecruitingApproveStepPO> stepPOList = Lists.newArrayList();
        Mockito.when(stepRepository.queryApproveStepByIds(Arrays.asList(1L))).thenReturn(stepPOList);
        Mockito.when(stepRepository.updateApproveStatus(1L,1,"111",2,"1")).thenReturn(1);
        Result<Boolean>  result = recruitingCommandService.singleApproval(soaRequestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void vaccineNucleicChildMap(){
        Map<String,Object>  nucleicAcidMap = Maps.newHashMap();
        nucleicAcidMap.put(NUCLEICACIDVACCINE_ITEM+6,1);
        Map<String,Object> map = recruitingCommandService.vaccineNucleicChildMap(6,1,nucleicAcidMap);
        Assert.assertTrue(map!=null);
    }

    @Test
    public void initApproveStatus(){
        VehicleAddSOARequestType requestType = new VehicleAddSOARequestType();
        requestType.setNetAppealMaterials("11");
        DrvAddSOARequestType soaRequestType = new DrvAddSOARequestType();
        soaRequestType.setVaccineReportImg("11");
        Integer in = recruitingCommandService.initApproveStatus(4,requestType,soaRequestType);
        Assert.assertTrue(in >0);
    }

    @Test
    public void initApproveStatus5(){
        VehicleAddSOARequestType requestType = new VehicleAddSOARequestType();
        requestType.setNetAppealMaterials("11");
        DrvAddSOARequestType soaRequestType = new DrvAddSOARequestType();
        soaRequestType.setVaccineReportImg("11");
        Integer integer = recruitingCommandService.initApproveStatus(5,requestType,soaRequestType);
        Assert.assertTrue(integer!=null);
    }

    @Test
    public void initApproveStatus6(){
        VehicleAddSOARequestType requestType = new VehicleAddSOARequestType();
        requestType.setNetAppealMaterials("11");
        DrvAddSOARequestType soaRequestType = new DrvAddSOARequestType();
        soaRequestType.setVaccineReportImg("11");
        Integer integer = recruitingCommandService.initApproveStatus(6,requestType,soaRequestType);
        Assert.assertTrue(integer!=null);
    }

    @Test
    public void initApproveStatus102(){
        VehicleAddSOARequestType requestType = new VehicleAddSOARequestType();
        requestType.setNetAppealMaterials("11");
        DrvAddSOARequestType soaRequestType = new DrvAddSOARequestType();
        soaRequestType.setVaccineReportImg("11");
        Integer integer = recruitingCommandService.initApproveStatus(102,requestType,soaRequestType);
        Assert.assertTrue(integer!=null);
    }

    @Test
    public void synChildCheckStatus(){
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setCityId(1L);
        drvRecruitingPO.setVersionFlag(3);
        drvRecruitingPO.setIgtCode("86");
        drvRecruitingPO.setDrvPhone("11");
        drvRecruitingPO.setDrvFrom(2);
        drvRecruitingPO.setDrvName("111");
        drvRecruitingPO.setDatachangeCreatetime(DateUtil.string2Timestamp("2022-01-01 12:12:12",DateUtil.YYYYMMDDHHMMSS));
        drvRecruitingPO.setSupplierId(1L);
        Mockito.when(drvRecruitingRepository.queryByPK(1L)).thenReturn(drvRecruitingPO);
        List<TmsRecruitingApproveStepPO> stepPOList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveItem(1);
        stepPO.setApproveStatus(1);
        stepPO.setApproveFrom(1);
        stepPOList.add(stepPO);
        Mockito.when(stepRepository.queryApproveStepList(1L, 1)).thenReturn(stepPOList);
        List<TmsRecruitingApproveStepChildPO> childPOList =Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setCheckStatus(1);
        childPO.setChildItem(1);
        childPOList.add(childPO);
        Mockito.when(childRepository.queryChildByStepIdList(Arrays.asList(1L))).thenReturn(childPOList);
        Boolean b = recruitingCommandService.synChildCheckStatus(1L, TmsTransportConstant.RecruitingTypeEnum.drv,4,"11",null,null,null);
        Assert.assertTrue(b);
    }

    @Test
    public void updateChildStatus(){
        List<TmsRecruitingApproveStepPO> stepList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(1);
        stepPO.setApproveItem(1);
        stepList.add(stepPO);
        Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap = Maps.newHashMap();
        List<TmsRecruitingApproveStepChildPO> childPOList = Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setCheckStatus(1);
        childPOList.add(childPO);
        childMap.put(1L,childPOList);
        Map<String,Integer> childStatusMap = Maps.newHashMap();
        String moifyUser = "111";
//        Mockito.when(childRepository.updateChildCheckStatus(childPO.getId(),1,moifyUser)).thenReturn(1);
        Boolean b =recruitingCommandService.updateChildStatus(stepList,childMap,childStatusMap,moifyUser);
        Assert.assertTrue(b);
    }

    @Test
    public void systemAutoPassSingle(){
        List<TmsRecruitingApproveStepPO> stepList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(0);
        stepPO.setApproveItem(1);
        stepList.add(stepPO);

        List<TmsRecruitingApproveStepPO> supplierstepList = Lists.newArrayList();
         stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(0);
        stepPO.setApproveItem(1);
        supplierstepList.add(stepPO);
        Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap = Maps.newHashMap();
        List<TmsRecruitingApproveStepChildPO> childPOList = Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setCheckStatus(1);
        childPOList.add(childPO);
        childMap.put(1L,childPOList);
        String moifyUser = "111";
//        Mockito.when(childRepository.updateChildCheckStatus(childPO.getId(),1,moifyUser)).thenReturn(1);
        Map<Integer, TmsCertificateCheckPO> checkPOMap = Maps.newHashMap();
        TmsCertificateCheckPO checkPO = new TmsCertificateCheckPO();
        checkPO.setCheckId(1L);
        checkPO.setCheckStatus(2);
        checkPOMap.put(1,checkPO);
        Mockito.when(queryService.queryCertificateCheckToMap(1L,1)).thenReturn(checkPOMap);
        Boolean b = recruitingCommandService.systemAutoPassSingle(stepList,supplierstepList,childMap,1L,1,1,null,null,null);
        Assert.assertTrue(b);
    }

    @Test
    public void sendSmsToH5(){
        Mockito.when(approvalProcessAuthQconfig.getDrvRecruitingCode()).thenReturn("333");
        Mockito.when(approvalProcessAuthQconfig.getDrvRecruitingDetailUrl()).thenReturn("111");
        Result<Boolean> reesult = Result.Builder.<Boolean>newResult().success().build();
//        Mockito.when(commandService.sendMessageByPhone("86","11","333",Maps.newHashMap())).thenReturn(reesult);
        Boolean b = recruitingCommandService.sendSmsToH5("11","11","2021-01-01 12:12:12",1L,1L,"86");
        Assert.assertTrue(b);
    }

    @Test
    public void approvePass10(){
        List<TmsRecruitingApproveStepPO> stepList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(1);
        stepPO.setApproveItem(1);
        stepList.add(stepPO);

        List<TmsRecruitingApproveStepPO> supplierstepList = Lists.newArrayList();
        stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(1);
        stepPO.setApproveItem(1);
        supplierstepList.add(stepPO);
        Mockito.when(stepRepository.update(stepPO)).thenReturn(1);
        Boolean b = recruitingCommandService.approvePass10(stepList,supplierstepList);
        Assert.assertTrue(b);
    }

    @Test
    public void recruitingApprove6(){
        List<TmsRecruitingApproveStepPO> stepList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(1);
        stepPO.setApproveItem(1);
        stepPO.setFinalPassStatus(0);
        stepList.add(stepPO);

        List<TmsRecruitingApproveStepPO> supplierstepList = Lists.newArrayList();
        stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(1);
        stepPO.setApproveItem(1);
        supplierstepList.add(stepPO);
        Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap = Maps.newHashMap();
        List<TmsRecruitingApproveStepChildPO> childPOList = Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setCheckStatus(1);
        childPOList.add(childPO);
        childMap.put(1L,childPOList);
         Map<Integer, TmsRecruitingApproveStepPO> bdSingleMap = Maps.newHashMap();
         bdSingleMap.put(1,stepPO);
        Map<String, Integer> bdChildStatusMap =Maps.newHashMap();
        bdChildStatusMap.put("11",1);
        Boolean b = recruitingCommandService.recruitingApprove6(stepList,bdSingleMap,childMap,bdChildStatusMap,"111");
        Assert.assertTrue(b);
    }

    @Test
    public void childStatuaMap(){
        List<TmsRecruitingApproveStepPO> stepList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(1);
        stepPO.setApproveItem(1);
        stepPO.setFinalPassStatus(0);
        stepList.add(stepPO);
        Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap = Maps.newHashMap();
        List<TmsRecruitingApproveStepChildPO> childPOList = Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setCheckStatus(1);
        childPOList.add(childPO);
        childMap.put(1L,childPOList);
        Map<String,Integer> map = recruitingCommandService.childStatuaMap(stepList,childMap);
        Assert.assertTrue(map!=null);
    }

    @Test
    public void encapsulationField1(){
        Map<Integer, List<TmsRecruitingApproveStepPO>> integerListMap = Maps.newHashMap();
        List<TmsRecruitingApproveStepPO> stepList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(1);
        stepPO.setApproveItem(1);
        stepPO.setFinalPassStatus(0);
        stepList.add(stepPO);
        integerListMap.put(2,stepList);
        Map<Integer, TmsRecruitingApproveStepPO> bdSingleMap = Maps.newHashMap();
        bdSingleMap.put(1,stepPO);
        List<Long> bdList = Lists.newArrayList();
        List<TmsRecruitingApproveStepPO> stepList1 = Lists.newArrayList();
        recruitingCommandService.encapsulationField(integerListMap,bdList,bdSingleMap,stepList1,stepList1);
        Assert.assertTrue(true);
    }

    @Test
    public void turnDownReason(){
        String str = recruitingCommandService.turnDownReason(1L,1,1L,"1",6);
        Assert.assertTrue(!str.isEmpty());
    }

    @Test
    public void systemAutoUpdatePass(){
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        Mockito.when(stepRepository.update(stepPO)).thenReturn(1);
        Assert.assertTrue(true);
        Boolean b = recruitingCommandService.systemAutoUpdatePass(stepPO,null,null,null);
        Assert.assertTrue(b);
    }

    @Test
    public void checkStatusBol(){
        Boolean b = recruitingCommandService.checkStatusBol(1,1);
        Assert.assertTrue(!b);
    }

    @Test
    public void checkNowRoleApproveStatus(){
        Map<String,String> map = Maps.newHashMap();
        map.put("accountType","2");
        SessionHolder.setSessionSource(map);
        Mockito.when(enumRepository.getAreaScope(1L)).thenReturn(0);
        Boolean b = recruitingCommandService.checkNowRoleApproveStatus(1,1L,3);
        Assert.assertTrue(b);
    }

    @Test
    public void checkNowRoleApproveStatus1(){
        Map<String,String> map = Maps.newHashMap();
        map.put("accountType","1");
        SessionHolder.setSessionSource(map);
        Mockito.when(enumRepository.getAreaScope(1L)).thenReturn(0);
        Boolean b = recruitingCommandService.checkNowRoleApproveStatus(1,1L,3);
        Assert.assertTrue(b);
    }

    @Test
    public void judgeCertificateCheckPassFlag(){
        TmsCertificateCheckPO tmsCertificateCheckPO = null;
        Boolean b = recruitingCommandService.judgeCertificateCheckPassFlag(3,tmsCertificateCheckPO);
        Assert.assertTrue(b);
    }

    @Test
    public void getStepModColl(){
        List<SingleApprovalSOADTO> approvalSOADTOS = Lists.newArrayList();
        SingleApprovalSOADTO soadto = new SingleApprovalSOADTO();
        soadto.setApproveStepId(1L);
        soadto.setApproveStatus(1);
        approvalSOADTOS.add(soadto);
        List<StepModRecordParams> list = recruitingCommandService.getStepModColl(approvalSOADTOS,"system");
        Assert.assertTrue(list.size() > 0);
    }

    @Test
    public void getChildStepModColl(){
        List<SingleApprovalSOADTO> approvalSOADTOS = Lists.newArrayList();
        SingleApprovalSOADTO soadto = new SingleApprovalSOADTO();
        soadto.setApproveStepId(1L);
        soadto.setApproveStatus(1);
        List<ApproveStepChildListSOADTO> childList = Lists.newArrayList();
        ApproveStepChildListSOADTO childListSOADTO = new ApproveStepChildListSOADTO();
        childListSOADTO.setApproveStepChildId(1L);
        childListSOADTO.setCheckStatus(1);
        childListSOADTO.setChildItem(1);
        childList.add(childListSOADTO);
        List<StepChildModRecordParams>  lsit = recruitingCommandService.getChildStepModColl(approvalSOADTOS,"system");
        Assert.assertTrue(lsit.size() ==0);
    }

    @Test
    public void oFflineChildStatusRecord(){
        List<TmsRecruitingApproveStepPO> stepList = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(0);
        stepPO.setApproveItem(1);
        stepList.add(stepPO);
        List<TmsRecruitingApproveStepPO> supplierstepList = Lists.newArrayList();
        stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveStatus(0);
        stepPO.setApproveItem(1);
        supplierstepList.add(stepPO);
        Map<Long,List<TmsRecruitingApproveStepChildPO>> childMap = Maps.newHashMap();
        List<TmsRecruitingApproveStepChildPO> childPOList = Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setCheckStatus(1);
        childPO.setChildItem(1);
        childPOList.add(childPO);
        childMap.put(1L,childPOList);
        String moifyUser = "111";
        Map<String,Integer> childStatusMap = Maps.newHashMap();
        childStatusMap.put("1_1",1);
        List<StepChildModRecordParams> childRecordParams = Lists.newArrayList();
//        Mockito.when(tmsModRecordCommandService.saveChildSingleRrd(childRecordParams,moifyUser,1L,1L,1)).thenReturn(true);
        Boolean b = recruitingCommandService.oFflineChildStatusRecord(stepList,childMap,childStatusMap,"11",1L,1L,1);
        Assert.assertTrue(b);
    }

    @Test
    public void setChildItemGroupColl(){
        List<Long> childStepIds = Lists.newArrayList();
        List<Long> certificateIds = Arrays.asList(1L);
        Map<String,Integer> tarCheckStatusMap = Maps.newHashMap();
        tarCheckStatusMap.put("1_1",1);
        List<SingleApprovalSOADTO> approvalSOADTOS = Lists.newArrayList();
        SingleApprovalSOADTO soadto = new SingleApprovalSOADTO();
        soadto.setApproveStepId(1L);
        soadto.setApproveStatus(0);
        soadto.setApproveReason("1");
        List<ApproveStepChildListSOADTO> childList = Lists.newArrayList();
        ApproveStepChildListSOADTO childListSOADTO = new ApproveStepChildListSOADTO();
        childListSOADTO.setApproveStepChildId(1L);
        childListSOADTO.setChildItem(1);
        childListSOADTO.setCheckStatus(1);
        childListSOADTO.setSupplierCheckStatus(1);
        childList.add(childListSOADTO);
        soadto.setChildList(childList);
        approvalSOADTOS.add(soadto);
        recruitingCommandService.setChildItemGroupColl(childStepIds,certificateIds,tarCheckStatusMap,approvalSOADTOS);
        Assert.assertTrue(true);
    }

    @Test
    public void setChildStepColl(){
        List<Long> childStepIds = Arrays.asList(1L);
        List<Long> certificateIds = Arrays.asList(1L);
        Map<String,Integer> tarCheckStatusMap = Maps.newHashMap();
        tarCheckStatusMap.put("1_1",1);
        List<TmsRecruitingApproveStepChildPO> childPOList = Lists.newArrayList();
        TmsRecruitingApproveStepChildPO childPO = new TmsRecruitingApproveStepChildPO();
        childPO.setRecruitingApproveStepId(1L);
        childPO.setId(1L);
        childPO.setChildItem(1);
        childPO.setCheckStatus(0);
        childPOList.add(childPO);
        Mockito.when(childRepository.queryChildByIds(childStepIds)).thenReturn(childPOList);
        recruitingCommandService.setChildStepColl(childStepIds,tarCheckStatusMap,"11",Lists.newArrayList());
        Assert.assertTrue(true);
    }

    @Test
    public void setCertificateColl(){
        List<Long> childStepIds = Arrays.asList(1L);
        List<Long> certificateIds = Arrays.asList(1L);
        Map<String,Integer> tarCheckStatusMap = Maps.newHashMap();
        tarCheckStatusMap.put("1_3",1);
        List<TmsCertificateCheckPO> childPOList = Lists.newArrayList();
        TmsCertificateCheckPO certificateCheckPO = new TmsCertificateCheckPO();
        certificateCheckPO.setCertificateType(1);
        certificateCheckPO.setCheckId(1L);
        certificateCheckPO.setId(1L);
        childPOList.add(certificateCheckPO);
        Mockito.when(checkRepository.queryCerCheckListByIds(Arrays.asList(1L))).thenReturn(childPOList);
        recruitingCommandService.setCertificateColl(childStepIds,tarCheckStatusMap,"11",Lists.newArrayList());
        Assert.assertTrue(true);
    }

    @Test
    public void raisingPickUpInitValue(){
        Map<Integer,Boolean> map = Maps.newHashMap();
        map.put(0,false);
        map.put(1,true);
        Mockito.when(tmsTransportQconfig.getRaisingPickUpValuesMap()).thenReturn(map);
        Mockito.when(enumRepository.getAreaScope(1L)).thenReturn(1);
        Boolean b = recruitingCommandService.raisingPickUpInitValue(1L);
        Assert.assertTrue(b);
    }

    @Test
    public void testRecruitingUpdatePass1() throws SQLException {
        DrvRecruitingPO drvRecruitingPO = new DrvRecruitingPO();
        drvRecruitingPO.setCityId(379L);
        drvRecruitingPO.setInternalScope(1);
        drvRecruitingPO.setActive(true);
        drvRecruitingPO.setDrvFrom(1);
        drvRecruitingPO.setVehicleId(1L);
        List<DrvRecruitingPO> recruitingPOS = Lists.newArrayList(drvRecruitingPO);
        Mockito.when(enumRepository.getAreaScope(drvRecruitingPO.getCityId())).thenReturn(1);
        Mockito.when(drvRecruitingRepository.getDrvRecruitingList(Lists.newArrayList(1L))).thenReturn(recruitingPOS);
        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(1L),"1",true,"1",1, new ArrayList<>());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void getVehicleRecruitingIdVehicleIdMap() throws SQLException {
        Set<Long> vehList = Sets.newHashSet();
        vehList.add(1L);
        List<VehicleRecruitingPO> vehicleRecruitingPOList = Lists.newArrayList();
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setVehicleLicense("111");
        vehicleRecruitingPO.setCityId(379L);
        vehicleRecruitingPO.setVersionFlag(4);
        vehicleRecruitingPOList.add(vehicleRecruitingPO);
        Mockito.when(vehicleRecruitingRepository.getVehicleRecruitingList(vehList)).thenReturn(vehicleRecruitingPOList);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(false).build();
        Mockito.when(vehicleQueryService.isVehicleLicenseUniqueness(null, vehicleRecruitingPO.getVehicleLicense())).thenReturn(result);
        Mockito.when(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId())).thenReturn(1);
        Map<Long, Long> resultMap = recruitingCommandService.getVehicleRecruitingIdVehicleIdMap(vehList,"111",true);
        Assert.assertTrue(resultMap!=null);
    }

    @Test
    public void testRecruitingUpdatePass2() throws SQLException {
        List<VehicleRecruitingPO> vehicleRecruitingPOList = Lists.newArrayList();
        VehicleRecruitingPO recruitingPOS = new VehicleRecruitingPO();
        recruitingPOS.setVehicleId(1L);
        recruitingPOS.setApproverStatus(10);
        recruitingPOS.setActive(true);
        recruitingPOS.setVehicleLicense("111");
        recruitingPOS.setCityId(739L);
        recruitingPOS.setVersionFlag(3);
        recruitingPOS.setVin("111");
        vehicleRecruitingPOList.add(recruitingPOS);
        Set<Long> longSet = Sets.newHashSet();
        longSet.add(1L);
        Mockito.when(vehicleRecruitingRepository.getVehicleRecruitingList(longSet)).thenReturn(vehicleRecruitingPOList);
        Result<Boolean> isVehicleLicenseUniqueness = Result.Builder.<Boolean>newResult().withData(true).success().build();
        Mockito.when(vehicleQueryService.isVehicleLicenseUniqueness(null, recruitingPOS.getVehicleLicense())).thenReturn(isVehicleLicenseUniqueness);
        Mockito.when(vehicleRepository.checkVehOnly(recruitingPOS.getVin(),TmsTransportConstant.VehOnlyTypeEnum.vehicle_vin.getCode())).thenReturn(true);
        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(1L),"1",true,"1",2, new ArrayList<>());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void testRecruitingUpdatePass3() throws SQLException {
        List<VehicleRecruitingPO> vehicleRecruitingPOList = Lists.newArrayList();
        VehicleRecruitingPO recruitingPOS = new VehicleRecruitingPO();
        recruitingPOS.setVehicleId(1L);
        recruitingPOS.setApproverStatus(10);
        recruitingPOS.setActive(true);
        recruitingPOS.setVehicleLicense("111");
        recruitingPOS.setCityId(739L);
        recruitingPOS.setVin("111");
        recruitingPOS.setVehicleColorId(1);
        vehicleRecruitingPOList.add(recruitingPOS);
        Set<Long> longSet = Sets.newHashSet();
        longSet.add(1L);
        Mockito.when(vehicleRecruitingRepository.getVehicleRecruitingList(longSet)).thenReturn(vehicleRecruitingPOList);
        Result<Boolean> isVehicleLicenseUniqueness = Result.Builder.<Boolean>newResult().withData(true).success().build();
        Mockito.when(vehicleQueryService.isVehicleLicenseUniqueness(null, recruitingPOS.getVehicleLicense())).thenReturn(isVehicleLicenseUniqueness);
        Result<Boolean> result = recruitingCommandService.recruitingUpdatePass(Lists.newArrayList(1L),"1",true,"1",2, new ArrayList<>());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void saveOverseasTags() throws SQLException {
        SaveOverseasTagsSOARequestType requestType = new SaveOverseasTagsSOARequestType();
        requestType.setRecruitingId(1L);
        requestType.setRecruitingType(1);
        List<SaveOverseasTagsParamsSOAVO> paramsList = Lists.newArrayList();
        SaveOverseasTagsParamsSOAVO paramsSOAVO = new SaveOverseasTagsParamsSOAVO();
        paramsSOAVO.setStepId(1L);
        paramsSOAVO.setCheckStatus(1);
        paramsSOAVO.setApproveReason("111");
        paramsList.add(paramsSOAVO);
        requestType.setParamsList(paramsList);
        Map<String, String> params  = Maps.newHashMap();
        params.put("accountType","2");
        params.put("accountName","111");
        SessionHolder.setSessionSource(params);
        List<TmsRecruitingApproveStepPO> approveStepPOS = Lists.newArrayList();
        TmsRecruitingApproveStepPO stepPO = new TmsRecruitingApproveStepPO();
        stepPO.setId(1L);
        stepPO.setApproveItem(8);
        stepPO.setApproveStatus(0);
        stepPO.setApproveReason("111");
        approveStepPOS.add(stepPO);
        Mockito.when(stepRepository.queryApproveStepByIds(Arrays.asList(1L))).thenReturn(approveStepPOS);
        Mockito.when(stepRepository.updateApproveStatus(1L, 1, "111",2,"111")).thenReturn(1);
        Result<Boolean> result = recruitingCommandService.saveOverseasTags(requestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void initOverseasSingleData() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(105);
        modelSOA.setPassStatus(1);
        ocrPassList.add(modelSOA);
        Boolean result = recruitingCommandService.initOverseasSingleData(1L,1L,SingleApproveTypeEnum.DRV ,ocrPassList,"111");
        Assert.assertTrue(result);
    }

    @Test
    public void overseasNewBusinessApprove() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(105);
        modelSOA.setPassStatus(1);
        ocrPassList.add(modelSOA);
        Mockito.when(driverQueryService.overseasIsBusiness(1L, 5, 1,BusinessTypeEnum.OTHER)).thenReturn(true);
        Result<Boolean> result = recruitingCommandService.overseasNewBusinessApprove(1L,5,1,1L,ocrPassList,RecruitingTypeEnum.drv,SingleApproveTypeEnum.DRV,"111");
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void overseasNewBusinessApprove1() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(105);
        modelSOA.setPassStatus(0);
        ocrPassList.add(modelSOA);
        Mockito.when(driverQueryService.overseasIsBusiness(1L, 5, 1,BusinessTypeEnum.OTHER)).thenReturn(true);
        Result<Boolean> result = recruitingCommandService.overseasNewBusinessApprove(1L,5,1,1L,ocrPassList,RecruitingTypeEnum.drv,SingleApproveTypeEnum.DRV,"111");
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void getOverseasStepModColl() throws SQLException {
        List<SaveOverseasTagsParamsSOAVO> approvalSOADTOS = Lists.newArrayList();
        SaveOverseasTagsParamsSOAVO paramsSOAVO = new SaveOverseasTagsParamsSOAVO();
        paramsSOAVO.setStepId(1L);
        approvalSOADTOS.add(paramsSOAVO);
        List<StepModRecordParams> result = recruitingCommandService.getOverseasStepModColl(approvalSOADTOS,"111");
        Assert.assertTrue(!result.isEmpty());
    }

    @Test
    public void getVehicleRecruitingIdVehicleIdMap1() throws SQLException {
        Set<Long> vehList = Sets.newHashSet();
        vehList.add(1L);
        List<VehicleRecruitingPO> vehicleRecruitingPOList = Lists.newArrayList();
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setVehicleLicense("111");
        vehicleRecruitingPO.setCityId(379L);
        vehicleRecruitingPO.setVersionFlag(4);
        vehicleRecruitingPO.setSupplierId(1L);
        vehicleRecruitingPOList.add(vehicleRecruitingPO);
        Mockito.when(vehicleRecruitingRepository.getVehicleRecruitingList(vehList)).thenReturn(vehicleRecruitingPOList);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(vehicleQueryService.isVehicleLicenseUniqueness(null, vehicleRecruitingPO.getVehicleLicense())).thenReturn(result);
        Mockito.when(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId())).thenReturn(1);
        Mockito.when(enumRepository.getCountryId(vehicleRecruitingPO.getCityId())).thenReturn(78L);
        Mockito.when(overseasQconfig.getVehicleUnactCountryConf()).thenReturn("78");
        Mockito.when(tmsPmsproductQueryService.checkSkuIsExist(vehicleRecruitingPO.getSupplierId())).thenReturn(false);
        try {
            Map<Long, Long> resultMap = recruitingCommandService.getVehicleRecruitingIdVehicleIdMap(vehList,"111",true);
            Assert.assertTrue(resultMap!=null);
        }catch (Exception e){

        }
    }


    @Test
    public void getVehicleRecruitingIdVehicleIdMap2() throws SQLException {
        Set<Long> vehList = Sets.newHashSet();
        vehList.add(1L);
        List<VehicleRecruitingPO> vehicleRecruitingPOList = Lists.newArrayList();
        VehicleRecruitingPO vehicleRecruitingPO = new VehicleRecruitingPO();
        vehicleRecruitingPO.setVehicleLicense("111");
        vehicleRecruitingPO.setCityId(379L);
        vehicleRecruitingPO.setVersionFlag(4);
        vehicleRecruitingPO.setSupplierId(1L);
        vehicleRecruitingPOList.add(vehicleRecruitingPO);
        Mockito.when(vehicleRecruitingRepository.getVehicleRecruitingList(vehList)).thenReturn(vehicleRecruitingPOList);
        Result<Boolean> result = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(vehicleQueryService.isVehicleLicenseUniqueness(null, vehicleRecruitingPO.getVehicleLicense())).thenReturn(result);
        Mockito.when(enumRepository.getAreaScope(vehicleRecruitingPO.getCityId())).thenReturn(1);
        Mockito.when(enumRepository.getCountryId(vehicleRecruitingPO.getCityId())).thenReturn(78L);
        Mockito.when(overseasQconfig.getVehicleUnactCountryConf()).thenReturn("77");
        Mockito.when(tmsPmsproductQueryService.checkSkuIsExist(vehicleRecruitingPO.getSupplierId())).thenReturn(false);
        try {
            Map<Long, Long> resultMap = recruitingCommandService.getVehicleRecruitingIdVehicleIdMap(vehList,"111",true);
            Assert.assertTrue(resultMap!=null);
        }catch (Exception e){

        }
    }

    @Test
    public void registryDriverAccountSuccess() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTO.setRegisterSuccess(true);
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setUid("111");
        Mockito.when(driverAccountManagementHelper.registerDriverUserAccount(Mockito.any())).thenReturn(registerResultDTO);
        Boolean resultMap = recruitingCommandService.registryDriverAccountSuccess(drvDriverPO,1L,4);
        Assert.assertTrue(resultMap!=null);
    }

    @Test
    public void registryDriverAccountSuccess1() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setNeedRegisterAccount(false);
        registerResultDTO.setRegisterSuccess(true);
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setUid("111");
        Mockito.when(driverAccountManagementHelper.registerDriverUserAccount(Mockito.any())).thenReturn(registerResultDTO);
        Boolean resultMap = recruitingCommandService.registryDriverAccountSuccess(drvDriverPO,1L,4);
        Assert.assertTrue(resultMap!=null);
    }

    @Test
    public void registryDriverAccountSuccess2() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTO.setRegisterSuccess(false);
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setUid("111");
        Mockito.when(driverAccountManagementHelper.registerDriverUserAccount(Mockito.any())).thenReturn(registerResultDTO);
        Boolean resultMap = recruitingCommandService.registryDriverAccountSuccess(drvDriverPO,1L,4);
        Assert.assertTrue(resultMap!=null);
    }

    @Test
    public void registryDriverAccountSuccess3() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        DriverAccountRegisterResultDTO registerResultDTO = new DriverAccountRegisterResultDTO();
        registerResultDTO.setNeedRegisterAccount(true);
        registerResultDTO.setRegisterSuccess(true);
        registerResultDTO.setDrvId(1L);
        registerResultDTO.setUid("111");
        Mockito.when(driverAccountManagementHelper.registerDriverUserAccount(Mockito.any())).thenReturn(registerResultDTO);
        Boolean resultMap = recruitingCommandService.registryDriverAccountSuccess(drvDriverPO,1L,4);
        Assert.assertTrue(resultMap);
    }

}
