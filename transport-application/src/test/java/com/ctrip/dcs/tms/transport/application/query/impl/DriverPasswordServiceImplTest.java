package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DriverPasswordServiceImplTest {

    @InjectMocks
    private DriverPasswordServiceImpl driverPasswordService;

    @Mock
    private ApprovalProcessAuthQconfig authConfig;
    @Before
    public void setUp() throws Exception {
        Mockito.when(authConfig.getPwdKey()).thenReturn("vehVehiclePODalRepository");
    }

    @Test
    public void isPasswordEqual() {
        boolean b = driverPasswordService.isPasswordEqual("aa","aa","aa");
        Assert.assertTrue(!b);
    }

    @Test
    public void encryptPwd() {
        String str = driverPasswordService.encryptPwd("aa","aa");
        Assert.assertTrue(!str.isEmpty());
    }

    @Test
    public void genPwdSalt() {
        String  str = driverPasswordService.genPwdSalt();
        Assert.assertTrue(!str.isEmpty());
    }

    @Test
    public void genResetPwd() {
        String str = driverPasswordService.genResetPwd();
        Assert.assertTrue(!str.isEmpty());
    }
}