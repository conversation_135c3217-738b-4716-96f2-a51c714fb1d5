package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.dcs.pms.product.api.QueryProductSkuListRequestType;
import com.ctrip.dcs.pms.product.api.QueryProductSkuListResponseType;
import com.ctrip.dcs.pms.product.api.dto.SkuDTO;
import com.ctrip.dcs.price.compute.price.facade.param.QueryPoiAreaInfoResponseType;
import com.ctrip.dcs.price.sku.pricing.facade.dto.SimpleAreaGroupDTO;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryAreaGroupByIdRequestType;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryAreaGroupByIdResponseType;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryPointAreaInfoRequestType;
import com.ctrip.dcs.price.sku.pricing.facade.param.QueryPointAreaInfoResponseType;
import com.ctrip.dcs.scm.sdk.domain.CategoryRepository;
import com.ctrip.dcs.tms.transport.api.model.ConfigItemSOAType;
import com.ctrip.dcs.tms.transport.api.model.DriverRelationListRequestSOAType;
import com.ctrip.dcs.tms.transport.api.model.DriverRelationSOADTO;
import com.ctrip.dcs.tms.transport.api.model.InOrderConfigSOAType;
import com.ctrip.dcs.tms.transport.api.model.OrderTimeScope;
import com.ctrip.dcs.tms.transport.api.model.PoiDTO;
import com.ctrip.dcs.tms.transport.api.model.QueryApplyTransGroupsSkuForDspInfo;
import com.ctrip.dcs.tms.transport.api.model.QueryApplyTransGroupsSkuForDspRequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryBingIngSkuSOADTO;
import com.ctrip.dcs.tms.transport.api.model.QueryBingIngSkuSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupListSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupsForDspInfo;
import com.ctrip.dcs.tms.transport.api.model.QueryTransportInformResponseType;
import com.ctrip.dcs.tms.transport.application.common.MockExcutorService;
import com.ctrip.dcs.tms.transport.application.convert.TransportGroupConverter;
import com.ctrip.dcs.tms.transport.application.dto.TransportGroupInOrderConfigDTO;
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.ComputePriceSoaServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.MsgQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.IntoOrderConfigGateway;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.PhoneNumberServiceGateway;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.SkuPriceSoaServiceClientProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DriverRelationDetailPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.DrvLeaveDetailPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspIntoOrderConfigPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupPO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.TspTransportGroupSkuAreaRelationPO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.TmsTransportConstant;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.thread.IThreadPoolService;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.SensitiveDataControl;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.TransportgroupGateway;
import com.ctrip.dcs.tms.transport.infrastructure.gateway.TransportgroupSkuRelationGateway;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDriverLeaveRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.DrvDrvierRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.InOrderConfigRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TransportGroupRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupDriverRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuArearRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.TspTransportGroupSkuRelationRepository;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryDriverRelationDetailParam;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryTransportGroupCondition;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.QueryTransportGroupModel;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.RestResponseResult;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.FixedLocation;
import com.ctrip.igt.bizcomponent.basicdata.fixedlocation.Location;
import com.ctrip.igt.framework.common.base.PageHolder;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@RunWith(MockitoJUnitRunner.class)
@PrepareForTest({CompletableFuture.class})
public class TransportGroupQueryServiceImplTest extends Mockito {

    @InjectMocks
    TransportGroupQueryServiceImpl service;

    @Mock
    TransportgroupGateway transportgroupGateway;

    @Mock
    TransportgroupSkuRelationGateway transportgroupSkuRelationGateway;

    @Mock
    private SkuPriceSoaServiceClientProxy skuPriceSoaServiceClientProxy;
    @Mock
    private TransportGroupRepository transportGroupRepository;
    @Mock
    private TmsTransportQconfig tmsTransportQconfig;
    @Mock
    private AuthorizationCheckService authorizationCheckService;
    @Mock
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;
    @Mock
    EnumRepository enumRepository;
    @Mock
    private ProductionLineUtil productionLineUtil;
    @Mock
    private DrvDriverLeaveRepository drvDriverLeaveRepository;

    @Mock
    private InOrderConfigRepository inOrderConfigRepository;
    @Mock
    private SensitiveDataControl control;

    @Mock
    private TspTransportGroupSkuArearRelationRepository arearRelationRepository;

    @Mock
    private PhoneNumberServiceGateway phoneNumberSplitServiceProxy;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    IThreadPoolService threadPoolService;

    @Mock
    TspTransportGroupSkuRelationRepository transportGroupSkuRelationRepository;

    @Mock
    IntoOrderConfigGateway intoOrderConfigGateway;

    @Mock
    ComputePriceSoaServiceClientProxy computePriceSoaServiceClientProxy;

    @Mock
    MsgQconfig msgQconfig;

    @Before
    public void setUp() throws Exception {
        Mockito.when(tmsTransportQconfig.getRelationDrvListSwitch()).thenReturn(true);
        Mockito.when(tmsTransportQconfig.getTakeTimeScopeSwitch()).thenReturn(true);
    }

    @Test
    public void getAreaCityIds() {
        QueryAreaGroupByIdRequestType requestType = new QueryAreaGroupByIdRequestType();
        requestType.setAreaGroupIds(Arrays.asList(1L));
        QueryAreaGroupByIdResponseType responseType = new QueryAreaGroupByIdResponseType();
        List<SimpleAreaGroupDTO> areaGroups = Lists.newArrayList();
        SimpleAreaGroupDTO areaGroupDTO = new SimpleAreaGroupDTO();
        Map<String, String> regionInfo = Maps.newHashMap();
        regionInfo.put("1/1/1","111");
        areaGroupDTO.setRegionInfo(regionInfo);
        areaGroups.add(areaGroupDTO);
        responseType.setAreaGroups(areaGroups);
        when(skuPriceSoaServiceClientProxy.queryAreaGroupById(requestType)).thenReturn(responseType);
        List<Long> lsit = service.getAreaCityIds(1,1L);
        Assert.assertTrue(lsit.isEmpty());
    }

    @Test
    public void getAreaCityIdsIF1() {
        QueryAreaGroupByIdRequestType requestType = new QueryAreaGroupByIdRequestType();
        requestType.setAreaGroupIds(Arrays.asList(1L));
        QueryAreaGroupByIdResponseType responseType = new QueryAreaGroupByIdResponseType();
        List<SimpleAreaGroupDTO> areaGroups = Lists.newArrayList();
        SimpleAreaGroupDTO areaGroupDTO = new SimpleAreaGroupDTO();
        Map<String, String> regionInfo = Maps.newHashMap();
        regionInfo.put("1/1/1","111");
        areaGroupDTO.setRegionInfo(regionInfo);
        areaGroups.add(areaGroupDTO);
        responseType.setAreaGroups(areaGroups);
        when(skuPriceSoaServiceClientProxy.queryAreaGroupById(requestType)).thenReturn(responseType);
        List<Long>  list = service.getAreaCityIds(2,1L);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void getAreaCityIdsIF2() {
        QueryAreaGroupByIdRequestType requestType = new QueryAreaGroupByIdRequestType();
        requestType.setAreaGroupIds(Arrays.asList(1L));
        QueryAreaGroupByIdResponseType responseType = new QueryAreaGroupByIdResponseType();
        List<SimpleAreaGroupDTO> areaGroups = Lists.newArrayList();
        SimpleAreaGroupDTO areaGroupDTO = new SimpleAreaGroupDTO();
        Map<String, String> regionInfo = Maps.newHashMap();
        regionInfo.put("1/1/1","111");
        areaGroupDTO.setRegionInfo(regionInfo);
        areaGroups.add(areaGroupDTO);
//        responseType.setAreaGroups(areaGroups);
        when(skuPriceSoaServiceClientProxy.queryAreaGroupById(requestType)).thenReturn(responseType);
        List<Long>  list = service.getAreaCityIds(2,1L);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void getAreaCityIdsIF3() {
        QueryAreaGroupByIdRequestType requestType = new QueryAreaGroupByIdRequestType();
        requestType.setAreaGroupIds(Arrays.asList(1L));
        QueryAreaGroupByIdResponseType responseType = new QueryAreaGroupByIdResponseType();
        List<SimpleAreaGroupDTO> areaGroups = Lists.newArrayList();
        SimpleAreaGroupDTO areaGroupDTO = new SimpleAreaGroupDTO();
        Map<String, String> regionInfo = Maps.newHashMap();
        regionInfo.put("","111");
        areaGroupDTO.setRegionInfo(regionInfo);
        areaGroups.add(areaGroupDTO);
        responseType.setAreaGroups(areaGroups);
        when(skuPriceSoaServiceClientProxy.queryAreaGroupById(requestType)).thenReturn(responseType);
        List<Long>  list =service.getAreaCityIds(1,1L);
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void getAreaCityIdsIF4() {
        QueryAreaGroupByIdRequestType requestType = new QueryAreaGroupByIdRequestType();
        requestType.setAreaGroupIds(Arrays.asList(1L));
        QueryAreaGroupByIdResponseType responseType = new QueryAreaGroupByIdResponseType();
        List<SimpleAreaGroupDTO> areaGroups = Lists.newArrayList();
        SimpleAreaGroupDTO areaGroupDTO = new SimpleAreaGroupDTO();
        Map<String, String> regionInfo = Maps.newHashMap();
        regionInfo.put("1","111");
        areaGroupDTO.setRegionInfo(regionInfo);
        areaGroupDTO.setAreaGroupId(1L);
        areaGroups.add(areaGroupDTO);
        responseType.setAreaGroups(areaGroups);
        when(skuPriceSoaServiceClientProxy.queryAreaGroupById(requestType)).thenReturn(responseType);
        List<Long>  list =service.getAreaCityIds(1,1L);
        Assert.assertTrue(!list.isEmpty());
    }

    @Test
    public void queryRelationDrvList() {
        DriverRelationListRequestSOAType requestSOAType = new DriverRelationListRequestSOAType();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setTransportGroupMode(1001);
        when(transportGroupRepository.queryTransportGroupDetail(1L)).thenReturn(transportGroupPO);
        requestSOAType.setTransportGroupId(1L);
        requestSOAType.setSupplierIdList(Arrays.asList(1L));
        requestSOAType.setIsHasRelation(0);
        Map<String, String> map = Maps.newHashMap();
        map.put("accountId","30804");
        map.put("accountType","1");
        SessionHolder.setSessionSource(map);
        QueryDriverRelationDetailParam param = new QueryDriverRelationDetailParam();
        param.setTransportGroupId(1L);
        param.setIsHasRelation(0);
        param.setSupplierIdList(Arrays.asList(1L));
//        when(tspTransportGroupDriverRelationRepository.queryRelationDrvList(param)).thenReturn(Lists.newArrayList());
        when(enumRepository.getDrvLanguageMap()).thenReturn(Maps.newConcurrentMap());
        when(authorizationCheckService.checkAuthorizationTransportGroup(ImmutableList.of(requestSOAType.getTransportGroupId()), requestSOAType.getSupplierIdList().get(0))).thenReturn(true);
        when(productionLineUtil.getDAYProductLineCode()).thenReturn(1);
//        when(productionLineUtil.isProductLineCodeCheck(Arrays.asList(1),1)).thenReturn(true);
        Result<PageHolder<DriverRelationSOADTO>> result =  service.queryRelationDrvList(requestSOAType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryRelationDrvListTest() {
        DriverRelationListRequestSOAType requestSOAType = new DriverRelationListRequestSOAType();
        requestSOAType.setIsHasRelation(1);
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupMode(1001);
        requestSOAType.setTransportGroupId(1L);
        when(transportGroupRepository.queryTransportGroupDetail(requestSOAType.getTransportGroupId())).thenReturn(transportGroupPO);
//        when(productionLineUtil.isProductLineCodeCheck(Lists.newArrayList(), 0)).thenReturn(true);
        Result<PageHolder<DriverRelationSOADTO>> result = service.queryRelationDrvList(requestSOAType);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void getDrvLeaveStatus(){
        List<DrvLeaveDetailPO> drvDriverLeavePOList = Lists.newArrayList();
        DrvLeaveDetailPO drvLeaveDetailPO = new DrvLeaveDetailPO();
        drvLeaveDetailPO.setDrvId(1L);
        drvDriverLeavePOList.add(drvLeaveDetailPO);
        when(drvDriverLeaveRepository.queryDrvLeaveIng(Arrays.asList(1L))).thenReturn(drvDriverLeavePOList);
        Map<Long,Integer>  map = service.getDrvLeaveStatus(Arrays.asList(1L));
        Assert.assertTrue(map!=null);
    }

    @Test
    public void driverRelationDetailPOToDTO(){
        List<DriverRelationDetailPO> poList = Lists.newArrayList();
        DriverRelationDetailPO driverRelationDetailPO = new DriverRelationDetailPO();
        driverRelationDetailPO.setDrvId(1L);
        driverRelationDetailPO.setDrvFrom(1);
        poList.add(driverRelationDetailPO);
        Integer transportGroupMode = 1001;
        Long pointCityId = 1L;
        List<DriverRelationSOADTO>  lsit = service.driverRelationDetailPOToDTO(poList,transportGroupMode,pointCityId,null,null, true);
        Assert.assertTrue(!lsit.isEmpty());
    }

    @Test
    public void queryTransportGroupsForDsp() throws Exception {
        QueryTransportGroupModel queryTransportGroupModel = new QueryTransportGroupModel();
        queryTransportGroupModel.setSkuIds(Arrays.asList(1L));
        queryTransportGroupModel.setTransportGroupMode(1001);
        queryTransportGroupModel.setPoiType("GCJ02");
        queryTransportGroupModel.setLatitude(1D);
        queryTransportGroupModel.setLongitude(1D);
        queryTransportGroupModel.setPoiRef("pr");
        queryTransportGroupModel.setCarPlaceId("cpi");
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOS = Lists.newArrayList();
        TspTransportGroupSkuAreaRelationPO tspTransportGroupSkuAreaRelationPO = new TspTransportGroupSkuAreaRelationPO();
        tspTransportGroupSkuAreaRelationPO.setSkuId(1L);
        tspTransportGroupSkuAreaRelationPO.setTransportGroupId(1L);
        skuAreaRelationPOS.add(tspTransportGroupSkuAreaRelationPO);
        Mockito.when(transportgroupSkuRelationGateway.querySkuRelationListBySkuIds(queryTransportGroupModel.getSkuIds())).thenReturn(skuAreaRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setSupplierId(1L);
        transportGroupPO.setGroupStatus(0);
        transportGroupPO.setTransportGroupMode(1001);
        transportGroupPO.setAreaGroupId(1L);
        transportGroupPOList.add(transportGroupPO);
        Mockito.when(transportgroupGateway.queryTspTransportByIds(Arrays.asList(1L))).thenReturn(transportGroupPOList);
        QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
        queryPointAreaInfoRequestType.setCoordinateType(queryTransportGroupModel.getPoiType());
        queryPointAreaInfoRequestType.setPointLat(new BigDecimal(queryTransportGroupModel.getLatitude()));
        queryPointAreaInfoRequestType.setPointLng(new BigDecimal(queryTransportGroupModel.getLongitude()));
        queryPointAreaInfoRequestType.setAreaGroupIds(Arrays.asList(1L));
        queryPointAreaInfoRequestType.setPoiRef(queryTransportGroupModel.getPoiRef());
        queryPointAreaInfoRequestType.setCarplaceId(queryTransportGroupModel.getCarPlaceId());
        QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = new QueryPointAreaInfoResponseType();
        RestResponseResult restResponseResult = new RestResponseResult();
        restResponseResult.setRcode("200");
        queryPointAreaInfoResponseType.setResstatus(restResponseResult);
        Map<String, String> areaGroupInfo = Maps.newHashMap();
        areaGroupInfo.put("1","true");
        queryPointAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType)).thenReturn(queryPointAreaInfoResponseType);
        List<TspIntoOrderConfigPO> intoOrderConfigPOS = Lists.newArrayList();
        TspIntoOrderConfigPO tspIntoOrderConfigPO = new TspIntoOrderConfigPO();
        tspIntoOrderConfigPO.setTransportGroupId(1L);
        intoOrderConfigPOS.add(tspIntoOrderConfigPO);
        Mockito.when(intoOrderConfigGateway.queryInOrderConfigs(Arrays.asList(1L))).thenReturn(intoOrderConfigPOS);
        Result<List<QueryTransportGroupsForDspInfo>> result = service.queryTransportGroupsForDsp(queryTransportGroupModel);
        Assert.assertTrue(result.isSuccess());

    }

    @Test
    public void queryTransportGroupsForDsp1() throws Exception {
        QueryTransportGroupModel queryTransportGroupModel = new QueryTransportGroupModel();
        queryTransportGroupModel.setSkuIds(Arrays.asList(1L));
        queryTransportGroupModel.setTransportGroupMode(1001);
        queryTransportGroupModel.setPoiRef("pr");
        queryTransportGroupModel.setCarPlaceId("cpi");
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOS = Lists.newArrayList();
        TspTransportGroupSkuAreaRelationPO tspTransportGroupSkuAreaRelationPO = new TspTransportGroupSkuAreaRelationPO();
        tspTransportGroupSkuAreaRelationPO.setSkuId(1L);
        tspTransportGroupSkuAreaRelationPO.setTransportGroupId(1L);
        skuAreaRelationPOS.add(tspTransportGroupSkuAreaRelationPO);
        Mockito.when(transportgroupSkuRelationGateway.querySkuRelationListBySkuIds(queryTransportGroupModel.getSkuIds())).thenReturn(skuAreaRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setSupplierId(1L);
        transportGroupPO.setGroupStatus(0);
        transportGroupPO.setTransportGroupMode(1001);
        transportGroupPO.setAreaGroupId(1L);
        transportGroupPOList.add(transportGroupPO);
        Mockito.when(transportgroupGateway.queryTspTransportByIds(Arrays.asList(1L))).thenReturn(transportGroupPOList);
        QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
        queryPointAreaInfoRequestType.setCoordinateType(queryTransportGroupModel.getPoiType());
        queryPointAreaInfoRequestType.setAreaGroupIds(Arrays.asList(1L));
        queryPointAreaInfoRequestType.setPoiRef(queryTransportGroupModel.getPoiRef());
        queryPointAreaInfoRequestType.setCarplaceId(queryTransportGroupModel.getCarPlaceId());
        QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = new QueryPointAreaInfoResponseType();
        RestResponseResult restResponseResult = new RestResponseResult();
        restResponseResult.setRcode("200");
        queryPointAreaInfoResponseType.setResstatus(restResponseResult);
        Map<String, String> areaGroupInfo = Maps.newHashMap();
        areaGroupInfo.put("1","true");
        queryPointAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType)).thenReturn(queryPointAreaInfoResponseType);
        List<TspIntoOrderConfigPO> intoOrderConfigPOS = Lists.newArrayList();
        TspIntoOrderConfigPO tspIntoOrderConfigPO = new TspIntoOrderConfigPO();
        tspIntoOrderConfigPO.setTransportGroupId(1L);
        intoOrderConfigPOS.add(tspIntoOrderConfigPO);
        Mockito.when(intoOrderConfigGateway.queryInOrderConfigs(Arrays.asList(1L))).thenReturn(intoOrderConfigPOS);
        Result<List<QueryTransportGroupsForDspInfo>> result = service.queryTransportGroupsForDsp(queryTransportGroupModel);
        Assert.assertTrue(result.isSuccess());

    }

    @Test
    public void queryTransportGroupsForDsp2() throws Exception {
        QueryTransportGroupModel queryTransportGroupModel = new QueryTransportGroupModel();
        queryTransportGroupModel.setSkuIds(Arrays.asList(1L));
        queryTransportGroupModel.setTransportGroupMode(1001);
        queryTransportGroupModel.setCarPlaceId("cpi");
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOS = Lists.newArrayList();
        TspTransportGroupSkuAreaRelationPO tspTransportGroupSkuAreaRelationPO = new TspTransportGroupSkuAreaRelationPO();
        tspTransportGroupSkuAreaRelationPO.setSkuId(1L);
        tspTransportGroupSkuAreaRelationPO.setTransportGroupId(1L);
        skuAreaRelationPOS.add(tspTransportGroupSkuAreaRelationPO);
        Mockito.when(transportgroupSkuRelationGateway.querySkuRelationListBySkuIds(queryTransportGroupModel.getSkuIds())).thenReturn(skuAreaRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setSupplierId(1L);
        transportGroupPO.setGroupStatus(0);
        transportGroupPO.setTransportGroupMode(1001);
        transportGroupPO.setAreaGroupId(1L);
        transportGroupPOList.add(transportGroupPO);
        Mockito.when(transportgroupGateway.queryTspTransportByIds(Arrays.asList(1L))).thenReturn(transportGroupPOList);
        QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
        queryPointAreaInfoRequestType.setCoordinateType(queryTransportGroupModel.getPoiType());
        queryPointAreaInfoRequestType.setAreaGroupIds(Arrays.asList(1L));
        queryPointAreaInfoRequestType.setPoiRef(queryTransportGroupModel.getPoiRef());
        queryPointAreaInfoRequestType.setCarplaceId(queryTransportGroupModel.getCarPlaceId());
        QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = new QueryPointAreaInfoResponseType();
        RestResponseResult restResponseResult = new RestResponseResult();
        restResponseResult.setRcode("200");
        queryPointAreaInfoResponseType.setResstatus(restResponseResult);
        Map<String, String> areaGroupInfo = Maps.newHashMap();
        areaGroupInfo.put("1","true");
        queryPointAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType)).thenReturn(queryPointAreaInfoResponseType);
        List<TspIntoOrderConfigPO> intoOrderConfigPOS = Lists.newArrayList();
        TspIntoOrderConfigPO tspIntoOrderConfigPO = new TspIntoOrderConfigPO();
        tspIntoOrderConfigPO.setTransportGroupId(1L);
        intoOrderConfigPOS.add(tspIntoOrderConfigPO);
        Mockito.when(intoOrderConfigGateway.queryInOrderConfigs(Arrays.asList(1L))).thenReturn(intoOrderConfigPOS);
        Result<List<QueryTransportGroupsForDspInfo>> result = service.queryTransportGroupsForDsp(queryTransportGroupModel);
        Assert.assertTrue(result.isSuccess());

    }

    @Test
    public void queryTransportGroupsForDspWithPoiListNotEmpty() throws Exception {
        QueryTransportGroupModel queryTransportGroupModel = new QueryTransportGroupModel();
        queryTransportGroupModel.setSkuIds(Arrays.asList(1L));
        queryTransportGroupModel.setTransportGroupMode(1001);
        queryTransportGroupModel.setCarPlaceId("cpi");
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOS = Lists.newArrayList();
        TspTransportGroupSkuAreaRelationPO tspTransportGroupSkuAreaRelationPO = new TspTransportGroupSkuAreaRelationPO();
        tspTransportGroupSkuAreaRelationPO.setSkuId(1L);
        tspTransportGroupSkuAreaRelationPO.setTransportGroupId(1L);
        skuAreaRelationPOS.add(tspTransportGroupSkuAreaRelationPO);
        Mockito.when(transportgroupSkuRelationGateway.querySkuRelationListBySkuIds(queryTransportGroupModel.getSkuIds())).thenReturn(skuAreaRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setSupplierId(1L);
        transportGroupPO.setGroupStatus(0);
        transportGroupPO.setTransportGroupMode(1001);
        transportGroupPO.setAreaGroupId(1L);
        transportGroupPOList.add(transportGroupPO);
        Mockito.when(transportgroupGateway.queryTspTransportByIds(Arrays.asList(1L))).thenReturn(transportGroupPOList);
        QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
        queryPointAreaInfoRequestType.setCoordinateType(queryTransportGroupModel.getPoiType());
        queryPointAreaInfoRequestType.setAreaGroupIds(Arrays.asList(1L));
        queryPointAreaInfoRequestType.setPoiRef(queryTransportGroupModel.getPoiRef());
        queryPointAreaInfoRequestType.setCarplaceId(queryTransportGroupModel.getCarPlaceId());
        QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = new QueryPointAreaInfoResponseType();
        RestResponseResult restResponseResult = new RestResponseResult();
        restResponseResult.setRcode("200");
        queryPointAreaInfoResponseType.setResstatus(restResponseResult);
        Map<String, String> areaGroupInfo = Maps.newHashMap();
        areaGroupInfo.put("1","true");
        queryPointAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType)).thenReturn(queryPointAreaInfoResponseType);
        List<TspIntoOrderConfigPO> intoOrderConfigPOS = Lists.newArrayList();
        TspIntoOrderConfigPO tspIntoOrderConfigPO = new TspIntoOrderConfigPO();
        tspIntoOrderConfigPO.setTransportGroupId(1L);
        intoOrderConfigPOS.add(tspIntoOrderConfigPO);
        Mockito.when(intoOrderConfigGateway.queryInOrderConfigs(Arrays.asList(1L))).thenReturn(intoOrderConfigPOS);
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setLatitude(BigDecimal.valueOf(1));
        poiDTO.setLatitude(BigDecimal.valueOf(1));
        poiDTO.setPoiRef("poiRef");
        poiDTO.setPoiType("pioType");
        queryTransportGroupModel.setPoiList(Lists.newArrayList(poiDTO));
        QueryPoiAreaInfoResponseType poiAreaInfoResponseType = new QueryPoiAreaInfoResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        poiAreaInfoResponseType.setResponseResult(responseResult);
        poiAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(computePriceSoaServiceClientProxy.queryPoiAreaInfo(Mockito.any())).thenReturn(poiAreaInfoResponseType);
        Result<List<QueryTransportGroupsForDspInfo>> result = service.queryTransportGroupsForDsp(queryTransportGroupModel);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryTransportGroupsForDspWithPoiListNotEmpty2() throws Exception {
        QueryTransportGroupModel queryTransportGroupModel = new QueryTransportGroupModel();
        queryTransportGroupModel.setSkuIds(Arrays.asList(1L));
        queryTransportGroupModel.setTransportGroupMode(1001);
        queryTransportGroupModel.setCarPlaceId("cpi");
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOS = Lists.newArrayList();
        TspTransportGroupSkuAreaRelationPO tspTransportGroupSkuAreaRelationPO = new TspTransportGroupSkuAreaRelationPO();
        tspTransportGroupSkuAreaRelationPO.setSkuId(1L);
        tspTransportGroupSkuAreaRelationPO.setTransportGroupId(1L);
        skuAreaRelationPOS.add(tspTransportGroupSkuAreaRelationPO);
        Mockito.when(transportgroupSkuRelationGateway.querySkuRelationListBySkuIds(queryTransportGroupModel.getSkuIds())).thenReturn(skuAreaRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setSupplierId(1L);
        transportGroupPO.setGroupStatus(0);
        transportGroupPO.setTransportGroupMode(1001);
        transportGroupPO.setAreaGroupId(1L);
        transportGroupPOList.add(transportGroupPO);
        Mockito.when(transportgroupGateway.queryTspTransportByIds(Arrays.asList(1L))).thenReturn(transportGroupPOList);
        QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
        queryPointAreaInfoRequestType.setCoordinateType(queryTransportGroupModel.getPoiType());
        queryPointAreaInfoRequestType.setAreaGroupIds(Arrays.asList(1L));
        queryPointAreaInfoRequestType.setPoiRef(queryTransportGroupModel.getPoiRef());
        queryPointAreaInfoRequestType.setCarplaceId(queryTransportGroupModel.getCarPlaceId());
        QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = new QueryPointAreaInfoResponseType();
        RestResponseResult restResponseResult = new RestResponseResult();
        restResponseResult.setRcode("200");
        queryPointAreaInfoResponseType.setResstatus(restResponseResult);
        Map<String, String> areaGroupInfo = Maps.newHashMap();
        areaGroupInfo.put("1","true");
        queryPointAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType)).thenReturn(queryPointAreaInfoResponseType);
        List<TspIntoOrderConfigPO> intoOrderConfigPOS = Lists.newArrayList();
        TspIntoOrderConfigPO tspIntoOrderConfigPO = new TspIntoOrderConfigPO();
        tspIntoOrderConfigPO.setTransportGroupId(1L);
        intoOrderConfigPOS.add(tspIntoOrderConfigPO);
        PoiDTO poiDTO = new PoiDTO();
        poiDTO.setLatitude(BigDecimal.valueOf(1));
        poiDTO.setLatitude(BigDecimal.valueOf(1));
        poiDTO.setPoiRef("poiRef");
        poiDTO.setPoiType("pioType");
        queryTransportGroupModel.setPoiList(Lists.newArrayList(poiDTO));
        QueryPoiAreaInfoResponseType poiAreaInfoResponseType = new QueryPoiAreaInfoResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("500");
        poiAreaInfoResponseType.setResponseResult(responseResult);
        poiAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(computePriceSoaServiceClientProxy.queryPoiAreaInfo(Mockito.any())).thenReturn(poiAreaInfoResponseType);
        Mockito.when(msgQconfig.getTransportMsgSelectPointServiceAreaInfoError()).thenReturn("error");
        Result<List<QueryTransportGroupsForDspInfo>> result = service.queryTransportGroupsForDsp(queryTransportGroupModel);
        Assert.assertFalse(result.isSuccess());
    }


    @Test
    public void queryApplyTransGroupsSkuForDsp(){
        QueryApplyTransGroupsSkuForDspRequestType requestType = new QueryApplyTransGroupsSkuForDspRequestType();
        requestType.setCityIds(Arrays.asList(1L));
        requestType.setCarTypeIds(Arrays.asList(1L));
        List<TspTransportGroupPO> tspTransportGroupPOS = Lists.newArrayList();
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setTransportGroupId(1L);
        tspTransportGroupPO.setPointCityId(1L);
        tspTransportGroupPO.setVehicleTypeId(1L);
        tspTransportGroupPOS.add(tspTransportGroupPO);
        Mockito.when(transportGroupRepository.queryApplyTransportGroupByCityIds(Arrays.asList(1L),Arrays.asList(1L))).thenReturn(tspTransportGroupPOS);
        List<TspTransportGroupSkuAreaRelationPO> skuAreaRelationPOS = Lists.newArrayList();
        TspTransportGroupSkuAreaRelationPO skuAreaRelationPO = new TspTransportGroupSkuAreaRelationPO();
        skuAreaRelationPO.setTransportGroupId(1L);
        skuAreaRelationPO.setSkuId(1L);
        skuAreaRelationPOS.add(skuAreaRelationPO);
        Mockito.when(transportGroupSkuRelationRepository.querySkuRelationList(Arrays.asList(1L),Boolean.TRUE)).thenReturn(skuAreaRelationPOS);
        Result<List<QueryApplyTransGroupsSkuForDspInfo>> result = service.queryApplyTransGroupsSkuForDsp(requestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void queryTransportGroupListEmptyTest() {
        TspTransportGroupPO groupPO = new TspTransportGroupPO();
        QueryTransportGroupListSOARequestType soaRequestType = new QueryTransportGroupListSOARequestType();
        soaRequestType.setSkuIdList(Lists.newArrayList(12L));
        Result<PageHolder<TspTransportGroupPO>> pageHolderResult = service.queryTransportGroupList(groupPO, soaRequestType);
        Assert.assertTrue(pageHolderResult.isSuccess());
        Assert.assertTrue(pageHolderResult != null);
        Assert.assertTrue(pageHolderResult.getData() != null);
        Assert.assertTrue(CollectionUtils.isEmpty(pageHolderResult.getData().getData()));
    }

    @Test
    public void queryTransportGroupListTest() {
        TspTransportGroupPO groupPO = new TspTransportGroupPO();
        QueryTransportGroupListSOARequestType soaRequestType = new QueryTransportGroupListSOARequestType();
        soaRequestType.setSkuIdList(Lists.newArrayList(12L));
        TspTransportGroupSkuAreaRelationPO skuAreaRelationPO = new TspTransportGroupSkuAreaRelationPO();
        skuAreaRelationPO.setTransportGroupId(1L);
        Mockito.when(arearRelationRepository.queryTransportGroupSkuIds(Mockito.any())).thenReturn(Lists.newArrayList(skuAreaRelationPO));
        Result<PageHolder<TspTransportGroupPO>> pageHolderRes = service.queryTransportGroupList(groupPO, soaRequestType);
        Assert.assertTrue(pageHolderRes.isSuccess());
        Assert.assertTrue(pageHolderRes != null);
        Assert.assertTrue(pageHolderRes.getData() != null);
        Assert.assertTrue(CollectionUtils.isEmpty(pageHolderRes.getData().getData()));
        soaRequestType.setTransportGroupIdList(Lists.newArrayList(1L));
        Result<PageHolder<TspTransportGroupPO>> pageHolderResult = service.queryTransportGroupList(groupPO, soaRequestType);
        Assert.assertTrue(pageHolderResult.isSuccess());
        Assert.assertTrue(pageHolderResult != null);
        Assert.assertTrue(pageHolderResult.getData() != null);
        Assert.assertTrue(CollectionUtils.isEmpty(pageHolderResult.getData().getData()));
    }

    @Test
    public void queryOrderTimeScopeTestCase1() {
        QueryTransportGroupCondition condition = getCondition();
//        Mockito.when(arearRelationRepository.queryTransportGroupSkuIds(Mockito.any())).thenReturn(Lists.newArrayList());
        List<OrderTimeScope> scopeList = service.queryOrderTimeScope(condition.getSkuIdList(), condition);
        Assert.assertTrue(scopeList != null);
        Assert.assertTrue(scopeList.size() == condition.getSkuIdList().size());
        Assert.assertTrue(scopeList.get(0).getSkuId() == condition.getSkuIdList().get(0));
    }

    private QueryTransportGroupCondition getCondition() {
        QueryTransportGroupCondition condition = new QueryTransportGroupCondition();
        condition.setSkuIdList(Lists.newArrayList(1L,2L,3L));
        return condition;
    }

    private QueryTransportGroupCondition getConditionWithPoint() {
        QueryTransportGroupCondition condition = getCondition();
        condition.setLatitude(new BigDecimal(39.999987));
        condition.setLongitude(new BigDecimal(116.390054));
        condition.setPoiType("GCJ02");
        return condition;
    }

    @Test
    public void grayTransportId(){
        tmsTransportQconfig.getApplyTransportGrayConfig();
        Mockito.when(tmsTransportQconfig.getApplyTransportGrayConfig()).thenReturn("1,2");
        Boolean result = service.grayTransportId(1L);
        Assert.assertTrue(result);
    }

    @Test
    public void compareResult() {
        QueryTransportGroupCondition condition = new QueryTransportGroupCondition();
        condition.setSkuIdList(Arrays.asList(1L));
        condition.setLatitude(new BigDecimal(1));
        condition.setLongitude(new BigDecimal(1));
        condition.setPoiType("GCJ02");
        Map<Long,Boolean> orgResultMap = Maps.newHashMap();
        orgResultMap.put(1L,true);
        Map<Long,Boolean> newResultMap = Maps.newHashMap();
        newResultMap.put(2L,true);
        Boolean result = service.compareResult(condition,orgResultMap,newResultMap);
        Assert.assertTrue(result);
    }

    @Test
    public void getAreaGroupIdMap() {
        QueryTransportGroupCondition condition = new QueryTransportGroupCondition();
        condition.setSkuIdList(Arrays.asList(1L));
        condition.setLatitude(new BigDecimal(1));
        condition.setLongitude(new BigDecimal(1));
        condition.setPoiType("GCJ02");
        Map<String, Boolean> paramAreaMap = Maps.newHashMap();
        paramAreaMap.put("1",true);
        condition.setParamAreaMap(paramAreaMap);
        Map<Long,Boolean> orgResultMap = Maps.newHashMap();
        orgResultMap.put(1L,true);
        Map<Long,Boolean> newResultMap = Maps.newHashMap();
        newResultMap.put(2L,true);
        Map<Long, Boolean> result = service.getAreaGroupIdMap(condition,Arrays.asList(1L));
        Assert.assertTrue(!result.isEmpty());
    }
    @Test
    public void queryInOrderConfig() throws Exception {
        List<TransportGroupInOrderConfigDTO> result = service.queryInOrderConfig(Arrays.asList(11L,12L), new Date(),2L,"SHA", 1);
        Assert.assertTrue(result.size() == 0);
        Mockito.when(transportGroupRepository.queryProductLine(Mockito.any())).thenReturn(Arrays.asList(new TspTransportGroupPO()));
        result = service.queryInOrderConfig(Arrays.asList(11L,12L), new Date(),2L,"SHA", 1);
        Assert.assertTrue(result.size() == 0);
        TspIntoOrderConfigPO configPO = new TspIntoOrderConfigPO();
        configPO.setConfig("[{\"time\":\"00:00-23:59\",\"orderCount\":600}]");
        Mockito.when(inOrderConfigRepository.queryInOrderConfigs(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(configPO));
        result = service.queryInOrderConfig(Arrays.asList(11L,12L), new Date(),2L,"SHA", 1);
        Assert.assertTrue(result.size() == 1);
    }

    @Test
    public void testQueryTransportInformWithValidGroupId2() {
        Long transportGroupId = 1L;
        TspTransportGroupPO groupPO = new TspTransportGroupPO();
        groupPO.setIgtCode("86");
        groupPO.setStandbyIgtCode("1");

        when(transportGroupRepository.queryTransportGroupDetail(transportGroupId)).thenReturn(groupPO);

        Result<QueryTransportInformResponseType> result = service.queryTransportInform(transportGroupId);

        Assert.assertTrue(result.isSuccess());
        QueryTransportInformResponseType res = result.getData();
        Assert.assertNotNull(res);
        Assert.assertEquals(Arrays.asList("EN"), res.getSupportLanguageList());

        verify(transportGroupRepository).queryTransportGroupDetail(transportGroupId);
    }

    @Test
    public void testQueryTransportInformWithValidGroupId3() {
        Long transportGroupId = 1L;

        when(transportGroupRepository.queryTransportGroupDetail(transportGroupId)).thenReturn(null);

        Result<QueryTransportInformResponseType> result = service.queryTransportInform(transportGroupId);

        Assert.assertFalse(result.isSuccess());
        Assert.assertTrue(result.getCode().equals(ServiceResponseConstants.ResStatus.EXCEPTION_CODE));
        Assert.assertTrue(result.getMsg().equals(String.format("unable to find available transport group. transportGroupId:%s", transportGroupId)));

        verify(transportGroupRepository).queryTransportGroupDetail(transportGroupId);
    }

    @Test
    public void testQueryTransportInformWithValidGroupId6() {
        Long transportGroupId = 1L;
        TspTransportGroupPO groupPO = new TspTransportGroupPO();
        groupPO.setDispatcherPhone("1234567890");
        groupPO.setIgtCode("886");
        groupPO.setStandbyPhone("0987654321");
        groupPO.setStandbyIgtCode("852");

        when(transportGroupRepository.queryTransportGroupDetail(transportGroupId)).thenReturn(groupPO);

        Result<QueryTransportInformResponseType> result = service.queryTransportInform(transportGroupId);

        Assert.assertTrue(result.isSuccess());
        QueryTransportInformResponseType res = result.getData();
        Assert.assertNotNull(res);
        Assert.assertEquals(Arrays.asList("EN"), res.getSupportLanguageList());
        Assert.assertEquals("886", res.getMainPhoneCountryCode());
        Assert.assertEquals("", res.getMainPhoneCityCode());
        Assert.assertEquals("1234567890", res.getMainPhoneBodyNumber());
        Assert.assertEquals("852", res.getBackupPhoneCountryCode());
        Assert.assertEquals("", res.getBackupPhoneCityCode());
        Assert.assertEquals("0987654321", res.getBackupPhoneBodyNumber());

        verify(transportGroupRepository).queryTransportGroupDetail(transportGroupId);
        verify(phoneNumberSplitServiceProxy).batchSplitNumber(any());
    }

    @Test
    public void testQuerQyTransportInformWithValidGroupId7() {
        Long transportGroupId = 1L;
        TspTransportGroupPO groupPO = new TspTransportGroupPO();
        groupPO.setDispatcherPhone("1234567890");
        groupPO.setIgtCode("917");
        groupPO.setStandbyPhone("0987654321");
        groupPO.setStandbyIgtCode("64");

        when(transportGroupRepository.queryTransportGroupDetail(transportGroupId)).thenReturn(groupPO);
        List<String> numbers = Lists.newArrayList();
        numbers.add("00" + groupPO.getIgtCode() + groupPO.getDispatcherPhone());
        numbers.add("00" + groupPO.getStandbyIgtCode() + groupPO.getStandbyPhone());
        Map<String, NumberDTO> numberDTOMap = Maps.newHashMap();
        NumberDTO numberDTO = new NumberDTO();
        numberDTO.setCountryCode("1");
        numberDTO.setCityCode("2");
        numberDTO.setBodyNumber("3");
        numberDTOMap.put(numbers.get(0), numberDTO);
        NumberDTO numberDTO2 = new NumberDTO();
        numberDTO2.setCountryCode("4");
        numberDTO2.setCityCode("5");
        numberDTO2.setBodyNumber("6");
        numberDTOMap.put(numbers.get(1), numberDTO2);
        when(phoneNumberSplitServiceProxy.batchSplitNumber(TransportGroupConverter.buildSplitNumberReq(numbers))).thenReturn(numberDTOMap);

        Result<QueryTransportInformResponseType> result = service.queryTransportInform(transportGroupId);

        Assert.assertTrue(result.isSuccess());
        QueryTransportInformResponseType res = result.getData();
        Assert.assertNotNull(res);
        Assert.assertEquals(Arrays.asList("EN"), res.getSupportLanguageList());
        Assert.assertEquals("1", res.getMainPhoneCountryCode());
        Assert.assertEquals("2", res.getMainPhoneCityCode());
        Assert.assertEquals("3", res.getMainPhoneBodyNumber());
        Assert.assertEquals("4", res.getBackupPhoneCountryCode());
        Assert.assertEquals("5", res.getBackupPhoneCityCode());
        Assert.assertEquals("6", res.getBackupPhoneBodyNumber());
    }

    @Test
    public void inOrderUpperLimit() {
        List<InOrderConfigSOAType> inOrderConfigs =Lists.newArrayList();
        InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
        List<ConfigItemSOAType> configItems = Lists.newArrayList();
        ConfigItemSOAType soaType = new ConfigItemSOAType();
        soaType.setTime("00:00-08:59");
        soaType.setOrderCount(1);
        configItems.add(soaType);
        inOrderConfigSOAType.setConfigItems(configItems);
        inOrderConfigs.add(inOrderConfigSOAType);
        Long cityId = 101941L;
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","1");
        SessionHolder.setSessionSource(params);
        Mockito.when(enumRepository.getAreaScope(cityId)).thenReturn(1);
        Mockito.when(tmsTransportQconfig.getInOrderUpperLimit()).thenReturn(10);
        Result<Boolean> result = service.inOrderUpperLimit(inOrderConfigs,cityId);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void inOrderUpperLimit1() {
        List<InOrderConfigSOAType> inOrderConfigs =Lists.newArrayList();
        InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
        List<ConfigItemSOAType> configItems = Lists.newArrayList();
        ConfigItemSOAType soaType = new ConfigItemSOAType();
        soaType.setTime("00:00-08:59");
        soaType.setOrderCount(1);
        configItems.add(soaType);
        inOrderConfigSOAType.setConfigItems(configItems);
        inOrderConfigs.add(inOrderConfigSOAType);
        Long cityId = 101941L;
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","1");
        Result<Boolean> result = service.inOrderUpperLimit(inOrderConfigs,cityId);
        Assert.assertTrue(result.isSuccess());
    }


    @Test
    public void inOrderUpperLimit2() {
        List<InOrderConfigSOAType> inOrderConfigs =Lists.newArrayList();
        InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
        List<ConfigItemSOAType> configItems = Lists.newArrayList();
        ConfigItemSOAType soaType = new ConfigItemSOAType();
        soaType.setTime("00:00-08:59");
        soaType.setOrderCount(1);
        configItems.add(soaType);
        inOrderConfigSOAType.setConfigItems(configItems);
        inOrderConfigs.add(inOrderConfigSOAType);
        Long cityId = 101941L;
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","2");
        SessionHolder.setSessionSource(params);
        Result<Boolean> result = service.inOrderUpperLimit(inOrderConfigs,cityId);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void inOrderUpperLimit3() {
        List<InOrderConfigSOAType> inOrderConfigs =Lists.newArrayList();
        InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
        List<ConfigItemSOAType> configItems = Lists.newArrayList();
        ConfigItemSOAType soaType = new ConfigItemSOAType();
        soaType.setTime("00:00-08:59");
        soaType.setOrderCount(1);
        configItems.add(soaType);
        inOrderConfigSOAType.setConfigItems(configItems);
        inOrderConfigs.add(inOrderConfigSOAType);
        Long cityId = 101941L;
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","1");
        SessionHolder.setSessionSource(params);
        Mockito.when(enumRepository.getAreaScope(cityId)).thenReturn(0);
        Result<Boolean> result = service.inOrderUpperLimit(inOrderConfigs,cityId);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void inOrderUpperLimit4() {
        List<InOrderConfigSOAType> inOrderConfigs =Lists.newArrayList();
        InOrderConfigSOAType inOrderConfigSOAType = new InOrderConfigSOAType();
        List<ConfigItemSOAType> configItems = Lists.newArrayList();
        ConfigItemSOAType soaType = new ConfigItemSOAType();
        soaType.setTime("00:00-08:59");
        soaType.setOrderCount(11);
        configItems.add(soaType);
        inOrderConfigSOAType.setConfigItems(configItems);
        inOrderConfigs.add(inOrderConfigSOAType);
        inOrderConfigSOAType = new InOrderConfigSOAType();
        soaType = new ConfigItemSOAType();
        soaType.setTime("00:00-08:59");
        soaType.setOrderCount(11);
        configItems.add(soaType);
        inOrderConfigSOAType.setConfigItems(Lists.newArrayList());
        inOrderConfigs.add(inOrderConfigSOAType);
        Long cityId = 101941L;
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","1");
        SessionHolder.setSessionSource(params);
        Mockito.when(enumRepository.getAreaScope(cityId)).thenReturn(1);
        Mockito.when(tmsTransportQconfig.getInOrderUpperLimit()).thenReturn(10);
        Result<Boolean> result = service.inOrderUpperLimit(inOrderConfigs,cityId);
        Assert.assertTrue(result.isSuccess());
    }


    @Test
    public void inOrderUpperLimit5() {
        List<InOrderConfigSOAType> inOrderConfigs =Lists.newArrayList();
        Result<Boolean> result = service.inOrderUpperLimit(inOrderConfigs,1L);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void getAreaGroupIdMap1() {
        QueryTransportGroupCondition condition = new QueryTransportGroupCondition();
        condition.setSkuIdList(Arrays.asList(1L));
        condition.setLatitude(new BigDecimal(1));
        condition.setLongitude(new BigDecimal(1));
        condition.setPoiType("GCJ02");
        condition.setPoiRef("pr");
        condition.setCarPlaceId("cpi");
        QueryPointAreaInfoRequestType queryPointAreaInfoRequestType = new QueryPointAreaInfoRequestType();
        queryPointAreaInfoRequestType.setCoordinateType(condition.getPoiType());
        queryPointAreaInfoRequestType.setPointLat(condition.getLatitude());
        queryPointAreaInfoRequestType.setPointLng(condition.getLongitude());
        queryPointAreaInfoRequestType.setAreaGroupIds(Arrays.asList(1L));
        queryPointAreaInfoRequestType.setPoiRef(condition.getPoiRef());
        queryPointAreaInfoRequestType.setCarplaceId(condition.getCarPlaceId());
        QueryPointAreaInfoResponseType queryPointAreaInfoResponseType = new QueryPointAreaInfoResponseType();
        RestResponseResult restResponseResult = new RestResponseResult();
        restResponseResult.setRcode("200");
        queryPointAreaInfoResponseType.setResstatus(restResponseResult);
        Map<String, String> areaGroupInfo = Maps.newHashMap();
        areaGroupInfo.put("1","true");
        queryPointAreaInfoResponseType.setAreaGroupInfo(areaGroupInfo);
        Mockito.when(skuPriceSoaServiceClientProxy.queryPointAreaInfo(queryPointAreaInfoRequestType)).thenReturn(queryPointAreaInfoResponseType);
        Map<Long, Boolean> result = service.getAreaGroupIdMap(condition,Arrays.asList(1L));
        Assert.assertTrue(result.get(1L));
    }

    @Test
    public void queryDrvIdByTransportGroups() {
        // Mock input data
        List<Long> transportGroupIdList = Arrays.asList(1L, 2L, 3L);
        Integer temporaryDispatchMark = 1;

        PowerMockito.when(tspTransportGroupDriverRelationRepository.queryDrvIdListByTransportGroups(transportGroupIdList)).thenReturn(Arrays.asList(4L, 5L, 6L));

        // Mock repository method
        List<Long> drvIdList = Arrays.asList(1L);
        List<Long> drvIdList2 = Arrays.asList(2L);
        List<Long> drvIdList3 = Arrays.asList(3L);
        Mockito.when(tmsTransportQconfig.getInQueryBatchSize()).thenReturn(1);
        Mockito.when(threadPoolService.getInQueryDriverPool()).thenReturn(new MockExcutorService());
        Mockito.when(tmsTransportQconfig.getInQueryBatchSwitch()).thenReturn(true);
        PowerMockito.when(drvDrvierRepository.queryDrvIdByMarkPage(Mockito.anyList(), Mockito.eq(temporaryDispatchMark))).thenReturn(drvIdList, drvIdList2, drvIdList3);

        // Call the method under test
        List<Long> result = service.queryDrvIdByTransportGroups(transportGroupIdList, temporaryDispatchMark);
        // Assert the result
        Assert.assertEquals(transportGroupIdList, result);
    }

    @Test
    public void queryDrvIdByTransportGroups2() {
        // Mock input data
        List<Long> transportGroupIdList = Arrays.asList(1L, 2L, 3L);
        Integer temporaryDispatchMark = 1;

        PowerMockito.when(tspTransportGroupDriverRelationRepository.queryDrvIdListByTransportGroups(transportGroupIdList)).thenReturn(Arrays.asList(4L, 5L, 6L));

        // Mock repository method
        List<Long> drvIdList = Arrays.asList(1L,2L,3L);
//        Mockito.when(tmsTransportQconfig.getInQueryBatchSize()).thenReturn(1);
//        Mockito.when(threadPoolService.getInQueryDriverPool()).thenReturn(new MockExcutorService());
        Mockito.when(tmsTransportQconfig.getInQueryBatchSwitch()).thenReturn(false);
        PowerMockito.when(drvDrvierRepository.queryDrvIdByMarkPage(Mockito.anyList(), Mockito.eq(temporaryDispatchMark))).thenReturn(drvIdList);

        // Call the method under test
        List<Long> result = service.queryDrvIdByTransportGroups(transportGroupIdList, temporaryDispatchMark);
        // Assert the result
        Assert.assertEquals(transportGroupIdList, result);
    }

    public void buildListRequestType() {
        QueryBingIngSkuSOARequestType queryBingIngSkuRequestType = new QueryBingIngSkuSOARequestType();
        queryBingIngSkuRequestType.setCityId(Lists.newArrayList(6L));
        List<Long> skuIds = Lists.newArrayList();
        List<Long> serviceProviderIds = Lists.newArrayList();
        Long lineCode = 1L;
        Mockito.when(productionLineUtil.getSubCategoryCode(1L)).thenReturn(Lists.newArrayList("airport_pickup", "airport_dropoff"));
        QueryProductSkuListRequestType response =
          service.buildListRequestType(queryBingIngSkuRequestType, skuIds, serviceProviderIds, lineCode);
        Assert.assertEquals(response.getInclusionFilter().getCategoryIds(), queryBingIngSkuRequestType.getCategoryId());
        Assert.assertEquals(Lists.newArrayList("airport_pickup", "airport_dropoff"), response.getInclusionFilter().getCategoryCodes());

        Mockito.when(productionLineUtil.getCategoryCodeList(anyList())).thenReturn(Lists.newArrayList("airport_pickup"));
        queryBingIngSkuRequestType.setCategoryId(Lists.newArrayList(6L));
        response =
          service.buildListRequestType(queryBingIngSkuRequestType, skuIds, serviceProviderIds, lineCode);

        Assert.assertEquals(Lists.newArrayList("airport_pickup"), response.getInclusionFilter().getCategoryCodes());

    }

    @Test
    public void getData() {
        QueryProductSkuListResponseType regionListResponseType = new QueryProductSkuListResponseType();
        SkuDTO skuDTO = new SkuDTO();
        skuDTO.setLineType(1);
        skuDTO.setPropertyList(Lists.newArrayList());
        regionListResponseType.setSkuList(Lists.newArrayList(skuDTO));
        DalRepository<TspTransportGroupPO> tspTransportGroupRepo = Mockito.mock(DalRepository.class);
        Mockito.when(transportGroupRepository.getTspTransportGroupRepo()).thenReturn(tspTransportGroupRepo);
        Mockito.when(tspTransportGroupRepo.queryByPk(anyLong())).thenReturn(new TspTransportGroupPO());
        FixedLocation fixedLocation = new FixedLocation();
        Location location = new Location();
        fixedLocation.setLocation(location);
        Mockito.when(enumRepository.getFixedLocation(any(), any())).thenReturn(fixedLocation);
        Mockito.when(enumRepository.getCityName(any())).thenReturn("city");
        Mockito.when(enumRepository.getVehicleTypeName(any())).thenReturn("veh");
        Mockito.when(productionLineUtil.getCategoryName(any())).thenReturn("cate");
        List<QueryBingIngSkuSOADTO> data = service.getData(regionListResponseType, 1L);

        Assert.assertTrue("linetype error", Objects.equals(data.get(0).getLineType(), 1));

    }
}
