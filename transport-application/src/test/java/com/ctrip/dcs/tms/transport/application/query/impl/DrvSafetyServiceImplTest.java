package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DrvSafetyServiceImplTest {

    @InjectMocks
    DrvSafetyServiceImpl drvSafetyService;
    @Mock
    private DrvDrvierRepository drvierRepository;

    @Mock
    private EpidemicPreventionControlQconfig epidemicPreventionControlQconfig;

    @Mock
    private DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;

    @Test
    public void queryUploadReportStatus() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        Mockito.when(drvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        EpidemicPreventionControlCityInfoDTO controlCityInfoDTO = new EpidemicPreventionControlCityInfoDTO();
        controlCityInfoDTO.setReportConstraintModel(1);
        controlCityInfoDTO.setCityId(1L);
        Mockito.when(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(drvDriverPO.getCityId())).thenReturn(controlCityInfoDTO);
        DrvEpidemicPreventionControlInfoPO infoPO = new DrvEpidemicPreventionControlInfoPO();
        infoPO.setNucleicAcidReportResultStatus(1);
        infoPO.setInformStatus(1);
        infoPO.setVaccineReportStatus(4);
        infoPO.setVaccineReportResultStatus(1);
        infoPO.setNucleicAcidReportStatus(4);
        Mockito.when(drvEpidemicPreventionControlInfoRepository.queryByDrvId(drvDriverPO.getDrvId())).thenReturn(infoPO);
        Result<Boolean> result = drvSafetyService.queryUploadReportStatus(1L);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void queryUploadReportStatus2() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        Mockito.when(drvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        EpidemicPreventionControlCityInfoDTO controlCityInfoDTO = new EpidemicPreventionControlCityInfoDTO();
        controlCityInfoDTO.setReportConstraintModel(2);
        controlCityInfoDTO.setCityId(1L);
        Mockito.when(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(drvDriverPO.getCityId())).thenReturn(controlCityInfoDTO);
        DrvEpidemicPreventionControlInfoPO infoPO = new DrvEpidemicPreventionControlInfoPO();
        infoPO.setNucleicAcidReportResultStatus(1);
        infoPO.setInformStatus(1);
        infoPO.setVaccineReportStatus(4);
        infoPO.setVaccineReportResultStatus(1);
        infoPO.setNucleicAcidReportStatus(4);
        Mockito.when(drvEpidemicPreventionControlInfoRepository.queryByDrvId(drvDriverPO.getDrvId())).thenReturn(infoPO);
        Result<Boolean> result = drvSafetyService.queryUploadReportStatus(1L);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void queryUploadReportStatus3() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        Mockito.when(drvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        EpidemicPreventionControlCityInfoDTO controlCityInfoDTO = new EpidemicPreventionControlCityInfoDTO();
        controlCityInfoDTO.setReportConstraintModel(3);
        controlCityInfoDTO.setCityId(1L);
        Mockito.when(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(drvDriverPO.getCityId())).thenReturn(controlCityInfoDTO);
        DrvEpidemicPreventionControlInfoPO infoPO = new DrvEpidemicPreventionControlInfoPO();
        infoPO.setNucleicAcidReportResultStatus(1);
        infoPO.setInformStatus(1);
        infoPO.setVaccineReportStatus(4);
        infoPO.setVaccineReportResultStatus(1);
        infoPO.setNucleicAcidReportStatus(4);
        Mockito.when(drvEpidemicPreventionControlInfoRepository.queryByDrvId(drvDriverPO.getDrvId())).thenReturn(infoPO);
        Result<Boolean> result = drvSafetyService.queryUploadReportStatus(1L);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void queryUploadReportStatus4() {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(1L);
        Mockito.when(drvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        EpidemicPreventionControlCityInfoDTO controlCityInfoDTO = new EpidemicPreventionControlCityInfoDTO();
        controlCityInfoDTO.setReportConstraintModel(4);
        controlCityInfoDTO.setCityId(1L);
        Mockito.when(epidemicPreventionControlQconfig.getEpidemicPreventionControlCityMap(drvDriverPO.getCityId())).thenReturn(controlCityInfoDTO);
        DrvEpidemicPreventionControlInfoPO infoPO = new DrvEpidemicPreventionControlInfoPO();
        infoPO.setNucleicAcidReportResultStatus(1);
        infoPO.setInformStatus(1);
        infoPO.setVaccineReportStatus(4);
        infoPO.setVaccineReportResultStatus(1);
        infoPO.setNucleicAcidReportStatus(4);
        Mockito.when(drvEpidemicPreventionControlInfoRepository.queryByDrvId(drvDriverPO.getDrvId())).thenReturn(infoPO);
        Result<Boolean> result = drvSafetyService.queryUploadReportStatus(1L);
        Assert.assertTrue(!result.isSuccess());
    }
}
