package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.price.sku.pricing.facade.dto.*;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.helper.ShortTransportGroupHelper;
import com.ctrip.dcs.tms.transport.application.query.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.http.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.GlobalApplyDriverLeaveDurationVO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.DateUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.ResponseResultUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.InsertBatchRelationParam;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.UpdateRelationStatusParam;
import com.ctrip.dcs.tms.transport.infrastructure.service.IVRCallService;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.internal.matchers.Any;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.SQLException;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class TransportGroupCommandServiceImplTest {

    @InjectMocks
    private TransportGroupCommandServiceImpl transportGroupCommandService;

    @Mock
    private TmsModRecordCommandService tmsModRecordCommandService;

    @Mock
    private TransportGroupRepository transportGroupRepository;

    @Mock
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;

    @Mock
    private TspTransportGroupSkuArearRelationRepository tspTransportGroupSkuArearRelationRepository;

    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Mock
    private TspTransportGroupPO tspTransportGroupPO;
    @Mock
    private WorkShiftRepository workShiftRepository;
    @Mock
    private DriverPointsQueryProxy driverPointsQueryProxy;
    @Mock
    private TransportGroupQueryService transportGroupQueryService;
    @Mock
    private DrvDrvierRepository drvDrvierRepository;
    @Mock
    TmsTransportQconfig qconfig;
    @Mock
    private DriverCommandService driverCommandService;
    @Mock
    TransportTrackRecordRepository transportTrackRecordRepository;
    @Mock
    DriverQueryService driverQueryService;

    @Mock
    private VehicleRepository vehicleRepository;

    @Spy
    private ProductionLineUtil productionLineUtil;

    @Spy
    private BindRuleConfig bindRuleConfig;

    @Mock
    DriverGuideProxy driverGuideProxy;

    @Mock
    ShortTransportGroupHelper shortTransportGroupHelper;

    @Mock
    IVRCallService ivrCallService;

    @Mock
    EnumRepository enumRepository;

    @Mock
    MobileHelper mobileHelper;

    @Test
    public void updateTransportGroupStatus() {
        tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setGroupStatus(0);
        tspTransportGroupPO.setTransportGroupId(1L);

        TspTransportGroupPO tspTransportGroupPO1 = new TspTransportGroupPO();
        tspTransportGroupPO1.setGroupStatus(1);
        tspTransportGroupPO1.setTransportGroupId(1L);
        Mockito.when(transportGroupRepository.queryTransportGroupDetail(tspTransportGroupPO.getTransportGroupId())).thenReturn(tspTransportGroupPO1);
        Mockito.when(qconfig.getProhibitOperationTransportGroupCodeList()).thenReturn(Arrays.asList(1002));
        Mockito.when(mobileHelper.isMobileVerified(any(), any(), any(), any())).thenReturn(ResponseResultUtil.success());
        Result<Boolean> result = transportGroupCommandService.updateTransportGroupStatus(tspTransportGroupPO);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateTransportGroupStatus2() {
        tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setGroupStatus(0);
        tspTransportGroupPO.setTransportGroupId(1L);

        TspTransportGroupPO tspTransportGroupPO1 = new TspTransportGroupPO();
        tspTransportGroupPO1.setGroupStatus(1);
        tspTransportGroupPO1.setTransportGroupId(1L);
        Mockito.when(transportGroupRepository.queryTransportGroupDetail(tspTransportGroupPO.getTransportGroupId())).thenReturn(tspTransportGroupPO1);
        Mockito.when(qconfig.getProhibitOperationTransportGroupCodeList()).thenReturn(Arrays.asList(1002));
        Mockito.when(mobileHelper.isMobileVerified(any(), any(), any(), any())).thenReturn(ResponseResultUtil.success());
        Result<Boolean> result = transportGroupCommandService.updateTransportGroupStatus(tspTransportGroupPO);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateTransportGroupStatus3() {
        tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setGroupStatus(0);
        tspTransportGroupPO.setCategorySynthesizeCode(4);
        tspTransportGroupPO.setTransportGroupId(1L);

        TspTransportGroupPO tspTransportGroupPO1 = new TspTransportGroupPO();
        tspTransportGroupPO1.setGroupStatus(1);
        tspTransportGroupPO1.setTransportGroupId(1L);
        Mockito.when(transportGroupRepository.queryTransportGroupDetail(tspTransportGroupPO.getTransportGroupId())).thenReturn(tspTransportGroupPO1);
        Mockito.when(qconfig.getProhibitOperationTransportGroupCodeList()).thenReturn(Arrays.asList(1002));
        Mockito.when(mobileHelper.isMobileVerified(any(), any(), any(), any())).thenReturn(ResponseResultUtil.success());
        Result<Boolean> result = transportGroupCommandService.updateTransportGroupStatus(tspTransportGroupPO);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateTransportGroupStatus4() {
        tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setGroupStatus(0);
        tspTransportGroupPO.setCategorySynthesizeCode(4);
        tspTransportGroupPO.setTransportGroupId(1L);

        TspTransportGroupPO tspTransportGroupPO1 = new TspTransportGroupPO();
        tspTransportGroupPO1.setGroupStatus(1);
        tspTransportGroupPO1.setTransportGroupId(1L);
        tspTransportGroupPO1.setCategorySynthesizeCode(4);
        tspTransportGroupPO1.setSupplierId(1L);
        Mockito.when(transportGroupRepository.queryTransportGroupDetail(tspTransportGroupPO.getTransportGroupId())).thenReturn(tspTransportGroupPO1);
        Mockito.when(qconfig.getProhibitOperationTransportGroupCodeList()).thenReturn(Arrays.asList(1002));
        Mockito.when(driverGuideProxy.getGrayControl(Mockito.anyLong())).thenReturn(true);
        Mockito.when(mobileHelper.isMobileVerified(any(), any(), any(), any())).thenReturn(ResponseResultUtil.success());
        Result<Boolean> result = transportGroupCommandService.updateTransportGroupStatus(tspTransportGroupPO);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void judgeMissingFill() {
        Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap = Maps.newHashMap();
        TspTransportGroupWorkShiftPO tspTransportGroupWorkShiftPO = new TspTransportGroupWorkShiftPO();
        tspTransportGroupWorkShiftPO.setId(1l);
        tspTransportGroupWorkShiftPO.setDriverUpperLimit(1L);
        workShiftPOSMap.put(1L,tspTransportGroupWorkShiftPO);
        Long workShiftId = 1L;
        Map<Long, List<Long>> workShiftApplyInfo = Maps.newHashMap();
        workShiftApplyInfo.put(1L, Arrays.asList(1L));
        String str = transportGroupCommandService.judgeMissingFill(workShiftPOSMap,workShiftId,workShiftApplyInfo);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void judgeMissingFill1() {
        Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap = Maps.newHashMap();
        TspTransportGroupWorkShiftPO tspTransportGroupWorkShiftPO = new TspTransportGroupWorkShiftPO();
        tspTransportGroupWorkShiftPO.setId(1l);
        tspTransportGroupWorkShiftPO.setDriverUpperLimit(0L);
        workShiftPOSMap.put(1L,tspTransportGroupWorkShiftPO);
        Long workShiftId = 1L;
        Map<Long, List<Long>> workShiftApplyInfo = Maps.newHashMap();
        workShiftApplyInfo.put(1L, Arrays.asList(1L));
        String str = transportGroupCommandService.judgeMissingFill(workShiftPOSMap,workShiftId,workShiftApplyInfo);
        Assert.assertTrue(str!=null);
    }

    @Test
    public void updateTransportGroupApplyStatus() {
        Map<Long, TspTransportGroupWorkShiftPO> workShiftPOSMap = Maps.newHashMap();
        TspTransportGroupWorkShiftPO tspTransportGroupWorkShiftPO = new TspTransportGroupWorkShiftPO();
        tspTransportGroupWorkShiftPO.setId(1l);
        tspTransportGroupWorkShiftPO.setDriverUpperLimit(1L);
        workShiftPOSMap.put(1L,tspTransportGroupWorkShiftPO);
        Map<Long, List<Long>> workShiftApplyInfo = Maps.newHashMap();
        workShiftApplyInfo.put(1L, Arrays.asList(1L));
        UpdateTransportGroupApplyStatusSOARequestType soaRequestType = new UpdateTransportGroupApplyStatusSOARequestType();
        soaRequestType.setUpdateType(2);
        soaRequestType.setTransportGroupId(1L);
        soaRequestType.setModifyUser("1");
        soaRequestType.setWorkShiftId(1L);
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setTransportGroupId(1L);
        tspTransportGroupPO.setGroupStatus(0);
        tspTransportGroupPO.setTransportGroupMode(1006);
        tspTransportGroupPO.setSupplierId(30804L);
        tspTransportGroupPO.setPointCityId(1L);
        tspTransportGroupPO.setVehicleTypeId(1L);
        Mockito.when(transportGroupRepository.queryTransportGroupDetail(1L)).thenReturn(tspTransportGroupPO);
        List<TspTransportGroupWorkShiftPO> workShiftPOS = Lists.newArrayList();
        TspTransportGroupWorkShiftPO shiftPO = new TspTransportGroupWorkShiftPO();
        shiftPO.setId(1L);
        shiftPO.setDriverUpperLimit(1L);
        workShiftPOS.add(shiftPO);
        Mockito.when(workShiftRepository.queryWorkShifts(tspTransportGroupPO.getSupplierId(), tspTransportGroupPO.getPointCityId(), tspTransportGroupPO.getVehicleTypeId(), tspTransportGroupPO.getTransportGroupMode(),null)).thenReturn(workShiftPOS);
        List<ApplyDriverRelationDetailPO> detailPOS = Lists.newArrayList();
        ApplyDriverRelationDetailPO relationDetailPO = new ApplyDriverRelationDetailPO();
        relationDetailPO.setWorkShiftId(1L);
        relationDetailPO.setApplyStatus(1);
        relationDetailPO.setDrvId(1L);
        relationDetailPO.setDrvStatus(1);
        relationDetailPO.setVehicleId(1L);
        relationDetailPO.setCityId(1L);
        detailPOS.add(relationDetailPO);
        Mockito.when(tspTransportGroupDriverRelationRepository.queryRelationDrvList(Lists.newArrayList(workShiftPOSMap.keySet()),Lists.newArrayList(TmsTransportConstant.ApplyStatusEnum.APPLIED.getCode(),TmsTransportConstant.ApplyStatusEnum.SUCCESS.getCode()))).thenReturn(detailPOS);
        Map<Long,List<DriverPoint>> longListMap = Maps.newHashMap();
        List<DriverPoint> driverPointList = Lists.newArrayList();
        DriverPoint driverPoint = new DriverPoint();
        driverPoint.setDriverId(1L);
        driverPoint.setPoints(50D);
        driverPoint.setCityRanking(1L);
        driverPointList.add(driverPoint);
        longListMap.put(1L,driverPointList);
        Result<Map<Long,List<DriverPoint>>> pointDrvMap = Result.Builder.<Map<Long,List<DriverPoint>>>newResult().success().withData(longListMap).build();
        Mockito.when(driverPointsQueryProxy.queryDrvCityRanking("1",1L)).thenReturn(pointDrvMap);
        Mockito.when(qconfig.getApplyTransportDurationDThreshold()).thenReturn(10);
        List<TransportTrackRecordPO> transportTrackRecordPOS = Lists.newArrayList();
        TransportTrackRecordPO transportTrackRecordPO = new TransportTrackRecordPO();
        transportTrackRecordPO.setDatachangeCreatetime(DateUtil.string2Timestamp("2023-02-26 00:00:00",DateUtil.YYYYMMDDHHMMSS));
        transportTrackRecordPOS.add(transportTrackRecordPO);
        Mockito.when(transportTrackRecordRepository.queryTrackByTransportGroupId(soaRequestType.getTransportGroupId(), TmsTransportConstant.ApplyTransPortOperationTypeEnum.GLOBAL_TRACK.getCode())).thenReturn(transportTrackRecordPOS);
        Mockito.when(qconfig.getApplyTransportDDayThreshold()).thenReturn(10);
        Map<Long,Long> durationMap =  Maps.newHashMap();
        durationMap.put(1L,20L);
        Result<UpdateTransportGroupApplyStatusSOADTO>  result = transportGroupCommandService.updateTransportGroupApplyStatus(soaRequestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void unBingTransportAssociated(){
        Long transportGroupId = 1L;
        Long pointCityId = 3L;
        Integer originAreaType = 1;
        Integer targetAreaType = 1;
        Long originAreaGroupId = 1L;
        Long targetAreaGroupId = 2L;
        String modifyUser = "11";
        Map<Long,List<SimpleAreaGroupDTO>> resultMap = Maps.newHashMap();
        List<SimpleAreaGroupDTO> areaGroupDTOList = Lists.newArrayList();
        SimpleAreaGroupDTO groupDTO = new SimpleAreaGroupDTO();
        Map<String,String> regionMap = Maps.newHashMap();
        regionMap.put("1/1/1","北京");
        groupDTO.setRegionInfo(regionMap);
        areaGroupDTOList.add(groupDTO);
        resultMap.put(1L,areaGroupDTOList);
         areaGroupDTOList = Lists.newArrayList();
         groupDTO = new SimpleAreaGroupDTO();
        regionMap = Maps.newHashMap();
        regionMap.put("1/1/2","上海");
        groupDTO.setRegionInfo(regionMap);
        areaGroupDTOList.add(groupDTO);
        resultMap.put(2L,areaGroupDTOList);
        Mockito.when(transportGroupQueryService.getAreaCityIdByGroupIds(Arrays.asList(1L,2L))).thenReturn(resultMap);
        List<Long> drvList = Lists.newArrayList();
        drvList.add(1L);
        Mockito.when(tspTransportGroupDriverRelationRepository.queryRelationDrvIdListByTransportGroupId(1L)).thenReturn(drvList);
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setCityId(1L);
        drvDriverPOList.add(driverPO);
        Mockito.when(drvDrvierRepository.queryDrvList(Arrays.asList(1L))).thenReturn(drvDriverPOList);
        Boolean b = transportGroupCommandService.unBingTransportAssociated(transportGroupId,pointCityId,originAreaType,targetAreaType,originAreaGroupId,targetAreaGroupId,modifyUser);
        Assert.assertTrue(!b);
    }

    @Test
    public void getDiyRegionCity(){
        List<SimpleAreaGroupDTO> list = Lists.newArrayList();
        SimpleAreaGroupDTO areaGroupDTO = new SimpleAreaGroupDTO();
        List<SimpleCityAreaInfoDTO> cityAreaInfoList = Lists.newArrayList();
        SimpleCityAreaInfoDTO infoDTO = new SimpleCityAreaInfoDTO();
        infoDTO.setCityId(1L);
        cityAreaInfoList.add(infoDTO);
        areaGroupDTO.setCityAreaInfoList(cityAreaInfoList);
        list.add(areaGroupDTO);
        Set<Long> set =  transportGroupCommandService.getDiyRegionCity(list);
        Assert.assertTrue(!set.isEmpty());
    }


    @Test
    public void bindDriverRelation(){
        DriverRelationBindRequestSOAType requestSOAType = new DriverRelationBindRequestSOAType();
        requestSOAType.setBindStatus(1);
        requestSOAType.setDrvIdList(Arrays.asList(1L));
        requestSOAType.setTransportGroupId(1L);
        requestSOAType.setOperator("111");
        requestSOAType.setWorkShiftId(1L);
        UpdateRelationStatusParam param = new UpdateRelationStatusParam(requestSOAType.getTransportGroupId(),requestSOAType.getDrvIdList()
                ,requestSOAType.getOperator(),requestSOAType.getWorkShiftId());
//        Mockito.when(tspTransportGroupDriverRelationRepository.updateRelationStatus(param)).thenReturn(100);
        Result<Boolean> result =  transportGroupCommandService.bindDriverRelation(requestSOAType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void bindDriverRelation1(){
        ReflectionTestUtils.setField(productionLineUtil, "bindRuleConfig", bindRuleConfig);

        DriverRelationBindRequestSOAType requestSOAType = new DriverRelationBindRequestSOAType();
        requestSOAType.setBindStatus(0);
        requestSOAType.setDrvIdList(Arrays.asList(1L));
        requestSOAType.setTransportGroupId(1L);
        requestSOAType.setOperator("111");
        requestSOAType.setWorkShiftId(1L);
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setTransportGroupMode(1001);
        tspTransportGroupPO.setTransportGroupId(1L);
        tspTransportGroupPO.setCategorySynthesizeCode(1);
        Mockito.when(transportGroupRepository.queryTransportGroupDetail(requestSOAType.getTransportGroupId())).thenReturn(tspTransportGroupPO);
        Mockito.when(tspTransportGroupDriverRelationRepository.queryRelationDrvListByTransportGroupIdAndDrvIdList(tspTransportGroupPO.getTransportGroupId(),requestSOAType.getDrvIdList(),requestSOAType.getWorkShiftId())).thenReturn(Arrays.asList(2L));
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setWorkPeriod("111");
        drvDriverPO.setVehicleId(1L);
        drvDriverPO.setCategorySynthesizeCode(1);
        DrvDriverPO drvDriverPO1 = new DrvDriverPO();
        drvDriverPO1.setDrvId(2L);
        drvDriverPO1.setWorkPeriod("111");
        drvDriverPO1.setCategorySynthesizeCode(4);
        DrvDriverPO drvDriverPO2 = new DrvDriverPO();
        drvDriverPO2.setDrvId(3L);
        drvDriverPO2.setVehicleId(2L);
        drvDriverPO2.setWorkPeriod("111");
        drvDriverPO2.setCategorySynthesizeCode(1);
        List<DrvDriverPO> drvDriverPOS = Lists.newArrayList();
        drvDriverPOS.add(drvDriverPO);
        drvDriverPOS.add(drvDriverPO1);
        drvDriverPOS.add(drvDriverPO2);
        Mockito.when(drvDrvierRepository.queryDrvList(requestSOAType.getDrvIdList())).thenReturn(drvDriverPOS);
        Result<Boolean> result1 = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(transportGroupQueryService.canBind(drvDriverPO, tspTransportGroupPO.getTransportGroupMode())).thenReturn(result1);
        InsertBatchRelationParam param1 = new InsertBatchRelationParam(requestSOAType.getTransportGroupId(),Arrays.asList(1L),requestSOAType.getOperator(),requestSOAType.getWorkShiftId());
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setVehicleId(1L);
        vehVehiclePO.setCategorySynthesizeCode(1);

        VehVehiclePO vehVehiclePO1 = new VehVehiclePO();
        vehVehiclePO1.setVehicleId(2L);
        vehVehiclePO1.setCategorySynthesizeCode(4);

        Mockito.when(vehicleRepository.queryVehicleByIds(Mockito.anyList())).thenReturn(Arrays.asList(vehVehiclePO,vehVehiclePO1));
        Mockito.when(shortTransportGroupHelper.checkShortTransportGroupDriver(any(), any(), any())).thenReturn(
          ResponseResultUtil.success());
//        Mockito.when(productionLineUtil.checkBind(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(true);
//        Mockito.when(tspTransportGroupDriverRelationRepository.insetBatch(param1)).thenReturn(1);
        Result<Boolean> result =  transportGroupCommandService.bindDriverRelation(requestSOAType);
        Assert.assertFalse(result.isSuccess());
    }

    @Test
    public void saveGlobalTrackRecord() throws SQLException {
        Map<Long, TspTransportGroupWorkShiftPO> workShiftPOMap = Maps.newConcurrentMap();
        TspTransportGroupWorkShiftPO shiftPO = new TspTransportGroupWorkShiftPO();
        shiftPO.setTransportGroupId(1L);
        workShiftPOMap.put(1L,shiftPO);
        TransportTrackRecordPO recordPO = new TransportTrackRecordPO();
        recordPO.setTransportGroupId(1L);
        recordPO.setOperationType(TmsTransportConstant.ApplyTransPortOperationTypeEnum.GLOBAL_TRACK.getCode());
        recordPO.setCreateUser("111");
        recordPO.setModifyUser("111");
//        Mockito.when(transportTrackRecordRepository.insert(recordPO)).thenReturn(1L);
        Boolean result = transportGroupCommandService.saveGlobalTrackRecord(1,workShiftPOMap,"111");
        Assert.assertTrue(result);
    }

    @Test
    public void drvDispatchunBoundTransport() throws SQLException {
        Map<String,String> params = Maps.newHashMap();
        params.put("accountName","111");
        SessionHolder.setSessionSource(params);
        List<TspTransportGroupDriverRelationPO> relationPOS = Lists.newArrayList();
        TspTransportGroupDriverRelationPO relationPO = new TspTransportGroupDriverRelationPO();
        relationPO.setTransportGroupId(1L);
        relationPOS.add(relationPO);
       Mockito.when(tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Arrays.asList(1L))).thenReturn(relationPOS);
        List<TspTransportGroupPO> transportGroupPOS = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setSupplierId(1L);
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPOS.add(transportGroupPO);
       Mockito.when(transportGroupRepository.queryTspTransportByIds(Arrays.asList(1L))).thenReturn(transportGroupPOS);
        Boolean result = transportGroupCommandService.drvDispatchunBoundTransport(1L,"111",1L);
        Assert.assertTrue(result);
    }
}
