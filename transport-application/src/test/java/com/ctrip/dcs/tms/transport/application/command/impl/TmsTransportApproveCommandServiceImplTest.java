package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OverageDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ChangeRecordAttributeNameQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.CommonConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverageQConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.model.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;
import org.springframework.beans.*;

import java.sql.*;
import java.sql.Date;
import java.util.Arrays;
import java.util.*;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.DRV_APPROVE_NEED_KEY;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.DRV_APPROVE_NEED_KEY__EXCEPT_NET_CERT;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.VEHICLE_APPROVE_NEED_KEY;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant.VEHICLE_APPROVE_NEED_KEY_EXCEPT_NET_CERT;

@RunWith(MockitoJUnitRunner.class)
public class TmsTransportApproveCommandServiceImplTest extends Mockito {

    @InjectMocks
    TmsTransportApproveCommandServiceImpl service;
    @Mock
    private TmsTransportApproveRepository approveRepository;
    @Mock
    EnumRepository enumRepository;

    @Mock
    private VehicleRepository vehicleRepository;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private TmsCertificateCheckRepository checkRepository;
    @Mock
    DrvEpidemicPreventionControlInfoRepository drvEpidemicPreventionControlInfoRepository;
    @Mock
    CertificateCheckCommandService certificateCheckCommandService;
    @Mock
    TmsQmqProducerCommandService tmsQmqProducerCommandService;
    @Mock
    TmsTransportQconfig qconfig;
    @Mock
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;

    @Mock
    NetCardCheckUtil netCardCheckUtil;

    @Mock
    private CommonConfig config;
    @Mock
    private OverageQConfig overageQConfig;

    @Test
    public void updateApproveStatus() {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","1");
        SessionHolder.setSessionSource(params);
        TransportApproveStatusUpdateSOARequestType soaRequestType = new TransportApproveStatusUpdateSOARequestType();
        List<UpdateApproveCertificateStatusSOADTO> checkStatusList = Lists.newArrayList();
        UpdateApproveCertificateStatusSOADTO soadto = new UpdateApproveCertificateStatusSOADTO();
        soadto.setCertificateType(3);
        soadto.setCheckStatus(1);
        checkStatusList.add(soadto);
        soaRequestType.setCheckStatusList(checkStatusList);
        soaRequestType.setIds(Arrays.asList(1L));
        soaRequestType.setApproveStatus(3);
        soaRequestType.setModifyUser("11");
        soaRequestType.setRemarks("1");
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setId(1L);
        approvePO.setApproveSourceId(1L);
        approvePO.setApproveSourceType(1);
        approvePO.setEventType(1);
        approvePO.setAccountId("2");
        approvePO.setApproveStatus(1);
        approvePO.setCertificateCheckResult("[{\"checkId\":1000012,\"checkType\":3,\"certificateType\":2,\"checkKeyword\":\"\",\"checkContent\":\"{\\\"ResponseStatus\\\":{\\\"Timestamp\\\":*************,\\\"Ack\\\":\\\"Success\\\",\\\"Errors\\\":[],\\\"Extension\\\":[]},\\\"responseResult\\\":{\\\"success\\\":true,\\\"returnCode\\\":\\\"200\\\",\\\"returnMessage\\\":\\\"服务执行成功\\\"},\\\"thirdCode\\\":\\\"2001\\\",\\\"resultMsg\\\":\\\"未查得数据\\\"}\",\"checkStatus\":3,\"createUser\":\"vb30804\",\"checkResult\":\"[{\\\"columnKey\\\":\\\"drvLicenseStatusKey\\\",\\\"columnValue\\\":\\\"行驶证返回信息\\\",\\\"resultKey\\\":\\\"drvLicenseStatusResultKey\\\",\\\"resultValue\\\":\\\"查询成功，无数据\\\"}]\"},{\"checkId\":1000012,\"checkType\":3,\"certificateType\":4,\"checkKeyword\":\"京A20185\",\"checkContent\":\"\",\"checkStatus\":4,\"createUser\":\"vb30804\",\"checkResult\":\"\"}]");
        approvePOList.add(approvePO);
        when(approveRepository.queryApproveListByIds(Arrays.asList(1L))).thenReturn(approvePOList);
        UpdateApproveStatusDO statusDO = new UpdateApproveStatusDO();
        statusDO.setIds(Arrays.asList(1L));
        statusDO.setApproveStatus(3);
        statusDO.setModifyUser("11");
        statusDO.setRemarks("1");
//        when(approveRepository.updateApproveStatus(statusDO)).thenReturn(1);
        Result<Boolean>  result = service.updateApproveStatus(soaRequestType);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateApproveStatus1() {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","1");
        SessionHolder.setSessionSource(params);
        TransportApproveStatusUpdateSOARequestType soaRequestType = new TransportApproveStatusUpdateSOARequestType();
        List<UpdateApproveCertificateStatusSOADTO> checkStatusList = Lists.newArrayList();
        UpdateApproveCertificateStatusSOADTO soadto = new UpdateApproveCertificateStatusSOADTO();
        soadto.setCertificateType(3);
        soadto.setCheckStatus(1);
        checkStatusList.add(soadto);
        soaRequestType.setIds(Arrays.asList(1L));
        soaRequestType.setApproveStatus(3);
        soaRequestType.setModifyUser("11");
        soaRequestType.setRemarks("1");
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setId(1L);
        approvePO.setApproveSourceId(1L);
        approvePO.setApproveSourceType(1);
        approvePO.setAccountId("2");
        approvePO.setApproveStatus(1);
        approvePO.setEventType(1);
        approvePO.setCertificateCheckResult("[{\"checkId\":1000012,\"checkType\":3,\"certificateType\":2,\"checkKeyword\":\"\",\"checkContent\":\"{\\\"ResponseStatus\\\":{\\\"Timestamp\\\":*************,\\\"Ack\\\":\\\"Success\\\",\\\"Errors\\\":[],\\\"Extension\\\":[]},\\\"responseResult\\\":{\\\"success\\\":true,\\\"returnCode\\\":\\\"200\\\",\\\"returnMessage\\\":\\\"服务执行成功\\\"},\\\"thirdCode\\\":\\\"2001\\\",\\\"resultMsg\\\":\\\"未查得数据\\\"}\",\"checkStatus\":3,\"createUser\":\"vb30804\",\"checkResult\":\"[{\\\"columnKey\\\":\\\"drvLicenseStatusKey\\\",\\\"columnValue\\\":\\\"行驶证返回信息\\\",\\\"resultKey\\\":\\\"drvLicenseStatusResultKey\\\",\\\"resultValue\\\":\\\"查询成功，无数据\\\"}]\"},{\"checkId\":1000012,\"checkType\":3,\"certificateType\":4,\"checkKeyword\":\"京A20185\",\"checkContent\":\"\",\"checkStatus\":4,\"createUser\":\"vb30804\",\"checkResult\":\"\"}]");
        approvePOList.add(approvePO);
        when(approveRepository.queryApproveListByIds(Arrays.asList(1L))).thenReturn(approvePOList);
        UpdateApproveStatusDO statusDO = new UpdateApproveStatusDO();
        statusDO.setIds(Arrays.asList(1L));
        statusDO.setApproveStatus(3);
        statusDO.setModifyUser("11");
        statusDO.setRemarks("1");
//        when(approveRepository.updateApproveStatus(statusDO)).thenReturn(1);
        Result<Boolean>  result = service.updateApproveStatus(soaRequestType);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void insertApprove() throws SQLException {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","1");
        params.put("accountId","1");
        SessionHolder.setSessionSource(params);
        AddApproveDTO addApproveDTO = new AddApproveDTO();
        addApproveDTO.setApproveName("1");
        addApproveDTO.setModifyUser("1");
        addApproveDTO.setRrdId(1L);
        addApproveDTO.setAttributeKeyValue(Maps.newHashMap());
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setIdcardImg("1");
        addApproveDTO.setNewMod(drvDriverPO);
        DrvDriverPO orgDrv = new DrvDriverPO();
        orgDrv.setIdcardImg("22");
        when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDrv);
        addApproveDTO.setRecordTypeEnum(TmsTransportConstant.ApproveSourceTypeEnum.DRV);
        addApproveDTO.setDomesticFlag(true);
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        BeanUtils.copyProperties(addApproveDTO,addApproveDTO);
//        when(approveRepository.insertTmsTransportApprovePO(approvePO)).thenReturn(1L);
        Long cityId = 1L;
        Result<Long>  result = service.insertApprove(addApproveDTO, TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate, cityId);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void checkColumnApproveIng() {
        Boolean result = Boolean.TRUE;
        AddApproveDTO addApproveDTO = new AddApproveDTO();
        addApproveDTO.setRrdId(1L);
        addApproveDTO.setRecordTypeEnum(TmsTransportConstant.ApproveSourceTypeEnum.DRV);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setIdcardImg("1");
        addApproveDTO.setNewMod(drvDriverPO);
        addApproveDTO.setDomesticFlag(true);
        DrvDriverPO orgDrv = new DrvDriverPO();
        orgDrv.setIdcardImg("22");
        when(drvDrvierRepository.queryByPk(1L)).thenReturn(orgDrv);
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setApproveStatus(1);
        approvePO.setApproveContent("[{\"originalValue\":\"https://dimg04.c-ctrip.com/images/0413l120008ij4mkm1784.jpg\",\"changeItemName\":\"驾驶证照片\",\"changeValue\":\"https://dimg04.c-ctrip.com/images/0413l120008ij41mkm1784.jpg\",\"changeItem\":\"drvcardImg\"},{\"originalValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0415d1200000b5ur54A5E.png\",\"changeItemName\":\"证件照正面\",\"changeValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0415d12000010b5ur54A5E.png\",\"changeItem\":\"idcardImg\"},{\"originalValue\":\"\",\"changeItemName\":\"网约车人照\",\"changeValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x1200000b5uf214C8.png\",\"changeItem\":\"netVehiclePeoImg\"}]");
        approvePOList.add(approvePO);
//        when(approveRepository.queryApproveBySourceId(1L, 1, TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode())).thenReturn(approvePOList);
        Boolean b = service.checkColumnApproveIng(addApproveDTO);
        Assert.assertTrue(!b);
    }

    @Test
    public void getNeedArr() {
        TmsTransportConstant.ApproveSourceTypeEnum typeEnum = TmsTransportConstant.ApproveSourceTypeEnum.DRV;

        Mockito.when(netCardCheckUtil.isNetCardNoNeedCheck(1L)).thenReturn(true);
        Mockito.when(netCardCheckUtil.isNetCardNoNeedCheck(2L)).thenReturn(false);

        String[] needArr = service.getNeedArr(typeEnum, 1L);
        Assert.assertEquals(1, needArr.length);
        Assert.assertEquals(needArr[0], DRV_APPROVE_NEED_KEY__EXCEPT_NET_CERT[0]);

        needArr = service.getNeedArr(typeEnum, 2L);
        Assert.assertEquals(2, needArr.length);
        Assert.assertEquals(needArr[0], DRV_APPROVE_NEED_KEY[0]);
        Assert.assertEquals(needArr[1], DRV_APPROVE_NEED_KEY[1]);

        typeEnum = TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE;
        needArr = service.getNeedArr(typeEnum, 1L);
        Assert.assertEquals(2, needArr.length);
        Assert.assertEquals(needArr[0], VEHICLE_APPROVE_NEED_KEY_EXCEPT_NET_CERT[0]);

        needArr = service.getNeedArr(typeEnum, 2L);
        Assert.assertEquals(3, needArr.length);
        Assert.assertEquals(needArr[0], VEHICLE_APPROVE_NEED_KEY[0]);
        Assert.assertEquals(needArr[1], VEHICLE_APPROVE_NEED_KEY[1]);


    }

    @Test
    public void getAssembleJsonList() {
        Boolean result = Boolean.TRUE;
        DrvDriverPO orgDrv = new DrvDriverPO();
        orgDrv.setIdcardImg("22");
        DrvDriverPO newDrv = new DrvDriverPO();
        newDrv.setIdcardImg("2");
        String str = service.getAssembleJsonList(orgDrv, newDrv, Maps.newHashMap(), DRV_APPROVE_NEED_KEY, new ArrayList<>());
        Assert.assertTrue(str == null);
    }


    @Test
    public void getAssembleJsonList1() {
        Boolean result = Boolean.TRUE;
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setRegstDate(new Date(1733297383205L));
        vehiclePO.setVehicleCertiImg("123123");

        VehVehiclePO vehiclePO1 = new VehVehiclePO();
        vehiclePO1.setRegstDate(new Date(1733211276000L));
        vehiclePO1.setVehicleCertiImg("123123");

        List<String> objects = new ArrayList<>();
        objects.add("vehicleCertiImg");
        objects.add("regstDate");
        List<List<String>> all = new ArrayList<>();
        all.add(objects);
        String str = service.getAssembleJsonList(vehiclePO, vehiclePO1, Maps.newHashMap(), VEHICLE_APPROVE_NEED_KEY, all);
        Assert.assertEquals("[{\"originalValue\":\"2024-12-04\",\"changeValue\":\"2024-12-03\",\"changeItem\":\"regstDate\"},{\"originalValue\":\"123123\",\"changeValue\":\"123123\",\"changeItem\":\"vehicleCertiImg\"}]", str);
    }



    @Test
    public void optationApprovad() {
        Boolean result = Boolean.TRUE;
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setApproveContent("[{\"originalValue\":\"https://dimg04.c-ctrip.com/images/0413l120008ij4mkm1784.jpg\",\"changeItemName\":\"驾驶证照片\",\"changeValue\":\"https://dimg04.c-ctrip.com/images/0413l120008ij41mkm1784.jpg\",\"changeItem\":\"drvcardImg\"},{\"originalValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0415d1200000b5ur54A5E.png\",\"changeItemName\":\"证件照正面\",\"changeValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0415d12000010b5ur54A5E.png\",\"changeItem\":\"idcardImg\"},{\"originalValue\":\"\",\"changeItemName\":\"网约车人照\",\"changeValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x1200000b5uf214C8.png\",\"changeItem\":\"netVehiclePeoImg\"}]");
        approvePO.setId(1L);
        approvePO.setApproveSourceType(1);
        approvePO.setApproveSourceId(1L);
        approvePOList.add(approvePO);
        Map<Long,String> modifyUser = Maps.newHashMap();
//        Mockito.doAnswer(invocation -> null).when(tmsQmqProducerCommandService).sendDrvChangeQmq(1L,1,1);
        service.optationApprovad(approvePOList,modifyUser, 2, "");
        Assert.assertEquals(result, true);
    }

    @Test
    public void optationApprovadForDrv() {
        Boolean result = Boolean.TRUE;
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setApproveContent("[{\"originalValue\":\"https://dimg04.c-ctrip.com/images/0413l120008ij4mkm1784.jpg\",\"changeItemName\":\"驾驶证照片\",\"changeValue\":\"https://dimg04.c-ctrip.com/images/0413l120008ij41mkm1784.jpg\",\"changeItem\":\"drvcardImg\"},{\"originalValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0415d1200000b5ur54A5E.png\",\"changeItemName\":\"证件照正面\",\"changeValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0415d12000010b5ur54A5E.png\",\"changeItem\":\"idcardImg\"},{\"originalValue\":\"\",\"changeItemName\":\"网约车人照\",\"changeValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/0410x1200000b5uf214C8.png\",\"changeItem\":\"netVehiclePeoImg\"}]");
        approvePO.setId(1L);
        approvePO.setApproveSourceType(1);
        approvePO.setApproveSourceId(1L);
        approvePO.setApproveSourceType(TmsTransportConstant.ApproveSourceTypeEnum.DRV.getCode());
        approvePOList.add(approvePO);
        Map<Long,String> modifyUser = Maps.newHashMap();
        //        Mockito.doAnswer(invocation -> null).when(tmsQmqProducerCommandService).sendDrvChangeQmq(1L,1,1);
        service.optationApprovad(approvePOList,modifyUser, 2, "");
        Assert.assertEquals(result, true);
    }


    @Test
    public void optationApprovad1() throws SQLException {
        List<VehVehiclePO> list = new ArrayList<>();
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setVehicleId(1L);
        vehVehiclePO.setCityId(1L);
        vehVehiclePO.setVehicleTypeId(117L);
        vehVehiclePO.setRegstDate(Date.valueOf("2024-02-04"));
        vehVehiclePO.setVehicleAgeType(VehicleAgeTypeEnum.OVERAGE.getCode());


        VehVehiclePO vehVehiclePO1 = new VehVehiclePO();
        vehVehiclePO1.setVehicleId(2L);
        vehVehiclePO1.setCityId(1L);
        vehVehiclePO1.setVehicleTypeId(117L);
        vehVehiclePO1.setRegstDate(Date.valueOf("2018-02-04"));
        vehVehiclePO1.setVehicleAgeType(VehicleAgeTypeEnum.NOT_OVERAGE.getCode());

        VehVehiclePO vehVehiclePO12 = new VehVehiclePO();
        vehVehiclePO12.setVehicleId(2L);
        vehVehiclePO12.setCityId(1L);
        vehVehiclePO12.setVehicleTypeId(117L);
        vehVehiclePO12.setRegstDate(Date.valueOf("2024-02-04"));
        vehVehiclePO12.setVehicleAgeType(VehicleAgeTypeEnum.NOT_OVERAGE.getCode());

        list.add(vehVehiclePO);
        list.add(vehVehiclePO1);
        list.add(vehVehiclePO12);
        Mockito.when(vehicleRepository.queryVehVehicleByIds(Mockito.anyList())).thenReturn(list);
        Mockito.when(config.getOverageGraySwitch()).thenReturn(true);
        Mockito.when(config.getCityIdList()).thenReturn(Arrays.asList(1L));
        Mockito.when(enumRepository.queryByCityIds(Mockito.anyList())).thenReturn(Arrays.asList(City.builder().chineseMainland(true).id(1L).build()));
        OverageDTO overageDTO = new OverageDTO();
        overageDTO.setCityId(1L);
        overageDTO.setVehicleTypeId(117L);
        overageDTO.setAccessLimit(4.75D);
        overageDTO.setOverage(5D);
        Mockito.when(overageQConfig.getOverageMap(Mockito.anyLong(), Mockito.anyLong())).thenReturn(overageDTO);
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setApproveContent("[{\"originalValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/4109111e000sss0ss000a9hrBB4E.jpg\",\"changeItemName\":\"车辆行驶证照片\",\"changeValue\":\"https://dimg.fws.qa.nt.ctripcorp.com/images/4109111e000s111ss0ss000a9hrBB4E.jpg\",\"changeItem\":\"vehicleCertiImg\"}]");
        approvePO.setId(1L);
        approvePO.setApproveSourceType(2);
        approvePO.setApproveSourceId(1L);
        approvePOList.add(approvePO);
        Map<Long,String> modifyUser = Maps.newHashMap();
        service.optationApprovad(approvePOList,modifyUser, 2, "");
        Assert.assertEquals(approvePO.getId(), Long.valueOf(1L));
    }

    @Test
    public void syncCertificateCheckDB() {
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setId(1L);
        approvePO.setCertificateCheckResult("[{\"checkId\":79,\"checkType\":2,\"certificateType\":1,\"checkKeyword\":\"\",\"checkContent\":\"{\\\"data\\\":{\\\"ResponseStatus\\\":{\\\"Timestamp\\\":1610009624922,\\\"Ack\\\":\\\"Success\\\",\\\"Errors\\\":[],\\\"Extension\\\":[]},\\\"responseResult\\\":{\\\"success\\\":true,\\\"returnCode\\\":\\\"200\\\",\\\"returnMessage\\\":\\\"服务执行成功\\\"},\\\"thirdCode\\\":\\\"3002\\\",\\\"resultMsg\\\":\\\"参数错误\\\"},\\\"success\\\":true}\",\"checkStatus\":3,\"createUser\":\"system\",\"checkResult\":\"[]\"}]");
        approvePOList.add(approvePO);
        service.syncCertificateCheckDB(approvePOList);
        Assert.assertTrue(true);
    }

    @Test
    public void syncDriverPreventionControlInfo() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setDrvStatus(1);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        DrvEpidemicPreventionControlInfoPO infoPO = new DrvEpidemicPreventionControlInfoPO();
        infoPO.setNucleicAcidReportTempContent("{\"driverId\":77,\"driverName\":\"赵六**32\",\"driverIdNumber\":\"110101****01011204\",\"detectionType\":1,\"isConfirmSubmit\":false,\"nucleicAcidTestingTime\":\"2021-04-15\",\"nucleicAcidTestingResult\":\"阴性\",\"vaccineName\":\"国药疫苗\",\"vaccinationCount\":2,\"firstVaccinationTime\":\"2021-01-03\",\"secondVaccinationTime\":\"2021-02-03\",\"detectionReportUrl\":\"~~~~~~~~~\",\"detectionReportOrgName\":\"~~~~~~~~~~~~\"}");
        Mockito.when(drvEpidemicPreventionControlInfoRepository.queryByDrvId(1L)).thenReturn(infoPO);
        Mockito.when(drvDrvierRepository.updateDrv(drvDriverPO)).thenReturn(1);
        Mockito.when(drvEpidemicPreventionControlInfoRepository.updateEpidemicPreventionControlInfo(infoPO)).thenReturn(1);
//        Mockito.when(certificateCheckCommandService.insertCheckRecordDB(1L,2,2,"","",Lists.newArrayList(),1,"1")).thenReturn(1L);
        service.syncDriverPreventionControlInfo(1L,2,"`1",true);
        Assert.assertTrue(true);
    }

    @Test
    public void keepOnlyApprove() throws SQLException {
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setDrvName("1");
        driverPO.setSupplierId(1L);
        QueryApproveListDO params = new QueryApproveListDO();
        params.setApproveSourceId(driverPO.getDrvId());
        params.setEventType(TmsTransportConstant.EnentTypeEnum.nucleicAcidTest.getCode());
        params.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode());
//        Mockito.when(approveRepository.countQueryApproveList(params)).thenReturn(1);
        TmsTransportApprovePO po = new TmsTransportApprovePO();
        BeanUtils.copyProperties(driverPO,po);
//        Mockito.when(approveRepository.insertTmsTransportApprovePO(po)).thenReturn(1L);
        Boolean b = service.keepOnlyApprove(driverPO,true);
        Assert.assertTrue(!b);
    }

    @Test
    public void doDrvHeadApproveTest() {
        Boolean result = Boolean.TRUE;
        TmsTransportApprovePO transportApprovePO = new TmsTransportApprovePO();
        transportApprovePO.setApproveSourceId(1L);
        List<ApproveContentSOADTO> approveContentSOADTOS = Lists.newArrayList();
        ApproveContentSOADTO a = new ApproveContentSOADTO();
        a.setChangeValue("1");
        a.setOriginalValue("2");
        approveContentSOADTOS.add(a);
        ApproveContentSOADTO b = new ApproveContentSOADTO();
        b.setChangeValue("true");
        approveContentSOADTOS.add(b);
        transportApprovePO.setApproveContent(JsonUtil.toJson(approveContentSOADTOS));
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(new DrvDriverPO());
        TransportApproveStatusUpdateSOARequestType soaRequestType = new TransportApproveStatusUpdateSOARequestType();
        soaRequestType.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.APPROVAL_REJECTED.getCode());
        service.doDrvHeadApprove(transportApprovePO, soaRequestType);
        Assert.assertEquals(result, true);
    }

    @Test
    public void doDrvHeadApproveTest2() {
        Boolean result = Boolean.TRUE;
        TmsTransportApprovePO transportApprovePO = new TmsTransportApprovePO();
        transportApprovePO.setApproveSourceId(1L);
        List<ApproveContentSOADTO> approveContentSOADTOS = Lists.newArrayList();
        ApproveContentSOADTO a = new ApproveContentSOADTO();
        a.setChangeValue("1");
        a.setOriginalValue("2");
        approveContentSOADTOS.add(a);
        ApproveContentSOADTO b = new ApproveContentSOADTO();
        b.setChangeValue("true");
        approveContentSOADTOS.add(b);
        transportApprovePO.setApproveContent(JsonUtil.toJson(approveContentSOADTOS));
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(new DrvDriverPO());
        TransportApproveStatusUpdateSOARequestType soaRequestType = new TransportApproveStatusUpdateSOARequestType();
        soaRequestType.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.APPROVED.getCode());
        service.doDrvHeadApprove(transportApprovePO, soaRequestType);
        Assert.assertEquals(result, true);
    }

    @Test
    public void doDrvHeadApproveTest1() {
        TransportApproveStatusUpdateSOARequestType soaRequestType = new TransportApproveStatusUpdateSOARequestType();
        soaRequestType.setApproveStatus(TmsTransportConstant.TransportApproveStatusEnum.APPROVAL_REJECTED.getCode());
        service.doDrvHeadApprove(new TmsTransportApprovePO(), soaRequestType);
        Assert.assertTrue(true);
    }

    @Test
    public void getOverseasList() {
        VehVehiclePO orgVeh = new VehVehiclePO();
        orgVeh.setVehicleColorId(1L);
        orgVeh.setVehicleFullImg("131233");
        orgVeh.setVehicleLicense("111");
        orgVeh.setVehicleCertiImg("123");

        VehVehiclePO newVeh = new VehVehiclePO();
        newVeh.setVehicleColorId(2L);
        newVeh.setVehicleFullImg("43274382312");
        newVeh.setVehicleLicense("222");
        newVeh.setVehicleCertiImg("123");
        String[] VEHICLE_APPROVE_NEED_KEY = new String[]{ "vehicleLicense","vehicleFullImg","vehicleCertiImg"};
        List<List<String>> pairField = new ArrayList<>();
        pairField.add(Arrays.asList("vehicleCertiImg", "vehicleLicense"));
        List<String> result = service.getOverseasList(orgVeh, newVeh, Maps.newHashMap(), VEHICLE_APPROVE_NEED_KEY, pairField);
        Assert.assertTrue(!result.isEmpty());
    }

    @Test
    public void checkColumnApproveIng1() {
        AddApproveDTO addApproveDTO = new AddApproveDTO();
        addApproveDTO.setRrdId(1L);
        addApproveDTO.setRecordTypeEnum(TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE);
        VehVehiclePO drvDriverPO = new VehVehiclePO();
        drvDriverPO.setVehicleColorId(1L);
        addApproveDTO.setNewMod(drvDriverPO);
        addApproveDTO.setDomesticFlag(false);
        VehVehiclePO orgDrv = new VehVehiclePO();
        orgDrv.setVehicleColorId(2L);
        when(vehicleRepository.queryByPk(1L)).thenReturn(orgDrv);
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setApproveStatus(1);
        approvePO.setApproveContent("[{\"originalValue\":\"39\",\"changeItemName\":\"车身颜色\",\"changeValue\":\"38\",\"changeItem\":\"vehicleColorId\"}]");
        approvePOList.add(approvePO);
        when(approveRepository.queryApproveBySourceId(addApproveDTO.getRrdId(), addApproveDTO.getRecordTypeEnum().getCode(), TmsTransportConstant.TransportApproveStatusEnum.WAITAPPROVE.getCode(),addApproveDTO.getEventType())).thenReturn(approvePOList);
        String qconfigStr = "{\"1\":\"\",\"2\":\"vehicleColorId\"}";
//        when(qconfig.getOverseasApproveFields()).thenReturn(qconfigStr);
        Boolean b = service.checkColumnApproveIng(addApproveDTO);
        Assert.assertTrue(b);
    }

    @Test
    public void insertOverseasApprove() throws SQLException {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountType","1");
        params.put("accountId","1");
        SessionHolder.setSessionSource(params);
        AddApproveDTO addApproveDTO = new AddApproveDTO();
        addApproveDTO.setRrdId(1L);
        addApproveDTO.setRecordTypeEnum(TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE);
        addApproveDTO.setDomesticFlag(false);
        VehVehiclePO drvDriverPO = new VehVehiclePO();
        drvDriverPO.setVehicleColorId(1L);
        addApproveDTO.setNewMod(drvDriverPO);
        Map<String,String> vehicleMap = Maps.newHashMap();
        vehicleMap.put("vehicleColorId","111");
        addApproveDTO.setAttributeKeyValue(vehicleMap);
        VehVehiclePO orgDrv = new VehVehiclePO();
        orgDrv.setVehicleColorId(2L);
        when(vehicleRepository.queryByPk(1L)).thenReturn(orgDrv);
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        BeanUtils.copyProperties(addApproveDTO,addApproveDTO);
        String qconfigStr = "{\"1\":\"\",\"2\":\"vehicleColorId\"}";
//        when(qconfig.getOverseasApproveFields()).thenReturn(qconfigStr);
        Result<Long>  result = service.insertOverseasApprove(addApproveDTO, TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void needInApproveFields() throws SQLException {
        String qconfigStr = "{\"1\":\"\",\"2\":\"vehicleColorId\"}";
//        when(qconfig.getOverseasApproveFields()).thenReturn(qconfigStr);
        String[]  result = service.needInApproveFields(TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE,Lists.newArrayList(),Boolean.FALSE, true, 0,37L);
        Assert.assertTrue(result.length > 0);
    }

    @Test
    public void insertOverseasTags() throws SQLException {
        List<TmsTransportApprovePO> approvePOList = Lists.newArrayList();
        TmsTransportApprovePO approvePO = new TmsTransportApprovePO();
        approvePO.setId(1L);
        approvePO.setApproveSourceId(1L);
        approvePO.setApproveSourceType(2);
        approvePO.setApproveStatus(1);
        approvePO.setApproveContent("[{\"originalValue\":\"39\",\"changeItemName\":\"车身颜色\",\"changeValue\":\"38\",\"changeItem\":\"vehicleColorId\"}]");
        approvePO.setEventType(TmsTransportConstant.EnentTypeEnum.OVERSEASVEHMOD.getCode());
        approvePOList.add(approvePO);
        Boolean  result = service.insertOverseasTags(approvePOList,TmsTransportConstant.CheckStatusEnum.THROUGH);
        Assert.assertTrue(result);
    }

    @Test
    public void vehicleInApproveFields() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassStatusList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(104);
        modelSOA.setPassStatus(0);
        ocrPassStatusList.add(modelSOA);
        String[]  result = service.vehicleInApproveFields(ocrPassStatusList, true, 1, 37L);
        Assert.assertTrue(result.length > 0);
    }

    @Test
    public void syncOverseasDrvVehLic() throws SQLException {

        List<DrvDriverPO> drvDriverPOS = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setVehicleId(1L);
        drvDriverPO.setVehicleLicense("111");
        drvDriverPO.setInternalScope(1);
        drvDriverPOS.add(drvDriverPO);
        List<VehVehiclePO> updateVehicleList = Lists.newArrayList();
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setVehicleId(1L);
        vehVehiclePO.setVehicleLicense("21222");
        updateVehicleList.add(vehVehiclePO);
        Boolean  result = service.syncOverseasDrvVehLic(updateVehicleList,drvDriverPOS);
        Assert.assertTrue(result);
    }

    @Test
    public void vehicleInApproveFields1() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassStatusList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(104);
        modelSOA.setPassStatus(1);
        ocrPassStatusList.add(modelSOA);
        OcrPassStatusModelSOA modelSOA1 = new OcrPassStatusModelSOA();
        modelSOA1.setOcrId(1L);
        modelSOA1.setOcrItem(105);
        modelSOA1.setPassStatus(1);
        ocrPassStatusList.add(modelSOA1);
        String[]  result = service.vehicleInApproveFields(ocrPassStatusList, true, 1, 37L);
        Assert.assertTrue(result.length > 0);
    }

    @Test
    public void drvInApproveFields() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassStatusList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(104);
        modelSOA.setPassStatus(1);
        ocrPassStatusList.add(modelSOA);
        String[]  result = service.drvInApproveFields(ocrPassStatusList);
        Assert.assertTrue(result.length > 0);
    }

    @Test
    public void drvInApproveFields1() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassStatusList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(104);
        modelSOA.setPassStatus(0);
        ocrPassStatusList.add(modelSOA);
        String[]  result = service.drvInApproveFields(ocrPassStatusList);
        Assert.assertTrue(result.length > 0);
    }

    @Test
    public void drvInApproveFields3() throws SQLException {
        String[]  result = service.drvInApproveFields(Lists.newArrayList());
        Assert.assertTrue(result.length > 0);
    }

}
