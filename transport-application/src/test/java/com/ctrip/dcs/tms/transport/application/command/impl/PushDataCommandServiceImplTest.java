package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.*;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.google.common.collect.Maps;
import com.google.common.collect.*;
import okhttp3.Request;
import okhttp3.*;
import org.assertj.core.util.Lists;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.junit.*;

import java.sql.Date;
import java.util.*;

@RunWith(MockitoJUnitRunner.class)
public class PushDataCommandServiceImplTest extends Mockito {

    @InjectMocks
    PushDataCommandServiceImpl pushDataCommandService;
    @Mock
    EnumRepository enumRepository;

    @Mock
    TmsTransportQconfig qconfig;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Before
    public void ready() {
        Mockito.when(qconfig.getHangzhouDataAuthorization()).thenReturn("0");
        Mockito.when(qconfig.getPushDataUrlMap()).thenReturn(ImmutableMap.of("DrvFileDataUrl","wss://dimg04.c-ctrip.com/images/0415n120008gdbsll0C2D.jpg"));
    }

    @Test
    public void pushDrirverInfo() {
        Map<String,String> resultMap = Maps.newHashMap();
        resultMap.put("DriverInfoUrl","1");
        when(qconfig.getPushDataUrlMap()).thenReturn(resultMap);
        when(qconfig.getPushDataSwitch()).thenReturn(true);
        List<DriverVehicleInfoDTO> infoDTOList = Lists.newArrayList();
        DriverVehicleInfoDTO driverVehicleInfoDTO = new DriverVehicleInfoDTO();
        driverVehicleInfoDTO.setDriverId("1");
        infoDTOList.add(driverVehicleInfoDTO);
        when(qconfig.getHangzhouDataAuthorization()).thenReturn("111");
        Boolean b = pushDataCommandService.pushDrirverInfo(infoDTOList);
//        Mockito.when(qconfig.getHangzhouDataAuthorization()).thenReturn("0");
//        Mockito.when(qconfig.getPushDataUrlMap()).thenReturn(ImmutableMap.of());
        Assert.assertTrue(b);
    }

    @Test
    public void prepare4pushDrvUpdateData() {
        pushDataCommandService.prepare4pushDrvUpdateData(1L, CommonEnum.RecordTypeEnum.DRIVER);
        Assert.assertTrue(true);
    }

    @Test
    public void prepare4pushDrvUpdateData1() {
        pushDataCommandService.prepare4pushDrvUpdateData(1L, CommonEnum.RecordTypeEnum.VEHICLE);
        Assert.assertTrue(true);
    }

    @Test
    public void prepare4DrvIncrease() {
        List<CommunicationsDrvInfoPO> drvInfoPOS = Lists.newArrayList();
//        when(drvDrvierRepository.queryDriverInfo4TrafficAgency(PushDataCommandService.HangZhouCityId, ImmutableList.of(1L), TmsTransportConstant.DrvStatusEnum.ONLINE.getCode(),0,1)).thenReturn(drvInfoPOS);
        pushDataCommandService.prepare4DrvIncrease(1L);
        Assert.assertTrue(true);
    }

    @Test
    public void dealDTO() {
        List<CommunicationsDrvInfoPO> drvInfoPOS = Lists.newArrayList();
        CommunicationsDrvInfoPO drvInfoPO = new CommunicationsDrvInfoPO();
        drvInfoPO.setDrvId(1L);
        drvInfoPO.setDrvName("111");
        drvInfoPO.setDrvIdcard("110101199003075592");
        drvInfoPO.setSex("1");
        drvInfoPO.setCertiDate(Date.valueOf("2020-02-02"));
        drvInfoPO.setVehicleId(11L);
        drvInfoPO.setVehicleLicense("11");
        drvInfoPO.setVehicleBrandId(1L);
        drvInfoPO.setVehicleColorId(1L);
        drvInfoPO.setVehicleTypeId(1L);
        drvInfoPO.setVin("111");
        drvInfoPOS.add(drvInfoPO);
        when(enumRepository.getBandName(1L)).thenReturn("1");
        when(enumRepository.getColorName(1L)).thenReturn("1");
        when(enumRepository.getVehicleTypeName(1L)).thenReturn("1");
        List<DriverVehicleInfoDTO> infoDTOS = pushDataCommandService.dealDTO(drvInfoPOS);
        Assert.assertTrue(infoDTOS.size() > 0);
    }

}
