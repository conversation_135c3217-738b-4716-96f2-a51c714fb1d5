package com.ctrip.dcs.tms.transport.application.command.impl;

import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum.TRANSPORT_DRIVER_NAME_NOT_MATCH_LICENSE_NAME;
import static com.ctrip.dcs.tms.transport.infrastructure.common.constant.ErrorCodeEnum.TRANSPORT_DRIVER_UPDATE_ACCOUNT_FAILED;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.sql.SQLException;
import java.util.*;

import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.application.command.*;
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService;
import com.ctrip.dcs.tms.transport.application.query.DriverPasswordService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.QueryCategoryService;
import com.ctrip.dcs.tms.transport.application.query.TmsPmsproductQueryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.model.DriverGuideDTO;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.*;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.Constant;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ChangeRecordAttributeNameQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.OverseasQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TransportCommonQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.MobileHelper;
import com.ctrip.dcs.tms.transport.infrastructure.common.util.TmsTransUtil;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.dal.DalRepositoryImpl;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.assertj.core.util.Lists;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;

import com.ctrip.igt.framework.dal.DalRepository;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SessionHolder.class,Shark.class, TmsTransUtil.class})
@SuppressStaticInitializationFor({"com.ctrip.ibu.platform.shark.sdk.api.Shark"})
public class DriverCommandServiceImplTest {

    @InjectMocks
    private DriverCommandServiceImpl driverCommandService;

    @Mock
    private TmsModRecordCommandService tmsModRecordCommandService;

    @Mock
    private DrvDrvierRepository drvDrvierRepository;

    @Mock
    private DalRepositoryImpl<DrvDriverPO> drvDriverPODalRepository;
    @Mock
    private DriverQueryService driverQueryService;
    @Mock
    DriverGroupRelationRepository driverGroupRelationRepository;
    @Mock
    private ProductionLineUtil productionLineUtil;
    @Mock
    private TmsPmsproductQueryService pmsproductQueryService;
    @Mock
    TmsTransportQconfig qconfig;
    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    private TspTransportGroupDriverRelationRepository tspTransportGroupDriverRelationRepository;
    @Mock
    private TransportGroupRepository transportGroupRepository;

    @Mock
    private TmsTransportApproveRepository approveRepository;
    @Mock
    private DrvHealthPunchRepository drvHealthPunchRepository;
    @Mock
    AuthorizationCheckService authorizationCheckService;
    @Mock
    private DrvRecruitingRepository drvRecruitingRepository;
    @Mock
    private TransportGroupCommandService transportGroupCommandService;

    @Mock
    DrvDispatchRelationRepository drvDispatchRelationRepository;
    @Mock
    private TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Mock
    TmsDrvFreezeRepository tmsDrvFreezeRepository;

    @Mock
    private DrvFreezeRecordRepository drvFreezeRecordRepository;

    @Mock
    TmsTransportApproveCommandService approveCommandService;

    @Mock
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;

    @Mock
    TmsCertificateCheckRepository checkRepository;

    @Mock
    DriverPasswordService service;

    @Mock
    OverseasQconfig overseasQconfig;

    @Mock
    EnumRepository enumRepository;
    @Mock
    TmsVerifyEventCommandService tmsVerifyEventCommandService;
    @Mock
    TmsDrvLoginInformationRepository informationRepository;
    @Mock
    DriverAccountManagementHelper driverAccountManagementHelper;

    @Mock
    DrvVehRecruitingCommandService drvVehRecruitingCommandService;

    @Mock
    ModRecordRespository modRecordRespository;

    @Mock
    RecruitingCommandService recruitingCommandService;

    @Mock
    DalRepository<VehVehiclePO> vehVehicleRepo;

    @Mock
    TransportCommonQconfig commonQconfig;

    @Mock
    MobileHelper mobileHelper;

    @Mock
    DriverGuideProxy driverGuidProxy;

    @Mock
    QueryCategoryService queryCategoryService;

    @Mock
    DriverCommandManager driverCommandManager;

    @Mock
    OverseaDriverCommandHandler overseaDriverCommandHandler;

    @Before
    public void setUp() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setCityId(2L);
        drvDriverPO.setIntendVehicleTypeId("117,118");
        drvDriverPO.setVehicleLicense("asddff");
        PowerMockito.mockStatic(Shark.class);
        PowerMockito.mockStatic(TmsTransUtil.class);
        when(drvDrvierRepository.getDrvDriverRepo()).thenReturn(drvDriverPODalRepository);
        when(drvDriverPODalRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDriverPO);
    }

    @Test
    public void updateDrvIntendVehicleType() {
        UpdateDrvIntendVehicleTypeRequestType updateDrvIntendVehicleTypeRequestType = new UpdateDrvIntendVehicleTypeRequestType();
        updateDrvIntendVehicleTypeRequestType.setDrvId(1L);
        Result<Boolean>  result = driverCommandService.updateDrvIntendVehicleType(updateDrvIntendVehicleTypeRequestType);
        Assert.assertTrue(result.isSuccess() == false);
    }

    @Test
    public void updateDrvTest() {
        DrvDriverPO driverPO1 = new DrvDriverPO();
        driverPO1.setDrvId(1L);
        driverPO1.setIgtCode("2");
        driverPO1.setDrvPhone("********");
        driverPO1.setEmail("12@12");
        driverPO1.setLoginAccount("21431");
        driverPO1.setVehicleId(2L);
        driverPO1.setSupplierId(1L);
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setIgtCode("2");
        driverPO.setDrvPhone("********");
        driverPO.setEmail("12@12");
        driverPO.setLoginAccount("21431");
        driverPO.setVehicleId(1L);
        driverPO.setSupplierId(1L);
        when(drvDrvierRepository.queryByPk(1L)).thenReturn(null);
        when(mobileHelper.isMobileValid(any(),any(),any())).thenReturn(Result.Builder.<Boolean>newResult().success().build());
        Result<Long>  result =  driverCommandService.updateDrv(driverPO,true,Lists.newArrayList());
        Assert.assertTrue(result.isSuccess() == false);
    }

    @Test
    public void updateDrvTest1() {
        DrvDriverPO driverPO1 = new DrvDriverPO();
        driverPO1.setDrvId(1L);
        driverPO1.setIgtCode("2");
        driverPO1.setDrvPhone("********");
        driverPO1.setEmail("12@12");
        driverPO1.setLoginAccount("21431");
        driverPO1.setVehicleId(2L);
        driverPO1.setSupplierId(1L);
        driverPO1.setActive(true);
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setIgtCode("2");
        driverPO.setDrvPhone("********");
        driverPO.setEmail("12@12");
        driverPO.setLoginAccount("21431");
        driverPO.setVehicleId(1L);
        driverPO.setSupplierId(1L);
        driverPO.setActive(true);
        when(drvDrvierRepository.queryByPk(1L)).thenReturn(driverPO1);
        when(driverQueryService.checkWorkPeriod(null)).thenReturn(true);
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setSupplierId(1L);
        vehiclePO.setHasDrv(false);
        when(vehicleRepository.queryByPk(1L)).thenReturn(vehiclePO);
        List<TspTransportGroupDriverRelationPO> driverRelationPOS = Lists.newArrayList();
        TspTransportGroupDriverRelationPO relationPO = new TspTransportGroupDriverRelationPO();
        relationPO.setTransportGroupId(1L);
        driverRelationPOS.add(relationPO);
        when(tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Lists.newArrayList(1L))).thenReturn(driverRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setTransportGroupName("231");
        transportGroupPOList.add(transportGroupPO);
        when(transportGroupRepository.queryDriverRelatedTransportGroupByModeList(Lists.newArrayList(1L), null)).thenReturn(transportGroupPOList);

        when(productionLineUtil.bindTransportCheck(null, null)).thenReturn(Result.Builder.<String>newResult().success().build());

        when(mobileHelper.isMobileValid(any(),any(),any())).thenReturn(Result.Builder.<Boolean>newResult().success().build());

        Result<Long>  result =   driverCommandService.updateDrv(driverPO,true,Lists.newArrayList());
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updateDrvTest2() {
        DrvDriverPO driverPO1 = new DrvDriverPO();
        driverPO1.setDrvId(1L);
        driverPO1.setDrvPhone("********");
        driverPO1.setEmail("12@12");
        driverPO1.setLoginAccount("21431");
        driverPO1.setVehicleId(2L);
        driverPO1.setSupplierId(1L);
        driverPO1.setActive(true);
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setIgtCode("2");
        driverPO.setDrvPhone("********");
        driverPO.setEmail("12@12");
        driverPO.setLoginAccount("21431");
        driverPO.setSupplierId(1L);
        driverPO.setActive(true);
        when(drvDrvierRepository.queryByPk(1L)).thenReturn(driverPO1);
        when(driverQueryService.checkWorkPeriod(null)).thenReturn(true);
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setSupplierId(1L);
        vehiclePO.setHasDrv(false);
//        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehiclePO);
        List<TspTransportGroupDriverRelationPO> driverRelationPOS = Lists.newArrayList();
        TspTransportGroupDriverRelationPO relationPO = new TspTransportGroupDriverRelationPO();
        relationPO.setTransportGroupId(1L);
        driverRelationPOS.add(relationPO);
        when(tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Lists.newArrayList(1L))).thenReturn(driverRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setTransportGroupName("231");
        transportGroupPOList.add(transportGroupPO);
        when(mobileHelper.isMobileValid(any(),any(),any())).thenReturn(Result.Builder.<Boolean>newResult().success().build());

        //        Mockito.when(transportGroupRepository.queryDriverRelatedTransportGroupByModeList(Lists.newArrayList(1L), null)).thenReturn(transportGroupPOList);

//        Mockito.when(productionLineUtil.bindTransportCheck(null, null)).thenReturn(Result.Builder.<String>newResult().success().build());


        Result<Long>  result =    driverCommandService.updateDrv(driverPO,true,Lists.newArrayList());
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void updateDrvTest3() {
        DrvDriverPO driverPO1 = new DrvDriverPO();
        driverPO1.setDrvId(1L);
        driverPO1.setDrvPhone("********");
        driverPO1.setEmail("12@12");
        driverPO1.setLoginAccount("21431");
        driverPO1.setVehicleId(2L);
        driverPO1.setSupplierId(1L);
        driverPO1.setActive(true);
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setIgtCode("2");
        driverPO.setDrvPhone("********");
        driverPO.setEmail("12@12");
        driverPO.setLoginAccount("21431");
        driverPO.setSupplierId(2L);
        driverPO.setActive(true);
        when(drvDrvierRepository.queryByPk(1L)).thenReturn(driverPO1);
        when(driverQueryService.checkWorkPeriod(null)).thenReturn(true);
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setSupplierId(1L);
        vehiclePO.setHasDrv(false);
        //        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehiclePO);
        List<TspTransportGroupDriverRelationPO> driverRelationPOS = Lists.newArrayList();
        TspTransportGroupDriverRelationPO relationPO = new TspTransportGroupDriverRelationPO();
        relationPO.setTransportGroupId(1L);
        driverRelationPOS.add(relationPO);
        when(tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Lists.newArrayList(1L))).thenReturn(driverRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setTransportGroupName("231");
        transportGroupPOList.add(transportGroupPO);
        when(mobileHelper.isMobileValid(any(),any(),any())).thenReturn(Result.Builder.<Boolean>newResult().success().build());
        when(productionLineUtil.isOnlyDayProductLine(Mockito.any())).thenReturn(true);
        when(driverGuidProxy.getGrayControl(Mockito.any())).thenReturn(true);

        Result<Long>  result =    driverCommandService.updateDrv(driverPO,true,Lists.newArrayList());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void updateDrvTest4() {
        DrvDriverPO driverPO1 = new DrvDriverPO();
        driverPO1.setDrvId(1L);
        driverPO1.setDrvPhone("********");
        driverPO1.setEmail("12@12");
        driverPO1.setLoginAccount("21431");
        driverPO1.setVehicleId(2L);
        driverPO1.setSupplierId(1L);
        driverPO1.setActive(true);
        DrvDriverPO driverPO = new DrvDriverPO();
        driverPO.setDrvId(1L);
        driverPO.setIgtCode("2");
        driverPO.setDrvPhone("********");
        driverPO.setEmail("12@12");
        driverPO.setLoginAccount("21431");
        driverPO.setSupplierId(2L);
        driverPO.setActive(true);
        when(drvDrvierRepository.queryByPk(1L)).thenReturn(driverPO1);
        when(driverQueryService.checkWorkPeriod(null)).thenReturn(true);
        VehVehiclePO vehiclePO = new VehVehiclePO();
        vehiclePO.setSupplierId(1L);
        vehiclePO.setHasDrv(false);
        //        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehiclePO);
        List<TspTransportGroupDriverRelationPO> driverRelationPOS = Lists.newArrayList();
        TspTransportGroupDriverRelationPO relationPO = new TspTransportGroupDriverRelationPO();
        relationPO.setTransportGroupId(1L);
        driverRelationPOS.add(relationPO);
        when(tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Lists.newArrayList(1L))).thenReturn(driverRelationPOS);
        List<TspTransportGroupPO> transportGroupPOList = Lists.newArrayList();
        TspTransportGroupPO transportGroupPO = new TspTransportGroupPO();
        transportGroupPO.setTransportGroupId(1L);
        transportGroupPO.setTransportGroupName("231");
        transportGroupPOList.add(transportGroupPO);
        when(mobileHelper.isMobileValid(any(),any(),any())).thenReturn(Result.Builder.<Boolean>newResult().success().build());
        when(productionLineUtil.isOnlyDayProductLine(Mockito.any())).thenReturn(false);
        when(driverGuidProxy.getGrayControl(Mockito.any())).thenReturn(false);

        Result<Long>  result =    driverCommandService.updateDrv(driverPO,true,Lists.newArrayList());
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void driverCommandServiceTest() throws SQLException {
        Boolean result = Boolean.TRUE;
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","2");
        params.put("accountType","1");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        driverCommandService.insertHeadPortraitApprove(new DrvDriverPO(),new DrvDriverPO(), true);
        Assert.assertEquals(result, true);
    }

    @Test
    public void updateDrvUid() {
        Result<Boolean> result = driverCommandService.updateDrvUid(1L,"uid", "1", "1","1");
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void drvHealthPunchAdd() throws SQLException {
        DrvHealthPunchAddRequestType requestType = new DrvHealthPunchAddRequestType();
        requestType.setDrvId(1L);
        DrvHealthPunchPO drvHealthPunchPO = new DrvHealthPunchPO();
        BeanUtils.copyProperties(requestType,drvHealthPunchPO);
        when(drvHealthPunchRepository.insert(drvHealthPunchPO)).thenReturn(1L);
        Result<Boolean> result =  driverCommandService.drvHealthPunchAdd(requestType);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void toDrvPOBean() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setAreaScope(1);
        Mockito.when(qconfig.getOverseasDefaultDrvHead()).thenReturn("111");
        DrvDriverPO result2 = driverCommandService.toDrvPOBean(requestType);
        Assert.assertTrue(result2 != null);
    }

    @Test
    public void toDrvPOBean1() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setAreaScope(0);
        Mockito.when(qconfig.getOverseasDefaultDrvHead()).thenReturn("111");
        DrvDriverPO result2 = driverCommandService.toDrvPOBean(requestType);
        Assert.assertTrue(result2 != null);
    }

    @Test
    public void drvDispatchRelationUpdate() {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","1");
        params.put("accountType","2");
        SessionHolder.setSessionSource(params);
        DrvDispatchRelationUpdateSOARequestType requestType = new DrvDispatchRelationUpdateSOARequestType();
        requestType.setDrvId(1L);
        requestType.setSupplierId(1L);
        requestType.setOperationType(1);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setSupplierId(2L);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setInternalScope(1);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        Result<Boolean> cityResult = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverQueryService.drvDispatchCityGray(drvDriverPO.getCityId())).thenReturn(cityResult);
        Mockito.when(drvDispatchRelationRepository.queryDrvDisPatchRecord(1L, 1L,Boolean.TRUE)).thenReturn(Lists.newArrayList());
        Result<Boolean> result2 = driverCommandService.drvDispatchRelationUpdate(requestType);
        Assert.assertTrue(result2 != null);
    }

    @Test
    public void drvDispatchRelationUpdate1() {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","1");
        params.put("accountType","2");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        DrvDispatchRelationUpdateSOARequestType requestType = new DrvDispatchRelationUpdateSOARequestType();
        requestType.setDrvId(1L);
        requestType.setSupplierId(1L);
        requestType.setOperationType(0);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setSupplierId(2L);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setInternalScope(1);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        Result<Boolean> cityResult = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverQueryService.drvDispatchCityGray(drvDriverPO.getCityId())).thenReturn(cityResult);
        List<DrvDispatchRelationPO> drvDispatchRelationPOS = Lists.newArrayList();
        DrvDispatchRelationPO drvDispatchRelationPO = new DrvDispatchRelationPO();
        drvDispatchRelationPO.setActive(true);
        drvDispatchRelationPOS.add(drvDispatchRelationPO);
        Mockito.when(drvDispatchRelationRepository.queryDrvDisPatchRecord(1L, 1L,Boolean.TRUE)).thenReturn(drvDispatchRelationPOS);
        Mockito.when(drvDispatchRelationRepository.operationDrvDispatchUnBing(1L,1L,"1")).thenReturn(1);
        Mockito.when(transportGroupCommandService.drvDispatchunBoundTransport(1L,SessionHolder.getRestSessionAccountName(),1L)).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.drvDispatchRelationUpdate(requestType);
        Assert.assertTrue(result2 != null);
    }

    @Test
    public void vbkDrvDispatchBinding() {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","2");
        params.put("accountType","1");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        DrvDispatchRelationUpdateSOARequestType requestType = new DrvDispatchRelationUpdateSOARequestType();
        requestType.setDrvId(1L);
        requestType.setSupplierId(1L);
        requestType.setOperationType(0);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setSupplierId(2L);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setInternalScope(1);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        Result<Boolean> cityResult = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverQueryService.drvDispatchCityGray(drvDriverPO.getCityId())).thenReturn(cityResult);
        List<DrvDispatchRelationPO> drvDispatchRelationPOS = Lists.newArrayList();
        DrvDispatchRelationPO drvDispatchRelationPO = new DrvDispatchRelationPO();
        drvDispatchRelationPO.setActive(true);
        drvDispatchRelationPOS.add(drvDispatchRelationPO);
        Mockito.when(drvDispatchRelationRepository.queryDrvDisPatchRecord(1L, 1L,null)).thenReturn(drvDispatchRelationPOS);
        Mockito.when(drvDispatchRelationRepository.operationDrvDispatchUnBing(1L,1L,"1")).thenReturn(1);
        Mockito.when(transportGroupCommandService.drvDispatchunBoundTransport(1L,SessionHolder.getRestSessionAccountName(),1L)).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.vbkDrvDispatchBinding(1L,1L);
        Assert.assertTrue(result2 != null);
    }

    @Test
    public void vbkDrvDispatchBinding1() {
        Map<String, String> params = Maps.newHashMap();
        params.put("accountId","2");
        params.put("accountType","1");
        params.put("accountName","1");
        SessionHolder.setSessionSource(params);
        DrvDispatchRelationUpdateSOARequestType requestType = new DrvDispatchRelationUpdateSOARequestType();
        requestType.setDrvId(1L);
        requestType.setSupplierId(1L);
        requestType.setOperationType(0);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setSupplierId(2L);
        drvDriverPO.setCityId(1L);
        drvDriverPO.setInternalScope(1);
        Mockito.when(drvDrvierRepository.queryByPk(1L)).thenReturn(drvDriverPO);
        Result<Boolean> cityResult = Result.Builder.<Boolean>newResult().success().withData(true).build();
        Mockito.when(driverQueryService.drvDispatchCityGray(drvDriverPO.getCityId())).thenReturn(cityResult);
        List<DrvDispatchRelationPO> drvDispatchRelationPOS = Lists.newArrayList();
        DrvDispatchRelationPO drvDispatchRelationPO = new DrvDispatchRelationPO();
        drvDispatchRelationPO.setActive(false);
        drvDispatchRelationPOS.add(drvDispatchRelationPO);
        Mockito.when(drvDispatchRelationRepository.queryDrvDisPatchRecord(1L, 1L,null)).thenReturn(drvDispatchRelationPOS);
        Mockito.when(drvDispatchRelationRepository.operationDrvDispatchUnBing(1L,1L,"1")).thenReturn(1);
        Mockito.when(transportGroupCommandService.drvDispatchunBoundTransport(1L,SessionHolder.getRestSessionAccountName(),1L)).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.vbkDrvDispatchBinding(1L,1L);
        Assert.assertTrue(result2 != null);
    }

    @Test
    public void saveDrvUnFreezeRecord() {
        List<DrvDriverPO> drvDriverPOList = Lists.newArrayList();
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvStatus(2);
        drvDriverPO.setDrvId(1L);
        drvDriverPOList.add(drvDriverPO);
        Set<Long> set = Sets.newHashSet();
        set.add(1L);
        List<TmsDrvFreezePO> drvFreezePOList = Lists.newArrayList();
        TmsDrvFreezePO drvFreezePO = new TmsDrvFreezePO();
        drvFreezePO.setFreezeStatus(2);
        drvFreezePOList.add(drvFreezePO);
        Mockito.when(tmsDrvFreezeRepository.queryDrvFreezeByDrvIds(set)).thenReturn(drvFreezePOList);
        Boolean result2 = driverCommandService.saveDrvUnFreezeRecord(drvDriverPOList,"111");
        Assert.assertTrue(result2);
    }

    @Test
    public void updateDrvSuccessFollow() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvPhone("111");
        drvDriverPO.setEmail("111");
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setLoginAccount("111");
        List<UpdateCertificateStatusSOADTO> checkStatusList = Lists.newArrayList();
        UpdateCertificateStatusSOADTO soadto = new UpdateCertificateStatusSOADTO();
        soadto.setId(1L);
        soadto.setCheckStatus(1);
        checkStatusList.add(soadto);
        drvDriverPO.setCheckStatusList(checkStatusList);
        DrvDriverPO originDrv = new DrvDriverPO();
        originDrv.setDrvPhone("222");
        originDrv.setEmail("222");
        originDrv.setLoginAccount("111");
        Mockito.when(commonQconfig.getRegisterDriverAccountWithNewInterfaceSwitch()).thenReturn("ON");
        Boolean result2 = driverCommandService.updateDrvSuccessFollow(drvDriverPO,true,1L,1L,originDrv,true);
        Assert.assertTrue(result2);
    }

    @Test
    public void compareAttribute() {
        DrvDriverPO drvierPo = new DrvDriverPO();
        drvierPo.setDrvPhone("111");
        drvierPo.setInternalScope(0);
        drvierPo.setDrvLicenseName("abc");

        DrvDriverPO drvierPo1 = new DrvDriverPO();
        drvierPo1.setDrvPhone("111");
        drvierPo1.setInternalScope(0);
        drvierPo1.setDrvLicenseName("abcd");
        Result<Long> res = driverCommandService.compareAttribute(drvierPo, drvierPo1);
        Assert.assertEquals(res.getCode(), TRANSPORT_DRIVER_NAME_NOT_MATCH_LICENSE_NAME.getCode());
    }

    @Test(expected = BizException.class)
    public void updateDrvSuccessFollowExpectException() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvPhone("111");
        drvDriverPO.setEmail("111");
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setLoginAccount("111");
        List<UpdateCertificateStatusSOADTO> checkStatusList = Lists.newArrayList();
        UpdateCertificateStatusSOADTO soadto = new UpdateCertificateStatusSOADTO();
        soadto.setId(1L);
        soadto.setCheckStatus(1);
        checkStatusList.add(soadto);
        drvDriverPO.setCheckStatusList(checkStatusList);
        DrvDriverPO originDrv = new DrvDriverPO();
        originDrv.setDrvPhone("222");
        originDrv.setEmail("222");
        originDrv.setLoginAccount("111");
        Mockito.when(checkRepository.updateCheckStatus(any(),any())).thenThrow(BizException.class);
        Boolean result2 = driverCommandService.updateDrvSuccessFollow(drvDriverPO,true,1L,1L,originDrv,true);
        Assert.assertTrue(result2);
    }

    @Test
    public void overseasExecuteEditApprove() throws SQLException {
        List<OcrPassStatusModelSOA> ocrPassStatusList = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(8);
        modelSOA.setPassStatus(0);
        ocrPassStatusList.add(modelSOA);
        Mockito.when(driverQueryService.overseasSupplierIsGray(1L, 1)).thenReturn(true);
        Boolean result2 = driverCommandService.overseasExecuteEditApprove(1L,1,ocrPassStatusList);
        Assert.assertTrue(result2);
    }

    @Test
    public void add() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setAreaScope(1);
        requestType.setLoginPwd("111");
        requestType.setProLineList(Arrays.asList(1));
        requestType.setWorkPeriod("111");
        Mockito.when(qconfig.getOverseasDefaultDrvHead()).thenReturn("111");
        Mockito.when(service.isPasswordValid(requestType.getLoginPwd())).thenReturn(false);
        Mockito.when(driverQueryService.checkWorkPeriod("111")).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.addDrv(requestType);
        Assert.assertTrue(!result2.isSuccess());
    }

    @Test
    public void add1() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setAreaScope(1);
        requestType.setLoginPwd("111");
        requestType.setProLineList(Arrays.asList(1));
        requestType.setWorkPeriod("111");
        requestType.setSupplierId(1L);
        requestType.setVersionFlag(1);
        requestType.setOcrPassStatusList(new ArrayList<>());
        requestType.setModifyUser("user");
        requestType.setVehicleId(1L);
        Mockito.when(qconfig.getOverseasDefaultDrvHead()).thenReturn("111");
        Mockito.when(service.isPasswordValid(requestType.getLoginPwd())).thenReturn(true);
        Mockito.when(driverQueryService.checkWorkPeriod("111")).thenReturn(true);
        Mockito.when(drvRecruitingRepository.addDrvRecruiting(Mockito.any())).thenReturn(1L);
        Mockito.when(drvVehRecruitingCommandService.toDoNucleicAcidLabelMap(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new HashMap<>());
        Mockito.when(changeRecordAttributeNameQconfig.getDriverRecordMap()).thenReturn(new HashMap<>());
        Mockito.when(recruitingCommandService.overseasNewBusinessApprove(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyLong(), Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build());
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCategorySynthesizeCode(1);
        Mockito.when(vehicleRepository.queryByPk(Mockito.anyLong())).thenReturn(vehVehiclePO);
        Mockito.when(productionLineUtil.getIntegratedLine(Mockito.anyList())).thenReturn(1);
        Mockito.when(vehicleRepository.getVehVehicleRepo()).thenReturn(vehVehicleRepo);
        Mockito.when(vehVehicleRepo.queryByPk(Mockito.anyLong())).thenReturn(vehVehiclePO);
        Mockito.when(productionLineUtil.bindTransportCheck(Mockito.anyInt(), Mockito.anyInt())).thenReturn(Result.Builder.<String>newResult().success().build());
        Mockito.when(mobileHelper.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().build());
        Mockito.when(driverCommandManager.getHandler(any())).thenReturn(overseaDriverCommandHandler);
        Mockito.when(overseaDriverCommandHandler.support(any())).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.addDrv(requestType);
        Assert.assertTrue(result2.isSuccess());
    }


    @Test
    public void add2() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setAreaScope(1);
        requestType.setLoginPwd("111");
        requestType.setProLineList(Arrays.asList(1));
        requestType.setWorkPeriod("111");
        requestType.setSupplierId(1L);
        requestType.setVersionFlag(1);
        requestType.setOcrPassStatusList(new ArrayList<>());
        requestType.setModifyUser("user");
        requestType.setVehicleId(1L);
        Mockito.when(qconfig.getOverseasDefaultDrvHead()).thenReturn("111");
        Mockito.when(service.isPasswordValid(requestType.getLoginPwd())).thenReturn(true);
        Mockito.when(driverQueryService.checkWorkPeriod("111")).thenReturn(true);
        Mockito.when(drvRecruitingRepository.addDrvRecruiting(Mockito.any())).thenReturn(1L);
        Mockito.when(drvVehRecruitingCommandService.toDoNucleicAcidLabelMap(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(new HashMap<>());
        Mockito.when(changeRecordAttributeNameQconfig.getDriverRecordMap()).thenReturn(new HashMap<>());
        Mockito.when(recruitingCommandService.overseasNewBusinessApprove(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyLong(), Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build());
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setCategorySynthesizeCode(1);
        Mockito.when(vehicleRepository.queryByPk(Mockito.anyLong())).thenReturn(vehVehiclePO);
        Mockito.when(productionLineUtil.getIntegratedLine(Mockito.anyList())).thenReturn(1);
        Mockito.when(vehicleRepository.getVehVehicleRepo()).thenReturn(vehVehicleRepo);
        Mockito.when(vehVehicleRepo.queryByPk(Mockito.anyLong())).thenReturn(vehVehiclePO);
        Mockito.when(qconfig.getCheckBindFlag()).thenReturn(true);
        TspTransportGroupDriverRelationPO tspTransportGroupDriverRelationPO = new TspTransportGroupDriverRelationPO();
        tspTransportGroupDriverRelationPO.setTransportGroupId(1L);
        Mockito.when(tspTransportGroupDriverRelationRepository.queryTransportGroupIdByDrvIds(Mockito.anyList())).thenReturn(Arrays.asList(tspTransportGroupDriverRelationPO));
        TspTransportGroupPO tspTransportGroupPO = new TspTransportGroupPO();
        tspTransportGroupPO.setCategorySynthesizeCode(1);
        Mockito.when(transportGroupRepository.queryTspTransportByIds(Mockito.anyList())).thenReturn(Arrays.asList(tspTransportGroupPO));
        Mockito.when(productionLineUtil.getShowProductionLineList(Mockito.anyInt())).thenReturn(Arrays.asList(1));
        Mockito.when(productionLineUtil.checkBind(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(Result.Builder.<String>newResult().success().build());
        Mockito.when(mobileHelper.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().success().build());
        Mockito.when(driverCommandManager.getHandler(any())).thenReturn(overseaDriverCommandHandler);
        Mockito.when(overseaDriverCommandHandler.support(any())).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.addDrv(requestType);
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void add2MobileCheck() throws SQLException {
        DrvAddSOARequestType requestType = new DrvAddSOARequestType();
        requestType.setAreaScope(1);
        requestType.setLoginPwd("111");
        requestType.setProLineList(Arrays.asList(1));
        requestType.setWorkPeriod("111");
        requestType.setSupplierId(1L);
        requestType.setVersionFlag(1);
        requestType.setOcrPassStatusList(new ArrayList<>());
        requestType.setModifyUser("user");
        requestType.setVehicleId(1L);
        Mockito.when(mobileHelper.isMobileValid(any(), any(), any())).thenReturn(Result.Builder.<Boolean>newResult().fail().withCode("error")
          .build());
        Result<Boolean> result2 = driverCommandService.addDrv(requestType);
        Assert.assertTrue(!result2.isSuccess());
        Assert.assertTrue( "code:" + result2.getCode(), result2.getCode().equals("error"));
    }


    @Test
    public void drvAddPaiayAccount() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDriverPO);
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(),Mockito.anyInt())).thenReturn(false);
        Result<Boolean> result2 = driverCommandService.drvAddPaiayAccount(1L,"111","<EMAIL>");
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void drvAddPaiayAccount1() throws SQLException {
        Result<Boolean> result2 = driverCommandService.drvAddPaiayAccount(null,"111","<EMAIL>");
        Assert.assertTrue(!result2.isSuccess());
    }


    @Test
    public void drvAddPaiayAccount2() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(null);
        Result<Boolean> result2 = driverCommandService.drvAddPaiayAccount(1L,"111","<EMAIL>");
        Assert.assertTrue(!result2.isSuccess());
    }


    @Test
    public void drvAddPaiayAccount3() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setPaiayAccount("1111");
        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDriverPO);
        Result<Boolean> result2 = driverCommandService.drvAddPaiayAccount(1L,"111","<EMAIL>");
        Assert.assertTrue(!result2.isSuccess());
    }


    @Test
    public void drvAddPaiayAccount4() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setPaiayEmail("1111");
        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDriverPO);
        Result<Boolean> result2 = driverCommandService.drvAddPaiayAccount(1L,"111","<EMAIL>");
        Assert.assertTrue(!result2.isSuccess());
    }


    @Test
    public void drvAddPaiayAccount5() throws SQLException {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDriverPO);
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(),Mockito.anyInt())).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.drvAddPaiayAccount(1L,"111","<EMAIL>");
        Assert.assertTrue(!result2.isSuccess());
    }

    @Test
    public void addDrvCommonCheckNew() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayAccount("111");
        drvDriverPO.setPaiayEmail("111");
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(),Mockito.anyInt())).thenReturn(false);
        Result<Boolean> result2 = driverCommandService.addDrvCommonCheckNew(drvDriverPO,false);
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void addDrvCommonCheckNew1() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayEmail("111");
        Result<Boolean> result2 = driverCommandService.addDrvCommonCheckNew(drvDriverPO,false);
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void addDrvCommonCheckNew2() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayAccount("111");
        Result<Boolean> result2 = driverCommandService.addDrvCommonCheckNew(drvDriverPO,false);
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void addDrvCommonCheckNew3() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayAccount("111");
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.addDrvCommonCheckNew(drvDriverPO,false);
        Assert.assertTrue(!result2.isSuccess());
    }

    @Test
    public void addDrvCommonCheckNew4() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayEmail("111");
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        Result<Boolean> result2 = driverCommandService.addDrvCommonCheckNew(drvDriverPO,false);
        Assert.assertTrue(!result2.isSuccess());
    }

    @Test
    public void addDrvCommonCheckNew5() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayEmail("111");
        drvDriverPO.setDrvPhone("123");
        drvDriverPO.setTemporaryDispatchMark(2);
        drvDriverPO.setCityId(1L);
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(), Mockito.anyInt())).thenReturn(false);
        Mockito.when(drvDrvierRepository.drvDispatchcheckDrvPhoneOnly(Mockito.anyString())).thenReturn(drvDriverPO);
        Mockito.when(driverQueryService.drvDispatchCityGray(Mockito.anyLong())).thenReturn(Result.Builder.<Boolean>newResult().success().withData(Boolean.TRUE).build());
        Result<Boolean> result2 = driverCommandService.addDrvCommonCheckNew(drvDriverPO,false);
        Assert.assertTrue(!result2.isSuccess());
    }

    @Test
    public void commonCheckNew() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayAccount("111");
        drvDriverPO.setPaiayEmail("111");
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(),Mockito.anyInt())).thenReturn(false);
        Result<Long> result2 = driverCommandService.commonCheckNew(drvDriverPO,false);
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void commonCheckNew1() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayEmail("111");
        Result<Long> result2 = driverCommandService.commonCheckNew(drvDriverPO,false);
        Assert.assertTrue(result2.isSuccess());
    }


    @Test
    public void commonCheckNew2() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayAccount("111");
        Result<Long> result2 = driverCommandService.commonCheckNew(drvDriverPO,false);
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void commonCheckNew3() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayAccount("111");
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        Result<Long> result2 = driverCommandService.commonCheckNew(drvDriverPO,false);
        Assert.assertTrue(!result2.isSuccess());
    }

    @Test
    public void commonCheckNew4() throws Exception {
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvId(1L);
        drvDriverPO.setInternalScope(1);
        drvDriverPO.setPaiayEmail("111");
        Mockito.when(drvDrvierRepository.checkDrvOnly(Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        Result<Long> result2 = driverCommandService.commonCheckNew(drvDriverPO,false);
        Assert.assertTrue(!result2.isSuccess());
    }

    @Test
    public void createDrvChangeEquipmentEvent() throws Exception {
        CreateDrvChangeEquipmentEventRequestType requestType = new CreateDrvChangeEquipmentEventRequestType();
        requestType.setDriverImei("11");
        requestType.setDrvId(1L);
        DrvDriverPO drvDriverPO = new DrvDriverPO();
        drvDriverPO.setDrvName("111");
        drvDriverPO.setDrvId(1L);
        Mockito.when(drvDrvierRepository.queryByPk(Mockito.anyLong())).thenReturn(drvDriverPO);
        Result<Boolean> result2 = driverCommandService.createDrvChangeEquipmentEvent(requestType);
        Assert.assertTrue(result2.isSuccess());
    }

    @Test
    public void testSendRealNameAuth() {
        // 构建测试数据：原始司机信息
        DrvDriverPO originDrv = new DrvDriverPO();
        originDrv.setDrvId(100L);
        originDrv.setDrvPhone("13800000000");
        originDrv.setDrvName("张三");
        originDrv.setIdcardImg("old_idcard_img_url");
        originDrv.setIdcardBackImg("old_idcard_back_img_url");
        originDrv.setScenePhoto("old_scene_photo_url");
        originDrv.setDrvIdcard("330101199001010000");

        // 构建测试数据：更新后的司机信息 - 手机号变更
        DrvDriverPO updatedDrv = new DrvDriverPO();
        updatedDrv.setDrvId(100L);
        updatedDrv.setDrvPhone("13900000000"); // 手机号变更
        updatedDrv.setDrvName("张三");
        updatedDrv.setIdcardImg("old_idcard_img_url");
        updatedDrv.setIdcardBackImg("old_idcard_back_img_url");
        updatedDrv.setScenePhoto("old_scene_photo_url");
        updatedDrv.setDrvIdcard("330101199001010000");

        // 调用测试方法
        driverCommandService.sendRealNameAuth(updatedDrv, originDrv);

        // 验证tmsQmqProducerCommandService.sendRealNameAuth被调用，且参数为包含司机ID的列表
        Mockito.verify(tmsQmqProducerCommandService).sendRealNameAuth(Collections.singletonList(updatedDrv.getDrvId()));

        // 重置Mock
        Mockito.reset(tmsQmqProducerCommandService);

        // 测试场景：没有信息变更时，不应调用sendRealNameAuth
        DrvDriverPO noChangeDrv = new DrvDriverPO();
        noChangeDrv.setDrvId(100L);
        noChangeDrv.setDrvPhone("13800000000");
        noChangeDrv.setDrvName("张三");
        noChangeDrv.setIdcardImg("old_idcard_img_url");
        noChangeDrv.setIdcardBackImg("old_idcard_back_img_url");
        noChangeDrv.setScenePhoto("old_scene_photo_url");
        noChangeDrv.setDrvIdcard("330101199001010000");

        // 调用测试方法
        driverCommandService.sendRealNameAuth(noChangeDrv, originDrv);

        // 验证tmsQmqProducerCommandService.sendRealNameAuth未被调用
        Mockito.verify(tmsQmqProducerCommandService, Mockito.never()).sendRealNameAuth(Mockito.anyList());
    }
}
