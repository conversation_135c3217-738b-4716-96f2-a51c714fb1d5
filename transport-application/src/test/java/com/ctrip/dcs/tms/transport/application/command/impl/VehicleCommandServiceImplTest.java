package com.ctrip.dcs.tms.transport.application.command.impl;

import com.ctrip.dcs.tms.transport.api.model.CategorySOADTO;
import com.ctrip.dcs.tms.transport.api.model.OcrPassStatusModelSOA;
import com.ctrip.dcs.tms.transport.api.model.VehicleDetailDTO;
import com.ctrip.dcs.tms.transport.api.model.VehicleUpdateSOARequestType;
import com.ctrip.dcs.tms.transport.application.command.TmsQmqProducerCommandService;
import com.ctrip.dcs.tms.transport.application.command.TmsTransportApproveCommandService;
import com.ctrip.dcs.tms.transport.application.query.AuthorizationCheckService;
import com.ctrip.dcs.tms.transport.application.query.DriverQueryService;
import com.ctrip.dcs.tms.transport.application.query.QueryCategoryService;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.extenal.rpc.DriverGuideProxy;
import com.ctrip.dcs.tms.transport.infrastructure.adapter.storage.db.dcstranportdb.po.VehVehiclePO;
import com.ctrip.dcs.tms.transport.infrastructure.common.cache.SessionHolder;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ProductionLineUtil;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.CheckComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.ChangeRecordAttributeNameQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.TmsTransportQconfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.*;
import com.ctrip.igt.framework.common.result.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Date;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class VehicleCommandServiceImplTest {

    @InjectMocks
    VehicleCommandServiceImpl service;
    @Mock
    private VehicleRepository vehicleRepository;
    @Mock
    private DrvDrvierRepository drvDrvierRepository;
    @Mock
    TmsTransportApproveCommandService approveCommandService;

    @Mock
    private ChangeRecordAttributeNameQconfig changeRecordAttributeNameQconfig;
    @Mock
    ModRecordRespository modRecordRespository;
    @Mock
    TmsTransportQconfig tmsTransportQconfig;
    @Mock
    AuthorizationCheckService authorizationCheckService;
    @Mock
    private EnumRepository enumRepository;
    @Mock
    private VehicleRecruitingRepository vehicleRecruitingRepository;
    @Mock
    private DriverQueryService driverQueryService;
    @Mock
    TmsQmqProducerCommandService tmsQmqProducerCommandService;

    @Mock
    VehicleGlobalIdRecordRepository vehicleGlobalIdRecordRepository;

    @Mock
    ProductionLineUtil productionLineUtil;

    @Mock
    QueryCategoryService queryCategoryService;

    @Mock
    DriverGuideProxy driverGuidProxy;

    @Test
    public void updateVehicle() throws SQLException {
        VehicleUpdateSOARequestType requestType = new VehicleUpdateSOARequestType();
        requestType.setVehicleId(1L);
        requestType.setAreaScope(1);
        requestType.setVehicleTypeId(1L);
        requestType.setModifyUser("11");
        VehicleDetailDTO vehicleDetailDTO = new VehicleDetailDTO();
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setVehicleId(1L);
        vehVehiclePO.setVehicleTypeId(1L);
        vehVehiclePO.setModifyUser("11");
        vehVehiclePO.setRegstDate(new Date(1111L));
        vehVehiclePO.setSupplierId(1L);
        vehVehiclePO.setVehicleLicense("1");
        vehVehiclePO.setActive(true);
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehVehiclePO);
//        Mockito.when(changeRecordAttributeNameQconfig.getVehicleRecordMap()).thenReturn(Maps.newHashMap());
//        Mockito.when(approveCommandService.checkColumnApproveIng(AddApproveDTO.buildcheckColumnApproveIngDTO(1L,vehVehiclePO, TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE))).thenReturn(false);
        Result<Long> result = Result.Builder.<Long>newResult().success().withData(1L).build();
//        Mockito.when(approveCommandService.insertApprove(AddApproveDTO.buildAddDTO(1L,1L,vehVehiclePO,"1",TmsTransportConstant.ApproveSourceTypeEnum.VEHICLE,changeRecordAttributeNameQconfig.getVehicleRecordMap(),vehVehiclePO.getModifyUser(),null), TmsTransportConstant.EnentTypeEnum.drvAndVehicleUpdate)).thenReturn(result);
//        Mockito.when(drvDrvierRepository.updateDrvVehicleType(1L, 1L)).thenReturn(1);
//        Mockito.when(modRecordRespository.insetModRecord(1L, vehVehiclePO, CommonEnum.RecordTypeEnum.VEHICLE, changeRecordAttributeNameQconfig.getVehicleRecordMap(),"11")).thenReturn(true);
//        Mockito.when(vehicleRepository.updateVehicle(vehVehiclePO)).thenReturn(1);
//        Mockito.when(drvDrvierRepository.queryDriverByVehicleId(1L)).thenReturn(1L);
//        Mockito.when(tmsTransportQconfig.getUpdateApproveSwitch()).thenReturn(true);
        try {
            Result<Boolean>  result1 = service.updateVehicle(requestType);
            Assert.assertTrue(!result1.isSuccess());
        }catch (Exception e){

        }
    }

    @Test
    public void updateVehicleWithSupplierCityCategoryChange() throws SQLException {
        VehicleUpdateSOARequestType requestType = new VehicleUpdateSOARequestType();
        requestType.setVehicleId(1L);
        requestType.setAreaScope(1);
        requestType.setVehicleTypeId(1L);
        requestType.setModifyUser("11");
        requestType.setSupplierId(1L);
        requestType.setCityId(1L);
        requestType.setProLineList(Lists.newArrayList(4));
        VehicleDetailDTO vehicleDetailDTO = new VehicleDetailDTO();
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        vehVehiclePO.setVehicleId(1L);
        vehVehiclePO.setVehicleTypeId(1L);
        vehVehiclePO.setModifyUser("11");
        vehVehiclePO.setRegstDate(new Date(1111L));
        vehVehiclePO.setSupplierId(1L);
        vehVehiclePO.setVehicleLicense("1");
        vehVehiclePO.setActive(true);
        vehVehiclePO.setCategorySynthesizeCode(1);
        Mockito.when(vehicleRepository.queryByPk(1L)).thenReturn(vehVehiclePO);
        Mockito.when(productionLineUtil.isProductLineCodeNewAddDayCheck(Mockito.any(), Mockito.any())).thenReturn(false);
        Mockito.when(productionLineUtil.isOnlyDayProductLine(Mockito.any())).thenReturn(true);
        Mockito.when(driverGuidProxy.getGrayControl(Mockito.any())).thenReturn(true);
        Mockito.when(queryCategoryService.getContractList(Mockito.any(), Mockito.anyList())).thenReturn(Lists.newArrayList(new CategorySOADTO()));
        Result<Long> result = Result.Builder.<Long>newResult().success().withData(1L).build();

        Result<Boolean>  result1 = service.updateVehicle(requestType);
        Assert.assertTrue(!result1.isSuccess());
    }

    @Test
    public void resultErrorInfo() {
        Result<Boolean> result = service.resultErrorInfo("code", "111", null, false);
        Assert.assertTrue(!result.isSuccess());
    }

    @Test
    public void approveCopyOrgin(){
        VehVehiclePO vehVehiclePO = new VehVehiclePO();
        VehVehiclePO vehicleDetailDTO = new VehVehiclePO();
        vehicleDetailDTO.setCityId(1L);
        Long supplierId = 1L;
        List<OcrPassStatusModelSOA> modelSOAS = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(104);
        modelSOA.setPassStatus(0);
        modelSOAS.add(modelSOA);
        modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(2L);
        modelSOA.setOcrItem(105);
        modelSOA.setPassStatus(0);
        modelSOAS.add(modelSOA);
        Mockito.when(enumRepository.getAreaScope(vehicleDetailDTO.getCityId())).thenReturn(1);
        Mockito.when(driverQueryService.overseasSupplierIsGray(supplierId, 1)).thenReturn(false);
        CheckComplianceDTO checkComplianceDTO = new CheckComplianceDTO();
        Boolean result =  service.approveCopyOrgin(vehVehiclePO,vehicleDetailDTO,supplierId,modelSOAS,Boolean.FALSE, checkComplianceDTO);
        Assert.assertTrue(result);
    }

    @Test
    public void syncOverseasDrvVehLic(){
        List<OcrPassStatusModelSOA> modelSOAS = Lists.newArrayList();
        OcrPassStatusModelSOA modelSOA = new OcrPassStatusModelSOA();
        modelSOA.setOcrId(1L);
        modelSOA.setOcrItem(104);
        modelSOA.setPassStatus(1);
        modelSOAS.add(modelSOA);
        Map<String, String> params = Maps.newHashMap();
        params.put("accountName","111");
        SessionHolder.setSessionSource(params);
        Boolean result =  service.syncOverseasDrvVehLic(1L,"111",modelSOAS);
        Assert.assertTrue(result);
    }

    @Test
    public void generateGlobalId(){
        Mockito.when(vehicleGlobalIdRecordRepository.generateGlobalId("111","111")).thenReturn(Result.Builder.<Long>newResult().success().withData(1L).build());
        Result<Long> generateGlobalId = service.generateGlobalId("111", "111");
        Assert.assertTrue(generateGlobalId.isSuccess());
    }
}
